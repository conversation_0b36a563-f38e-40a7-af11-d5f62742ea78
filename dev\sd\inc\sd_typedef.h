/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef SD_TYPEDEF_H
#define SD_TYPEDEF_H
//define SD RSP TYPE
#define NO_RESP 					SDC_CMD_NC_NB   //send cmd. without receive response; without check busy
#define RESP_1  					SDC_CMD_NC_6B   //send cmd. receive 6 byte response; without check busy
#define RESP_1B 					SDC_CMD_WC_6B   //send cmd. receive 6 byte response; with check busy
#define RESP_2  					SDC_CMD_NC_17B  //send cmd. receive 17 byte response; without check busy
#define RESP_3  					SDC_CMD_NC_6B  //send cmd. receive 6 byte response; without check busy
#define RESP_6  					SDC_CMD_NC_6B  //send cmd. receive 6 byte response; without check busy
#define RESP_7  					SDC_CMD_NC_6B   //send cmd. receive 6 byte response; without check busy

//define SD CMD TYPE
#define SD_CMD_IDLE						0		//NO_RESP
#define MMC_CMD_OP_COND					1		//RESP_3
#define SD_CMD_ALLSEND_CID				2		//RESP_2
#define SD_CMD_SEND_RCA					3		//RESP_6
#define SD_CMD_SWITCH_FUNC				6		//RESP_1
#define SD_CMD_SELECT_CARD				7		//RESP_1B
#define SD_CMD_IF_CON					8		//RESP_1
#define	MMC_CMD_SEND_EXT_CSD			8		//RESP_1
#define SD_CMD_SEND_CSD					9		//RESP_2
#define SD_CMD_SEND_CID					10		//RESP_2
#define SD_CMD_VOLTAGE_SWITCH			11		//RESP_1
#define SD_CMD_STOP						12		//RESP_1B
#define SD_CMD_SEND_STATUS				13		//RESP_1
#define	SDMMC_CMD_BUSTEST_R				14		//RESP_1
#define SD_CMD_INACTIVE					15		//NO_RESP
#define SD_CMD_SETBLOCKLEN				16		//RESP_1
#define SD_CMD_READ_SINGLE_BLOCK		17		//RESP_1
#define SD_CMD_READ_MULTI_BLOCK			18		//RESP_1
#define	SD_SEND_TUNING_BLOCK 			19		//RESP_1
#define SD_CMD_WRITE_SINGLE_BLOCK		24		//RESP_1
#define SD_CMD_WRITE_MULTI_BLOCK		25		//RESP_1
#define SD_CMD_PROGRAM_CSD				27		//RESP_1
#define SD_CMD_SET_WRITE_PROTECT		28		//RESP_1B
#define SD_CMD_CLR_WRITE_PROTECT		29		//RESP_1B
#define SD_CMD_SEND_WRITE_PROTECT		30		//RESP_1
#define SD_CMD_ERASE_WR_BLK_START		32		//RESP_1
#define SD_CMD_ERASE_WR_BLK_END			33		//RESP_1
#define SD_CMD_ERASE					38		//RESP_1B
#define SD_CMD_APP						55		//RESP_1
#define SD_CMD_GEN						56		//RESP_1

#define SD_ACMD_SETBUSWIDTH				6		//RESP_1	//2B'00:1BIT, 2B'10:4BIT
#define SD_ACMD_SETSDSTATUS				13		//RESP_1	
#define SD_ACMD_SEND_NUM_WR_BLOCKS		22		//RESP_1	32BIT RSP+CRC
#define SD_ACMD_SET_WR_BLK_ERASE_COUNT	23		//RESP_1
#define SD_ACMD_APP_OP_COND				41		//RESP_3:ocr without busy
#define SD_ACMD_SET_CLR_CARD_SELECT		42		//RESP_1: [1]SET/[0]CLR
#define SD_ACMD_SEND_SCR				51		//RESP_1

//OCR位域
#define OCR_ALL_READY           		0x80000000                              
#define OCR_HC_CCS              		0x40000000                              
#define	OCR_SEC_MODE					0x40000000
#define OCR_S18R              			0x01000000  

//chrt 定义
//BIT 0-0: sd cmd8 suport	 (yes/no)
#define	SD_CMD8_CHRT					(~(1 << 0))
#define	SD_CMD8_SPT						(1 << 0)	
#define	SD_CMD8_CAT						(0 << 0)
//BIT 1-3: card type (sd/mmc/ms/cf)
#define	CARD_TYPE						(~(3 << 1))
#define	CARD_NO							(0 << 1)
#define	CARD_SD							(1 << 1)
#define	CARD_MMC						(2 << 1)

//BIT 4-4: capacity   (<2G / >2G)
#define	SD_CAP_TYPE						(~(1 << 4))
#define SD_NCS							(0 << 4)
#define	SD_HCS							(1 << 4)

//BIT 5-5: function switch (en/disable)
#define	SD_CMD6							(~(1 << 5))
#define	SD_CMD6_SPT						(1 << 5)	
#define	SD_CMD6_CAT						(0 << 5)

//BIT 6-7: func 
#define	SD_FUNC_TYPE					(~(3 << 6))
#define	SD_FUNC_SDR12					(0 << 6)
#define	SD_FUNC_SDR25					(1 << 6)
//#define	SD_FUNC_SDR50				(2 << 6)











typedef struct SD_CMD_OP_S{
	s8  cur_idx;
	s8  next_idx;
	u8  cmd;
	u8  rsp;
	u32 arg;
	int (*probe)(void);
	int (*pack)(void);
}SD_CMD_OP_T;
typedef enum
{
	SDC_STATE_NULL = 0,
    
    SDC_STATE_READ,
    SDC_STATE_WRITE,
 //   SD_ABEND,
    SDC_STATE_FREE,
    SDC_STATE_ERROR,
}SDC_STAT_E;
typedef enum
{
    SDC_TYPE_NULL,
    SDC_TYPE_10,
    SDC_TYPE_20_NC,
    SDC_TYPE_20_HC,
    SDC_TYPE_MMC
}SDC_TYPE_E;


typedef struct SDC_STAT_S
{
	u8 	    busy;  
    u8		chrt;
	u8	    eCardState;
	u8   	bus_width;
    u32   	dwCap;
	u32		dwRCA;
    u32   	dwNextLBA;
	//u32     mmcdevtype;
	SD_CMD_OP_T	SdCmdOp;
	int     loopcnt;
}SDC_STAT_T;

#define		set_chrt(reg,flag,val)		reg = ((reg & flag) | (val))
#define		get_chrt(reg,flag)			(reg & (~flag))








#endif
