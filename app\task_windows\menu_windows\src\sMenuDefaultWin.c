/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DEFAULT_TIPS_ID =0,
	DEFAULT_SELECT_ID
};
UNUSED ALIGNED(4) const widgetCreateInfor defaultWin[] =
{
#if UI_SHOW_SMALL_PANEL == 0
	createFrameWin(						Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_GREY_W,WIN_ABS_POS),
	createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	<PERSON>y(0), 	Rw(180),Rh(100),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createItemManage(DEFAULT_SELECT_ID,	Rx(0),	Ry(100),Rw(180),Rh(40),	INVALID_COLOR),
#else
	createFrameWin(						Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_DimGray,WIN_ABS_POS),
	createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createItemManage(DEFAULT_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40),	INVALID_COLOR),
#endif
	widgetEnd(),
};



