/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"
#define  ICON_BUFF_NUM         5
#define  ICON_BUFF_SIZE        (32*32L)



typedef struct Icon_Info_S
{
	u32 width;
	u32 height;
	u32 offset;
}Icon_Info_T; //DO NOT MODIFY THIS
typedef struct Icon_Buff_S
{
	u32 resid;
	u32 resAddr;
	u32 use_time;
	u16 width;
	u16 height;	
	u32 datalen;
	u8* databuf;	
}Icon_Buff_T;
typedef struct Icon_Ctrl_S
{
	u32 resStartAddr;
	u32 curIconId;
	Icon_Info_T curIconInfo;
	R_ICON_T *R_Icon_Table;
	u32		  R_Icon_ID_Max;
	Icon_Buff_T icon[ICON_BUFF_NUM];
}Icon_Ctrl_T;

ALIGNED(4) static Icon_Ctrl_T iconCtrl;
/*******************************************************************************
* Function Name  : R_loadResource
* Description    : load resource table             
* Input          : R_ICON_T *res : resource table  
                   unsigned long int r_id : resource id
				   unsigned long int cnt  : table length
* Output         : 
* Return         : none
*******************************************************************************/
static void res_iconTableInit(R_ICON_T *res,u32 r_id_max)
{
	u32 r_id;
	u32 i;
	if(res == NULL)
		return;
	r_id = *((u32 *)res);
	
    if(RES_ID_IS_ICON(r_id))
	{
		R_ICON_T * icon_table 		= (R_ICON_T*)res;
		iconCtrl.R_Icon_Table  		= (R_ICON_T*)res;
		iconCtrl.R_Icon_ID_Max 		= r_id_max;
		for(i = 0; i < RES_IDNUM_GET(r_id_max); i++ )
		{
			icon_table->r_addr   = 0;
			icon_table->r_width  = 0;
			icon_table->r_height = 0;
			icon_table++;
		}
	}
}
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_iconInit(INT16U idx,R_ICON_T *res,u32 r_id_max)
{
	int ret;
	iconCtrl.resStartAddr = 0;
	iconCtrl.curIconId	  = 0xffffffff;
	iconCtrl.R_Icon_Table = (R_ICON_T*)NULL;
	ret = nv_open(idx);
	if(ret < 0)
	{
		deg_Printf("[RES] icon init err\n");
		return -1;
	}
    iconCtrl.resStartAddr 	= ret;
	deg_Printf("[RES] icon init ok :%x\n",iconCtrl.resStartAddr);
	res_iconTableInit(res, r_id_max);
	return 0;
}
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
void res_iconBuffInit(void)
{
	int i;
	for(i = 0; i < ICON_BUFF_NUM; i++)
	{
		hal_sysMemFree(iconCtrl.icon[i].databuf);
		iconCtrl.icon[i].resid 		= INVALID_RES_ID;
		iconCtrl.icon[i].use_time 	= 0;
		iconCtrl.icon[i].databuf	= NULL;
		iconCtrl.icon[i].datalen    = 0;
		
	}
}
/*******************************************************************************
* Function Name  : res_iconGetSize
* Description    : get icon size
* Input          : int num : icon index
				   INT16U *width : width
				   INT16U *height: height
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_iconGetSizeByIndex(int num,u16 *width,u16 *height)
{
	if(iconCtrl.resStartAddr == 0)
	{
		return -1;
	}
    if(iconCtrl.curIconId != num)
    {
		nv_read(iconCtrl.resStartAddr + sizeof(Icon_Info_T)*num, (void *)&iconCtrl.curIconInfo, sizeof(Icon_Info_T));
		iconCtrl.curIconId = num;
    }

	if(width)
		*width = iconCtrl.curIconInfo.width;
	if(height)
		*height = iconCtrl.curIconInfo.height;

	return 0;
}
/*******************************************************************************
* Function Name  : res_iconGetAddr
* Description    : get icon addr
* Input          : int num : icon index                      
* Output         : none                                            
* Return         : int : address
*******************************************************************************/
int res_iconGetAddrByIndex(int num)
{
	if(iconCtrl.resStartAddr == 0)
	{
		return 0;
	}
	if(iconCtrl.curIconId != num)
	{
		nv_read(iconCtrl.resStartAddr + sizeof(Icon_Info_T)*num, (void*)&iconCtrl.curIconInfo, sizeof(Icon_Info_T));
		iconCtrl.curIconId = num;
	}

	return (iconCtrl.resStartAddr + iconCtrl.curIconInfo.offset);
}
/*******************************************************************************
* Function Name  : R_iconGetIndex
* Description    : get icon index        
* Input          :  unsigned long int r_id : icon id
* Output        : 
* Return         : int  -1 : fail
                            :index  
******************************************************************************/
static R_ICON_T* res_icon_GetTab(u32 r_id)
{
    u32 index,i;
	R_ICON_T * icon_table;
	if(RES_ID_IS_ICON(r_id))
	{
	    if(r_id >= iconCtrl.R_Icon_ID_Max || iconCtrl.R_Icon_Table == NULL)
			return NULL;
		index = RES_IDNUM_GET(r_id);
		icon_table = iconCtrl.R_Icon_Table + index;
		if(icon_table->r_id == r_id)
			return icon_table;
			
		for(i=0;i < RES_IDNUM_GET(iconCtrl.R_Icon_ID_Max);i++)
		{
			if(icon_table->r_id == r_id)
				return icon_table;
		}
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : R_iconGetDataAndSize
* Description    : get icon data and size        
* Input          :  unsigned long int r_id : icon id
                       unsigned short *width : width
                       unsigned short *heigth:height
* Output        : 
* Return         : int  0 : fail
                            :data  
*******************************************************************************/
u32 res_icon_GetAddrAndSize(u32 r_id,u16 *width,u16 *height)
{
	R_ICON_T * icon_table = res_icon_GetTab(r_id);
	if(icon_table)
	{
		if(icon_table->r_width == 0 || icon_table->r_height == 0)
			res_iconGetSizeByIndex(RES_IDNUM_GET(r_id),&(icon_table->r_width),&(icon_table->r_height));
		if(icon_table->r_addr == 0)
			icon_table->r_addr = res_iconGetAddrByIndex(RES_IDNUM_GET(r_id));
		if(width)
			*width  = icon_table->r_width;
		if(height)
			*height = icon_table->r_height;
		return icon_table->r_addr;
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : R_iconGetTColor
* Description    : free icon t color       
* Input          :  unsigned long int r_id : icon id
* Output        : 
* Return         : int 
                            :color  
*******************************************************************************/
u32 res_icon_GetTColor(u32 r_id)
{
	R_ICON_T * icon_table = (R_ICON_T*)res_icon_GetTab(r_id);
	if(icon_table)
	{
		return icon_table->r_tcolor;
	}
	return INVALID_COLOR;
}	

/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
void res_iconBuffTimeUpdate(void)
{
	int i;
	for(i = 0; i < ICON_BUFF_NUM; i++)
		iconCtrl.icon[i].use_time++;
}
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
static void res_iconBuffUpdate(u32 id,Icon_Buff_T* icon)
{
	u16 width,height;
	u32 len;
	u32 addr;
	addr = res_icon_GetAddrAndSize(id,&width,&height);
	if(addr == 0)
		return;
	len = width*height;
	if(icon->databuf)
	{
		if(icon->datalen < len)
		{
			hal_sysMemFree(icon->databuf);
			icon->databuf = NULL;
		}else if(icon->datalen >= len*4)
		{
			hal_sysMemFree(icon->databuf);
			icon->databuf = NULL;
		}
	}
	if(icon->databuf == NULL)
	{
		icon->datalen = (len+0x3ff)&~0x3ff;
		icon->databuf = hal_sysMemMalloc((len+0x3ff)&~0x3ff);
	}
	if(icon->databuf == NULL)
	{
		icon->resid = INVALID_RES_ID;
		return;
	}
	if(nv_read(addr,(void *)icon->databuf,len)<0)
		return;
	icon->resid 	= id;
	icon->resAddr 	= addr;
	icon->use_time  = 0;
	icon->width		= width;
	icon->height	= height;
}
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u8* res_iconGetData(u32 id,u32 resaddr,u32 width)
{
	u32 i,num=0;
	Icon_Buff_T* buff=NULL;
	u32 notUseTimeMax = 0;
	for(i=0;i<ICON_BUFF_NUM;i++)
	{
		if(iconCtrl.icon[i].resid == id)
			break;
		if(iconCtrl.icon[i].resid == INVALID_RES_ID)
			buff = &iconCtrl.icon[i];
		if(notUseTimeMax < iconCtrl.icon[i].use_time)
		{
			notUseTimeMax = iconCtrl.icon[i].use_time;
			num = i;
		}
	}
	if(i < ICON_BUFF_NUM)
	{
		if(iconCtrl.icon[i].use_time > 2)
			iconCtrl.icon[i].use_time -=2;
		else
			iconCtrl.icon[i].use_time = 0;
		return iconCtrl.icon[i].databuf + (resaddr - iconCtrl.icon[i].resAddr);
	}
	if(buff == NULL)
	{
		buff = &iconCtrl.icon[num]; 
	}
	res_iconBuffUpdate(id,buff);
	
	if(buff->resid != id)
	{
		deg_Printf("load icon [%x] failed\n",id);
		return NULL;
	}
	return buff->databuf + (resaddr - buff->resAddr);
}
/*******************************************************************************
* Function Name  : res_iconGetPalette
* Description    : get icon palette
* Input          : u8 *buffer : buffer 
* Output         : none                                            
* Return         : int : palette size
*******************************************************************************/
int res_iconGetPalette(INT16U idx, u8* buffer)
{
	int ret,size;

	ret = nv_open(idx);
	if(ret<0)
	{
		deg_Printf("[RES] palette init err\n");
		return -1;
	}
	size = nv_size(idx);

	nv_read(ret,buffer,size);

	return size;
}

