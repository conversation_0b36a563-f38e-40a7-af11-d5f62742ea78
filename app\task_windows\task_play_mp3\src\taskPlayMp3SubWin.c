/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYMP3SUB_LRC_ID =0,
	PLAYMP3SUB_PROCESSBAR_ID,
	PLAYMP3SUB_PLAYCURTIME_ID,
	PLAYMP3SUB_PLAYMODE_ID,
	PLAYMP3SUB_PLAYSTAT_ID,
	PLAYMP3SUB_PLAYTOTALTIME_ID,
	
};

UNUSED ALIGNED(4) const widgetCreateInfor playMp3SubWin[] = 
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Black,WIN_ABS_POS),
	createStringEx(PLAYMP3SUB_LRC_ID,			Rx(0),   Ry(0),   Rw(320), Rh(200),RAM_ID_MAKE(" "),ALIGNMENT_CENTER,R_ID_PALETTE_White, DEFAULT_FONT, ALIGNMENT_CENTER,R_ID_PALETTE_Blue, DEFAULT_FONT,16),
	createProgressBar(PLAYMP3SUB_PROCESSBAR_ID,	Rx(0),   Ry(200), Rw(320), Rh(5),  R_ID_PALETTE_DarkGray,R_ID_PALETTE_DoderBlue,ALIGNMENT_LEFT),	
	createStringIcon(PLAYMP3SUB_PLAYCURTIME_ID,	Rx(0),   Ry(210), Rw(80),  Rh(30), RAM_ID_MAKE(" "),ALIGNMENT_LEFT,  R_ID_PALETTE_White,DEFAULT_FONT),	
	createImageIcon(PLAYMP3SUB_PLAYMODE_ID,     Rx(80),  Ry(210), Rw(40),  Rh(30), R_ID_ICON_MUSICMODECONTINUE,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3SUB_PLAYSTAT_ID,     Rx(140), Ry(210), Rw(40),  Rh(30), R_ID_ICON_MTPLAY,ALIGNMENT_CENTER),
	createStringIcon(PLAYMP3SUB_PLAYTOTALTIME_ID,Rx(240),  Ry(210), Rw(80),Rh(30), RAM_ID_MAKE(" "),ALIGNMENT_RIGHT,  R_ID_PALETTE_White,DEFAULT_FONT),	
																						
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playMp3SubPlayRateShow
* Description    : playMp3SubPlayRateShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubPlayRateShow(winHandle handle)
{
	uiWinSetPorgressRate(winItem(handle,PLAYMP3SUB_PROCESSBAR_ID),(SysCtrl.play_cur_time * 100)/SysCtrl.play_total_time);
}
/*******************************************************************************
* Function Name  : playMp3SubPlayCurTimeShow
* Description    : playMp3SubPlayCurTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubPlayCurTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYMP3SUB_PLAYCURTIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
	//uiWinSetVisible(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID),1);
}
/*******************************************************************************
* Function Name  : playMp3SubPlayTotalTimeShow
* Description    : playMp3SubPlayTotalTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubPlayTotalTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYMP3SUB_PLAYTOTALTIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
	//uiWinSetVisible(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID),1);
}
/*******************************************************************************
* Function Name  : playMp3SubPlayTotalTimeShow
* Description    : playMp3SubPlayTotalTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubPlayModeShow(winHandle handle)
{
	if(task_playMp3_op.playmode == PLAY_MP3_MODE_NONE)
	{
		uiWinSetVisible(winItem(handle,PLAYMP3SUB_PLAYMODE_ID),0);
	}else
	{
		uiWinSetResid(winItem(handle,PLAYMP3SUB_PLAYMODE_ID),task_playMp3_op.playmode);
		uiWinSetVisible(winItem(handle,PLAYMP3SUB_PLAYMODE_ID),1);
	}	
}
/*******************************************************************************
* Function Name  : playMp3SubPlayStatShow
* Description    : playMp3SubPlayStatShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubPlayStatShow(winHandle handle)
{
	if(mp3_dec_sta() >= MP3_DEC_START && mp3_dac_sta() >= MP3_DAC_START)
	{
		uiWinSetResid(winItem(handle,PLAYMP3SUB_PLAYSTAT_ID),R_ID_ICON_MTPAUSE);
	}else
	{
		uiWinSetResid(winItem(handle,PLAYMP3SUB_PLAYSTAT_ID),R_ID_ICON_MTPLAY);
	}
}