/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

#define SYSDEVICE_Q_SIZE    20

ALIGNED(4) System_Ctrl_T SysCtrl;

ALIGNED(4) static MSG_T sysDeviceQStack[SYSDEVICE_Q_SIZE];


ALIGNED(4) static INT8U  sensor_filter_table[] = {
	SENSOR_FILTER_NOCOLOR,

};


/*******************************************************************************
* Function Name  : app_logo_show
* Description    : app_logo_show: show image & start music
* Input          : INT8U music_en : music play or not
				   INT8U wait : wait music end or not
				   INT8U stat : power on or power off.1->power on,other->power off
* Output         : none
* Return         : int 0;
*******************************************************************************/
int app_logo_show(INT8U music_en,INT8U wait,INT8U stat)
{
	INT16U image,music;

	if(stat == 1) // power on
	{
		image = R_ID_IMAGE_POWER_ON;
		music = R_ID_MUSIC_POWER_ON;//R_ID_BIN_POWER_ON_MP3;
	}
	else // power off
	{
		image = R_ID_IMAGE_POWER_OFF;
		music = R_ID_MUSIC_POWER_OFF;//R_ID_BIN_POWER_ON_MP3;
	}
	res_image_show(image);
	
	//if(res_image_show(image)<0)// show logo by video layer resolution
	//	 return -1;
	hx330x_lcdShowWaitDone();
	XOSTimeDly(50);
	
	task_com_lcdbk_set(1);
	if(stat)
	XOSTimeDly(500);


	if(music_en) // if play music
	{
		return res_music_start(music,wait,FUN_KEYSOUND_VOLUME);

		//return res_mp3_start(music,wait,FUN_KEYSOUND_VOLUME);
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : app_logo_gif_show
* Description    : app_logo_gif_show: show image & start music
* Input          : INT8U music_en : music play or not
				   INT8U wait : wait music end or not
				   INT8U stat : power on or power off.1->power on,other->power off
				   INT8U uidisable: 1: no show ui layer
* Output         : none
* Return         : int 0;
*******************************************************************************/
#if 0
int app_logo_gif_show(INT8U music_en,INT8U wait,INT8U stat, INT8U uidisable)
{
	INT16U image,music;
	int res;
	if(stat == 1) // power on
	{
		image = 0;//R_ID_FILE_GIF_POWER_ON;
		music = R_ID_MUSIC_POWER_ON;
	}
	else // power off
	{
		image = 0;//R_ID_FILE_GIF_POWER_OFF;
		music = R_ID_MUSIC_POWER_OFF;
	}
	res = res_gif_show_start(image, uidisable);
	if(res < 0)
	{
		deg_Printf("res_gif_show_start fail\n");
		return -1;
	}
	hx330x_lcdShowWaitDone();
	task_com_lcdbk_set(1);

	if(music_en) // if play music
	{
		res_music_start(music,0,FUN_KEYSOUND_VOLUME);
	}
	while(1)
	{
		if(res_gif_show_process() < 0)
		{
			break;
		}
	}
	res_gif_show_stop();
	if(wait)
	{
		u32 timeout = 0x3ffffff;
		while((audioPlaybackGetStatus() == MEDIA_STAT_PLAY) && timeout--);
		res_music_end();
	}
	
		

	return 0;
}
#endif
/*******************************************************************************
* Function Name  : app_version_get
* Description    : get system version
* Input          : none
* Output         : none
* Return         : char *
*******************************************************************************/
void app_version_get(void)
{
#if VERSION_LOAD_AUTO >0
	int fd,len;
	fd = nv_open(R_ID_BIN_VERSION);
	if(fd<0)
       goto VERSION_CODE;
	len = nv_size(R_ID_BIN_VERSION);
	if(len>31)
		len = 31;
	nv_read(fd,SysCtrl.version_str,len);
	SysCtrl.version_str[len] = 0;
	deg_Printf("res version:%s, len:%d\n",SysCtrl.version_str, len);
	return;

VERSION_CODE:
#endif
	hx330x_str_ncpy(SysCtrl.version_str,SYSTEM_VERSION,31);
	//deg_Printf("sdk version:%s\n",SysCtrl.version_str);
}
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none
* Return         : none
*******************************************************************************/
void app_draw_init(void)
{
    app_lcdUiShowInit();                         // enable ui display layer
	uiWinDrawInit(app_lcdUiDrawIdleFrameGet());
	uiWinInit();
}
/*******************************************************************************
* Function Name  : app_init
* Description    : app_init
* Input          : none
* Output         : none
* Return         :0
*******************************************************************************/
int app_init(void)
{
	hal_sysInit();	//initial system for free run
	deg_Printf("system power on\n");
	deg_Printf("XOSC:%d\n",hardware_setup.use_xosc);
	//deg_Printf("XSFR_CLK_TUNE:%x, OUT %x, IN %x\n",XSFR_CLK_TUNE,(XSFR_CLK_TUNE>>7)&0x1f,XSFR_CLK_TUNE&0x1f);
	//deg_Printf("XSFR_DLLCON0:%x, CH1 %x, CH2 %x\n",XSFR_DLLCON0,(XSFR_DLLCON0>>25)&0xf,(XSFR_DLLCON0>>21)&0xf);
//----------initial system work queue.work queues are isr callback function
	app_taskInit();
//----------sys dev initial,LCD,LED,SPI,ADC,DAC....
	dev_api_node_init();
//----------get board device ioctrl handler
    memset(&SysCtrl,0,sizeof(System_Ctrl_T));
	SysCtrl.dev_fd_dusb 	= dev_open(DEV_NAME_DUSB);
	SysCtrl.dev_fd_battery 	= dev_open(DEV_NAME_BATTERY);
    SysCtrl.dev_fd_gsensor 	= dev_open(DEV_NAME_GSENSOR);
	SysCtrl.dev_fd_ir 		= dev_open(DEV_NAME_IR);
	SysCtrl.dev_fd_key 		= dev_open(DEV_NAME_KEY);
	SysCtrl.dev_fd_lcd     	= dev_open(DEV_NAME_LCD);
	SysCtrl.dev_fd_led     	= dev_open(DEV_NAME_LED);
	SysCtrl.dev_fd_sensor  	= dev_open(DEV_NAME_SENSOR);
	SysCtrl.dev_fd_sdc  	= dev_open(DEV_NAME_SDCARD);
	SysCtrl.dev_fd_husb		= dev_open(DEV_NAME_HUSB);
	SysCtrl.dev_fd_tp		= dev_open(DEV_NAME_TP);
	SysCtrl.dev_stat_power  = POWERON_FLAG_MASK;
	SysCtrl.dev_fd_led_pwm	= dev_open(DEV_NAME_LED_PWM);

	if(SysCtrl.dev_fd_battery < 0)
	{
		app_uninit();
	}
//----------user menu configure set
	userConfig_Init();
//----------power on flag check
	int ret,temp;

	ret = dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_PARK_READ,(INT32U)&temp);
    if((ret>=0) && temp && user_configValue2Int(CONFIG_ID_PARKMODE))
    {
		SysCtrl.dev_stat_power = POWERON_FLAG_GSENSOR|POWERON_FLAG_FIRST;  // gsensor wakeup by parking mode active
		SysCtrl.dev_stat_gsensorlock = 1;  // set lock flag
		deg_Printf("gsen wakeup parking mode active\n");

    }
    else
	{
		if(hx330x_rtcAlarmWakeUpFlag())
		{
			SysCtrl.dev_stat_power = POWERON_FLAG_RTC|POWERON_FLAG_FIRST;
			deg_Printf("----------rtc wakeup\n");
		}
		else
		{
			ret = dev_ioctrl(SysCtrl.dev_fd_key,DEV_KEY_POWER_READ,(INT32U )&temp);
			if(ret >= 0 && temp)
				SysCtrl.dev_stat_power = POWERON_FLAG_KEY|POWERON_FLAG_FIRST;
			else
			{
			    dev_ioctrl(SysCtrl.dev_fd_dusb,DEV_DUSB_PWR_CHECK,(INT32U)&temp);
				if(temp)
					SysCtrl.dev_stat_power = POWERON_FLAG_DCIN|POWERON_FLAG_FIRST; // dcin or usb in power on
				else
					SysCtrl.dev_stat_power = POWERON_FLAG_KEY|POWERON_FLAG_FIRST; // dcin or usb in power on
			}
		}
    }
#if FUN_BATTERY_CHARGE_SHOW
	if (!(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN))
#endif
	{
		app_logo_show(0,1,1);  // power on.music en,do not wait music end	//---->show logo here,can speed start logo show.make user feeling system starting faster
	}
	app_version_get();
//--------initial filelist  for fs
	filelist_api_Init();
//--------initial font  & ui & configure
	res_font_Init(R_ID_BIN_FONTLIB, R_ID_BIN_LANGSTR);
    res_iconInit(R_ID_BIN_ICONLIB,User_Icon_Table,R_ICON_MAX);
//---------update time RTC time
#if FUN_BATTERY_CHARGE_SHOW
	if(!(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN))
#endif
   	 	dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 1); // back light on

	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);

	SysCtrl.led_pwm_level = 3;
	SysCtrl.led_onoff_sw = 1;
#if FUN_BATTERY_CHARGE_SHOW
	if(!(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN))
#endif
	{
//--------initial pwm_led	
		task_com_led_on();
	}
//----------sys XOSC callback regitster
	XWorkCreate(20*X_TICK,hal_isp_process);  // isp process
#if HAL_CFG_RTC_TRIM_EN //use rc to trim rtc
	if(hardware_setup.use_xosc == 0)
	{
		XWorkCreate(1000*X_TICK,hal_rtcTrimCallBack); //rc trim
	}
#endif
    DATE_TIME_T *rtcTime = hal_rtcTimeGet();

	if(userConfigInitial() || (rtcTime->year<2022)) // user configure reset
	{
    #if DATETIME_LOAD_AUTO >0
        rtcTime->year 	= user_config_get(CONFIG_ID_YEAR);
		rtcTime->month 	= user_config_get(CONFIG_ID_MONTH);
		rtcTime->day 	= user_config_get(CONFIG_ID_MDAY);
		rtcTime->hour 	= user_config_get(CONFIG_ID_HOUR);
		rtcTime->min 	= user_config_get(CONFIG_ID_MIN);
		rtcTime->sec 	= user_config_get(CONFIG_ID_SEC);
    #else
		rtcTime->year 	= 2022;
		rtcTime->month 	= 4;
		rtcTime->day 	= 1;
		rtcTime->hour 	= 0;
		rtcTime->min 	= 0;
		rtcTime->sec 	= 0;
    #endif
		hal_rtcTimeSet(rtcTime); // default time ->2017/01/01 00:00:00
	}
	nv_jpg_ex_force_init(userConfigInitial());
	SysCtrl.lcdshow_win_mode_save = LCDSHOW_ONLYWINA;
    SysCtrl.lcdshow_win_mode 	 = LCDSHOW_ONLYWINA;

    SysCtrl.avi_list = -1;
	SysCtrl.avia_list = -1;
	SysCtrl.avib_list = -1;
	SysCtrl.jpg_list = -1;
	SysCtrl.wav_list = -1;
	SysCtrl.mp3_list = -1;

#if TASK_SCAN_FILE_EVERY_TIME == 0
	nv_jpg_init();
	SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
	filelist_api_scan(SysCtrl.spi_jpg_list);
#else
	SysCtrl.spi_jpg_list = -1;
#endif
	SysCtrl.powerOnTime=0;
	SysCtrl.dev_stat_keysound = user_configValue2Int(CONFIG_ID_KEYSOUND);
	SysCtrl.sysQ = XMsgQCreate(sysDeviceQStack,SYSDEVICE_Q_SIZE);

	dev_ioctrl(SysCtrl.dev_fd_gsensor, DEV_GSENSOR_LOCK_WRITE,(INT32U)user_configValue2Int(CONFIG_ID_GSENSOR));	//set gsensor default level
	dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
	dev_ioctrl(SysCtrl.dev_fd_ir, DEV_IR_WRITE, user_configValue2Int(CONFIG_ID_IR_LED));

	SysCtrl.dev_stat_sdc  		= SDC_STAT_NULL;//SDC_STAT_UNSTABLE;
	SysCtrl.dev_dusb_stat   	= USBDEV_STAT_NULL;
	SysCtrl.dev_stat_battery 	= BATTERY_STAT_MAX;  // DEFAULT VALUE
	SysCtrl.sensor_filter_index	= -1;
//--------initial fs
	fs_exfunc_init();
//--------board check ,the first time.
	task_com_service(0); // check board state


	app_draw_init();			// enable ui display layer and ui manager
	res_keysound_init(KEYSOUND_LOAD_AUTO,R_ID_MUSIC_KEY_SOUND,FUN_KEYSOUND_VOLUME);
	user_config_cfgSysAll();
	SysCtrl.lcd_scaler_level 	= 100;
	SysCtrl.poweronFirst=1;
	//if all device is working , baterry may low, must off sysytem
	return 0;
}
/*******************************************************************************
* Function Name  : app_uninit
* Description    : app_uninit
* Input          : none
* Output         : none
* Return         :0
*******************************************************************************/
int app_uninit(void)
{
	int ret,temp;

    dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
	dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_OFF_WRITE, 0); // lcd off
	dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_ON_OFF_WRITE, 0); // led off
	
	//task_com_led_off();
    deg_Printf("system power off\n");


    if(SysCtrl.dev_stat_battery != BATTERY_STAT_0)
    {
	    while(1) // wait power release
	    {
	        ret = dev_ioctrl(SysCtrl.dev_fd_key,DEV_KEY_POWER_READ,(INT32U )&temp);
			if((ret < 0) ||(temp==0))
				break;
	    }
    }

	temp = user_configValue2Int(CONFIG_ID_PARKMODE);
    dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_MOTION_STABLE,temp); // wait gsensor stable

    dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_PARK_WRITE,temp); 	// gsensor park mode set.do not care gsensor.
    dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0); // led off
    dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_POWER_CTRL,0); // usensor off
    hal_timerStop(HAL_CFG_OS_TIMER);
	hal_sysUninit();

	dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
#if FUN_BATTERY_CHARGE_SHOW
	if(ret >= 0 && temp) 
	{
		SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
		hal_sysReset(); // reset
		while(1);
	}
#endif
	
	if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL))
	{
		deg_Printf("during system off, usb dc in, restart system \n");
		//hal_wkiWakeupTriger(1); //wki wakeup rising trigger
		//hal_wkiCleanPending();
		//hal_vddWKOEnable(0);
		SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
		hal_sysReset(); // reset
		while(1);
		//	hal_vddWKOEnable(1);
		//	goto POWER_OFF_DCIN;
	}else{
		hal_wkiWakeupTriger(1); //wki wakeup rising trigger
		hal_wkiCleanPending();
		hal_batDetectEnable(0);
		//hal_rtcAlarm_weakup_reset(10); //for test, after m seconds , rtc alarm occur and auto power on
		hal_vddWKOEnable(0);
		hx330x_sysCpuMsDelay(50);
	}
	while(1)
	{
		dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_LOCK_READ,(INT32U)&temp);
		if(hal_rtcAlarmStatusGet(1))
		{
			hal_sysReset();
			while(1);
		}
			
		temp = 0;
		dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK,(INT32U)&temp);
		if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL))
		{
			SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
			hal_sysReset(); // reset
			while(1);
		}
	}
	return 0; // makesure the program won't be here
}
/*******************************************************************************
* Function Name  :  app_sendDrawUIMsg
* Description    : system event service for event and key get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void app_sendDrawUIMsg(void)
{
	app_msgDealByType(SYS_DRAW_UI,uiWinGetCurrent(),0,NULL);
};

/*******************************************************************************
* Function Name  : uidraw_Service
* Description    : uidraw_Service
* Input          : taskID id,sysTask* task
* Output         : none
* Return         : none
*******************************************************************************/
void app_draw_Service(u8 force)
{
	if(uiWinDrawProcess() < 0)
	{
		if(force == 0)
			return;
	}
	app_sendDrawUIMsg();
	uiWinDrawUpdate();
}
/*******************************************************************************
* Function Name  :  app_systemService
* Description    : system event service for event and key get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void app_systemService(void)
{
	hal_wdtClear();
	task_com_service(1);
	app_msgDeal();
	app_draw_Service(0);

}
/*******************************************************************************
* Function Name  : app_sensor_filter_ctrl
* Description    : app_sensor_filter_ctrl
* Input          : u32 stat: SENSOR_FILTER_CHANGE_NONE, SENSOR_FILTER_CHANGE_NEXT, SENSOR_FILTER_CHANGE_PREV
* Output         : None
* Return         : None
*******************************************************************************/
void app_sensor_filter_ctrl(u32 stat)
{
	deg_Printf("app_sensor_filter_ctrl:%d\n", stat);
	if(stat == SENSOR_FILTER_CHANGE_NONE)
	{
		SysCtrl.sensor_filter_index = -1;
	}else if(stat == SENSOR_FILTER_CHANGE_NEXT)
	{
		if(SysCtrl.sensor_filter_index < 0)
		{
			SysCtrl.sensor_filter_index = 0;
		}else
		{
			SysCtrl.sensor_filter_index++;
			if(SysCtrl.sensor_filter_index >= ARRAY_NUM(sensor_filter_table))
			{
				SysCtrl.sensor_filter_index = -1;
			}
		}
	}else if(stat == SENSOR_FILTER_CHANGE_PREV)
	{
		if(SysCtrl.sensor_filter_index < 0)
		{
			SysCtrl.sensor_filter_index = ARRAY_NUM(sensor_filter_table) - 1;
		}else
		{
			//if(SysCtrl.sensor_filter_index == 0)
			//{
			//	SysCtrl.sensor_filter_index = ARRAY_NUM(sensor_filter_table) - 1;
			//}else
			{
				SysCtrl.sensor_filter_index--;
			}
		}

	}else 
	{
		return;
	}
	deg_Printf("SysCtrl.sensor_filter_index:%d\n", SysCtrl.sensor_filter_index);
	if(SysCtrl.sensor_filter_index < 0)
	{
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_FILTER_CFG, SENSOR_FILTER_NONE);
	}else
	{
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_FILTER_CFG, sensor_filter_table[SysCtrl.sensor_filter_index]);
	}	
}