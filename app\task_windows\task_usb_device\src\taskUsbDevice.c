/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
ALIGNED(4) USBDEVICE_OP_T  usbDeviceOp;
/*******************************************************************************
* Function Name  : taskUsbDeviceOpen
* Description    : taskUsbDeviceOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/

#define USER_PC_DONT_CLOSE_CSI		0

static void taskUsbDeviceOpen(u32 arg)
{
    SysCtrl.dev_stat_power &= ~(POWERON_FLAG_FIRST|POWERON_FLAG_WAIT);
	usbDeviceOp.cur_sel_index 		= 0;
	usbDeviceOp.usb_process_flag 	= 0;
	usbDeviceOp.cur_page_num		= 0;
	task_com_usbhost_set(USBHOST_STAT_OUT);
	husb_api_usensor_detech();
	#if USER_PC_DONT_CLOSE_CSI == 0
		app_lcdCsiVideoShowStop();
		hal_csiEnable(0);
		app_lcdShowWinModeCfg(LCDSHOW_WIN_DISABLE);
		//uiOpenWindow(&usbDeviceWindow,0,0);
		// hal_lcdUiEnable(UI_LAYER0,1);
		// res_image_show(R_ID_IMAGE_POWER_OFF);
		
		uiOpenWindow(&UsbDeviceSelectWindow,0,0);
	#endif

	#if USER_PC_DONT_CLOSE_CSI == 1
		VIDEO_ARG_T arg1;
		arg1.avi_arg.width	= 1280;
		arg1.avi_arg.height = 720;
		videoRecordInit(&arg1); // enable csi&mjpeg 
		while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY);
		dusb_api_Init(USB_DEVTYPE_COMBINE);
		usbDeviceOp.usb_process_flag = 1;
	#endif
}
/*******************************************************************************
* Function Name  : taskUsbDeviceClose
* Description    : taskUsbDeviceClose function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceClose(uint32 arg)
{
	task_com_usbhost_set(USBHOST_STAT_NULL);
	XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_TRIGLE,0));//wxn--不直接hal_lcdUiEnable(UI_LAYER0,1);的原因是
														//直接显示UI的话，切到拍照模式时会有USB的菜单显示出来
	//hal_lcdUiEnable(UI_LAYER0,1);
}
/*******************************************************************************
* Function Name  : taskUsbDeviceService
* Description    : taskUsbDeviceService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceService(uint32 arg)
{
	if(usbDeviceOp.usb_process_flag)
	{
		if(false == dusb_api_Process())
		{
			deg_Printf("usb update\n");
			SysCtrl.dev_stat_lcd = 0;
			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
			//dusb_api_Uninit();
			app_taskStart(TASK_USB_UPDATE,0);
			//app_taskStart(TASK_POWER_OFF,0);
		}		
	}

}

ALIGNED(4) sysTask_T taskUSBDevice =
{
	"usb device",
	0,
	taskUsbDeviceOpen,
	taskUsbDeviceClose,
	taskUsbDeviceService,
};


