/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef TOUCHPANEL_IIC_H
#define TOUCHPANEL_IIC_H

/*******************************************************************************
* Function Name  : tp_iic_init
* Description	 : tp_iic_init
* Input		     : None
* Output		 : None
* Return		 : none
*******************************************************************************/
void tp_iic_init(void);
/*******************************************************************************
* Function Name  : tp_iic_config
* Description	 : tp_iic_config
* Input		     : u8 slaveId, u8 addr_num
* Output		 : None
* Return		 : none
*******************************************************************************/
void tp_iic_config(u8 slaveId, u8 addr_num);
/*******************************************************************************
* Function Name  : tp_iic_write
* Description	 : tp_iic_write
* Input		     : u32 addr, u8* buf, u8 len
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_iic_write(u32 addr, u8* buf, u8 len);
/*******************************************************************************
* Function Name  : tp_iic_read
* Description	 : tp_iic_read
* Input		     : u32 addr, u8* buf, u8 len
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_iic_read(u32 addr, u8* buf, u8 len);

#endif
