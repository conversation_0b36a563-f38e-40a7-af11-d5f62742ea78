/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	AUTO_POWER_OFF_TITLE_ID=0,
	AUTO_POWER_OFF_RECT_ID,
	AUTO_POWER_OFF_LINE_ID,
	AUTO_POWER_OFF_SELECT_ID,	
};

UNUSED ALIGNED(4) const widgetCreateInfor autoPowerOffWin[] =
{
    createFrameWin(						Rx(70),	<PERSON>y(52), <PERSON>w(180),<PERSON>h(142),<PERSON>_<PERSON>_PALETTE_Black, WIN_ABS_POS),
	createRect(AUTO_POWER_OFF_LINE_ID,         	Rx(1),	<PERSON>y(1),  Rw(178),Rh(140), R_ID_LINE),
	createStringIcon(AUTO_POWER_OFF_TITLE_ID,	Rx(1), 	Ry(1), 	Rw(178),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	createRect(AUTO_POWER_OFF_RECT_ID,       	Rx(1),	Ry(33), Rw(178),Rh(1),	R_ID_PALETTE_DarkGreen),
	createItemManage(AUTO_POWER_OFF_SELECT_ID,	Rx(1),	Ry(34), Rw(178),Rh(107),INVALID_COLOR),
	// createFrameWin(						Rx(70),	Ry(70), Rw(180),Rh(120),R_ID_LINE, WIN_ABS_POS),
	// // createStringIcon(AUTO_POWER_OFF_TITLE_ID,	Rx(0), 	Ry(0), 	Rw(180),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	// createRect(AUTO_POWER_OFF_RECT_ID,       	Rx(1),	Ry(1), Rw(178),Rh(118),	R_ID_LINE),
	// createItemManage(AUTO_POWER_OFF_SELECT_ID,	Rx(1),	Ry(1), Rw(178),Rh(118), INVALID_COLOR),
	widgetEnd(),
}; 