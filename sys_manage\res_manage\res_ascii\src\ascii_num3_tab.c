/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num3 :13*64
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
const unsigned char ascii_num3_32[]= // ' '
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_33[]= // '!'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x07,0x00,
   0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_35[]= // '#'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x3e,0x00,
   0x00,0xf8,0x3e,0x00,0x01,0xf8,0x7e,0x00,0x01,0xf0,0x7c,0x00,0x01,0xf0,0x7c,0x00,
   0x01,0xf0,0x7c,0x00,0x01,0xf0,0x7c,0x00,0x01,0xf0,0x7c,0x00,0x03,0xe0,0xf8,0x00,
   0xff,0xff,0xff,0x80,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0x80,
   0x07,0xc1,0xf0,0x00,0x07,0xc1,0xf0,0x00,0x07,0xc1,0xf0,0x00,0x07,0xc1,0xf0,0x00,
   0x07,0xc1,0xf0,0x00,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0x80,
   0xff,0xff,0xff,0x80,0x0f,0x83,0xe0,0x00,0x1f,0x07,0xe0,0x00,0x1f,0x07,0xc0,0x00,
   0x1f,0x07,0xc0,0x00,0x1f,0x07,0xc0,0x00,0x1f,0x07,0xc0,0x00,0x3f,0x0f,0xc0,0x00,
   0x3e,0x0f,0x80,0x00,0x3e,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_36[]= // '$'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x00,0x00,0x1c,0x00,0x00,0x00,0xff,0x00,0x00,
   0x03,0xff,0xe0,0x00,0x07,0xff,0xf0,0x00,0x0f,0xdf,0xf8,0x00,0x0f,0x9c,0xf8,0x00,
   0x1f,0x1c,0xfc,0x00,0x1f,0x1c,0x7c,0x00,0x1f,0x1c,0x7c,0x00,0x1f,0x1c,0x00,0x00,
   0x1f,0x1c,0x00,0x00,0x1f,0x9c,0x00,0x00,0x0f,0x9c,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x07,0xfe,0x00,0x00,0x03,0xff,0xc0,0x00,0x00,0xff,0xf0,0x00,0x00,0x3f,0xf8,0x00,
   0x00,0x1f,0xfc,0x00,0x00,0x1c,0xfc,0x00,0x00,0x1c,0x7e,0x00,0x00,0x1c,0x3e,0x00,
   0x1e,0x1c,0x3e,0x00,0x3e,0x1c,0x3e,0x00,0x3f,0x1c,0x3e,0x00,0x3f,0x1c,0x3e,0x00,
   0x1f,0x1c,0x7c,0x00,0x1f,0x9c,0xfc,0x00,0x0f,0xdd,0xf8,0x00,0x07,0xff,0xf0,0x00,
   0x03,0xff,0xe0,0x00,0x00,0xff,0x80,0x00,0x00,0x1c,0x00,0x00,0x00,0x1c,0x00,0x00,
   0x00,0x1c,0x00,0x00,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_37[]= // '%'
{
   0x27,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xf8,0x00,0x3c,0x00,
   0x07,0xfe,0x00,0x78,0x00,0x0f,0xff,0x00,0x78,0x00,0x0f,0x9f,0x00,0xf0,0x00,0x1f,
   0x0f,0x80,0xf0,0x00,0x1e,0x07,0x81,0xe0,0x00,0x1e,0x07,0x81,0xe0,0x00,0x1e,0x07,
   0x83,0xc0,0x00,0x1e,0x07,0x83,0xc0,0x00,0x1e,0x07,0x87,0x80,0x00,0x1e,0x07,0x8f,
   0x80,0x00,0x1f,0x0f,0x8f,0x00,0x00,0x0f,0x9f,0x1e,0x00,0x00,0x0f,0xfe,0x1e,0x00,
   0x00,0x07,0xfe,0x3c,0x00,0x00,0x01,0xf8,0x3c,0x00,0x00,0x00,0x00,0x78,0x3f,0x00,
   0x00,0x00,0x78,0xff,0xc0,0x00,0x00,0xf1,0xff,0xe0,0x00,0x00,0xf1,0xf3,0xe0,0x00,
   0x01,0xe3,0xe1,0xf0,0x00,0x03,0xe3,0xc0,0xf0,0x00,0x03,0xc3,0xc0,0xf0,0x00,0x07,
   0x83,0xc0,0xf0,0x00,0x07,0x83,0xc0,0xf0,0x00,0x0f,0x03,0xc0,0xf0,0x00,0x0f,0x03,
   0xc0,0xf0,0x00,0x1e,0x03,0xe1,0xf0,0x00,0x1e,0x01,0xf3,0xe0,0x00,0x3c,0x01,0xff,
   0xc0,0x00,0x3c,0x00,0xff,0xc0,0x00,0x78,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_38[]= // '&'
{
   0x1e,0x40,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,
   0x7f,0xff,0xff,0xfc,0x7f,0xff,0xff,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_39[]= // '''
{
   0x09,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x1c,0x00,
   0x1c,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_40[]= // '('
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_41[]= // ')'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x00,
   0xc0,0x00,0xc0,0x00,0xe0,0x00,0xf0,0x00,0xf0,0x00,0x78,0x00,0x38,0x00,0x38,0x00,
   0x3c,0x00,0x1c,0x00,0x1c,0x00,0x1e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,
   0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x1e,0x00,0x1c,0x00,
   0x1c,0x00,0x3c,0x00,0x38,0x00,0x38,0x00,0x78,0x00,0xf0,0x00,0xf0,0x00,0xe0,0x00,
   0xc0,0x00,0xc0,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_42[]= // '*'
{
   0x12,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xe0,0x00,
   0x01,0xe0,0x00,0x01,0xe0,0x00,0x19,0xe6,0x00,0x3f,0xff,0x00,0x3f,0xff,0x00,0x0f,
   0xfe,0x00,0x01,0xf0,0x00,0x03,0xf8,0x00,0x07,0xfc,0x00,0x07,0xbc,0x00,0x0f,0x3e,
   0x00,0x06,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_43[]= // '+'
{
   0x1a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x3f,0xff,0xfe,0x00,0x3f,0xff,0xfe,0x00,0x3f,0xff,0xfe,0x00,
   0x3f,0xff,0xfe,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_44[]= // ','
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x07,0x00,0x0f,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_45[]= // '-'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x7f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_46[]= // '.'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_47[]= // '/'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,
   0x00,0xf8,0x00,0xf0,0x00,0xf0,0x00,0xf0,0x01,0xe0,0x01,0xe0,0x01,0xe0,0x03,0xe0,
   0x03,0xc0,0x03,0xc0,0x03,0xc0,0x07,0x80,0x07,0x80,0x07,0x80,0x0f,0x80,0x0f,0x00,
   0x0f,0x00,0x0f,0x00,0x1e,0x00,0x1e,0x00,0x1e,0x00,0x3e,0x00,0x3c,0x00,0x3c,0x00,
   0x3c,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0xf8,0x00,0xf0,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_48[]= // '0'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x01,0xff,0xc0,0x00,
   0x03,0xff,0xe0,0x00,0x07,0xe3,0xf0,0x00,0x0f,0xc1,0xf8,0x00,0x0f,0x80,0xf8,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1e,0x00,0x3c,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x1e,0x00,0x3c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x0f,0x80,0xf8,0x00,0x0f,0xc1,0xf8,0x00,0x07,0xe3,0xf0,0x00,0x03,0xff,0xe0,0x00,
   0x01,0xff,0xc0,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_49[]= // '1'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x00,
   0x00,0x07,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x1f,0x80,0x00,0x00,0x3f,0x80,0x00,
   0x00,0x7f,0x80,0x00,0x01,0xff,0x80,0x00,0x07,0xef,0x80,0x00,0x0f,0xcf,0x80,0x00,
   0x0f,0x0f,0x80,0x00,0x0c,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_50[]= // '2'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x80,0x00,0x03,0xff,0xe0,0x00,
   0x07,0xff,0xf0,0x00,0x0f,0xc1,0xf8,0x00,0x1f,0x80,0xfc,0x00,0x1f,0x00,0x7c,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,
   0x00,0x00,0x3e,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0xf8,0x00,0x00,0x01,0xf8,0x00,0x00,0x03,0xf0,0x00,0x00,0x07,0xe0,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x1f,0x80,0x00,0x00,0x7f,0x00,0x00,0x00,0xfe,0x00,0x00,
   0x01,0xfc,0x00,0x00,0x03,0xf8,0x00,0x00,0x07,0xf0,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x1f,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x7f,0xff,0xfe,0x00,
   0x7f,0xff,0xfe,0x00,0x7f,0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_51[]= // '3'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x03,0xff,0x80,0x00,
   0x07,0xff,0xe0,0x00,0x0f,0xc7,0xf0,0x00,0x1f,0x81,0xf0,0x00,0x1f,0x00,0xf8,0x00,
   0x3f,0x00,0xf8,0x00,0x3e,0x00,0xf8,0x00,0x1e,0x00,0xf8,0x00,0x00,0x00,0xf8,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x03,0xf0,0x00,0x00,0x07,0xe0,0x00,0x00,0x7f,0xc0,0x00,
   0x00,0x7f,0xc0,0x00,0x00,0x7f,0xf0,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,
   0x00,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x80,0xf8,0x00,0x0f,0xc3,0xf8,0x00,0x07,0xff,0xf0,0x00,
   0x03,0xff,0xc0,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_52[]= // '4'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x07,0xe0,0x00,0x00,0x0f,0xe0,0x00,0x00,0x0f,0xe0,0x00,
   0x00,0x1f,0xe0,0x00,0x00,0x3f,0xe0,0x00,0x00,0x3f,0xe0,0x00,0x00,0x7b,0xe0,0x00,
   0x00,0x7b,0xe0,0x00,0x00,0xf3,0xe0,0x00,0x01,0xf3,0xe0,0x00,0x01,0xe3,0xe0,0x00,
   0x03,0xc3,0xe0,0x00,0x07,0xc3,0xe0,0x00,0x07,0x83,0xe0,0x00,0x0f,0x03,0xe0,0x00,
   0x1f,0x03,0xe0,0x00,0x1e,0x03,0xe0,0x00,0x3c,0x03,0xe0,0x00,0x7c,0x03,0xe0,0x00,
   0x7f,0xff,0xfe,0x00,0x7f,0xff,0xfe,0x00,0x7f,0xff,0xfe,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x03,0xe0,0x00,0x00,0x03,0xe0,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_53[]= // '5'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xff,0xfc,0x00,
   0x03,0xff,0xfc,0x00,0x03,0xff,0xfc,0x00,0x07,0xc0,0x00,0x00,0x07,0xc0,0x00,0x00,
   0x07,0xc0,0x00,0x00,0x07,0xc0,0x00,0x00,0x07,0xc0,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0xbf,0x80,0x00,0x0f,0xff,0xe0,0x00,0x0f,0xff,0xf0,0x00,
   0x1f,0xe3,0xf8,0x00,0x1f,0x80,0xfc,0x00,0x1f,0x00,0x7c,0x00,0x00,0x00,0x7e,0x00,
   0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,
   0x00,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x7e,0x00,0x3f,0x00,0x7c,0x00,
   0x1f,0x00,0xfc,0x00,0x1f,0x80,0xf8,0x00,0x0f,0xc3,0xf0,0x00,0x07,0xff,0xe0,0x00,
   0x03,0xff,0xc0,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_54[]= // '6'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x80,0x00,0x01,0xff,0xe0,0x00,
   0x03,0xff,0xf0,0x00,0x07,0xe3,0xf8,0x00,0x07,0xc0,0xf8,0x00,0x0f,0x80,0xfc,0x00,
   0x0f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,
   0x3e,0x00,0x00,0x00,0x3e,0x3f,0x80,0x00,0x3e,0xff,0xe0,0x00,0x3f,0xff,0xf0,0x00,
   0x3f,0xe3,0xf8,0x00,0x3f,0x80,0xfc,0x00,0x3f,0x00,0x7c,0x00,0x3f,0x00,0x7c,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x1e,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x7c,0x00,
   0x0f,0x80,0x7c,0x00,0x0f,0x80,0xf8,0x00,0x07,0xe3,0xf8,0x00,0x03,0xff,0xf0,0x00,
   0x01,0xff,0xe0,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_55[]= // '7'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xfe,0x00,
   0x3f,0xff,0xfe,0x00,0x3f,0xff,0xfe,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0xf8,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x01,0xf0,0x00,0x00,0x03,0xf0,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x07,0xc0,0x00,0x00,0x07,0xc0,0x00,0x00,0x0f,0x80,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x7e,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0xf8,0x00,0x00,0x01,0xf8,0x00,0x00,0x01,0xf8,0x00,0x00,0x01,0xf0,0x00,0x00,
   0x01,0xf0,0x00,0x00,0x03,0xf0,0x00,0x00,0x03,0xf0,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x03,0xe0,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_56[]= // '8'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x03,0xff,0xc0,0x00,
   0x07,0xff,0xf0,0x00,0x0f,0xe3,0xf0,0x00,0x0f,0x80,0xf8,0x00,0x1f,0x80,0xfc,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x80,0xfc,0x00,0x0f,0x80,0xf8,0x00,0x07,0xe3,0xf0,0x00,0x03,0xff,0xe0,0x00,
   0x01,0xff,0xc0,0x00,0x07,0xff,0xf0,0x00,0x0f,0xc3,0xf8,0x00,0x1f,0x80,0xfc,0x00,
   0x1f,0x00,0x7c,0x00,0x3f,0x00,0x7e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3f,0x00,0x7e,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x80,0xfc,0x00,0x0f,0xe3,0xf8,0x00,0x07,0xff,0xf0,0x00,
   0x03,0xff,0xe0,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_57[]= // '9'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x03,0xff,0xc0,0x00,
   0x07,0xff,0xe0,0x00,0x0f,0xe3,0xf0,0x00,0x0f,0xc0,0xf8,0x00,0x1f,0x80,0x78,0x00,
   0x1f,0x00,0x7c,0x00,0x3f,0x00,0x3c,0x00,0x3e,0x00,0x3c,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x1f,0x00,0x7e,0x00,0x1f,0x00,0x7e,0x00,0x1f,0x80,0xfe,0x00,0x0f,0xe3,0xfe,0x00,
   0x07,0xff,0xfe,0x00,0x03,0xff,0xbe,0x00,0x00,0xfe,0x3e,0x00,0x00,0x00,0x3e,0x00,
   0x00,0x00,0x3c,0x00,0x00,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x78,0x00,
   0x1f,0x80,0xf8,0x00,0x0f,0x81,0xf0,0x00,0x0f,0xc3,0xf0,0x00,0x07,0xff,0xe0,0x00,
   0x03,0xff,0xc0,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_58[]= // ':'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_59[]= // ';'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x07,0x00,0x0f,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_60[]= // '<'
{
   0x1a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x06,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0xfe,0x00,0x00,0x03,0xfe,0x00,
   0x00,0x1f,0xfe,0x00,0x00,0x7f,0xf0,0x00,0x03,0xff,0xc0,0x00,0x0f,0xfe,0x00,0x00,
   0x1f,0xf8,0x00,0x00,0x1f,0xe0,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,
   0x1f,0xf8,0x00,0x00,0x0f,0xfe,0x00,0x00,0x03,0xff,0xc0,0x00,0x00,0x7f,0xf0,0x00,
   0x00,0x1f,0xfe,0x00,0x00,0x03,0xfe,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x3e,0x00,
   0x00,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_61[]= // '='
{
   0x1a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,
   0x1f,0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_62[]= // '>'
{
   0x1a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x18,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x1f,0xf0,0x00,0x00,
   0x1f,0xfe,0x00,0x00,0x03,0xff,0x80,0x00,0x00,0xff,0xf0,0x00,0x00,0x1f,0xfc,0x00,
   0x00,0x07,0xfe,0x00,0x00,0x01,0xfe,0x00,0x00,0x00,0x3e,0x00,0x00,0x01,0xfe,0x00,
   0x00,0x07,0xfe,0x00,0x00,0x1f,0xfc,0x00,0x00,0xff,0xf0,0x00,0x03,0xff,0x80,0x00,
   0x1f,0xfe,0x00,0x00,0x1f,0xf0,0x00,0x00,0x1f,0xc0,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_63[]= // '?'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x80,0x00,
   0x03,0xff,0xe0,0x00,0x07,0xff,0xf0,0x00,0x0f,0xe1,0xf8,0x00,0x1f,0x80,0xfc,0x00,
   0x1f,0x00,0x7c,0x00,0x3f,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x03,0xf0,0x00,0x00,0x07,0xf0,0x00,0x00,0x0f,0xe0,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x1f,0x80,0x00,0x00,0x3f,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_64[]= // '@'
{
   0x2d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,
   0x00,0x03,0xff,0xff,0x00,0x00,0x00,0x0f,0xff,0xff,0xc0,0x00,0x00,0x1f,0xe0,0x1f,
   0xf0,0x00,0x00,0x7f,0x00,0x03,0xf8,0x00,0x00,0xfc,0x00,0x00,0xfc,0x00,0x01,0xf8,
   0x00,0x00,0x7e,0x00,0x01,0xf0,0x00,0x00,0x3e,0x00,0x03,0xe0,0x3f,0x00,0x1f,0x00,
   0x07,0xc0,0xff,0xdf,0x8f,0x80,0x07,0x81,0xff,0xff,0x07,0x80,0x0f,0x03,0xf1,0xff,
   0x07,0x80,0x0f,0x07,0xe0,0xff,0x07,0xc0,0x1e,0x0f,0xc0,0x7f,0x03,0xc0,0x1e,0x0f,
   0x80,0x3f,0x03,0xc0,0x1e,0x1f,0x80,0x3e,0x03,0xc0,0x1c,0x1f,0x00,0x3e,0x03,0xc0,
   0x3c,0x1f,0x00,0x3e,0x03,0xc0,0x3c,0x3e,0x00,0x3e,0x03,0xc0,0x3c,0x3e,0x00,0x3e,
   0x03,0xc0,0x3c,0x3e,0x00,0x3c,0x07,0x80,0x3c,0x3e,0x00,0x7c,0x07,0x80,0x3c,0x3e,
   0x00,0x7c,0x0f,0x80,0x3c,0x3e,0x00,0xfc,0x0f,0x00,0x3c,0x3e,0x00,0xfc,0x1f,0x00,
   0x3e,0x1f,0x01,0xfc,0x3e,0x00,0x1e,0x1f,0x03,0xf8,0x7c,0x00,0x1e,0x0f,0xc7,0xfc,
   0xf8,0x00,0x1f,0x0f,0xff,0xff,0xf0,0x00,0x0f,0x07,0xfe,0x7f,0xc0,0x00,0x0f,0x81,
   0xf8,0x3f,0x00,0x00,0x07,0xc0,0x00,0x00,0x01,0xe0,0x07,0xe0,0x00,0x00,0x03,0xc0,
   0x03,0xf0,0x00,0x00,0x07,0x80,0x01,0xfc,0x00,0x00,0x1f,0x00,0x00,0xff,0x00,0x00,
   0x7e,0x00,0x00,0x3f,0xe0,0x07,0xfc,0x00,0x00,0x1f,0xff,0xff,0xf0,0x00,0x00,0x03,
   0xff,0xff,0xc0,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_65[]= // 'A'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x1f,0xe0,0x00,0x00,0x1f,0xe0,0x00,0x00,0x1f,0xf0,0x00,
   0x00,0x3f,0xf0,0x00,0x00,0x3f,0xf0,0x00,0x00,0x3f,0xf8,0x00,0x00,0x7c,0xf8,0x00,
   0x00,0x7c,0xfc,0x00,0x00,0xfc,0x7c,0x00,0x00,0xf8,0x7c,0x00,0x00,0xf8,0x3e,0x00,
   0x01,0xf0,0x3e,0x00,0x01,0xf0,0x3f,0x00,0x01,0xf0,0x1f,0x00,0x03,0xe0,0x1f,0x00,
   0x03,0xe0,0x0f,0x80,0x07,0xff,0xff,0x80,0x07,0xff,0xff,0xc0,0x07,0xff,0xff,0xc0,
   0x0f,0xff,0xff,0xc0,0x0f,0x80,0x03,0xe0,0x0f,0x80,0x03,0xe0,0x1f,0x00,0x03,0xf0,
   0x1f,0x00,0x01,0xf0,0x3f,0x00,0x01,0xf0,0x3e,0x00,0x00,0xf8,0x3e,0x00,0x00,0xf8,
   0x7c,0x00,0x00,0xfc,0x7c,0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_66[]= // 'B'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xf0,0x00,
   0x0f,0xff,0xfe,0x00,0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0x80,0x0f,0x80,0x1f,0x80,
   0x0f,0x80,0x0f,0xc0,0x0f,0x80,0x07,0xc0,0x0f,0x80,0x07,0xc0,0x0f,0x80,0x07,0xc0,
   0x0f,0x80,0x07,0xc0,0x0f,0x80,0x07,0xc0,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x1f,0x00,
   0x0f,0xff,0xfe,0x00,0x0f,0xff,0xfc,0x00,0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0x80,
   0x0f,0x80,0x0f,0xc0,0x0f,0x80,0x03,0xe0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x03,0xf0,
   0x0f,0x80,0x03,0xe0,0x0f,0x80,0x0f,0xe0,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0x80,
   0x0f,0xff,0xff,0x00,0x0f,0xff,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_67[]= // 'C'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfc,0x00,
   0x00,0x3f,0xff,0x00,0x00,0xff,0xff,0xc0,0x01,0xff,0xff,0xe0,0x03,0xfc,0x0f,0xf0,
   0x07,0xf0,0x03,0xf0,0x0f,0xc0,0x01,0xf8,0x0f,0x80,0x00,0xf8,0x1f,0x80,0x00,0xfc,
   0x1f,0x00,0x00,0x70,0x1f,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,
   0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,
   0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x1f,0x00,0x00,0x38,
   0x1f,0x00,0x00,0x7e,0x1f,0x80,0x00,0x7e,0x0f,0x80,0x00,0xfc,0x0f,0xc0,0x00,0xfc,
   0x07,0xf0,0x03,0xf8,0x03,0xfc,0x0f,0xf0,0x01,0xff,0xff,0xe0,0x00,0xff,0xff,0xc0,
   0x00,0x7f,0xff,0x80,0x00,0x0f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_68[]= // 'D'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xf8,0x00,
   0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0x80,0x0f,0xff,0xff,0xc0,0x0f,0x80,0x1f,0xe0,
   0x0f,0x80,0x07,0xf0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x01,0xf8,0x0f,0x80,0x00,0xf8,
   0x0f,0x80,0x00,0xf8,0x0f,0x80,0x00,0xfc,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,
   0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,
   0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0xf8,
   0x0f,0x80,0x00,0xf8,0x0f,0x80,0x00,0xf8,0x0f,0x80,0x01,0xf8,0x0f,0x80,0x03,0xf0,
   0x0f,0x80,0x07,0xf0,0x0f,0x80,0x1f,0xe0,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0x80,
   0x0f,0xff,0xfe,0x00,0x0f,0xff,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_69[]= // 'E'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xff,0xe0,
   0x0f,0xff,0xff,0xe0,0x0f,0xff,0xff,0xe0,0x0f,0xff,0xff,0xe0,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0xc0,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xf0,
   0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_70[]= // 'F'
{
   0x1b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xff,0x80,
   0x0f,0xff,0xff,0x80,0x0f,0xff,0xff,0x80,0x0f,0xff,0xff,0x80,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0xff,0xfe,0x00,0x0f,0xff,0xfe,0x00,0x0f,0xff,0xfe,0x00,0x0f,0xff,0xfe,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_71[]= // 'G'
{
   0x22,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xff,0x00,0x00,
   0x00,0x3f,0xff,0xc0,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xff,0xff,0xf8,0x00,0x03,
   0xfe,0x03,0xfc,0x00,0x07,0xf0,0x00,0xfe,0x00,0x07,0xe0,0x00,0x7e,0x00,0x0f,0xc0,
   0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x00,0x00,0x1c,0x00,0x1f,0x00,0x00,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,
   0x00,0x3e,0x00,0x00,0x00,0x00,0x3e,0x00,0x3f,0xff,0x00,0x3e,0x00,0x3f,0xff,0x00,
   0x3e,0x00,0x3f,0xff,0x00,0x3e,0x00,0x3f,0xff,0x00,0x3e,0x00,0x00,0x1f,0x00,0x1f,
   0x00,0x00,0x1f,0x00,0x1f,0x00,0x00,0x1f,0x00,0x1f,0x80,0x00,0x1f,0x00,0x0f,0xc0,
   0x00,0x1f,0x00,0x0f,0xe0,0x00,0x3f,0x00,0x07,0xf0,0x00,0x7f,0x00,0x03,0xfe,0x03,
   0xfe,0x00,0x01,0xff,0xff,0xfc,0x00,0x00,0x7f,0xff,0xf0,0x00,0x00,0x3f,0xff,0xc0,
   0x00,0x00,0x07,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_72[]= // 'H'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_73[]= // 'I'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_74[]= // 'J'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,
   0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,
   0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,
   0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,
   0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x00,0x03,0xe0,0x7c,0x03,0xe0,0x7c,
   0x03,0xe0,0x7c,0x03,0xe0,0x7e,0x03,0xe0,0x7e,0x07,0xc0,0x3f,0x0f,0xc0,0x3f,0xff,
   0xc0,0x1f,0xff,0x80,0x0f,0xff,0x00,0x03,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_75[]= // 'K'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x03,0xf8,
   0x0f,0x80,0x07,0xe0,0x0f,0x80,0x0f,0xc0,0x0f,0x80,0x1f,0x80,0x0f,0x80,0x3f,0x00,
   0x0f,0x80,0x7e,0x00,0x0f,0x80,0xfc,0x00,0x0f,0x81,0xf8,0x00,0x0f,0x83,0xf0,0x00,
   0x0f,0x87,0xe0,0x00,0x0f,0x8f,0xc0,0x00,0x0f,0x9f,0x80,0x00,0x0f,0xbf,0x80,0x00,
   0x0f,0xff,0xc0,0x00,0x0f,0xff,0xc0,0x00,0x0f,0xff,0xe0,0x00,0x0f,0xfb,0xf0,0x00,
   0x0f,0xf1,0xf0,0x00,0x0f,0xe1,0xf8,0x00,0x0f,0xc0,0xfc,0x00,0x0f,0x80,0x7e,0x00,
   0x0f,0x80,0x3e,0x00,0x0f,0x80,0x3f,0x00,0x0f,0x80,0x1f,0x80,0x0f,0x80,0x0f,0xc0,
   0x0f,0x80,0x07,0xc0,0x0f,0x80,0x07,0xe0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf8,0x0f,0x80,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_76[]= // 'L'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0x00,
   0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_77[]= // 'M'
{
   0x25,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x00,0x3f,0x80,
   0x0f,0xe0,0x00,0x3f,0x80,0x0f,0xe0,0x00,0x3f,0x80,0x0f,0xf0,0x00,0x7f,0x80,0x0f,
   0xf0,0x00,0x7f,0x80,0x0f,0xf0,0x00,0x7f,0x80,0x0f,0xf8,0x00,0x7f,0x80,0x0f,0xf8,
   0x00,0xff,0x80,0x0f,0xf8,0x00,0xff,0x80,0x0f,0xbc,0x00,0xff,0x80,0x0f,0xbc,0x01,
   0xef,0x80,0x0f,0xbc,0x01,0xef,0x80,0x0f,0x9e,0x01,0xef,0x80,0x0f,0x9e,0x03,0xcf,
   0x80,0x0f,0x9e,0x03,0xcf,0x80,0x0f,0x8f,0x03,0xcf,0x80,0x0f,0x8f,0x07,0x8f,0x80,
   0x0f,0x8f,0x07,0x8f,0x80,0x0f,0x87,0x87,0x8f,0x80,0x0f,0x87,0x87,0x8f,0x80,0x0f,
   0x87,0x8f,0x0f,0x80,0x0f,0x83,0xcf,0x0f,0x80,0x0f,0x83,0xcf,0x0f,0x80,0x0f,0x83,
   0xde,0x0f,0x80,0x0f,0x81,0xfe,0x0f,0x80,0x0f,0x81,0xfe,0x0f,0x80,0x0f,0x81,0xfe,
   0x0f,0x80,0x0f,0x80,0xfc,0x0f,0x80,0x0f,0x80,0xfc,0x0f,0x80,0x0f,0x80,0xfc,0x0f,
   0x80,0x0f,0x80,0x78,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_78[]= // 'N'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xc0,0x01,0xf0,
   0x0f,0xc0,0x01,0xf0,0x0f,0xe0,0x01,0xf0,0x0f,0xe0,0x01,0xf0,0x0f,0xf0,0x01,0xf0,
   0x0f,0xf8,0x01,0xf0,0x0f,0xf8,0x01,0xf0,0x0f,0xfc,0x01,0xf0,0x0f,0xfc,0x01,0xf0,
   0x0f,0xbe,0x01,0xf0,0x0f,0xbf,0x01,0xf0,0x0f,0x9f,0x01,0xf0,0x0f,0x9f,0x81,0xf0,
   0x0f,0x8f,0xc1,0xf0,0x0f,0x87,0xc1,0xf0,0x0f,0x87,0xe1,0xf0,0x0f,0x83,0xe1,0xf0,
   0x0f,0x83,0xf1,0xf0,0x0f,0x81,0xf9,0xf0,0x0f,0x80,0xf9,0xf0,0x0f,0x80,0xfd,0xf0,
   0x0f,0x80,0x7d,0xf0,0x0f,0x80,0x3f,0xf0,0x0f,0x80,0x3f,0xf0,0x0f,0x80,0x1f,0xf0,
   0x0f,0x80,0x1f,0xf0,0x0f,0x80,0x0f,0xf0,0x0f,0x80,0x07,0xf0,0x0f,0x80,0x07,0xf0,
   0x0f,0x80,0x03,0xf0,0x0f,0x80,0x03,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_79[]= // 'O'
{
   0x22,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x00,0x3f,0xff,0x00,0x00,0x00,0xff,0xff,0xc0,0x00,0x01,0xff,0xff,0xe0,0x00,0x03,
   0xfc,0x0f,0xf0,0x00,0x07,0xf0,0x03,0xf8,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0x80,
   0x00,0x7c,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x00,0x00,0x3e,0x00,0x1f,0x00,0x00,
   0x3e,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,
   0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,
   0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x1f,
   0x00,0x00,0x3e,0x00,0x1f,0x00,0x00,0x3e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x0f,0xc0,
   0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x07,0xf0,0x03,0xf8,0x00,0x03,0xfc,0x0f,
   0xf0,0x00,0x01,0xff,0xff,0xe0,0x00,0x00,0xff,0xff,0xc0,0x00,0x00,0x3f,0xff,0x00,
   0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_80[]= // 'P'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xfc,0x00,
   0x0f,0xff,0xff,0x00,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0xc0,0x0f,0x80,0x0f,0xe0,
   0x0f,0x80,0x03,0xe0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x07,0xe0,
   0x0f,0x80,0x0f,0xe0,0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0x80,0x0f,0xff,0xff,0x00,
   0x0f,0xff,0xf8,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,
   0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_81[]= // 'Q'
{
   0x22,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x00,0x3f,0xff,0x00,0x00,0x00,0xff,0xff,0xc0,0x00,0x01,0xff,0xff,0xe0,0x00,0x03,
   0xfc,0x0f,0xf0,0x00,0x07,0xf0,0x03,0xf8,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0x80,
   0x00,0xfc,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x00,0x00,0x3e,0x00,0x1f,0x00,0x00,
   0x3e,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,
   0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,
   0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x1f,0x00,0x3e,0x00,0x00,0x3f,0x00,0x1f,
   0x00,0x00,0x3e,0x00,0x1f,0x00,0x00,0x3e,0x00,0x1f,0x80,0x70,0x7e,0x00,0x0f,0x80,
   0x78,0xfc,0x00,0x0f,0xc0,0x7f,0xfc,0x00,0x07,0xf0,0x1f,0xf8,0x00,0x03,0xfc,0x0f,
   0xf0,0x00,0x01,0xff,0xff,0xf0,0x00,0x00,0xff,0xff,0xfc,0x00,0x00,0x3f,0xff,0x7f,
   0x00,0x00,0x0f,0xf8,0x1f,0x80,0x00,0x00,0x00,0x07,0x00,0x00,0x00,0x00,0x03,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_82[]= // 'R'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xfe,0x00,
   0x0f,0xff,0xff,0xc0,0x0f,0xff,0xff,0xe0,0x0f,0xff,0xff,0xf0,0x0f,0x80,0x07,0xf0,
   0x0f,0x80,0x01,0xf8,0x0f,0x80,0x01,0xf8,0x0f,0x80,0x00,0xf8,0x0f,0x80,0x00,0xf8,
   0x0f,0x80,0x00,0xf8,0x0f,0x80,0x01,0xf8,0x0f,0x80,0x01,0xf8,0x0f,0x80,0x07,0xf0,
   0x0f,0xff,0xff,0xf0,0x0f,0xff,0xff,0xe0,0x0f,0xff,0xff,0x80,0x0f,0xff,0xfe,0x00,
   0x0f,0x80,0x7c,0x00,0x0f,0x80,0x3f,0x00,0x0f,0x80,0x1f,0x80,0x0f,0x80,0x0f,0xc0,
   0x0f,0x80,0x07,0xc0,0x0f,0x80,0x07,0xe0,0x0f,0x80,0x03,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf8,0x0f,0x80,0x00,0xf8,0x0f,0x80,0x00,0x7c,0x0f,0x80,0x00,0x7e,
   0x0f,0x80,0x00,0x3e,0x0f,0x80,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_83[]= // 'S'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xe0,0x00,
   0x01,0xff,0xfc,0x00,0x03,0xff,0xff,0x00,0x07,0xff,0xff,0x80,0x0f,0xe0,0x3f,0x80,
   0x0f,0x80,0x0f,0xc0,0x1f,0x00,0x07,0xc0,0x1f,0x00,0x07,0xe0,0x1f,0x00,0x03,0xe0,
   0x1f,0x00,0x03,0xe0,0x1f,0x80,0x00,0x00,0x0f,0xe0,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x07,0xff,0xe0,0x00,0x01,0xff,0xfc,0x00,0x00,0x7f,0xff,0x00,0x00,0x07,0xff,0xc0,
   0x00,0x00,0x7f,0xe0,0x00,0x00,0x0f,0xe0,0x00,0x00,0x03,0xf0,0x3e,0x00,0x01,0xf0,
   0x3e,0x00,0x01,0xf0,0x3f,0x00,0x01,0xf0,0x1f,0x00,0x01,0xf0,0x1f,0x80,0x03,0xf0,
   0x1f,0xc0,0x07,0xe0,0x0f,0xf8,0x1f,0xe0,0x07,0xff,0xff,0xc0,0x03,0xff,0xff,0x80,
   0x00,0xff,0xfe,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_84[]= // 'T'
{
   0x1b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xff,0xff,0xc0,
   0x7f,0xff,0xff,0xc0,0x7f,0xff,0xff,0xc0,0x7f,0xff,0xff,0xc0,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_85[]= // 'U'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,
   0x0f,0x80,0x01,0xf0,0x0f,0x80,0x01,0xf0,0x07,0xc0,0x03,0xe0,0x07,0xc0,0x03,0xe0,
   0x07,0xe0,0x07,0xe0,0x03,0xf8,0x1f,0xc0,0x03,0xff,0xff,0x80,0x01,0xff,0xff,0x80,
   0x00,0x7f,0xfe,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_86[]= // 'V'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x00,0x00,0xf8,
   0x7c,0x00,0x01,0xf0,0x7c,0x00,0x01,0xf0,0x7c,0x00,0x01,0xf0,0x3e,0x00,0x03,0xe0,
   0x3e,0x00,0x03,0xe0,0x3e,0x00,0x03,0xe0,0x1f,0x00,0x07,0xc0,0x1f,0x00,0x07,0xc0,
   0x0f,0x00,0x07,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x07,0x80,0x0f,0x00,
   0x07,0xc0,0x1f,0x00,0x07,0xc0,0x1f,0x00,0x03,0xe0,0x1e,0x00,0x03,0xe0,0x3e,0x00,
   0x03,0xe0,0x3c,0x00,0x01,0xf0,0x3c,0x00,0x01,0xf0,0x7c,0x00,0x00,0xf0,0x78,0x00,
   0x00,0xf8,0x78,0x00,0x00,0xf8,0xf8,0x00,0x00,0x78,0xf0,0x00,0x00,0x7d,0xf0,0x00,
   0x00,0x7d,0xe0,0x00,0x00,0x3f,0xe0,0x00,0x00,0x3f,0xe0,0x00,0x00,0x1f,0xc0,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_87[]= // 'W'
{
   0x2a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x00,0x1f,0x80,0x07,0xc0,
   0x7c,0x00,0x1f,0x80,0x07,0xc0,0x3e,0x00,0x1f,0x80,0x0f,0x80,0x3e,0x00,0x3f,0xc0,
   0x0f,0x80,0x3e,0x00,0x3f,0xc0,0x0f,0x80,0x1e,0x00,0x3f,0xc0,0x0f,0x00,0x1f,0x00,
   0x3f,0xc0,0x1f,0x00,0x1f,0x00,0x7b,0xe0,0x1f,0x00,0x1f,0x00,0x79,0xe0,0x1f,0x00,
   0x0f,0x00,0x79,0xe0,0x1e,0x00,0x0f,0x80,0xf9,0xf0,0x3e,0x00,0x0f,0x80,0xf1,0xf0,
   0x3e,0x00,0x0f,0x80,0xf0,0xf0,0x3e,0x00,0x07,0x81,0xf0,0xf0,0x3c,0x00,0x07,0xc1,
   0xe0,0xf8,0x7c,0x00,0x07,0xc1,0xe0,0xf8,0x7c,0x00,0x03,0xc3,0xe0,0x78,0x78,0x00,
   0x03,0xc3,0xc0,0x7c,0x78,0x00,0x03,0xe3,0xc0,0x7c,0xf8,0x00,0x03,0xe3,0xc0,0x3c,
   0xf8,0x00,0x01,0xe7,0x80,0x3c,0xf0,0x00,0x01,0xe7,0x80,0x3c,0xf0,0x00,0x01,0xf7,
   0x80,0x3f,0xf0,0x00,0x01,0xff,0x80,0x1f,0xf0,0x00,0x00,0xff,0x00,0x1f,0xe0,0x00,
   0x00,0xff,0x00,0x1f,0xe0,0x00,0x00,0xff,0x00,0x1f,0xe0,0x00,0x00,0x7e,0x00,0x0f,
   0xc0,0x00,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x00,0x7c,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_88[]= // 'X'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,0x01,0xf8,
   0x3f,0x00,0x03,0xf0,0x1f,0x80,0x07,0xe0,0x0f,0x80,0x07,0xc0,0x0f,0xc0,0x0f,0xc0,
   0x07,0xe0,0x1f,0x80,0x03,0xf0,0x3f,0x00,0x01,0xf0,0x3e,0x00,0x01,0xf8,0x7e,0x00,
   0x00,0xfc,0xfc,0x00,0x00,0x7f,0xf8,0x00,0x00,0x3f,0xf0,0x00,0x00,0x3f,0xf0,0x00,
   0x00,0x1f,0xe0,0x00,0x00,0x0f,0xc0,0x00,0x00,0x0f,0xc0,0x00,0x00,0x1f,0xe0,0x00,
   0x00,0x3f,0xf0,0x00,0x00,0x7f,0xf8,0x00,0x00,0x7e,0xf8,0x00,0x00,0xfc,0xfc,0x00,
   0x01,0xf8,0x7e,0x00,0x03,0xf0,0x3f,0x00,0x03,0xf0,0x1f,0x00,0x07,0xe0,0x1f,0x80,
   0x0f,0xc0,0x0f,0xc0,0x1f,0x80,0x07,0xe0,0x1f,0x00,0x03,0xe0,0x3f,0x00,0x03,0xf0,
   0x7e,0x00,0x01,0xf8,0xfc,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_89[]= // 'Y'
{
   0x1e,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x01,0xf8,
   0x7c,0x00,0x01,0xf0,0x7e,0x00,0x03,0xf0,0x3f,0x00,0x07,0xe0,0x1f,0x00,0x07,0xc0,
   0x1f,0x80,0x0f,0xc0,0x0f,0xc0,0x1f,0x80,0x07,0xc0,0x1f,0x80,0x07,0xe0,0x3f,0x00,
   0x03,0xf0,0x3e,0x00,0x01,0xf0,0x7e,0x00,0x01,0xf8,0xfc,0x00,0x00,0xfc,0xf8,0x00,
   0x00,0x7d,0xf8,0x00,0x00,0x7f,0xf0,0x00,0x00,0x3f,0xf0,0x00,0x00,0x1f,0xe0,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x0f,0xc0,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x0f,0x80,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_90[]= // 'Z'
{
   0x1b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xff,0xff,0x80,
   0x1f,0xff,0xff,0x80,0x1f,0xff,0xff,0x80,0x1f,0xff,0xff,0x80,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0xfc,0x00,0x00,0x01,0xf8,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x03,0xf0,0x00,0x00,0x07,0xe0,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x1f,0x80,0x00,0x00,0x3f,0x00,0x00,0x00,0x7e,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0xfc,0x00,0x00,0x01,0xf8,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x07,0xe0,0x00,0x00,0x0f,0xc0,0x00,0x00,0x0f,0xc0,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x7f,0xff,0xff,0xc0,0x7f,0xff,0xff,0xc0,
   0x7f,0xff,0xff,0xc0,0x7f,0xff,0xff,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_91[]= // '['
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xf0,
   0x1f,0xf0,0x1f,0xf0,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0xf0,0x1f,0xf0,0x1f,0xf0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_92[]= // '\'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf0,0x00,
   0xf8,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3e,0x00,
   0x1e,0x00,0x1e,0x00,0x1e,0x00,0x0f,0x00,0x0f,0x00,0x0f,0x00,0x0f,0x80,0x07,0x80,
   0x07,0x80,0x07,0x80,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xe0,0x01,0xe0,0x01,0xe0,
   0x01,0xe0,0x00,0xf0,0x00,0xf0,0x00,0xf0,0x00,0xf8,0x00,0x78,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_93[]= // ']'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xc0,
   0x7f,0xc0,0x7f,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x7f,0xc0,0x7f,0xc0,0x7f,0xc0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_94[]= // '^'
{
   0x15,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xf8,0x00,
   0x03,0xfc,0x00,0x03,0xfc,0x00,0x03,0xfc,0x00,0x07,0xfe,0x00,0x07,0xfe,0x00,0x07,
   0xfe,0x00,0x0f,0x9f,0x00,0x0f,0x9f,0x00,0x0f,0x9f,0x00,0x1f,0x0f,0x80,0x1f,0x0f,
   0x80,0x1f,0x0f,0x80,0x3e,0x07,0xc0,0x3e,0x07,0xc0,0x3e,0x07,0xc0,0x7c,0x03,0xe0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_95[]= // '_'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xff,0xfe,0xff,0xff,0xfe,0xff,
   0xff,0xfe,0xff,0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_96[]= // '`'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x80,
   0x1f,0x80,0x0f,0xc0,0x07,0xc0,0x03,0xe0,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_97[]= // 'a'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xc0,0x00,
   0x03,0xff,0xf0,0x00,0x07,0xff,0xf8,0x00,0x0f,0xc1,0xf8,0x00,0x0f,0x80,0xfc,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,
   0x00,0x01,0xfc,0x00,0x00,0x7f,0xfc,0x00,0x07,0xff,0xfc,0x00,0x0f,0xff,0x7c,0x00,
   0x1f,0xe0,0x7c,0x00,0x3f,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0xfc,0x00,
   0x3e,0x00,0xfc,0x00,0x3f,0x01,0xfc,0x00,0x1f,0x87,0xfc,0x00,0x1f,0xff,0xfc,0x00,
   0x0f,0xff,0x7c,0x00,0x03,0xf8,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_98[]= // 'b'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x3f,0x80,0x00,
   0x1f,0xff,0xe0,0x00,0x1f,0xff,0xf0,0x00,0x1f,0xe1,0xf8,0x00,0x1f,0xc0,0xf8,0x00,
   0x1f,0x80,0x7c,0x00,0x1f,0x80,0x7c,0x00,0x1f,0x80,0x3e,0x00,0x1f,0x00,0x3e,0x00,
   0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,
   0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x7e,0x00,0x1f,0x80,0x7c,0x00,
   0x1f,0x80,0x7c,0x00,0x1f,0xc0,0xf8,0x00,0x1f,0xe1,0xf8,0x00,0x1f,0xff,0xf0,0x00,
   0x1f,0x7f,0xe0,0x00,0x1f,0x3f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_99[]= // 'c'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x03,0xff,0xc0,0x07,0xff,0xe0,0x0f,0xc3,
   0xf0,0x0f,0x81,0xf0,0x1f,0x01,0xf8,0x1f,0x00,0xf8,0x3e,0x00,0xf0,0x3e,0x00,0x00,
   0x3e,0x00,0x00,0x3e,0x00,0x00,0x3e,0x00,0x00,0x3e,0x00,0x00,0x3e,0x00,0x00,0x3e,
   0x00,0xf8,0x3e,0x00,0xf8,0x1f,0x00,0xf8,0x1f,0x01,0xf0,0x0f,0x81,0xf0,0x0f,0xc7,
   0xe0,0x07,0xff,0xc0,0x03,0xff,0x80,0x00,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_100[]= // 'd'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0xfe,0x7c,0x00,
   0x03,0xff,0xfc,0x00,0x07,0xff,0xfc,0x00,0x0f,0xc3,0xfc,0x00,0x0f,0x81,0xfc,0x00,
   0x1f,0x00,0xfc,0x00,0x1f,0x00,0xfc,0x00,0x3e,0x00,0xfc,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3f,0x00,0x7c,0x00,0x1f,0x00,0xfc,0x00,
   0x1f,0x00,0xfc,0x00,0x0f,0x81,0xfc,0x00,0x0f,0xe3,0xfc,0x00,0x07,0xff,0xfc,0x00,
   0x03,0xff,0x7c,0x00,0x00,0xfe,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_101[]= // 'e'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,
   0x01,0xff,0xe0,0x00,0x07,0xff,0xf0,0x00,0x0f,0xe3,0xf8,0x00,0x0f,0x80,0xf8,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3f,0xff,0xfe,0x00,0x3f,0xff,0xfe,0x00,0x3f,0xff,0xfe,0x00,0x3e,0x00,0x00,0x00,
   0x3e,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x3f,0x00,0x3e,0x00,0x1f,0x00,0x7e,0x00,
   0x1f,0x80,0x7c,0x00,0x0f,0xc0,0xfc,0x00,0x0f,0xe1,0xf8,0x00,0x07,0xff,0xf0,0x00,
   0x01,0xff,0xe0,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_102[]= // 'f'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xf8,
   0x07,0xf8,0x07,0xf8,0x0f,0xc0,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x7f,0xf0,0x7f,0xf0,0x7f,0xf0,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_103[]= // 'g'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,
   0x03,0xff,0x7c,0x00,0x07,0xff,0xfc,0x00,0x0f,0xc3,0xfc,0x00,0x0f,0x81,0xfc,0x00,
   0x1f,0x00,0xfc,0x00,0x1f,0x00,0xfc,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x1f,0x00,0xfc,0x00,
   0x1f,0x00,0xfc,0x00,0x0f,0x81,0xfc,0x00,0x0f,0xc3,0xfc,0x00,0x07,0xff,0xfc,0x00,
   0x03,0xff,0xfc,0x00,0x00,0xfe,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0xf8,0x00,0x1f,0x80,0xf8,0x00,0x0f,0xc3,0xf0,0x00,
   0x0f,0xff,0xf0,0x00,0x03,0xff,0xc0,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_104[]= // 'h'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x1f,0xc0,0x00,
   0x1f,0x7f,0xf0,0x00,0x1f,0xff,0xf8,0x00,0x1f,0xe1,0xf8,0x00,0x1f,0xc0,0xfc,0x00,
   0x1f,0x80,0x7c,0x00,0x1f,0x80,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_105[]= // 'i'
{
   0x0b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_106[]= // 'j'
{
   0x0b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x3f,0x00,0xfe,0x00,0xfc,0x00,0xf8,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_107[]= // 'k'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,
   0x1f,0x00,0x00,0x1f,0x00,0x00,0x1f,0x00,0x00,0x1f,0x00,0x00,0x1f,0x00,0x00,0x1f,
   0x00,0x00,0x1f,0x00,0x00,0x1f,0x00,0x00,0x1f,0x01,0xfc,0x1f,0x03,0xf8,0x1f,0x07,
   0xf0,0x1f,0x0f,0xc0,0x1f,0x1f,0x80,0x1f,0x3f,0x00,0x1f,0x7e,0x00,0x1f,0xfc,0x00,
   0x1f,0xfc,0x00,0x1f,0xfe,0x00,0x1f,0xff,0x00,0x1f,0xdf,0x00,0x1f,0x9f,0x80,0x1f,
   0x0f,0xc0,0x1f,0x07,0xc0,0x1f,0x07,0xe0,0x1f,0x03,0xe0,0x1f,0x01,0xf0,0x1f,0x01,
   0xf8,0x1f,0x00,0xf8,0x1f,0x00,0xfc,0x1f,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_108[]= // 'l'
{
   0x0b,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_109[]= // 'm'
{
   0x25,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x3f,0x81,0xfc,0x00,0x1f,0x7f,0xe7,0xff,0x00,0x1f,0xff,0xef,
   0xff,0x80,0x1f,0xe3,0xff,0x1f,0x80,0x1f,0xc1,0xfe,0x0f,0xc0,0x1f,0x80,0xfc,0x07,
   0xc0,0x1f,0x80,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,
   0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,
   0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,
   0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,
   0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x1f,0x00,0xf8,0x07,
   0xc0,0x1f,0x00,0xf8,0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_110[]= // 'n'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xc0,0x00,
   0x1f,0x7f,0xf0,0x00,0x1f,0xff,0xf8,0x00,0x1f,0xe1,0xf8,0x00,0x1f,0xc0,0xfc,0x00,
   0x1f,0x80,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_111[]= // 'o'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,
   0x01,0xff,0xc0,0x00,0x07,0xff,0xf0,0x00,0x0f,0xe3,0xf8,0x00,0x0f,0x80,0xf8,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x3f,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,
   0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3e,0x00,0x3f,0x00,0x7e,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x0f,0x80,0xf8,0x00,0x0f,0xe3,0xf8,0x00,0x07,0xff,0xf0,0x00,
   0x01,0xff,0xc0,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_112[]= // 'p'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x80,0x00,
   0x1f,0x7f,0xe0,0x00,0x1f,0xff,0xf0,0x00,0x1f,0xf1,0xf8,0x00,0x1f,0xc0,0xf8,0x00,
   0x1f,0xc0,0x7c,0x00,0x1f,0x80,0x7c,0x00,0x1f,0x80,0x3e,0x00,0x1f,0x00,0x3e,0x00,
   0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,
   0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x00,0x3e,0x00,0x1f,0x80,0x7c,0x00,
   0x1f,0x80,0x7c,0x00,0x1f,0xc0,0xf8,0x00,0x1f,0xe1,0xf8,0x00,0x1f,0xff,0xf0,0x00,
   0x1f,0xff,0xc0,0x00,0x1f,0x3f,0x80,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_113[]= // 'q'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,
   0x03,0xff,0x7c,0x00,0x07,0xff,0xfc,0x00,0x0f,0xc7,0xfc,0x00,0x0f,0x81,0xfc,0x00,
   0x1f,0x01,0xfc,0x00,0x1f,0x00,0xfc,0x00,0x3e,0x00,0xfc,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,
   0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x3e,0x00,0x7c,0x00,0x1f,0x00,0xfc,0x00,
   0x1f,0x00,0xfc,0x00,0x0f,0x81,0xfc,0x00,0x0f,0xc3,0xfc,0x00,0x07,0xff,0xfc,0x00,
   0x01,0xff,0xfc,0x00,0x00,0xfe,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_114[]= // 'r'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,
   0x1f,0xfe,0x1f,0xfe,0x1f,0xc6,0x1f,0x80,0x1f,0x80,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,
   0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_115[]= // 's'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x01,0xfe,0x00,0x07,0xff,0x80,0x1f,0xff,0xc0,0x1f,0x87,
   0xe0,0x3e,0x03,0xf0,0x3e,0x01,0xf0,0x3e,0x01,0xf0,0x3f,0x00,0x00,0x3f,0xc0,0x00,
   0x1f,0xf8,0x00,0x0f,0xff,0x80,0x03,0xff,0xe0,0x00,0xff,0xf0,0x00,0x0f,0xf8,0x00,
   0x01,0xf8,0x3c,0x00,0xf8,0x7c,0x00,0xf8,0x7e,0x00,0xf8,0x3e,0x01,0xf0,0x3f,0x83,
   0xf0,0x1f,0xff,0xe0,0x07,0xff,0xc0,0x01,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_116[]= // 't'
{
   0x0d,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x01,0x80,0x07,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x7f,0xf0,0x7f,0xf0,0x7f,0xf0,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0xf8,0x07,0xf8,0x03,0xf8,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_117[]= // 'u'
{
   0x19,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,0x1f,0x00,0x7c,0x00,
   0x1f,0x00,0xfc,0x00,0x1f,0x81,0xfc,0x00,0x0f,0xc3,0xfc,0x00,0x0f,0xff,0xfc,0x00,
   0x07,0xff,0x7c,0x00,0x01,0xfc,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_118[]= // 'v'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x00,0x3e,0x3e,0x00,0x7c,0x3e,0x00,
   0x7c,0x3e,0x00,0x7c,0x1f,0x00,0xf8,0x1f,0x00,0xf8,0x1f,0x80,0xf8,0x0f,0x81,0xf0,
   0x0f,0x81,0xf0,0x0f,0xc3,0xf0,0x07,0xc3,0xe0,0x07,0xc3,0xe0,0x07,0xe7,0xe0,0x03,
   0xe7,0xc0,0x03,0xe7,0xc0,0x01,0xff,0x80,0x01,0xff,0x80,0x01,0xff,0x80,0x00,0xff,
   0x00,0x00,0xff,0x00,0x00,0xff,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_119[]= // 'w'
{
   0x20,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0xf8,0x03,0xc0,0x1f,0xf8,0x07,0xe0,0x1f,0xfc,0x07,0xe0,0x3f,0x7c,0x07,0xe0,0x3e,
   0x7c,0x07,0xf0,0x3e,0x7c,0x0f,0xf0,0x3e,0x3e,0x0f,0xf0,0x7c,0x3e,0x0f,0xf0,0x7c,
   0x3e,0x1e,0xf8,0x7c,0x1e,0x1e,0x78,0x78,0x1f,0x1e,0x78,0xf8,0x1f,0x3c,0x7c,0xf8,
   0x1f,0x3c,0x3c,0xf8,0x0f,0x3c,0x3d,0xf0,0x0f,0xfc,0x3f,0xf0,0x0f,0xf8,0x1f,0xf0,
   0x07,0xf8,0x1f,0xe0,0x07,0xf8,0x1f,0xe0,0x07,0xf0,0x0f,0xe0,0x03,0xf0,0x0f,0xe0,
   0x03,0xf0,0x0f,0xc0,0x03,0xe0,0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_120[]= // 'x'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,0xfc,0x3f,0x01,0xf8,0x1f,0x83,
   0xf0,0x0f,0x83,0xe0,0x0f,0xc7,0xe0,0x07,0xef,0xc0,0x03,0xff,0x80,0x01,0xff,0x00,
   0x01,0xff,0x00,0x00,0xfe,0x00,0x00,0x7e,0x00,0x00,0xfe,0x00,0x01,0xff,0x00,0x03,
   0xff,0x80,0x03,0xef,0x80,0x07,0xef,0xc0,0x0f,0xc7,0xe0,0x1f,0x83,0xf0,0x1f,0x01,
   0xf0,0x3f,0x01,0xf8,0x7e,0x00,0xfc,0xfc,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_121[]= // 'y'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x00,0x3e,0x3e,0x00,0x7c,0x3e,0x00,
   0x7c,0x3f,0x00,0xfc,0x1f,0x00,0xf8,0x1f,0x00,0xf8,0x0f,0x81,0xf0,0x0f,0x81,0xf0,
   0x0f,0xc1,0xf0,0x07,0xc3,0xe0,0x07,0xc3,0xe0,0x03,0xe7,0xe0,0x03,0xe7,0xc0,0x03,
   0xf7,0xc0,0x01,0xff,0x80,0x01,0xff,0x80,0x01,0xff,0x80,0x00,0xff,0x00,0x00,0xff,
   0x00,0x00,0x7e,0x00,0x00,0x7e,0x00,0x00,0x7e,0x00,0x00,0x7c,0x00,0x00,0x7c,0x00,
   0x00,0x7c,0x00,0x00,0xf8,0x00,0x00,0xf8,0x00,0x01,0xf0,0x00,0x1f,0xf0,0x00,0x1f,
   0xe0,0x00,0x1f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_122[]= // 'z'
{
   0x17,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xf8,0x3f,0xff,0xf8,0x3f,0xff,
   0xf8,0x00,0x03,0xf0,0x00,0x07,0xe0,0x00,0x0f,0xc0,0x00,0x0f,0xc0,0x00,0x1f,0x80,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x00,0xfc,0x00,0x01,0xf8,0x00,0x01,0xf8,0x00,0x03,
   0xf0,0x00,0x07,0xe0,0x00,0x0f,0xc0,0x00,0x1f,0x80,0x00,0x3f,0x00,0x00,0x3f,0x00,
   0x00,0x7f,0xff,0xfc,0x7f,0xff,0xfc,0x7f,0xff,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_123[]= // '{'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_124[]= // '|'
{
   0x0c,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,
   0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x0f,0x80,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_125[]= // '}'
{
   0x0f,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x00,0xc0,0x00,
   0xc0,0x00,0xe0,0x00,0xf0,0x00,0xf0,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0x70,0x00,
   0x70,0x00,0x70,0x00,0x70,0x00,0x78,0x00,0x78,0x00,0x3c,0x00,0x3c,0x00,0x1e,0x00,
   0x1f,0x00,0x1f,0x00,0x1e,0x00,0x3c,0x00,0x38,0x00,0x78,0x00,0x70,0x00,0x70,0x00,
   0x70,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0xf0,0x00,
   0xe0,0x00,0xe0,0x00,0xc0,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num3_126[]= // '~'
{
   0x1a,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x07,0xf0,0x06,0x00,0x0f,0xff,0x1e,0x00,0x1f,0xff,0xfe,0x00,0x1f,0xff,0xfe,0x00,
   0x1e,0x1f,0xfc,0x00,0x18,0x03,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
//-------------------------ascii table----------------------
ALIGNED(4) const unsigned char * const ascii_num3_table[]=
{
   ascii_num3_32,// ' '
   ascii_num3_33,// '!'
   ascii_num3_35,// '#'
   ascii_num3_36,// '$'
   ascii_num3_37,// '%'
   ascii_num3_38,// '&'
   ascii_num3_39,// '''
   ascii_num3_40,// '('
   ascii_num3_41,// ')'
   ascii_num3_42,// '*'
   ascii_num3_43,// '+'
   ascii_num3_44,// ','
   ascii_num3_45,// '-'
   ascii_num3_46,// '.'
   ascii_num3_47,// '/'
   ascii_num3_48,// '0'
   ascii_num3_49,// '1'
   ascii_num3_50,// '2'
   ascii_num3_51,// '3'
   ascii_num3_52,// '4'
   ascii_num3_53,// '5'
   ascii_num3_54,// '6'
   ascii_num3_55,// '7'
   ascii_num3_56,// '8'
   ascii_num3_57,// '9'
   ascii_num3_58,// ':'
   ascii_num3_59,// ';'
   ascii_num3_60,// '<'
   ascii_num3_61,// '='
   ascii_num3_62,// '>'
   ascii_num3_63,// '?'
   ascii_num3_64,// '@'
   ascii_num3_65,// 'A'
   ascii_num3_66,// 'B'
   ascii_num3_67,// 'C'
   ascii_num3_68,// 'D'
   ascii_num3_69,// 'E'
   ascii_num3_70,// 'F'
   ascii_num3_71,// 'G'
   ascii_num3_72,// 'H'
   ascii_num3_73,// 'I'
   ascii_num3_74,// 'J'
   ascii_num3_75,// 'K'
   ascii_num3_76,// 'L'
   ascii_num3_77,// 'M'
   ascii_num3_78,// 'N'
   ascii_num3_79,// 'O'
   ascii_num3_80,// 'P'
   ascii_num3_81,// 'Q'
   ascii_num3_82,// 'R'
   ascii_num3_83,// 'S'
   ascii_num3_84,// 'T'
   ascii_num3_85,// 'U'
   ascii_num3_86,// 'V'
   ascii_num3_87,// 'W'
   ascii_num3_88,// 'X'
   ascii_num3_89,// 'Y'
   ascii_num3_90,// 'Z'
   ascii_num3_91,// '['
   ascii_num3_92,// '\'
   ascii_num3_93,// ']'
   ascii_num3_94,// '^'
   ascii_num3_95,// '_'
   ascii_num3_96,// '`'
   ascii_num3_97,// 'a'
   ascii_num3_98,// 'b'
   ascii_num3_99,// 'c'
   ascii_num3_100,// 'd'
   ascii_num3_101,// 'e'
   ascii_num3_102,// 'f'
   ascii_num3_103,// 'g'
   ascii_num3_104,// 'h'
   ascii_num3_105,// 'i'
   ascii_num3_106,// 'j'
   ascii_num3_107,// 'k'
   ascii_num3_108,// 'l'
   ascii_num3_109,// 'm'
   ascii_num3_110,// 'n'
   ascii_num3_111,// 'o'
   ascii_num3_112,// 'p'
   ascii_num3_113,// 'q'
   ascii_num3_114,// 'r'
   ascii_num3_115,// 's'
   ascii_num3_116,// 't'
   ascii_num3_117,// 'u'
   ascii_num3_118,// 'v'
   ascii_num3_119,// 'w'
   ascii_num3_120,// 'x'
   ascii_num3_121,// 'y'
   ascii_num3_122,// 'z'
   ascii_num3_123,// '{'
   ascii_num3_124,// '|'
   ascii_num3_125,// '}'
   ascii_num3_126,// '~'
};
