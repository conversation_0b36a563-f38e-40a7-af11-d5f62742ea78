/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

GRAM_SECTION u8 dmauart_fifo[64];  //URAM_SECTION GRAM_SECTION
typedef struct DMAUART_OP_S
{
	u8  rx_data_save[64];
	u8* rx_fifo_start;
	u8* rx_fifo_end;
	u32 rx_cmd_len;
	u32 test_state;
}DMAUART_OP_T;

ALIGNED(4) static DMAUART_OP_T dmauart_op;
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartRxOverWait(void)
{
	hx330x_timerTickStart();
	while(1)
	{
		if(dmauart_op.rx_cmd_len <= hx330x_dmauart_rxCntGet())
			break;
		if(hx330x_timerTickCount() >= (hardware_setup.sys_clk/1000)) //wait 1ms
		{
			deg_Printf("hal_dmaUartRxOverWait timeout\n");
			break;
		}
		hal_wdtClear();
	}
	hx330x_timerTickStop();
}
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartRxDataOut(void)
{
	u32 i;
	u8* lastOutAddr = (u8*)hx330x_dmauart_rxOutAdrGet();
	u32 data_len = hx330x_dmauart_rxCntGet();
	//deg_Printf("lastOutAddr:%x,data_len:%d\n",lastOutAddr,data_len);
	for(i = 0; i < data_len; i++)
	{
		dmauart_op.rx_data_save[i] = *lastOutAddr++;
		if(lastOutAddr >= dmauart_op.rx_fifo_end)
			lastOutAddr = dmauart_op.rx_fifo_start;
	}
	hx330x_dmauart_rxFifoOut(data_len);
	debgbuf(dmauart_op.rx_data_save, data_len);
}
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartIRQHandler(u8 sta)
{
	if(sta & DMAUART_STA_OVERFLOW)
	{
		//deg_Printf("DMA UART RX OVERFLOW\n");
		hal_dmaUartRxOverWait();
		hal_dmaUartRxDataOut();
		//user can deal rx data by dmauart_op.rx_data_save[] here
	}
	if(sta & DMAUART_STA_TX_DONE)
	{
		deg_Printf("DMA UART TX DONE\n");
		hx330x_DmaUart_con_cfg(DMAUART_TX_IE,0); //TX DONE FLAG不能被清，DMA依赖这一bit，所以需要关TX IE
		dmauart_op.test_state = 0;
	}
	if(sta & DMAUART_STA_RX_DONE)
	{
		deg_Printf("DMA UART RX DONE\n");
	}
}
/*******************************************************************************
* Function Name  : hal_dmauartInit
* Description    : hal layer.dma uart initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartInit(void)
{
	//notice: TX/RX DMA ADDR should not in sdram 
	hx330x_bytes_memset((u8*)&dmauart_fifo, 0, sizeof(dmauart_fifo));
	hx330x_bytes_memset((u8*)&dmauart_op, 0, sizeof(dmauart_op));
	dmauart_op.rx_cmd_len    = 10;
	dmauart_op.rx_fifo_start = dmauart_fifo;
	dmauart_op.rx_fifo_end   = &dmauart_fifo[16];
	hx330x_dmaChannelEnable(DMA_SD0,1);
	hx330x_DmaUartInit(hardware_setup.dmaUart_baudrate); //NOT ENABLE TX INTERRUPT
	hx330x_DmaUart_CallbackRegister(hal_dmaUartIRQHandler);
	hx330x_dmauart_recvAutoDmakick(dmauart_fifo, DMAUART_OVERFLOW_8BYTE, DMAUART_RX_LOOPCNT_16BYTE);
}
/*******************************************************************************
* Function Name  : hal_dmauartTxDma
* Description    : hal_dmauartTxDma, disable tx isr
* Input          : u8 * buf:not support sdram
				   u32 len
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartTxDma(u8 * buf, u32 len)
{
	if((u32)buf & 0x02000000 ) //not support sdram
		return;
	if(dmauart_op.test_state)
	{
		deg_Printf("dma uart tx err state\n");
		return;
	}
	hx330x_dmauart_sendDma(buf, len - 1);
}
/*******************************************************************************
* Function Name  : hal_dmauartTxDmaKick
* Description    : hal_dmauartTxDmaKick, enable tx isr
* Input          : u8 * buf:not support sdram
				   u32 len
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartTxDmaKick(u8 * buf, u32 len)
{
	if((u32)buf & 0x02000000 ) //not support sdram
		return;
	hx330x_dmauart_sendDmakick(buf, len - 1);
	hx330x_DmaUart_con_cfg(DMAUART_TX_IE,1);
	dmauart_op.test_state = 1;
}

/*******************************************************************************
* Function Name  : hal_dmauartInit
* Description    : hal layer.dma uart initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartTest(void)
{
	deg_Printf("hal_dmauartTest\n");
	u8 dmaUartBuf[16]; //location in gram,beacause stack is in gram
	u8 i;
	hal_dmauartInit();
	//hx330x_dmauart_sendbyte(0x55);
	for(i = 0; i < sizeof(dmaUartBuf);i++)
		dmaUartBuf[i] = i + 0x10;
	hal_dmauartTxDma(dmaUartBuf, sizeof(dmaUartBuf));
	//hx330x_dmauart_sendbyte(0xAA);
	for(i = 0; i < sizeof(dmaUartBuf);i++)
		dmaUartBuf[i] = i + 0x20;
	hal_dmauartTxDmaKick(dmaUartBuf, sizeof(dmaUartBuf));
	while(dmauart_op.test_state) hal_wdtClear();
	hx330x_dmauart_sendbyte(0xCC);

	//hx330x_dmauart_recvBytekick();
	//hx330x_dmauart_recvAutoDmakick(dmauart_op.rx_fifo, DMAUART_OVERFLOW_8BYTE, DMAUART_RX_LOOPCNT_16BYTE);
}
