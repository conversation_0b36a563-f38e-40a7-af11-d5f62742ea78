/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : taskComMsgMode
* Description    : taskComMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int taskComMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		deg_Printf("[COM]:deal MSG Mode\n");
#if FUN_BATTERY_CHARGE_SHOW 
		if(app_taskCurId()!= TASK_BAT_CHARGE)
#endif
			app_taskChange();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : taskComMsgUSBHost
* Description    : taskComMsgUSBHost
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int taskComMsgUSBHost(winHandle handle,u32 parameNum,u32* parame)
{
	u32 usbhost_state = USBHOST_STAT_MAX;
	if(parameNum == 1)
		usbhost_state = parame[0];
	if(usbhost_state >= USBHOST_STAT_MAX)
		return 0;
	SysCtrl.dev_stat_power &= ~POWERON_FLAG_WAIT;
	switch(usbhost_state)
	{
		case USBHOST_STAT_NULL:
			SysCtrl.lcdshow_win_mode_save = SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
			if((app_taskCurId() == TASK_RECORD_VIDEO) || (app_taskCurId() == TASK_RECORD_PHOTO))
			{
				app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);	
			}
			break;
        case USBHOST_STAT_IN:
            if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
            {
                SysCtrl.dev_stat_power|= POWERON_FLAG_WAIT;
            }
            break;
		case USBHOST_STAT_SHOW:
			SysCtrl.lcdshow_win_mode_save = LCDSHOW_MAIN_WINA;
			if(app_taskCurId() == TASK_RECORD_VIDEO)
			{
				SysCtrl.lcdshow_win_mode = LCDSHOW_MAIN_WINA;
				app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);	
			}
			break;
		case USBHOST_STAT_ASTERN:
			//SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINB;
			if(app_taskCurId() == TASK_PLAY_VIDEO)
			{
				videoPlaybackStop();
				hal_csiEnable(1);
				app_lcdCsiVideoShowStart();
				app_lcdShowWinModeCfg(LCDSHOW_ONLYWINB);
				husb_api_usensor_relinkLcd_reg();
				uiOpenWindow(&asternWindow,0,0);
				deg_Printf("[COM]: play back astern on\n");
			}else
			{
				app_lcdShowWinModeCfg(LCDSHOW_ONLYWINB);
				uiOpenWindow(&asternWindow,0,0);
			}
			break;
		default: break;
	}
	return 0;
}

ALIGNED(4) const msgDealInfor taskComMsgDeal[]=
{
//	{KEY_EVENT_MODE,	taskComMsgMode},
	{SYS_EVENT_USBHOST,	taskComMsgUSBHost},
	{EVENT_MAX,NULL},
};






