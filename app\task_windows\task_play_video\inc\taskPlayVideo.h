/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_PLAY_VIDEO_H
#define  __TASK_PLAY_VIDEO_H

#define TASK_PLAYVIDEO_SUB_NONE				0
#define TASK_PLAYVIDEO_SUB_THUMBNALL		1
#define TASK_PLAYVIDEO_SUB_SLIDE			2
#define TASK_PLAYVIDEO_STOPLASTFRAME		0
#define TASK_PLAYVIDEO_STOPFIRSTFRAME      	1
//--------------------------- PLAY VIDEO CFG------------------------------------------------------------------
#define TASK_PLAYVIDEO_AUTOPLAY         	1      	// 0,playback end,display last frame of current file
													// 1,playback end,display first frame of current file
													// 2,playback end,display first frame of next file
#define TASK_PLAYVIDEO_SUB_SET         		TASK_PLAYVIDEO_SUB_NONE
#define TASK_PLAYVIDEO_STOPFRAME			TASK_PLAYVIDEO_STOPFIRSTFRAME


typedef enum{
	PLAYVIDEO_MAIN = 0,
	PLAYVIDEO_SLIDE,
	PLAYVIDEO_THUMBNALL,
	PLAYVIDEO_MODE_MAX,
}PLAYVIDEO_MODE;
typedef enum{
	PLAYVIDEO_SLIDE_STOP = 0,
	PLAYVIDEO_SLIDE_START,	
	PLAYVIDEO_SLIDE_PAUSE,
	PLAYVIDEO_SLIDE_STAT_MAX,		
}PLAYVIDEO_SLIDE_STAT;
typedef enum{
	SLIDE_RECT_MAIN = 0,
	SLIDE_RECT_SLIDE,
}PLAYVIDEO_RECTTYPE;
typedef enum{
	PLAYVIDEO_SLIDE_AROUND_OPEN = 0,	//四周扩散
	PLAYVIDEO_SLIDE_AROUND_CLOSE,	   	//四周收缩
	PLAYVIDEO_SLIDE_HOR_OPEN,		   	//水平扩散
	PLAYVIDEO_SLIDE_HOR_CLOSE,		   	//水平收缩
	PLAYVIDEO_SLIDE_VER_OPEN,			//垂直扩散
	PLAYVIDEO_SLIDE_VER_CLOSE,			//垂直收缩
	PLAYVIDEO_SLIDE_LEFT,				//向左覆盖
	PLAYVIDEO_SLIDE_RIGHT,				//向右覆盖
	PLAYVIDEO_SLIDE_UP,					//向上覆盖
	PLAYVIDEO_SLIDE_DOWN,				//向下覆盖
	PLAYVIDEO_SLIDE_TYPE_MAX,		
}PLAYVIDEO_SLIDE_STYLE;
typedef struct SLIDE_RECT_S
{
	u16 xs;
	u16 ys;
	u16 xe;
	u16 ye;
}SLIDE_RECT_T;
typedef struct PLAYVIDEO_SLIDE_OP_S
{
	u8   playstat;
	u8   rect_type;		//0: rect open, 1: rect close 
	u8   step_cur;
	u8   step_max;
	u32  style;
	u32  frame_interval;	//播放间隔：msec
	u32  step_interval;	//播放间隔：msec
	u32  playtime;			//播放间隔：msec
	u32  bufsize;
	u8*  mainBuf;
	u8*  slideBuf;
	int  file_index;
	
	SLIDE_RECT_T main_rect;
	SLIDE_RECT_T slide_rect;
}PLAYVIDEO_SLIDE_OP_T;
typedef struct PLAYVIDEO_OP_S
{
	u32  playMode;
	int  playErrIndex;
	int  list;
	//u32  playTotalTime;
	//u32  playLastTime;
	//u32  playCurTime;
    u8  upkeystate;
    u8  downkeystate;	
	PLAYVIDEO_SLIDE_OP_T slide;
}PLAYVIDEO_OP_T;


extern PLAYVIDEO_OP_T  playVideoOp;
EXTERN_WINDOW(playVideoMainWindow);
EXTERN_WINDOW(playVideoSlideWindow);
EXTERN_WINDOW(playVideoThumbnallWindow);
extern sysTask_T taskPlayVideo;

/*******************************************************************************
* Function Name  : taskPlayVideoThumbnallDrawImage
* Description    : taskPlayVideoThumbnallDrawImage
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskPlayVideoThumbnallDrawImage(lcdshow_frame_t* p_frame,int index,uiRect* tar_rect);
/*******************************************************************************
* Function Name  : taskPlayVideoSlideOpen
* Description    : taskPlayVideoSlideOpen
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskPlayVideoSlideOpen(void);
/*******************************************************************************
* Function Name  : taskPlayVideoSlideClose
* Description    : taskPlayVideoSlideClose
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
void taskPlayVideoSlideClose(void);
/*******************************************************************************
* Function Name  : taskPlayVideoSlidePause
* Description    : taskPlayVideoSlidePause
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlidePause(void);
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStart
* Description    : taskPlayVideoSlideStart
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlideStart(void);
/*******************************************************************************
* Function Name  : taskPlayVideoMainStart
* Description    : taskPlayVideoMainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayVideoMainStart(int index);








#endif
