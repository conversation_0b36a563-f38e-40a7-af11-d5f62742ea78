/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	VIDEO_RES_TITLE_ID=0,
    VIDEO_RES_LIEN_ID,
	VIDEO_RES_RECT_ID,
	VIDEO_RES_SELECT_ID,	
};

UNUSED ALIGNED(4) const widgetCreateInfor videoResolutionWin[] =
{
    createFrameWin(						Rx(70),	<PERSON>y(62), <PERSON>w(180),<PERSON>h(106),R_ID_PALETTE_Black, W<PERSON>_ABS_POS),
	createRect(VIDEO_RES_LIEN_ID,         	Rx(1),	<PERSON>y(1),  Rw(178),<PERSON>h(104), R_ID_LINE),
	createStringIcon(VIDEO_RES_TITLE_ID,	Rx(1), 	Ry(1), 	Rw(178),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	createRect(VIDEO_RES_RECT_ID,       	Rx(1),	Ry(33), Rw(178),Rh(1),	R_ID_PALETTE_DarkGreen),
	createItemManage(VIDEO_RES_SELECT_ID,	Rx(1),	Ry(34), Rw(178),Rh(71),INVALID_COLOR),
	// createFrameWin(						Rx(70),	Ry(70), Rw(180),Rh(120),R_ID_LINE, WIN_ABS_POS),
	// // createStringIcon(VIDEO_RES_TITLE_ID,	Rx(0), 	Ry(0), 	Rw(180),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	// createRect(VIDEO_RES_RECT_ID,       	Rx(1),	Ry(1), Rw(178),Rh(118),	R_ID_LINE),
	// createItemManage(VIDEO_RES_SELECT_ID,	Rx(1),	Ry(1), Rw(178),Rh(118), INVALID_COLOR),
	widgetEnd(),
}; 