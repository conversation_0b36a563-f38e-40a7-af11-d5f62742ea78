/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_DVP_720P_OV9732 > 0

SENSOR_INIT_SECTION const u8 OV9732InitTable[] = 
{	
	0x01, 0x03, 0x01,
	0x01, 0x00, 0x00,
	0x30, 0x01, 0x3f,
	0x30, 0x02, 0xff,
	0x30, 0x07, 0x00,
	0x30, 0x09, 0x03,
	0x30, 0x10, 0x00,
	0x30, 0x11, 0x00,
	0x30, 0x14, 0x36,
	0x30, 0x1e, 0x15,
	0x30, 0x30, 0x09,
	0x30, 0x80, 0x02,
	0x30, 0x81, 0x3c, //36m
	0x30, 0x82, 0x04,
	0x30, 0x83, 0x00,
	0x30, 0x84, 0x04,
	0x30, 0x85, 0x01,
	0x30, 0x86, 0x01,
	0x30, 0x89, 0x01,
	0x30, 0x8a, 0x00,
	0x31, 0x03, 0x01,
	0x36, 0x00, 0xf6,
	0x36, 0x01, 0x72,
	0x36, 0x11, 0xb0,
	0x36, 0x12, 0x35,
	0x36, 0x54, 0x10,
	0x36, 0x55, 0x77,
	0x36, 0x56, 0x77,
	0x36, 0x57, 0x07,
	0x36, 0x58, 0x22,
	0x36, 0x59, 0x22,
	0x36, 0x5a, 0x02,
	0x37, 0x00, 0x05,
	0x37, 0x01, 0x0a,
	0x37, 0x02, 0x08,
	0x37, 0x04, 0x09,
	0x37, 0x84, 0x05,
	0x37, 0x85, 0x55,
	0x37, 0xc0, 0x07,
	0x38, 0x00, 0x00,
	0x38, 0x01, 0x04,
	0x38, 0x02, 0x00,
	0x38, 0x03, 0x04,
	0x38, 0x04, 0x05,
	0x38, 0x05, 0x0b,//
	0x38, 0x06, 0x02,
	0x38, 0x07, 0xdb,
	0x38, 0x08, 0x05,
	0x38, 0x09, 0x00,
	0x38, 0x0a, 0x02,
	0x38, 0x0b, 0xd0,
	0x38, 0x0c, 0x05,
	//0x38, 0x0d, 0xc6,//
	0x38, 0x0d, 0x60,
	0x38, 0x0e, 0x03,
	0x38, 0x0f, 0x22,
	0x38, 0x10, 0x00,
	0x38, 0x11, 0x04,
	0x38, 0x12, 0x00,
	0x38, 0x13, 0x04,
	0x38, 0x16, 0x00,
	0x38, 0x17, 0x00,
	0x38, 0x18, 0x00,
	0x38, 0x19, 0x04,
	0x38, 0x20, 0x1c,
	0x38, 0x21, 0x00,
	0x38, 0x2c, 0x06,
	0x35, 0x00, 0x00,
	//0x35, 0x01, 0x31,
	//0x35, 0x02, 0x00,
	0x35, 0x01, 0xff,
	0x35, 0x02, 0x01,
	0x35, 0x03, 0x03,
	0x35, 0x04, 0x00,
	0x35, 0x05, 0x00,
	0x35, 0x09, 0x10,
	0x35, 0x0a, 0x00,
	0x35, 0x0b, 0x40,
	//0x35, 0x0b, 0x20,
	0x3d, 0x00, 0x00,
	0x3d, 0x01, 0x00,
	0x3d, 0x02, 0x00,
	0x3d, 0x03, 0x00,
	0x3d, 0x04, 0x00,
	0x3d, 0x05, 0x00,
	0x3d, 0x06, 0x00,
	0x3d, 0x07, 0x00,
	0x3d, 0x08, 0x00,
	0x3d, 0x09, 0x00,
	0x3d, 0x0a, 0x00,
	0x3d, 0x0b, 0x00,
	0x3d, 0x0c, 0x00,
	0x3d, 0x0d, 0x00,
	0x3d, 0x0e, 0x00,
	0x3d, 0x0f, 0x00,
	0x3d, 0x80, 0x00,
	0x3d, 0x81, 0x00,
	0x3d, 0x82, 0x38,
	0x3d, 0x83, 0xa4,
	0x3d, 0x84, 0x00,
	0x3d, 0x85, 0x00,
	0x3d, 0x86, 0x1f,
	0x3d, 0x87, 0x03,
	0x3d, 0x8b, 0x00,
	0x3d, 0x8f, 0x00,
	0x40, 0x01, 0xe0,
	0x40, 0x04, 0x00,
	0x40, 0x05, 0x02,
	0x40, 0x06, 0x01,
	0x40, 0x07, 0x40,
	0x40, 0x09, 0x0b,
	0x43, 0x00, 0x03,
	0x43, 0x01, 0xff,
	0x43, 0x04, 0x00,
	0x43, 0x05, 0x00,
	0x43, 0x09, 0x00,
	0x46, 0x00, 0x00,
	0x46, 0x01, 0x04,
	0x48, 0x00, 0x04,
	0x48, 0x05, 0x00,
	0x48, 0x21, 0x3c,
	0x48, 0x23, 0x3c,
	0x48, 0x37, 0x2d,
	0x4a, 0x00, 0x00,
	0x4f, 0x00, 0x80,
	0x4f, 0x01, 0x10,
	0x4f, 0x02, 0x00,
	0x4f, 0x03, 0x00,
	0x4f, 0x04, 0x00,
	0x4f, 0x05, 0x00,
	0x4f, 0x06, 0x00,
	0x4f, 0x07, 0x00,
	0x4f, 0x08, 0x00,
	0x4f, 0x09, 0x00,
	0x50, 0x00, 0x07,
	0x57, 0x81, 0x00, 
	0x57, 0x82, 0x77, 
	0x57, 0x83, 0x0f, 
	0x50, 0x0c, 0x00,
	0x50, 0x0d, 0x00,
	0x50, 0x0e, 0x00,
	0x50, 0x0f, 0x00,
	0x50, 0x10, 0x00,
	0x50, 0x11, 0x00,
	0x50, 0x12, 0x00,
	0x50, 0x13, 0x00,
	0x50, 0x14, 0x00,
	0x50, 0x15, 0x00,
	0x50, 0x16, 0x00,
	0x50, 0x17, 0x00,
	0x50, 0x80, 0x00,
	0x51, 0x80, 0x01,
	0x51, 0x81, 0x00,
	0x51, 0x82, 0x01,
	0x51, 0x83, 0x00,
	0x51, 0x84, 0x01,
	0x51, 0x85, 0x00,
	0x57, 0x08, 0x06,
	0x01, 0x00, 0x01,
	0x5c, 0x80, 0x05,
	0x5c, 0x81, 0x60,
	0x5c, 0x82, 0x09,
	0x5c, 0x83, 0x5f,
	0x5c, 0x85, 0x6c,
	0x56, 0x01, 0x04,
	0x56, 0x02, 0x02,
	0x56, 0x03, 0x01,
	0x56, 0x04, 0x04,
	0x56, 0x05, 0x02,
	0x56, 0x06, 0x01,
	0x54, 0x00, 0xff,
	0x54, 0x01, 0xc8,
	0x54, 0x02, 0x9f,
	0x54, 0x03, 0x8b,
	0x54, 0x04, 0x83,
	0x54, 0x05, 0x86,
	0x54, 0x06, 0x91,
	0x54, 0x07, 0xa9,
	0x54, 0x08, 0xe1,
	0x54, 0x09, 0xff,
	0x54, 0x0a, 0x7e,
	0x54, 0x0b, 0x60,
	0x54, 0x0c, 0x50,
	0x54, 0x0d, 0x47,
	0x54, 0x0e, 0x43,
	0x54, 0x0f, 0x44,
	0x54, 0x10, 0x49,
	0x54, 0x11, 0x55,
	0x54, 0x12, 0x65,
	0x54, 0x13, 0x8c,
	0x54, 0x14, 0x4f,
	0x54, 0x15, 0x3c,
	0x54, 0x16, 0x31,
	0x54, 0x17, 0x2a,
	0x54, 0x18, 0x27,
	0x54, 0x19, 0x28,
	0x54, 0x1a, 0x2c,
	0x54, 0x1b, 0x33,
	0x54, 0x1c, 0x3f,
	0x54, 0x1d, 0x56,
	0x54, 0x1e, 0x34,
	0x54, 0x1f, 0x26,
	0x54, 0x20, 0x1d,
	0x54, 0x21, 0x18,
	0x54, 0x22, 0x15,
	0x54, 0x23, 0x15,
	0x54, 0x24, 0x19,
	0x54, 0x25, 0x1f,
	0x54, 0x26, 0x29,
	0x54, 0x27, 0x39,
	0x54, 0x28, 0x27,
	0x54, 0x29, 0x1a,
	0x54, 0x2a, 0x11,
	0x54, 0x2b, 0x0a,
	0x54, 0x2c, 0x07,
	0x54, 0x2d, 0x07,
	0x54, 0x2e, 0x0b,
	0x54, 0x2f, 0x12,
	0x54, 0x30, 0x1c,
	0x54, 0x31, 0x2b,
	0x54, 0x32, 0x20,
	0x54, 0x33, 0x14,
	0x54, 0x34, 0x0a,
	0x54, 0x35, 0x05,
	0x54, 0x36, 0x02,
	0x54, 0x37, 0x02,
	0x54, 0x38, 0x05,
	0x54, 0x39, 0x0c,
	0x54, 0x3a, 0x16,
	0x54, 0x3b, 0x24,
	0x54, 0x3c, 0x21,
	0x54, 0x3d, 0x14,
	0x54, 0x3e, 0x0b,
	0x54, 0x3f, 0x04,
	0x54, 0x40, 0x02,
	0x54, 0x41, 0x02,
	0x54, 0x42, 0x06,
	0x54, 0x43, 0x0c,
	0x54, 0x44, 0x16,
	0x54, 0x45, 0x25,
	0x54, 0x46, 0x29,
	0x54, 0x47, 0x1b,
	0x54, 0x48, 0x12,
	0x54, 0x49, 0x0c,
	0x54, 0x4a, 0x08,
	0x54, 0x4b, 0x09,
	0x54, 0x4c, 0x0d,
	0x54, 0x4d, 0x14,
	0x54, 0x4e, 0x1e,
	0x54, 0x4f, 0x2c,
	0x54, 0x50, 0x36,
	0x54, 0x51, 0x28,
	0x54, 0x52, 0x1f,
	0x54, 0x53, 0x19,
	0x54, 0x54, 0x17,
	0x54, 0x55, 0x17,
	0x54, 0x56, 0x1b,
	0x54, 0x57, 0x21,
	0x54, 0x58, 0x2b,
	0x54, 0x59, 0x3d,
	0x54, 0x5a, 0x54,
	0x54, 0x5b, 0x40,
	0x54, 0x5c, 0x34,
	0x54, 0x5d, 0x2e,
	0x54, 0x5e, 0x2b,
	0x54, 0x5f, 0x2b,
	0x54, 0x60, 0x2f,
	0x54, 0x61, 0x38,
	0x54, 0x62, 0x46,
	0x54, 0x63, 0x5d,
	0x54, 0x64, 0x8a,
	0x54, 0x65, 0x6a,
	0x54, 0x66, 0x58,
	0x54, 0x67, 0x4f,
	0x54, 0x68, 0x4b,
	0x54, 0x69, 0x4d,
	0x54, 0x6a, 0x52,
	0x54, 0x6b, 0x5e,
	0x54, 0x6c, 0x72,
	0x54, 0x6d, 0x9f,
	0x54, 0x6e, 0xff,
	0x54, 0x6f, 0xe9,
	0x54, 0x70, 0xb6,
	0x54, 0x71, 0x9e,
	0x54, 0x72, 0x96,
	0x54, 0x73, 0x96,
	0x54, 0x74, 0xa4,
	0x54, 0x75, 0xc6,
	0x54, 0x76, 0xff,
	0x54, 0x77, 0xff,
	0x54, 0x78, 0x6d,
	0x54, 0x79, 0x70,
	0x54, 0x7a, 0x71,
	0x54, 0x7b, 0x74,
	0x54, 0x7c, 0x73,
	0x54, 0x7d, 0x73,
	0x54, 0x7e, 0x74,
	0x54, 0x7f, 0x73,
	0x54, 0x80, 0x73,
	0x54, 0x81, 0x70,
	0x54, 0x82, 0x70,
	0x54, 0x83, 0x6f,
	0x54, 0x84, 0x6f,
	0x54, 0x85, 0x70,
	0x54, 0x86, 0x71,
	0x54, 0x87, 0x71,
	0x54, 0x88, 0x71,
	0x54, 0x89, 0x71,
	0x54, 0x8a, 0x6f,
	0x54, 0x8b, 0x72,
	0x54, 0x8c, 0x70,
	0x54, 0x8d, 0x70,
	0x54, 0x8e, 0x72,
	0x54, 0x8f, 0x74,
	0x54, 0x90, 0x75,
	0x54, 0x91, 0x76,
	0x54, 0x92, 0x75,
	0x54, 0x93, 0x74,
	0x54, 0x94, 0x71,
	0x54, 0x95, 0x72,
	0x54, 0x96, 0x71,
	0x54, 0x97, 0x72,
	0x54, 0x98, 0x74,
	0x54, 0x99, 0x77,
	0x54, 0x9a, 0x79,
	0x54, 0x9b, 0x79,
	0x54, 0x9c, 0x79,
	0x54, 0x9d, 0x77,
	0x54, 0x9e, 0x74,
	0x54, 0x9f, 0x74,
	0x54, 0xa0, 0x72,
	0x54, 0xa1, 0x73,
	0x54, 0xa2, 0x77,
	0x54, 0xa3, 0x7c,
	0x54, 0xa4, 0x7f,
	0x54, 0xa5, 0x80,
	0x54, 0xa6, 0x7f,
	0x54, 0xa7, 0x7c,
	0x54, 0xa8, 0x77,
	0x54, 0xa9, 0x78,
	0x54, 0xaa, 0x74,
	0x54, 0xab, 0x73,
	0x54, 0xac, 0x79,
	0x54, 0xad, 0x7f,
	0x54, 0xae, 0x83,
	0x54, 0xaf, 0x84,
	0x54, 0xb0, 0x84,
	0x54, 0xb1, 0x7f,
	0x54, 0xb2, 0x7a,
	0x54, 0xb3, 0x79,
	0x54, 0xb4, 0x72,
	0x54, 0xb5, 0x73,
	0x54, 0xb6, 0x78,
	0x54, 0xb7, 0x7f,
	0x54, 0xb8, 0x83,
	0x54, 0xb9, 0x84,
	0x54, 0xba, 0x83,
	0x54, 0xbb, 0x7f,
	0x54, 0xbc, 0x7a,
	0x54, 0xbd, 0x79,
	0x54, 0xbe, 0x72,
	0x54, 0xbf, 0x72,
	0x54, 0xc0, 0x76,
	0x54, 0xc1, 0x7b,
	0x54, 0xc2, 0x7e,
	0x54, 0xc3, 0x7f,
	0x54, 0xc4, 0x7f,
	0x54, 0xc5, 0x7b,
	0x54, 0xc6, 0x77,
	0x54, 0xc7, 0x77,
	0x54, 0xc8, 0x71,
	0x54, 0xc9, 0x71,
	0x54, 0xca, 0x74,
	0x54, 0xcb, 0x77,
	0x54, 0xcc, 0x79,
	0x54, 0xcd, 0x7a,
	0x54, 0xce, 0x7a,
	0x54, 0xcf, 0x77,
	0x54, 0xd0, 0x75,
	0x54, 0xd1, 0x74,
	0x54, 0xd2, 0x71,
	0x54, 0xd3, 0x70,
	0x54, 0xd4, 0x72,
	0x54, 0xd5, 0x74,
	0x54, 0xd6, 0x76,
	0x54, 0xd7, 0x76,
	0x54, 0xd8, 0x76,
	0x54, 0xd9, 0x75,
	0x54, 0xda, 0x71,
	0x54, 0xdb, 0x73,
	0x54, 0xdc, 0x72,
	0x54, 0xdd, 0x6f,
	0x54, 0xde, 0x70,
	0x54, 0xdf, 0x71,
	0x54, 0xe0, 0x71,
	0x54, 0xe1, 0x72,
	0x54, 0xe2, 0x72,
	0x54, 0xe3, 0x71,
	0x54, 0xe4, 0x71,
	0x54, 0xe5, 0x72,
	0x54, 0xe6, 0x6b,
	0x54, 0xe7, 0x70,
	0x54, 0xe8, 0x71,
	0x54, 0xe9, 0x72,
	0x54, 0xea, 0x73,
	0x54, 0xeb, 0x73,
	0x54, 0xec, 0x73,
	0x54, 0xed, 0x72,
	0x54, 0xee, 0x71,
	0x54, 0xef, 0x6c,
	0x54, 0xf0, 0x8a,
	0x54, 0xf1, 0x8f,
	0x54, 0xf2, 0x8f,
	0x54, 0xf3, 0x8e,
	0x54, 0xf4, 0x90,
	0x54, 0xf5, 0x8e,
	0x54, 0xf6, 0x8f,
	0x54, 0xf7, 0x8f,
	0x54, 0xf8, 0x8e,
	0x54, 0xf9, 0x90,
	0x54, 0xfa, 0x8a,
	0x54, 0xfb, 0x89,
	0x54, 0xfc, 0x88,
	0x54, 0xfd, 0x88,
	0x54, 0xfe, 0x87,
	0x54, 0xff, 0x88,
	0x55, 0x00, 0x89,
	0x55, 0x01, 0x89,
	0x55, 0x02, 0x8c,
	0x55, 0x03, 0x8c,
	0x55, 0x04, 0x87,
	0x55, 0x05, 0x84,
	0x55, 0x06, 0x83,
	0x55, 0x07, 0x82,
	0x55, 0x08, 0x82,
	0x55, 0x09, 0x83,
	0x55, 0x0a, 0x83,
	0x55, 0x0b, 0x85,
	0x55, 0x0c, 0x87,
	0x55, 0x0d, 0x8c,
	0x55, 0x0e, 0x81,
	0x55, 0x0f, 0x80,
	0x55, 0x10, 0x80,
	0x55, 0x11, 0x7f,
	0x55, 0x12, 0x7f,
	0x55, 0x13, 0x80,
	0x55, 0x14, 0x81,
	0x55, 0x15, 0x82,
	0x55, 0x16, 0x85,
	0x55, 0x17, 0x86,
	0x55, 0x18, 0x7f,
	0x55, 0x19, 0x7e,
	0x55, 0x1a, 0x7e,
	0x55, 0x1b, 0x7f,
	0x55, 0x1c, 0x80,
	0x55, 0x1d, 0x81,
	0x55, 0x1e, 0x81,
	0x55, 0x1f, 0x82,
	0x55, 0x20, 0x83,
	0x55, 0x21, 0x86,
	0x55, 0x22, 0x7d,
	0x55, 0x23, 0x7c,
	0x55, 0x24, 0x7d,
	0x55, 0x25, 0x7f,
	0x55, 0x26, 0x80,
	0x55, 0x27, 0x81,
	0x55, 0x28, 0x82,
	0x55, 0x29, 0x82,
	0x55, 0x2a, 0x83,
	0x55, 0x2b, 0x85,
	0x55, 0x2c, 0x7d,
	0x55, 0x2d, 0x7c,
	0x55, 0x2e, 0x7d,
	0x55, 0x2f, 0x80,
	0x55, 0x30, 0x81,
	0x55, 0x31, 0x82,
	0x55, 0x32, 0x82,
	0x55, 0x33, 0x82,
	0x55, 0x34, 0x82,
	0x55, 0x35, 0x85,
	0x55, 0x36, 0x7e,
	0x55, 0x37, 0x7d,
	0x55, 0x38, 0x7f,
	0x55, 0x39, 0x80,
	0x55, 0x3a, 0x81,
	0x55, 0x3b, 0x81,
	0x55, 0x3c, 0x82,
	0x55, 0x3d, 0x82,
	0x55, 0x3e, 0x83,
	0x55, 0x3f, 0x84,
	0x55, 0x40, 0x81,
	0x55, 0x41, 0x80,
	0x55, 0x42, 0x7f,
	0x55, 0x43, 0x7f,
	0x55, 0x44, 0x7f,
	0x55, 0x45, 0x80,
	0x55, 0x46, 0x81,
	0x55, 0x47, 0x82,
	0x55, 0x48, 0x83,
	0x55, 0x49, 0x86,
	0x55, 0x4a, 0x87,
	0x55, 0x4b, 0x84,
	0x55, 0x4c, 0x82,
	0x55, 0x4d, 0x81,
	0x55, 0x4e, 0x81,
	0x55, 0x4f, 0x82,
	0x55, 0x50, 0x82,
	0x55, 0x51, 0x83,
	0x55, 0x52, 0x85,
	0x55, 0x53, 0x88,
	0x55, 0x54, 0x8a,
	0x55, 0x55, 0x89,
	0x55, 0x56, 0x88,
	0x55, 0x57, 0x87,
	0x55, 0x58, 0x86,
	0x55, 0x59, 0x87,
	0x55, 0x5a, 0x87,
	0x55, 0x5b, 0x88,
	0x55, 0x5c, 0x89,
	0x55, 0x5d, 0x8b,
	0x55, 0x5e, 0x88,
	0x55, 0x5f, 0x8e,
	0x55, 0x60, 0x8c,
	0x55, 0x61, 0x8d,
	0x55, 0x62, 0x8e,
	0x55, 0x63, 0x8c,
	0x55, 0x64, 0x8c,
	0x55, 0x65, 0x8b,
	0x55, 0x66, 0x8d,
	0x55, 0x67, 0x89,
	0x55, 0x70, 0x54,
	0x59, 0x40, 0x50,
	0x59, 0x41, 0x04,
	0x59, 0x42, 0x08,
	0x59, 0x43, 0x10,
	0x59, 0x44, 0x18,
	0x59, 0x45, 0x30,
	0x59, 0x46, 0x40,
	0x59, 0x47, 0x80,
	0x59, 0x48, 0xf0,
	0x59, 0x49, 0x10,
	0x59, 0x4a, 0x08,
	0x59, 0x4b, 0x18,
	0x5b, 0x80, 0x04,
	0x5b, 0x81, 0x0a,
	0x5b, 0x82, 0x18,
	0x5b, 0x83, 0x20,
	0x5b, 0x84, 0x30,
	0x5b, 0x85, 0x44,
	0x5b, 0x86, 0x10,
	0x5b, 0x87, 0x12,
	0x5b, 0x88, 0x14,
	0x5b, 0x89, 0x15,
	0x5b, 0x8a, 0x16,
	0x5b, 0x8b, 0x18,
	0x5b, 0x8c, 0x10,
	0x5b, 0x8d, 0x12,
	0x5b, 0x8e, 0x14,
	0x5b, 0x8f, 0x15,
	0x5b, 0x90, 0x16,
	0x5b, 0x91, 0x18,

	SENSOR_TAB_END
};


SENSOR_LSC_TAB_SECTION const  u16 ov9732_lsc_tab[572] = 
{
	583,463,384,341,316,308,314,334,374,446,551,
	545,435,364,323,300,290,297,318,356,425,527,
	519,415,349,309,287,278,284,302,338,402,504,
	503,398,333,296,276,268,276,290,325,387,482,
	488,388,329,290,268,260,271,283,316,374,468,
	485,383,323,286,265,256,268,283,312,369,464,
	480,383,324,285,264,255,265,282,313,369,461,
	485,386,327,290,266,260,265,283,313,373,463,
	498,399,336,294,273,265,270,287,320,381,474,
	519,413,346,306,281,275,278,297,334,394,494,
	543,437,362,321,297,286,291,312,349,414,516,
	582,463,386,339,313,303,308,330,372,439,547,
	590,467,392,344,319,306,311,334,376,446,551,

	583,463,384,341,316,308,314,334,374,446,551,
	545,435,364,323,300,290,297,318,356,425,527,
	519,415,349,309,287,278,284,302,338,402,504,
	503,398,333,296,276,268,276,290,325,387,482,
	488,388,329,290,268,260,271,283,316,374,468,
	485,383,323,286,265,256,268,283,312,369,464,
	480,383,324,285,264,255,265,282,313,369,461,
	485,386,327,290,266,260,265,283,313,373,463,
	498,399,336,294,273,265,270,287,320,381,474,
	519,413,346,306,281,275,278,297,334,394,494,
	543,437,362,321,297,286,291,312,349,414,516,
	582,463,386,339,313,303,308,330,372,439,547,
	590,467,392,344,319,306,311,334,376,446,551,

	583,463,384,341,316,308,314,334,374,446,551,
	545,435,364,323,300,290,297,318,356,425,527,
	519,415,349,309,287,278,284,302,338,402,504,
	503,398,333,296,276,268,276,290,325,387,482,
	488,388,329,290,268,260,271,283,316,374,468,
	485,383,323,286,265,256,268,283,312,369,464,
	480,383,324,285,264,255,265,282,313,369,461,
	485,386,327,290,266,260,265,283,313,373,463,
	498,399,336,294,273,265,270,287,320,381,474,
	519,413,346,306,281,275,278,297,334,394,494,
	543,437,362,321,297,286,291,312,349,414,516,
	582,463,386,339,313,303,308,330,372,439,547,
	590,467,392,344,319,306,311,334,376,446,551,

	583,463,384,341,316,308,314,334,374,446,551,
	545,435,364,323,300,290,297,318,356,425,527,
	519,415,349,309,287,278,284,302,338,402,504,
	503,398,333,296,276,268,276,290,325,387,482,
	488,388,329,290,268,260,271,283,316,374,468,
	485,383,323,286,265,256,268,283,312,369,464,
	480,383,324,285,264,255,265,282,313,369,461,
	485,386,327,290,266,260,265,283,313,373,463,
	498,399,336,294,273,265,270,287,320,381,474,
	519,413,346,306,281,275,278,297,334,394,494,
	543,437,362,321,297,286,291,312,349,414,516,
	582,463,386,339,313,303,308,330,372,439,547,
	590,467,392,344,319,306,311,334,376,446,551
};

/*static u32 sensor_ov9732_exp_rd(void)
{
	return 0; 
}*/
static void ov9732_hvblank(u32 h,u32 v)
{
//	u8 vsyn_width[6] = {0x47,0x02,0x00,0x47,0x03,0x00};
//	vsyn_width[]
//	sensor_iic_write(vsyn_width);
//	u8 vsyn_delay[9]={0x47,0x05,0x01,0x47,0x06,0xf5,0x47,0x07,0x90};
//	u8 vsyn_width[3] = {0x47,0x01,0x02};
////	sensor_iic_write(vsyn_width);
//	sensor_iic_write(&vsyn_delay[0]);
//	sensor_iic_write(&vsyn_delay[3]);
//	sensor_iic_write(&vsyn_delay[6]);
}

static void sensor_ov9732_exp_wr(u32 EV)
{
	u8 iicbuf[9] = {0x35,0x00,0x00,0x35,0x01,0x00,0x35,0x02,0x00};
	iicbuf[2] = ((EV & 0xf000)>>12);
	iicbuf[5] = ((EV & 0x0ff0)>>4);
	iicbuf[8] = ((EV & 0xf)<<4);

//	sensor_iic_enable();
//	sensor_iic_info();	
	sensor_iic_write(iicbuf);	
	//Delay_MS(1);
	sensor_iic_write(iicbuf+3);	
	sensor_iic_write(iicbuf+6);
//	 sensor_iic_diable();		
}
static void sensor_ov9732_gain_wr(u32 EV)
{
//	u32 i;
	u8 iicbuf[3] = {0x35,0x0b,0x00};
//	u32 rough_gain[5] = {256,512,1024,2048,4097};
	EV = hx330x_clip(EV,0,2327);
	/*
	for(i=0;i<4;i++){
		if((EV >= rough_gain[i])&&(EV < rough_gain[i+1])){
			iicbuf[2] |= (i<<4);
			iicbuf[2] |= (((EV<<8) / rough_gain[i]-256)>>4);
			break;
		}
	}*/
	iicbuf[2] = (EV >> 4);
//	sensor_iic_enable();
//	sensor_iic_info();		
	sensor_iic_write(iicbuf);	
//	 sensor_iic_diable();		
}
static void sensor_ov9732_exp_gain_wr(u32 exp,u32 gain){
	sensor_ov9732_exp_wr(exp);
	sensor_ov9732_gain_wr(gain);
}
SENSOR_OP_SECTION const Sensor_Adpt_T ov9732_adpt = 
{
	.typ 				= CSI_TYPE_RAW10| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 1,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 1,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 1,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 1,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 1,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = 1280, 			//sensor input width
	.senPixelh          = 720,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 1280,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 720,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 1280,				//csi input width
	.pixelh				= 720,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 0,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_BGGR,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB
	
	.sensorCore			= SYS_VOL_V1_5,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V1_8,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56
	
	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},
	.hvb_adapt = {
#if  (CURRENT_CHIP == FPGA)
		.pclk			= 42000000,			//csi pclk input
#else
		.pclk			= 42000000,			//csi pclk input
#endif	
		.v_len			= 800,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 20,				//sensor fps set
#else
		.fps			= 25,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_EN_  <<_BLC_POS_ | _ISP_EN_  <<_LSC_POS_  | _ISP_AUTO_<<_DDC_POS_   | _ISP_AUTO_<<_AWB_POS_  \
					|_ISP_EN_  <<_CCM_POS_ | _ISP_AUTO_<<_AE_POS_   | _ISP_AUTO_<<_DGAIN_POS_ | _ISP_AUTO_<<_YGAMA_POS_ \
					| _ISP_AUTO_<<_RGB_GAMA_POS_ | _ISP_AUTO_<<_CH_POS_\
					|_ISP_AUTO_<<_VDE_POS_ | _ISP_AUTO_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    | _ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
					
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= -16,					//BLC red adjust //signed 10bit
		.blkl_gr	= -16,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= -16,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= -16,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 1,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {1,1,1,1,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,11,15,18,20,21,22}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},					
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 330,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 330,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 630, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xc0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			133,137,140,142,144,144,145,145,144,143,142,135,133,131,129,126,127,123,123,120,116,113,109,103, 98, 92, 88, 84, 77, 70, 62, 53, //bgain_out_high
			128,132,136,138,140,141,141,141,140,139,137,129,125,123,118,118,118,115,110,111,109,103, 96, 91, 86, 81, 78, 74, 69, 64, 58, 53, //bgain_in_high
			128,132,135,137,137,136,135,133,130,127,123,119,113,108,106,105,101, 99, 97, 92, 83, 80, 77, 74, 64, 61, 58, 56, 54, 53, 52, 53, //bgain_in_low  
			128,132,135,135,135,133,131,127,123,119,114,109,104, 99, 97, 97, 93, 90, 84, 75, 70, 70, 66, 57, 54, 52, 49, 48, 46, 47, 49, 52 //bgain_out_low
		}		
	},	
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000,  
			0x00,	0x00,	0x100   
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},		
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {40,50,60,70,75,80,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 1120*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 2048*4,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 160,	//
			.ae_win_x1			= 320,
			.ae_win_x2			= 960,
			.ae_win_x3			= 1120,
			.ae_win_y0			= 200,
			.ae_win_y1			= 400,
			.ae_win_y2			= 640,
			.ae_win_y3			= 680,
			.weight_0_7			= 0x44111111,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x114f4114,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x11111444,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},				
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {7,7,7,7,7,7,7,7},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 16,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 16,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 1,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 1,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.sat		= {4,8,12,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 7,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {60,64,68,78,84,88,88,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 8,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {5,5,4,4,3,3,3,3},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {8,6,4,2,2,2,2,2}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,12,12,12,12,12,13,13,14,14,15,15,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {5,6,7,8,9,10,12,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 7,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (1280/4)*1,	
		.win_h_end		= (1280/4)*3,
		.win_v_start	= (720/4)*1,
		.win_v_end		= (720/4)*3,
	}, 
	.p_fun_adapt = {
		.fp_rotate		= NULL,
		.fp_hvblank		= ov9732_hvblank,
		.fp_exp_gain_wr	= sensor_ov9732_exp_gain_wr
	},	
};

SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T ov9732_init =
{
	.sensor_struct_addr   	= (u32 *)&ov9732_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)OV9732InitTable,     
	.sensor_init_tab_size 	= sizeof(OV9732InitTable),
	.lsc_tab_adr 			= (u32 *)ov9732_lsc_tab,     
	.lsc_tab_size 			= sizeof(ov9732_lsc_tab), 
	.sensor_name	  		= "OV9732_720P",
	.w_cmd            		= 0x6c,                   
	.r_cmd            		= 0x6d,                   
	.addr_num         		= 0x02,                   
	.data_num         		= 0x01,   
	.id               		= 0x32, 
	.id_reg           		= 0x300b,  
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_RESET_CTRL_EN = 1 有效
	.reset_en				= 0, //目前不使用
	.reset_valid			= 0, //0:低电平reset， 1: 高电平reset
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_PWDN_CTRL_EN = 1 有效
	.pwdn_en				= 1, //根据需要来配置
	.pwdn_valid				= 0, //pwdn_en = 1时有效，0:低电平 power down， 1: 高电平power down； pwdn_en = 1时 默认高电平POWER DOWN                
};





#endif

