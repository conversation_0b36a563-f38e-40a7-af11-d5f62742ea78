/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef _WINDOWS_API_H
#define  _WINDOWS_API_H

typedef struct WINDOWS_S
{
	msgDealInfor* 		msgDeal;
	widgetCreateInfor* 	widgetInfor;
	winHandle   		handle;
	u32  				repeateOpenSupport;
}WINDOWS_T;

/*******************************************************************************
* Function Name  : uiParentDealMsg
* Description    : uiParentDealMsg
* Input          : winHandle handle,u32 parentMsg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiParentDealMsg(winHandle handle,u32 parentMsg);
/*******************************************************************************
* Function Name  : uiOpenWindow
* Description    : uiOpenWindow
* Input          : WINDOWS_T* winInfor,u32 show,u32 argc,...
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiOpenWindow(WINDOWS_T* winInfor,u32 show,u32 argc,...);
/*******************************************************************************
* Function Name  : windowIsOpen
* Description    : windowIsOpen
* Input          : WINDOWS_T* winInfor,u32 show,u32 argc,...
* Output         : none                                            
* Return         : none 
*******************************************************************************/
bool windowIsOpen(WINDOWS_T* winInfor);


#define   winItem         					uiDialogItem
#define   WINDOW(name,msg,widget)        	WINDOWS_T name = {(msgDealInfor*)msg,(widgetCreateInfor*)widget,INVALID_HANDLE,0};
#define   MULTIWIN(name,msg,widget)        	WINDOWS_T name = {(msgDealInfor*)msg,(widgetCreateInfor*)widget,INVALID_HANDLE,1};
#define   EXTERN_WINDOW(name)				extern WINDOWS_T name



#endif

