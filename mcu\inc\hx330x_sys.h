/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_SYS_H
    #define HX330X_SYS_H



enum
{
   SYS_MODE_SLEEP = 0,
   SYS_MODE_HOLD,     //1
   SYS_MODE_IDLE,     //2
   SYS_CLK_IROM,      //3
   SYS_CLK_GRAM,      //4
   SYS_CLK_ICACHE,    //5
   SYS_CLK_DCACHE,    //6
   SYS_CLK_LCDSHOW,        //7
   SYS_CLK_SDRAM,     //8
   SYS_CLK_PIP,       //9
   SYS_CLK_TIMER0,    //10
   SYS_CLK_TIMER1,    //11
   SYS_CLK_TIMER2,    //12
   SYS_CLK_TIMER3,    //13
   SYS_CLK_IIC0,      //14
   SYS_CLK_MBIST,     //15
   SYS_CLK_SD1,       //16
   SYS_CLK_RTC,       //17
   SYS_CLK_UART1,     //18
   SYS_CLK_UART0,     //19
   SYS_CLK_SPI0,      //20
   SYS_CLK_SPI1,      //21
   SYS_CLK_SD0,       //22
   SYS_CLK_IIC1,      //23
   SYS_CLK_AUADC,     //24
   SYS_CLK_LCDC,      //25
   SYS_CLK_JPGA,      //26
   SYS_CLK_DVP_CSI,   //27
   SYS_CLK_USB20,     //28
   SYS_CLK_ADC,       //29
   SYS_CLK_DAC,       //30
   SYS_CLK_JPGB,      //31
   SYS_CLK_USB11,     //32
   SYS_CLK_UILZO,    //33
   SYS_CLK_ROTATE,    //34
   SYS_CLK_SPIF,      //35
   SYS_CLK_MIPI_CSI,  //36
};

#define  SYS_LDO_VDDSENIO   	0
#define  SYS_LDO_VDDSENCORE   	1
#define  SYS_LDO_BGOE   		2
#define  SYS_LDO_V1_2   		3
#define  SYS_LDO_SDC    		4
#define  SYS_LDO_DDR    		5

typedef enum 
{
	SYS_VOL_V0_71=0,
	SYS_VOL_V0_74,
	SYS_VOL_V0_77,
	SYS_VOL_V0_80,
	SYS_VOL_V0_83,
	SYS_VOL_V0_86,
	SYS_VOL_V0_89,
	SYS_VOL_V0_92,
	
	SYS_VOL_V1_00=0,
	SYS_VOL_V1_20,
	SYS_VOL_V1_25,
	SYS_VOL_V1_30,

	SYS_VOL_V1_2=0, //0
	SYS_VOL_V1_3,   //1
	SYS_VOL_V1_4,   //2
	SYS_VOL_V1_5,   //3
	SYS_VOL_V1_6,   //4
	SYS_VOL_V1_7,   //5
	SYS_VOL_V1_8,   //6
	SYS_VOL_V1_9,  //7
	SYS_VOL_V2_0,  //8
	SYS_VOL_V2_1,  //9
	SYS_VOL_V2_2,  //10
	SYS_VOL_V2_3,  //11
	SYS_VOL_V2_4,  //12
	SYS_VOL_V2_5,  //13
	SYS_VOL_V2_6,  //14
	SYS_VOL_V2_7,  //15
	SYS_VOL_V2_8,  //16
	SYS_VOL_V2_86,  //17
	SYS_VOL_V2_9,   //18
	SYS_VOL_V2_96,  //19
	SYS_VOL_V3_0,   //20 (default)
	SYS_VOL_V3_06,   //21
	SYS_VOL_V3_1,    //22
	SYS_VOL_V3_16,    //23
	SYS_VOL_V3_2,    //24
	SYS_VOL_V3_26,    //25
	SYS_VOL_V3_3,    //26
	SYS_VOL_V3_36,    //27
	SYS_VOL_V3_4,    //28
	SYS_VOL_V3_46,    //29
	SYS_VOL_V3_5,    //30
	SYS_VOL_V3_56,    //31
	
	SYS_VOL_END
}SYS_LDO_E;
typedef enum
{
	SYS_RESET_JPGA		= (1 << 0),
	SYS_RESET_CSI 		= (1 << 1),
	SYS_RESET_JPGB		= (1 << 2), 
	SYS_RESET_ROTATE	= (1 << 3),
	SYS_RESET_UILZO	= (1 << 4),
}SYS_RESET_E;

typedef struct MCP0_LLP_TAB_S
{
	u32 next_llp_addr;  //BIT[31~29]MCP0_LLP_FLAG, BIT[28~0] next llp addr
	u32 src_addr;
	u32 dst_addr;
	u32 cpy_len;
}MCP0_LLP_TAB_T;
typedef enum{
	NEXT_LLP_READY	= (1 << 31),
	CUR_LLP_USED	= (1 << 30),
	CUR_LLP_VALID	= (1 << 29),
}MCP0_LLP_FLAG;
typedef struct ADKEY_RESISTANCE_TAB_S{
	u32  resistance;
	u16  keytype;
	u16  longkeytype;
	u32  longkeyTime;
}ADKEY_RESISTANCE_TAB_T;
typedef struct HARDWARE_SETUP_S
{
	u32  sys_clk;
	u32  pll_clk;
	u32  use_xosc;
	u32  sdram_clk;
	u16  sdram_clk_src;
	u16  sdram_size;
	u32  boot_spiflash_div;
	//uart
	u32 uart_baudrate;
	u32 dmaUart_baudrate;
	u8 	uart_tx_pos;
	u8 	uart_rx_pos;
	u8  dmaUart_tx_pos;
	u8  dmaUart_rx_pos;
	//LED: double use uart
	u8  led_en;
	u8  led_valid;
	u16 led_ch;
	u16 led_pin;
	u16 led_reserve16;
	//LCD
	u8  ui_rotate_soft;
	u8  ui_need_softrotate;
	u16 ui_reserve16;
	u8  lcd_pos;
	u8  lcd_video_rotate_mode;
	u8  lcd_ui_rotate_mode;
	u8  lcd_reserve_u8;
	u16 lcd_ratio_mode;
	u16 lcd_lzo_mode;

	u16 lcd_backlight_ch;
	u16 lcd_backlight_pin;
	u16 lcd_spi_cs_ch;
	u16 lcd_spi_cs_pin;
	u16 lcd_spi_clk_ch;
	u16 lcd_spi_clk_pin;
	u16 lcd_spi_data_ch;
	u16 lcd_spi_data_pin;
	u16 lcd_mcu_rs_ch;
	u16 lcd_mcu_rs_pin;	
	
	int lcd_driver_res;
	//sensor
	u8  cmos_sensor_en;
	u8  cmos_sensor_pos;
	u8  cmos_sensor_iic_pos;	//IIC0_POS_NONE: use soft ch
	u8  cmos_sensor_iic_soft_delay;	
	u16 cmos_sensor_iic_soft_scl_ch;
	u16 cmos_sensor_iic_soft_scl_pin;	
	u16 cmos_sensor_iic_soft_sda_ch;
	u16 cmos_sensor_iic_soft_sda_pin;
	u32 cmos_sensor_iic_baudrate;	//IIC0_POS_NONE: use soft ch

	u8  cmos_sensor_reset_ctrl_en;
	u8  cmos_sensor_pwdn_ctrl_en;
	u8  cmos_sensor_switch_en;
	u8  cmos_sensor_sel;
	u8  cmos_sensor1_reset_valid;
	u8  cmos_sensor1_pwdn_valid;
	u8  cmos_sensor2_reset_valid;
	u8  cmos_sensor2_pwdn_valid;	
	u16 cmos_sensor1_reset_ch;
	u16 cmos_sensor1_reset_pin;
	u16 cmos_sensor1_pwdn_ch;
	u16 cmos_sensor1_pwdn_pin;	
	u16 cmos_sensor2_reset_ch;
	u16 cmos_sensor2_reset_pin;
	u16 cmos_sensor2_pwdn_ch;
	u16 cmos_sensor2_pwdn_pin;	

	//AD KEY
	u8  adkey_en;
	u8  adkey_pos;
	u8  pwrkey_use_adc;
	u8  pwrkey_valid;	
	u16 adkey_ch;
	u16 adkey_pin;
	u16 pwrkey_ch;	 
	u16 pwrkey_pin;
	u16 adkey_num;
	u16 adkey_value_range;
	u32 adkey_use_inner_10k;		//enable adkey pin chip inner pullup 10k resistance
	u32 adkey_pullup_resistance;	//欧姆
	ADKEY_RESISTANCE_TAB_T  adkey_tab[10];
	
	//SDCARD
	u32 sdcard_timeout;
	u8  sdcard_en;
	u8  sdcard_pos;
	u8  sdcard_bus_width;	
	//ir led
	u8  ir_led_en;
	u16 ir_led_ch;
	u16 ir_led_pin;	
	//usb20 dev
	u8  usb_dev_en;
	//usb20 host or usb11 host
	u8  usb_host_en;
	u8  usb_host_double_bond;
	u8  usb_host_ch; //USB20_CH, USB11_CH, AUTO_CH
	u8  usb_host_pwr_io;	//IO1_CH_NONE: POWER常开， IO1_CH_PD1,IO1_CH_PA7：通过这两个IO口控制
	u8  usb_host_dect_ctrl;  //1: 需要硬件DECT,    0:  使用软件DECT
	u16 usb_host_dect_ch;
	u16 usb_host_dect_pin;
	
	//gsensor
	u8  gsensor_en;
	u8  gsensor_iic_pos;
	//spi1
	u32 spi1_baudrate;
	u8  spi1_pos;
	u8  spi1_busmode;
	u8  spi1_clk_idle;
	u8  spi1_sample_edge;
	//baterry
	u8  battery_en;
	u8  boot_uart_en;
	u8  boot_save_sdram;
	u8  lcd_first_drop;

	u8  sensor_filter_en;
	u8  sensor_filter_num;
	u8  sensor_filter_type;
	u16 sensor_ratio_cfg;

	// touchpanel
	u8  tp_en;
	u8  tp_reset_en;
	u8  tp_reset_valid;
	u8  tp_iic_delay;
	u16 tp_reset_ch;
	u16 tp_reset_pin;
	u16 tp_iic_scl_ch;
	u16 tp_iic_scl_pin;
	u16 tp_iic_sda_ch;
	u16 tp_iic_sda_pin;

	//led_pwm
	u32 led_pwm_en;
	u32 led_pwm_valid;
	u32 led_pwm_ch;
	u32 led_pwm_pin;
	u32 led_pwn_timer;
	u32 led_pwm_timer_ch;
	u32 led_on_off_ch;
	u32 led_on_off_pin;
	u32 led_on_off2_ch;
	u32 led_on_off2_pin;
}HARDWARE_SETUP_T;
extern HARDWARE_SETUP_T hardware_setup;
/*******************************************************************************
* Function Name  : hx330x_sysDelay
* Description    : system delay ms
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_sysCpuMsDelay(int dtime);
/*******************************************************************************
* Function Name  : hx330x_sysCpuNopDelay
* Description    : system delay nop
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_sysCpuNopDelay(int dtime);
/*******************************************************************************
* Function Name  : hx330x_word_memcpy
* Description    : word unit memcpy
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_word_memcpy(u32 *dst,u32 *src,u32 cnt);

/*******************************************************************************
* Function Name  : hx330x_halfword_memcpy
* Description    : word unit memcpy
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_halfword_memcpy(u16 *dst,u16 *src,u32 cnt);

/*******************************************************************************
* Function Name  : hx330x_halfword_memcpy
* Description    : word unit memcpy
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_bytes_memcpy(u8 *dst,u8 *src,u32 cnt);

/*******************************************************************************
* Function Name  : hx330x_halfword_memcpy
* Description    : word unit memcpy
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_bytes_memset(u8 *dst,u8 val,u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_halfword_memcpy
* Description    : word unit memcpy
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_word_memset(u32 *dst,u32 val,u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_bytes_cmp
* Description    : compare 
* Input          : none
* Output         : None
* Return         : 0 : same, !0: different 
*******************************************************************************/ 
int hx330x_bytes_cmp(u8 *dst,u8 *src,u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_mtsfr_memcpy
* Description    : memcpy to sfr
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_mtsfr_memcpy(u32 sfr_adr,u32* src,u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_mfsfr_memcpy
* Description    : memcpy from sfr
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_mfsfr_memcpy(u32* dst,u32 sfr_adr,u32 cnt);

/*******************************************************************************
* Function Name  : table_init_data
* Description    : table_init_data
* Input          : u32 *p : table_init_data addr
* Output         : None
* Return         : None
*******************************************************************************/
u32 * table_init_data ( u32 *p);

/*******************************************************************************
* Function Name  : table_init_sfr
* Description    : table_init_sfr
* Input          : u32 *p : table_init_sfr addr
* Output         : None
* Return         : None
*******************************************************************************/
u32 * table_init_sfr ( u32 *p);
/*******************************************************************************
* Function Name  : hx330x_sysDcacheInit
* Description    : dcache initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_sysDcacheInit(void) ;
/*******************************************************************************
* Function Name  : hx330x_sysIcacheInit
* Description    : icache initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_sysIcacheInit(void);
/*******************************************************************************
* Function Name  : hx330x_sysDcacheWback
* Description    : data cache write back
* Input          : u32 addr : address
*                  u32 size : size
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysDcacheWback(u32 addr, u32 size);
 /*******************************************************************************
* Function Name  : hx330x_sysDcacheFlush
* Description    : data cache flush
* Input          : u32 addr : address
*                  u32 size : size
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysDcacheFlush(u32 addr, u32 size) ;
 /*******************************************************************************
* Function Name  : hx330x_sysDcacheInvalid
* Description    : data cache Invalid
* Input          : u32 addr : address
*                  u32 size : size
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysDcacheInvalid(u32 addr, u32 size);
/*******************************************************************************
* Function Name  : hx330x_sysLDOSet
* Description    : set system ldo for power support
* Input          : u8 ldo : ldo type
*                  u8 sel : power sel
*                  u8 en  : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysLDOSet(u8 ldo,u8 sel,u8 en);
/*******************************************************************************
* Function Name  : hx330x_sysClkSet
* Description    : system clk set
* Input          : u8 clk_type : clk type.SYS_CLK_E
*                  u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysClkSet(u8 clk_type,u8 en);
/*******************************************************************************
* Function Name  : hx330x_sysReset
* Description    : system module reset
* Input          : u8 clk_type : clk type.SYS_RESET_E
*                  u8 en : 1-reset,0-normal
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysReset(u8 rst_type,u8 en);
/*******************************************************************************
* Function Name  : hx330x_mcpy0_sdram2gram
* Description    : copy data from src to dst, use in main loop
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy0_sdram2gram(void *dst, void *src, int cnt);
/*******************************************************************************
* Function Name  : hx330x_mcpy0_sdram2gram_nocache
* Description    : copy data from src to dst, use in main loop, no through data cache
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy0_sdram2gram_nocache(void *dst, void *src, int cnt);
/*******************************************************************************
* Function Name  : hx330x_mcpy1_sdram2gram
* Description    : copy data from src to dst, no through data cache
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy1_sdram2gram_nocache_waitdone(void);

/*******************************************************************************
* Function Name  : hx330x_mcpy1_sdram2gram
* Description    : copy data from src to dst, no through data cache
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy1_sdram2gram_nocache_kick(void *dst, void *src, int cnt);
/*******************************************************************************
* Function Name  : hx330x_mcpy1_sdram2gram
* Description    : copy data from src to dst
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy1_sdram2gram(void *dst, void *src, int cnt);
/*******************************************************************************
* Function Name  : hx330x_mcpy1_sdram2gram
* Description    : copy data from src to dst, no through data cache
* Input          : *dst: gram or sdram addr
*                  *src: gram or sdram addr
*                  cnt :length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy1_sdram2gram_nocache(void *dst, void *src, int cnt);
/*******************************************************************************
* Function Name  : hx330x_mcpy0_llp
* Description    : mcp0 llp cpy 
* Input          : void *llp_start: llp tab addr
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mcpy0_llp(void *llp_start);
/*******************************************************************************
* Function Name  : hx330x_sysInit
* Description    : initial system for platform using.
* Input          : u32 *saddr : sdram free start address 
                      u32 *eaddr : sdram free end address
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysInit(u32 *saddr,u32 *eaddr);
/*******************************************************************************
* Function Name  : hx330x_sysUninit
* Description    : uninitial system .
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_sysUninit(void);

#define memcpy(dst, src, cnt)			hx330x_bytes_memcpy((u8*)(dst),(u8*)(src), cnt)
#define memset(dst, val, cnt)			hx330x_bytes_memset((u8*)(dst), (u8)(val), cnt)

#endif

