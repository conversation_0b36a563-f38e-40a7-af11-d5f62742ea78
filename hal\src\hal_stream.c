/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"

/*******************************************************************************
* Function Name  : hal_streamInit
* Description    : hal layer,stream management.for stream media.stream initial
* Input          : Stream_Head_T *head : stream head for operation handler
				   Stream_Node_T *pool : stream node
				   u32 num             : number of stream nodes
				   u32 mem             : start memory
				   u32 mem_len         : memory length
* Output         : None
* Return         : true: success
* 				   false: fail
*******************************************************************************/
bool hal_streamInit(Stream_Head_T *head,Stream_Node_T *pool,u32 num,u32 mem,u32 mem_len)
{
	int i;
	if((head==NULL)||(pool==NULL)||(mem == 0))
		return false;
	memset((u8*)pool, 0, sizeof(pool)*num);
	for(i=0;i<num;i++)
	{
		pool[i].sta 		= 0;
		pool[i].pbuf 		= NULL;
		pool[i].buflen 		=  0;
		pool[i].tsync 		=  0;
		pool[i].tsync_next 	=  0;
		pool[i].last = (i == 0)?&pool[num-1] : &pool[i-1];
		pool[i].next = ((i + 1) == num)?&pool[0] : &pool[i+1];
	}
	head->p_qdt = head->g_qdt = &pool[0];
	
	head->c_inf.semf      = 0;
	head->c_inf.pcache    = (u8 *)mem;
	head->c_inf.cachelen  = mem_len;
	head->c_inf.sf        = (u8 *)mem;
	head->c_inf.ef        = (u8 *)(mem+mem_len);
	
	head->head_size=0;
	return true;
}
/*******************************************************************************
* Function Name  : hal_srreamMalloc
* Description    : hal layer,stream management.for stream media.stream malloc a memory for write
* Input          : Stream_Head_T *head : stream head for operation handler
				   u32 mlen : need memory length
* Output         : None
* Return         : mem
*******************************************************************************/
SDRAM_TEXT_SECTION
u32 hal_streamMalloc(Stream_Head_T *head, u32 mlen)
{
	if(mlen == 0 || head->p_qdt->sta == 1)
	{
		return 0;
	}
	u32 malloc_buf = 0;
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	Stream_Node_T *p = head->p_qdt->last;
	Stream_Node_T *g = head->g_qdt;
	
	u32 sf = (u32)head->c_inf.sf;
	u32 ef = (u32)head->c_inf.ef;
	if((p->pbuf == NULL) && (g->pbuf == NULL))
	{
		//first macllo
		malloc_buf = (u32)sf;
	}else
	{
		//not first macllo
		u32 pt = (u32)(p ->pbuf + p ->buflen);
		u32 gt = (u32)(g ->pbuf);
		if((pt > gt) || (p ->buflen == 0 && pt == gt))  //in buf > get buf
		{
			if(ef >= (pt + mlen)) //in buf 后面的buf足够
			{
				malloc_buf = (u32)pt;
			}
			else 
			{
				if(gt >= (sf + mlen))
				{
					malloc_buf = (u32)sf;
				}
			}
		}else if((pt+mlen) <= gt)
		{
			malloc_buf =  (u32)pt;
		}
	}
	if(malloc_buf == 0)
	{
		//deg_Printf("NULL sf:%x,ef:%x,pt:%x,gt:%x,buflen:%x\n",sf,ef,(u32)(p ->pbuf),(u32)(head ->g_qdt->pbuf),p ->buflen);
		HAL_CRITICAL_EXIT() ;
		return 0;
	}
	head->p_qdt->sta 	= 0;
	head->p_qdt->pbuf 	= (u8*)malloc_buf;
	head->p_qdt->buflen = mlen;	
	head->p_qdt 		= head->p_qdt->next;//指向下一个buf
	head->c_inf.semf++;
//	if(head->c_inf.semf > 2) //adc pre malloc
//		debg("+sem:%x\n",head-> c_inf.semf);

	HAL_CRITICAL_EXIT() ;
	return (u32)malloc_buf;	
}
/*******************************************************************************
* Function Name  : hal_streamMallocDrop
* Description    : hal layer,when hal_streamMalloc, use last malloc buff
* Input          : Stream_Head_T *head : stream head for operation handler
				   u32 mlen : need memory length
* Output         : None
* Return         : mem
*******************************************************************************/
void hal_streamMallocDrop(Stream_Head_T *head, u32 mlen)
{
	u8 i;
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	
	Stream_Node_T *pt = head->p_qdt;
	for(i = 0; i <= head->c_inf.semf; i++)
		pt = pt->last;
	if(pt->sta == 1)
	{
		head->head_size -=pt->buflen;
		pt->sta 		= 0;
		pt->buflen 		= mlen;	
		pt->tsync		= 0;
		pt->tsync_next 	= 0;
		head->c_inf.semf++;
	}
	HAL_CRITICAL_EXIT() ;
}
/*******************************************************************************
* Function Name  : hal_streamIn
* Description    : hal layer,stream management.for stream media.stream queue in
* Input          : Stream_Head_T *head : stream head for operation handler
				   u32 p : memory address
				   u32 len: memory length
				   u32 t_sync: current time stamp
				   u32 t_sync_next: next time stamp
* Output         : None
* Return         : 0:success
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_streamIn(Stream_Head_T *head, u32 len,u32 t_sync,u32 t_sync_next)
{
	if(head->c_inf.semf == 0)
	{
		debg("cache_put err\n");//while(1);
		return false;
	}
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();	
	Stream_Node_T *pt = head->p_qdt;
	
	u8 i;
	for(i = 0; i < head->c_inf.semf; i++)
		pt = pt->last;
	//sem--
	head->c_inf.semf--;
	//debg("-sem:%x\n",head-> c_inf.semf);

	if((pt ->pbuf != NULL) /*&& (pt->buflen != 0)*/ && (pt->sta == 0))
	{

		pt->sta 		= 1;
		pt->buflen 		= len;	
		pt->tsync		= t_sync;
		pt->tsync_next 	= t_sync_next;
		head->head_size +=len;
		HAL_CRITICAL_EXIT() ;
		return true;
	}
	else
	{
		HAL_CRITICAL_EXIT() ;
		debg("qdt: <list full>\n");exception_trigger();while(1);
		return false;	
	}

}
/*******************************************************************************
* Function Name  : hal_streamOut
* Description    : hal layer,stream management.for stream media.stream queue out
* Input          : Stream_Head_T *head : stream head for operation handler
				u32 *p : memory address
				u32 *len: memory length
* Output         : None
* Return         : true:success
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_streamOut(Stream_Head_T *head, u32 *p, u32 *len,u32 *sync, u32 *sync_next)
{
	Stream_Node_T *gt = head->g_qdt;
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	if((gt->pbuf != NULL) /*&& (gt->buflen != 0)*/&& (gt->sta == 1))
	{
		if(p){
		    *p = (u32)gt->pbuf;	
		}
		if(len){
		    *len = gt->buflen;
		}
		if(sync){
		    *sync = gt->tsync;
		}
		if (sync_next){
			*sync_next = gt->tsync_next;
		}
		//gt->sta = 0;
		//gt->pbuf = NULL;
		//gt->buflen = 0;
		//head->g_qdt = gt->next;
		HAL_CRITICAL_EXIT() ;
		return true;
	}
	else
	{
		if(p){
		    *p = (u32)NULL;	
		}
		if(len){
		    *len = 0;
		}
		if(sync){
		    *sync = 0;
		}
		if (sync_next){
			*sync_next = 0;
		}
		HAL_CRITICAL_EXIT() ;
		return false;	
	}
}
/*******************************************************************************
* Function Name  : hal_streamOutNext
* Description    : hal layer,stream management.for stream media.stream queue out
* Input          : Stream_Head_T *head : stream head for operation handler
				u32 *p : memory address
				u32 *len: memory length
* Output         : None
* Return         : true:success
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_streamOutNext(Stream_Head_T *head, u32 *p, u32 *len,u32 *sync, u32 *sync_next)
{
	Stream_Node_T *gt = head->g_qdt->next;
	
	if((gt->pbuf != NULL) /*&& (gt->buflen != 0)*/&& (gt->sta == 1))
	{
		if(p){
		    *p = (u32)gt->pbuf;	
		}
		if(len){
		    *len = gt->buflen;
		}
		if(sync){
		    *sync = gt->tsync;
		}
		if (sync_next){
			*sync_next = gt->tsync_next;
		}
		//gt->sta = 0;
		//gt->pbuf = NULL;
		//gt->buflen = 0;
		//head->g_qdt = gt->next;
		return true;
	}
	else
	{
		if(p){
		    *p = (u32)NULL;	
		}
		if(len){
		    *len = 0;
		}
		if(sync){
		    *sync = 0;
		}
		if (sync_next){
			*sync_next = 0;
		}
		return false;	
	}
}
/*******************************************************************************
* Function Name  : hal_streamfree
* Description    : hal layer,stream management.for stream media.stream queue free
* Input          : Stream_Head_T *head : stream head for operation handler
* Output         : None
* Return         : true:success
*******************************************************************************/
SDRAM_TEXT_SECTION
bool hal_streamfree(Stream_Head_T *head)
{
	if(head && (head->g_qdt->pbuf != NULL) /*&& (head->g_qdt->buflen != 0)*/&& (head->g_qdt->sta == 1)){
		//*p = gt ->pbuf;	
		//*len = gt->buflen;
		HAL_CRITICAL_INIT();
		HAL_CRITICAL_ENTER();
		head->head_size -= head->g_qdt->buflen;
		head->g_qdt->sta = 0;
		head->g_qdt->pbuf = NULL;
		head->g_qdt->buflen = 0;
		head->g_qdt = head->g_qdt->next;
		HAL_CRITICAL_EXIT() ;
		
		return true;
		
	}else{
		//debg("N");
		return false;	
	}
}

/*******************************************************************************
* Function Name  : hal_stream_size
* Description    : hal layer,stream management.for stream media.stream queue total size
* Input          : Stream_Head_T *head : stream head for operation handler
* Output         : None
* Return         : u32 : stream in size
*******************************************************************************/
u32 hal_stream_size(Stream_Head_T *head)
{
	return head->head_size;
}





