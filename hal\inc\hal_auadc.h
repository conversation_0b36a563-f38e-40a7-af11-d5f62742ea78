/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_AUADC_H
    #define  HAL_AUADC_H

#define PCM_REC_TYPE_AVI      		0
#define PCM_REC_TYPE_WAV      		1
#define PCM_REC_TYPE_UAC      		2

#define PCM_REC_SYNC				4


/*******************************************************************************
* Function Name  : hal_auadc_stamp_out
* Description    : cal frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: current frame time stamp
*******************************************************************************/
u32 hal_auadc_stamp_out(void);
/*******************************************************************************
* Function Name  : hal_auadc_stamp_next
* Description    : cal next frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: next frame time stamp
*******************************************************************************/
u32 hal_auadc_stamp_next(void);
/*******************************************************************************
* Function Name  : hal_auadc_stamp_next
* Description    : cal next frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: next frame time stamp
*******************************************************************************/
void hal_auadcInit(void);

/*******************************************************************************
* Function Name  : hal_auadcMemInit
* Description    : hal layer.adadc memory initial
* Input          : 
* Output         : None
* Return         : int
*******************************************************************************/
bool hal_auadcMemInit(void);
bool hal_auadcMemInitB(void);
/*******************************************************************************
* Function Name  : hal_auadc_pcmsize_get
* Description    : hal layer.adadc memory buf size get
* Input          : 
* Output         : None
* Return         : int
*******************************************************************************/
u32 hal_auadc_pcmsize_get(void);

/*******************************************************************************
* Function Name  : hal_auadcMemUninit
* Description    : hal layer.adadc memory uninitial
* Input          : 
* Output         : None
* Return         : NONE
*******************************************************************************/
void hal_auadcMemUninit(void);
void hal_auadcMemUninitB(void);
/*******************************************************************************
* Function Name  : hal_auadc_cnt
* Description    : hal_auadc_cnt
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadc_cnt(void);

/*******************************************************************************
* Function Name  : hal_auadcmutebuf_get
* Description    : hal_auadcmutebuf_get
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_auadcmutebuf_get(void);
/*******************************************************************************
* Function Name  : hal_auadcStart
* Description    : hal layer.auadc start 
* Input          : u32 type: PCM_REC_TYPE_AVI, PCM_REC_TYPE_WAV, PCM_REC_TYPE_UAC
* 				   u32 frq: 8000, 16000,24000,32000
* 				   u32 volume: 0~MIC_VOLUME_MAX
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_auadcStart(u32 type, u32 frq,u32 volume);
bool hal_auadcStartB(u32 type, u32 frq,u32 volume);
/*******************************************************************************
* Function Name  : hal_auadcBufferGet
* Description    : hal layer.get pcm raw data addr & length
* Input          : u32 *len : buffer length
*                  u32 *syncnt: current frame sync stamp
				   u32 *syncnt_next: next frame sync stamp
* Output         : None
* Return         : void* : return buffer addr
*******************************************************************************/
void *hal_auadcBufferGet(u32 *len,u32 *syncnt, u32 *syncnt_next);

/*******************************************************************************
* Function Name  : hal_auadcBufferRelease
* Description    : hal layer.set pcm raw data addr & length
* Input          : void *buffer : buffer addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadcBufferRelease(void);

/*******************************************************************************
* Function Name  : hal_auadcBufferGetB
* Description    : hal layer.get pcm raw data addr & length
* Input          : u32 *len : buffer length
*                  u32 *syncnt: current frame sync stamp
				   u32 *syncnt_next: next frame sync stamp
* Output         : None
* Return         : void* : return buffer addr
*******************************************************************************/
void *hal_auadcBufferGetB(u32 *len,u32 *syncnt, u32 *syncnt_next);

/*******************************************************************************
* Function Name  : hal_auadcBufferReleaseB
* Description    : hal layer.set pcm raw data addr & length
* Input          : void *buffer : buffer addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadcBufferReleaseB(void);

/*******************************************************************************
* Function Name  : hal_auadcStop
* Description    : hal layer.auadc stop
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_auadcStop(void);

/*******************************************************************************
* Function Name  : hal_adcBuffer_prefull
* Description    : hal layer.auadc stop
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_adcBuffer_prefull(void);
bool hal_adcBufferB_prefull(void);


void hal_adc_volume_set(int volume);

void hal_adc_volume_setB(int volume);






#endif

