/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
/*******************************************************************************
* Function Name  : taskSdUpdateDrawStartAddrCal
* Description    : taskSdUpdateDrawStartAddrCal
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
SDRAM_TEXT_SECTION
uiColor* taskSdUpdateDrawStartAddrCal(s16 sx, s16 sy)
{
	if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_0)
	{
		return sd_update_op.uiDrawBuff + (s32)sy*sd_update_op.ui_w + sx;
	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_90)
	{
		return sd_update_op.uiDrawBuff + (sd_update_op.ui_w-sx-1)*sd_update_op.ui_h + sy;
		//return sd_update_op.uiDrawBuff + sx*sd_update_op.ui_h + (sd_update_op.ui_h-sy-1);	

	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_180)
	{
		return sd_update_op.uiDrawBuff + (sd_update_op.ui_h-sy-1)*sd_update_op.ui_w + sd_update_op.ui_w -sx-1;
	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_270)
	{
		return sd_update_op.uiDrawBuff + sx*sd_update_op.ui_h + (sd_update_op.ui_h-sy-1);
		//return sd_update_op.uiDrawBuff + (sd_update_op.ui_w-sx-1)*sd_update_op.ui_h + sy;
	}else
	{
		return NULL;
	}
}
/*******************************************************************************
* Function Name  : taskSdUpdateDrawAddrReCal
* Description    : taskSdUpdateDrawAddrReCal
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
SDRAM_TEXT_SECTION
uiColor* taskSdUpdateDrawAddrReCal(u8* baseAddr,s16 x, s16 y)
{
	if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_0)
	{
		return baseAddr + y*sd_update_op.ui_w + x;
	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_90)
	{
		return baseAddr -x*sd_update_op.ui_h + y;
		//return baseAddr + x*sd_update_op.ui_h - y;	
	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_180)
	{
		return baseAddr - y*sd_update_op.ui_w - x;
	}else if(sd_update_op.ui_scan_mode == LCD_DISPLAY_ROTATE_270)
	{
		return baseAddr + x*sd_update_op.ui_h - y;	
		//return baseAddr -x*sd_update_op.ui_h + y;
	}else
	{
		return NULL;
	}
}
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
SDRAM_TEXT_SECTION
static void taskSdUpdateDrawRect(uiRect* rect,uiColor color)
{
	s16 x,y;
	uiColor* dest;
	uiColor* destStart;
	destStart = taskSdUpdateDrawStartAddrCal(rect->x0, rect->y0);
	if(destStart == NULL)
		return;	
	for(y = rect->y0; y <= rect->y1; y++)
	{
		hal_wdtClear();
		dest = destStart;
		for(x = rect->x0; x <= rect->x1; x++)
		{
			*dest = color;
			dest = taskSdUpdateDrawAddrReCal(dest,1, 0);		
		}
		destStart = taskSdUpdateDrawAddrReCal(destStart, 0, 1);
	}
}
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
SDRAM_TEXT_SECTION
static void taskSdUpdateDrawProgressBar(u32 rate)
{
	uiProgressBarObj* pprogressBar = (uiProgressBarObj*)uiHandleToPtr(sd_update_op.progressBar);
	uiWinObj* pWin = &(pprogressBar->widget.win);
	u32 length;
	uiRect rect;
	pprogressBar->rate = rate;
	length = pWin->rect.x1-pWin->rect.x0+1;
	length = pprogressBar->rate*length/100;
	if(length==0)
		taskSdUpdateDrawRect(&(pWin->rect),pWin->bgColor);
	else if(pprogressBar->rate == 100)
		taskSdUpdateDrawRect(&(pWin->rect),pprogressBar->color);
	else
	{
		rect.y0 = pWin->rect.y0;
		rect.y1 = pWin->rect.y1;
		rect.x0 = pWin->rect.x0;
		rect.x1 = rect.x0+length-1;
		taskSdUpdateDrawRect(&rect,pprogressBar->color);
		rect.x0 = pWin->rect.x0+length-1;
		rect.x1 = rect.x1;
		taskSdUpdateDrawRect(&rect,pWin->bgColor);
	}
}

/*******************************************************************************
* Function Name  : taskSdUpdate_uiProgress
* Description    : taskSdUpdate_uiProgress
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
SDRAM_TEXT_SECTION
void taskSdUpdate_uiProgress(u32 progress)
{
	taskSdUpdateDrawProgressBar(progress);
	hx330x_sysDcacheWback((u32)sd_update_op.uiDrawBuff, sd_update_op.uiDrawSize);
	hx330x_lcdShowWaitDone();
	hx330x_lcdShowKick();
	//for te interrupt,kick send
	if(LCD_IF_IS_MCU())	
    	hx330x_lcdKick();
}
/*******************************************************************************
* Function Name  : taskSdUpdate_uiProgress
* Description    : taskSdUpdate_uiProgress
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
void taskSdUpdate_uiInit(void)
{
	//deg_Printf("taskSdUpdate_uiInit\n");
/*	hx330x_deWait();
	uiWinSetfgColor(uiRectCreateDirect(Rx(0),Ry(0),Rw(320),Rh(240),INVALID_HANDLE,WIN_ABS_POS,0), R_ID_PALETTE_Gray);
	sd_update_op.progressBar = uiProgressBarCreateDirect(Rx(70),Ry(117),Rw(180),Rh(6),INVALID_HANDLE,WIN_ABS_POS,0,ALIGNMENT_LEFT );
	uiWinSetfgColor(sd_update_op.progressBar, R_ID_PALETTE_Blue);
	uiWinSetbgColor(sd_update_op.progressBar, R_ID_PALETTE_DarkGray);
	winHandle strHandle = uiStringIconCreateDirect(Rx(70),Ry(125),Rw(180),Rh(35),INVALID_HANDLE,WIN_ABS_POS,0);
	uiWinSetStrInfor(strHandle, RES_FONT_NUM_DEFAULT, ALIGNMENT_CENTER,R_ID_PALETTE_Blue);
	uiWinSetResid(strHandle,R_ID_STR_UPDATE_START);
	app_draw_Service(0);*/
	
	uiWinSetfgColor(uiRectCreateDirect(Rx(0),Ry(0),Rw(320),Rh(240),INVALID_HANDLE,WIN_ABS_POS,0), R_ID_PALETTE_Gray);
	sd_update_op.progressBar = uiProgressBarCreateDirect(Rx(70),Ry(117),Rw(180),Rh(6),INVALID_HANDLE,WIN_ABS_POS,0, ALIGNMENT_LEFT);
	uiWinSetfgColor(sd_update_op.progressBar, R_ID_PALETTE_Blue);
	uiWinSetbgColor(sd_update_op.progressBar, R_ID_PALETTE_DarkGray);
	//sd_update_op.strHandle = uiStringIconCreateDirect(Rx(70),Ry(125),Rw(180),Rh(35),INVALID_HANDLE,WIN_ABS_POS,0);
	//uiWinSetStrInfor(sd_update_op.strHandle, RES_FONT_NUM_DEFAULT, ALIGNMENT_CENTER,R_ID_PALETTE_Blue);
	//uiWinSetResid(sd_update_op.strHandle,R_ID_STR_UPDATE_START);
	
	hx330x_bytes_memset(sd_update_op.uiDrawBuff,R_ID_PALETTE_Gray,sd_update_op.uiDrawSize);
	hx330x_sysDcacheWback((u32)sd_update_op.uiDrawBuff, sd_update_op.uiDrawSize);
	hx330x_lcdUiSetAddr(UI_LAYER0,(u32)sd_update_op.uiDrawBuff, sd_update_op.uiDrawSize);
	hx330x_lcdShowKick();
	
}


