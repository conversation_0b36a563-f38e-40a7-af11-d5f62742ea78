/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef USB_HOST_API_H_
#define USB_HOST_API_H_

#include "husb_enum.h"
#include "husb_tpbulk.h"
#include "husb_uvc.h"
#include "husb_usensor.h"


#define HUSB_CH_NUM								1 //2：双USB方案， 1：USB20和USB11只选择一个
			
#define HUSB20_RXEP_UVC        					USB20_EP1 //HOST OUT 端点，HOST 接收
#define HUSB20_RXEP_MSC							USB20_EP2 //HOST OUT 端点，HOST 接收
#define HUSB20_TXEP_MSC							USB20_EP2 //HOST IN 端点，HOST 发送
#define HUSB20_RXEP_UAC        					USB20_EP3 //HOST OUT 端点，HOST 接收
			
#define HUSB11_RXEP_UVC        					USB11_EP3 //HOST OUT 端点，HOST 接收
#define HUSB11_RXEP_MSC							USB11_EP1 //HOST OUT 端点，HOST 接收
#define HUSB11_TXEP_MSC							USB11_EP1 //HOST IN 端点，HOST 发送
#define HUSB11_RXEP_UAC        					USB11_EP2 //HOST OUT 端点，HOST 接收
			
			
#define UVC_FORMAT_YUV							VS_FORMAT_UNCOMPRESSED
#define UVC_FORMAT_MJP							VS_FORMAT_MJPEG

#define UVC_FRAME_1080P							((1080<<16)|(1980<<0))	
#define UVC_FRAME_720P							((720<<16)|(1280<<0))
#define UVC_FRAME_VGA							((480<<16)|(640<<0))
#define UVC_FRAME_QVGA							((240<<16)|(320<<0))
			


#define UVC_YUVFRAME_DEFAULT					UVC_FRAME_VGA
#define UVC_MJPFRAME_DEFAULT					UVC_FRAME_VGA

#define UVC_FORMAT_DEFAULT						UVC_FORMAT_MJP			
#define UVC_FORMAT_FIX_MJP						1
			
#define HOST_HUB_SUPPORT		 				0
#define __DEV_ADDR__			 				4
#define __USB_HUB_ADDR_			 				5
#define __INTFS_STACK__			 				16

#define USENSOR_FIRSTCONNECT_DROP_FRAME			5  //USENSOR初次连接时丢帧数量(有些USENSOR前几帧有问题)

#define UVC_CACHE_NUM							3

#define _UVC_JPG_CACHE_SIZE						(50*1024L)

typedef enum
{
	DEVICE_TYPE_NONE	= (0<<0), 
	DEVICE_TYPE_MSC     = (1<<0),
	DEVICE_TYPE_UVC 	= (1<<1),
	DEVICE_TYPE_UAC 	= (1<<2),
	DEVICE_TYPE_HID    	= (1<<3),
		
}DEVICE_TYPE;
typedef enum
{
	USB_NONE			= (0<<0),
	USB_INIT      		= (1<<0), //检测到USB插入状态
	USB_INSERT      	= (1<<0), //检测到USB插入状态
	USB_MSC_ATECH 		= (1<<1), //USB正确枚举完成，UVC或MSC配置完成
	USB_MSC_TRAN 		= (1<<2), //USB UVC或MSC数据传输阶段
	USB_UVC_ATECH 		= (1<<3), //USB正确枚举完成，UVC或MSC配置完成
	USB_UVC_TRAN 		= (1<<4), //USB UVC或MSC数据传输阶段
	USB_DTECH    		= (1<<5), //USB强制REMOVE状态
	USB_ASTERN  		= (1<<6), //USB进入倒车命令状态
	USB_ASTERN_CK_LOCK	= (1<<7), //USB进入倒车命令检查
}DEVICE_STA;


enum{
	USBNONE_CH	= 0,
	USB20_CH   	= 1,
	USB11_CH 	= 2,
	USBAUTO_CH		= 3,
}USB_CH;

typedef enum 
{
	PID_SETUP_PHASE = 0,	
	PID_KICK_PHASE,
	PID_IN_PHASE,
	PID_OUT_PHASE,
	SETUP_END_PHASE,
}PID_PHASE;

enum{
	PID_PHASE_NEXT  = 0,
	ENUM_PHASE_NEXT = 1,
	ENUM_FAIL       = 2,
	ENUM_END		= 3,
};

typedef struct
{
    u8  mrequest;
    u8  request;
    u16 value;
    u16 index;
    u16 length;
} SETUP_PKG, *P_SETUP_PKG;
typedef union
{
    SETUP_PKG setup;
    u8 set_pkt[8];
} SETUPPKT;
// Standard Device Descriptor
typedef struct
{
    u8  length;
    u8  descriptor_type;
    u16 bcd_usb;
    u8  device_class;
    u8  device_subclass;
    u8  device_protocol;
    u8  maxpacket_size0;
    u16 id_vendor;
    u16 id_product;
    u16 bcd_device;
    u8  manufacturer;
    u8  product;
    u8  serial_number;
    u8  num_configurations;
} __attribute__((packed)) SDEV_DESC, *P_DEV_DESC;
// Standard Configuration Descriptor
typedef struct
{
    u8  length;                // Size of descriptor in u8
    u8  type;                  // Configuration
    u16 t_length;              // Total length
    u8  num_intf;              // Number of interface
    u8  cv;                    // bConfigurationValue
    u8  index;                 // iConfiguration
    u8  attr;                  // Configuration Characteristic
    u8  max_power;             // Power config
} SCFG_DESC, *P_SCFG_DESC;

// Standard Interface Descriptor
typedef struct
{
    u8 length;
    u8 type;
    u8 num;
    u8 alt_tring;
    u8 end_points;
    u8 iclass;
    u8 sub;
    u8 proto;
    u8 index;
} SINTF_DESC, *P_INTF_DESC;

// Standard EndPoint Descriptor
typedef struct
{
    u8  length;
    u8  type;
    u8  ep_addr;
    u8  attr;
    u16 pay_load;               // low-speed this must be 0x08
    u8  interval;
} SEP_DESC, *P_EP_DESC;
// Standard HID  Descriptor
typedef struct
{
  	u8 	length;
	u8 	type;
	u16	bcdHID;
	u8 	countryCode;
	u8	num;
	u8  subtype;
	u16 sublength;
	u8  subtype_1;
	u16 sublength_1;
}SHID_DESC, *P_HID_DESC;
// msc desc struct (not use)
typedef struct
{
	u8 ctyp;

	u8 intfs;
	u8 altset;
	u8 endpts;
	
	u8 epin;
	u8 attrin;
	u16 inpload;
	
	u8 epout;
	u8 attrout;
	u16 outpload;

}MSCDEV;
//uvc desc struct
typedef struct
{
	u8 ctyp;
	
	u8 ctl_intfs;
	u8 ctl_altset;
	u8 ctl_ep;
	u8 ctl_attr;
	u16 ctl_pload;
	u8 ctl_interval;
		
	u8 strm_intfs;
	u8 strm_altset;
	u8 strm_ep;
	u8 strm_attr;
	u16 strm_pload;
	u8 strm_interval;
}__attribute__((packed)) UVCDEV;
 //HID desc struct
typedef struct
{
	u8 enable; //是否有该interface
  	u8 ctyp;
	u8 intfs;
	u8 altset;
	u8 endpts;

	u8 epin;
	u8 attrin;
	u16 inpload;
	
//	u8 epout;
//	u8 attrout;
//	u16 outpload;

	u8 report_type[2];
	u16 report_length[2];

}__attribute__((packed)) HIDDEV;

typedef struct{
	u8	bLength;                           
	u8	bDescriptorType;                   
	u8	bDescriptorSubtype;                
	u8	bFrameIndex;                   
	u8	bmCapabilities;                   
	u16	wWidth;                           
	u16	wHeight;      
	u32 wMinBitRate;
	u32 wMaxBitRate;
	u32 wMaxVideoFrameBufSzie;
	u32 wDefaultFrameInterval;
	u8	bFrameIntervalType;
	u32 wMaxFrameInterval;
	u32 wMinFrameInterval;
}__attribute__((packed)) VS_FRAME;
typedef struct{
	u8	bLength;                           
	u8	bDescriptorType;                   
	u8	bDescriptorSubtype;                
	u8	bFormatsIndex;   
	u8 	bNumFrameDescriptors;
	u8 	bDefaultFrameIndex;
}__attribute__((packed)) VS_FORMAT;
typedef struct{
	u8	bLength;                           
	u8	bDescriptorType;                   
	u8	bDescriptorSubtype;                
	u8	bNumFormats;                     
}__attribute__((packed)) VS_HEAD;
typedef struct
{
	SINTF_DESC intfs_desc;
	SEP_DESC   endpoint_desc[4];
}SINTFEP_DESC,*P_INTFEP_DESC;
typedef struct{
	VS_FORMAT  vs_format_s;
	VS_FRAME   vs_frame_s[16];
}VS_FORMAT_FRAME;

typedef struct{
	u8  format_num;  //如果 = 0，说明第一次选择
	u8  frame_num;
	u8  frame_max;
	u8	type;
	u16 width;
	u16 height;
	u32 wMaxVideoFrameBufSzie;
	u32 wDefaultFrameInterval;
}USENSOR_PROBE_RES;
typedef struct UVC_CACHE_S
{
	u32 sta; 
	u8* buff;	
	u32 size;
	u32 max_size;
	struct UVC_CACHE_S *next;
}UVC_CACHE;
typedef struct
{
	u8 *jbuf;
	u32 jlen;
	u32 usta;
	u32 utgl;
	u32 jUcnt;
	u32 notran_cnt;
}UVC_FSTACK;

typedef struct
{
	SETUP_PKG   setup;
	bool (*probe)(void *handle);
	u8 (*pack)(void *handle);
}HUSB_HCDTRB;
typedef struct
{
	HUSB_HCDTRB *phcdtrb;
	u8* dsc_buf;
	
	u8  ch;
	u8 	device_type;
	u8  device_sta;
	u8  pid_dir;
	u8  enum_phase;
	u8  pid_phase;	
	u16 inlen;
	u32 infs_num;
	u8 	hub_ports;
	u8 	hub_portstas; 
	//msc 
	u8  max_lun;
    u8  cur_lun;
	u32 msc_online;
	u32 msc_cap;
	u32 msc_slen;	
	//u8  scsi_buf[64];
	bool (*udisk_rd)(void *handle,u8 *pDataBuf, u32 dwLBA, u16 dwLBANum);
	bool (*udisk_wr)(void *handle,u8 *pDataBuf, u32 dwLBA, u16 dwLBANum);
	u32 (*udisk_cap)(void *handle);
	bool (*udisk_online)(void *handle);
	//uvc 
	u32  uvc_drop_frame;   //有些usensor连接后的前几帧会有问题
	USENSOR_PROBE_RES  usensor_res;
	u32  uvc_fifo_tgl;
	//uvc buf
	UVC_CACHE uvc_cache[UVC_CACHE_NUM];
	UVC_CACHE *uvc_fill_cache;
	UVC_CACHE *uvc_dcd_cache;
	UVC_CACHE *uvc_bak_cache;
	UVC_FSTACK uvc_fstack;
	volatile s32 uvc_jpgdcsta;
	bool (*usensor_atech_link)(void *handle);
	
} __attribute__((packed)) USB_STA;

typedef struct
{
	SETUP_PKG	setup;
	u8  		tempbuf[2048];
	u8          backstabuf[4];
	USB_STA     usbsta;
	SDEV_DESC	dev;
	SCFG_DESC	cfg;
	MSCDEV		msc;
	UVCDEV		uvc;
	SINTFEP_DESC infsep_dsc[16];
	VS_HEAD		vs_head_c;
	VS_FORMAT_FRAME vs_format_c[4]; //只支持YUV和MJP两种FORMAT,原本只需要2，这里预留到4
} HUSB_HANDLE;

//extern HUSB_HANDLE  husb_ctl[];
extern  const HUSB_HCDTRB husb_enum_hcdtrb[];
extern  const HUSB_HCDTRB husb_uvc_switch_hcdtrb[];
extern  const HUSB_HCDTRB husb_astern_hcdtrb[];
/*******************************************************************************
* Function Name  : husb_api_handle_get
* Description    : husb_api_handle_get
* Input          : None
* Output         : None
* Return         : HUSB_HANDLE *
*******************************************************************************/
HUSB_HANDLE* husb_api_handle_get(u8 ch);
/*******************************************************************************
* Function Name  : husb_api_u20_remove
* Description    : husb_api_u20_remove
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_u20_remove(void);
/*******************************************************************************
* Function Name  : husb_api_u11_remove
* Description    : husb_api_u11_remove
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_u11_remove(void);
/*******************************************************************************
* Function Name  : husb_api_init
* Description    : husb_api_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_init(u8 usb_ch);
/*******************************************************************************
* Function Name  : husb_api_msc_try_tran
* Description    : husb_api_msc_try_tran
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 husb_api_devicesta(u8 ch);
/*******************************************************************************
* Function Name  : husb_api_msc_try_tran
* Description    : husb_api_msc_try_tran
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_msc_try_tran(u8 ch);

/*******************************************************************************
* Function Name  : dev_husb_init
* Description    : dev_husb_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_husb_init(void);
/*******************************************************************************
* Function Name  : dev_husb_ioctrl
* Description    : dev_husb_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_husb_ioctrl(INT32U op,INT32U para);


#endif /* USB_HOST_API_H_ */

