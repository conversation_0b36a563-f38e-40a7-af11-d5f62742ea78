{"version": "0.2.0", "configurations": [{"name": "Debug HX330x", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/app/bin/Debug/hx330x_sdk.elf", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "miDebuggerPath": "E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin/or1k-unknown-elf-gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}