/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../app_common/inc/app_api.h"

ALIGNED(4) const USER_CFG_TAB_T user_cfg_tab[] =
{
	{ CONFIG_ID_YEAR,             2024},
	{ CONFIG_ID_MONTH,            1},
	{ CONFIG_ID_MDAY,             1},
	{ CONFIG_ID_WDAY,             6},
	{ CONFIG_ID_HOUR,             0},
	{ CONFIG_ID_MIN,              0},
	{ CONFIG_ID_SEC,              0},
	{ CONFIG_ID_LANGUAGE,         R_ID_STR_LAN_SCHINESE},//R_ID_STR_LAN_SCHINESE R_ID_STR_LAN_ENGLISH R_ID_STR_LAN_JAPANESE
	{ CONFIG_ID_AUTOOFF,          R_ID_STR_COM_OFF},
	{ CONFIG_ID_SCREENSAVE,       R_ID_STR_COM_OFF},
	{ CONFIG_ID_FREQUNCY,         R_ID_STR_COM_50HZ},
	{ CONFIG_ID_ROTATE,           R_ID_STR_COM_OFF},
	{ CONFIG_ID_FILLIGHT,         R_ID_STR_COM_OFF},
	{ CONFIG_ID_RESOLUTION,       R_ID_STR_RES_FHD},
#if ADD_FORMAT1_MENU_EN
	{ CONFIG_ID_TIMESTAMP,        R_ID_STR_COM_OFF},
#else
	{ CONFIG_ID_TIMESTAMP,        R_ID_STR_COM_OFF},
#endif
	{ CONFIG_ID_MOTIONDECTION,    R_ID_STR_COM_OFF},
	{ CONFIG_ID_PARKMODE,         R_ID_STR_COM_OFF},
	{ CONFIG_ID_GSENSOR,          R_ID_STR_COM_MIDDLE},
	{ CONFIG_ID_KEYSOUND,         R_ID_STR_COM_OFF},
	{ CONFIG_ID_IR_LED,           R_ID_STR_COM_OFF},
	{ CONFIG_ID_LOOPTIME,         R_ID_STR_COM_OFF},
	{ CONFIG_ID_AUDIOREC,         R_ID_STR_COM_ON},
	{ CONFIG_ID_EV,               R_ID_STR_COM_P0_0},
	{ CONFIG_ID_WBLANCE,          R_ID_STR_ISP_AUTO},
	{ CONFIG_ID_PRESLUTION,       R_ID_STR_RES_2M},
	{ CONFIG_ID_PFASTVIEW,        R_ID_STR_COM_OFF},
	{ CONFIG_ID_PTIMESTRAMP,      R_ID_STR_COM_ON},
	{ CONFIG_ID_PEV,              R_ID_STR_COM_P0_0},
	{ CONFIG_ID_VOLUME,           R_ID_STR_COM_LOW},
	{ CONFIG_ID_THUMBNAIL,        R_ID_STR_COM_ON},
	{ CONFIG_ID_GSENSORMODE,      R_ID_STR_COM_ON},
	{ CONFIG_ID_PLAY_MP3_MODE,    R_ID_ICON_MUSICMODECYCLE},
	{ CONFIG_ID_MAX,			  R_STR_MAX}
};


BOOT_DATA_KEPT_SECTION
HARDWARE_SETUP_T hardware_setup = {
	.sys_clk				= APB_CLK,
	.pll_clk				= PLL_CLK,
	.use_xosc				= USER_USE_XOSC,
	.sdram_clk				= SDRAM_CLK,
#if CURRENT_CHIP == FPGA
	.sdram_clk_src			= 0, //0:ddrpll 1:syspll
#else
	.sdram_clk_src			= 1, //0:ddrpll 1:syspll
#endif
	.sdram_size				= SDRAM_SIZE,
	.boot_spiflash_div		= ((PLL_CLK/2) / (BOOT_SPI_BAUDRATE) - 1),
	.uart_baudrate			= USER_UART_BAUDRATE,
	.uart_tx_pos			= USER_UART_TX_POS,
	.uart_rx_pos			= USER_UART_RX_POS,
	.dmaUart_baudrate       = USER_DMAUART_BAUDRATE,
	.dmaUart_tx_pos         = USER_DMAUART_TX_POS,
	.dmaUart_rx_pos         = USER_DMAUART_RX_POS,
	.led_en					= USER_USE_LED,
	.led_valid				= USER_LED_VALID,
	.led_ch					= USER_LED_CH,
	.led_pin				= USER_LED_PIN,
	
	.ui_rotate_soft			= 1, //1：可以省掉UI ROTATE的buf，节省内存使用；但是对于大屏可能菜单刷新速度会慢一些
	.ui_need_softrotate		= 0,
	.lcd_pos				= LCD_MAP_POS,
	.lcd_video_rotate_mode	= LCDSHOW_VIDEO_ROTATE_MORE_MODE,
	.lcd_ui_rotate_mode		= LCDSHOW_UI_ROTATE_MORE_MODE,
	.lcd_ratio_mode			= LCDSHOW_RATIO_CFG,
	.lcd_lzo_mode			= 1, //0:default mem size, 1~7: add n/8 size
	.lcd_backlight_ch		= LCD_BACKLIGHT_CH,
	.lcd_backlight_pin		= LCD_BACKLIGHT_PIN,
	.lcd_spi_cs_ch			= LCD_SPI_CS_CH,
	.lcd_spi_cs_pin			= LCD_SPI_CS_PIN,
	.lcd_spi_clk_ch			= LCD_SPI_CLK_CH,
	.lcd_spi_clk_pin		= LCD_SPI_CLK_PIN,
	.lcd_spi_data_ch		= LCD_SPI_DAT_CH,
	.lcd_spi_data_pin		= LCD_SPI_DAT_PIN,
	.lcd_mcu_rs_ch			= LCD_MCU_RS_CH,
	.lcd_mcu_rs_pin			= LCD_MCU_RS_PIN,
	.lcd_first_drop			= 10,
	.cmos_sensor_en			= USER_USE_CMOS_SENSOR,
	.cmos_sensor_pos		= CMOS_SENSOR_POS,
	.cmos_sensor_iic_pos	= CMOS_SENSOR_IIC_POS,
	.cmos_sensor_iic_soft_delay		= CMOS_SENSOR_IIC_SOFT_DELAY,
	.cmos_sensor_iic_soft_scl_ch	= CMOS_SENSOR_IIC_SOFT_SCL_CH,
	.cmos_sensor_iic_soft_scl_pin	= CMOS_SENSOR_IIC_SOFT_SCL_PIN,
	.cmos_sensor_iic_soft_sda_ch	= CMOS_SENSOR_IIC_SOFT_SDA_CH,
	.cmos_sensor_iic_soft_sda_pin	= CMOS_SENSOR_IIC_SOFT_SDA_PIN,
	.cmos_sensor_iic_baudrate		= CMOS_SENSOR_IIC_BAUDRATE,
	.cmos_sensor_reset_ctrl_en		= CMOS_SENSOR_RESET_CTRL_EN,
	.cmos_sensor_pwdn_ctrl_en		= CMOS_SENSOR_PWDN_CTRL_EN,
	.cmos_sensor_switch_en			= CMOS_SENSOR_SWITCH_EN,
	.cmos_sensor_sel				= CMOS_SENSOR_DEFAULT_SEL,

	.cmos_sensor1_reset_valid		= CMOS_SENSOR1_RESET_VALID,
	.cmos_sensor1_reset_ch			= CMOS_SENSOR1_RESET_CH,
	.cmos_sensor1_reset_pin			= CMOS_SENSOR1_RESET_PIN,
	.cmos_sensor1_pwdn_valid		= (CMOS_SENSOR_SWITCH_EN)?CMOS_SENSOR1_PWDN_VALID:1,
	.cmos_sensor1_pwdn_ch			= CMOS_SENSOR1_PWDN_CH,
	.cmos_sensor1_pwdn_pin			= CMOS_SENSOR1_PWDN_PIN,
	.cmos_sensor2_reset_valid		= CMOS_SENSOR2_RESET_VALID,
	.cmos_sensor2_reset_ch			= CMOS_SENSOR2_RESET_CH,
	.cmos_sensor2_reset_pin			= CMOS_SENSOR2_RESET_PIN,
	.cmos_sensor2_pwdn_valid		= CMOS_SENSOR2_PWDN_VALID,
	.cmos_sensor2_pwdn_ch			= CMOS_SENSOR2_PWDN_CH,
	.cmos_sensor2_pwdn_pin			= CMOS_SENSOR2_PWDN_PIN,
	.sensor_filter_en				= DEV_SENSOR_FILTER_EN,
	.sensor_ratio_cfg				= LCDSHOW_RATIO_BY_SENSOR,

	.adkey_en				= USER_USE_ADKEY,
	.adkey_pos				= ADKEY_POS,
	.pwrkey_use_adc			= POWERKEY_USE_ADC,
	.pwrkey_valid			= POWERKEY_VALID,
	.adkey_ch				= ADKEY_CH,
	.adkey_pin				= ADKEY_PIN,
	.pwrkey_ch				= POWERKEY_CH,
	.pwrkey_pin				= POWERKEY_PIN,
	.adkey_num				= ADKEY_NUM,
	.adkey_value_range		= ADKEY_VALUE_RANGE,
	.adkey_use_inner_10k	= ADKEY_USE_INNER_PULLUP_10K,
	.adkey_pullup_resistance= ADKEY_OUT_PULLUP_RESISTANCE,
	.adkey_tab				= {
#if ADKEY_NUM
		{ADKEY_1_RESITANCE, 	ADKEY_1_TYPE, 	ADKEY_1_LONGTYPE, 	ADKEY_1_LONGKEYTIME},
	#if ADKEY_NUM > 1
		{ADKEY_2_RESITANCE, 	ADKEY_2_TYPE, 	ADKEY_2_LONGTYPE, 	ADKEY_2_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 2
		{ADKEY_3_RESITANCE, 	ADKEY_3_TYPE, 	ADKEY_3_LONGTYPE, 	ADKEY_3_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 3
		{ADKEY_4_RESITANCE, 	ADKEY_4_TYPE, 	ADKEY_4_LONGTYPE, 	ADKEY_4_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 4
		{ADKEY_5_RESITANCE, 	ADKEY_5_TYPE, 	ADKEY_5_LONGTYPE, 	ADKEY_5_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 5
		{ADKEY_6_RESITANCE, 	ADKEY_6_TYPE, 	ADKEY_6_LONGTYPE, 	ADKEY_6_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 6
		{ADKEY_7_RESITANCE, 	ADKEY_7_TYPE, 	ADKEY_7_LONGTYPE, 	ADKEY_7_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 7
		{ADKEY_8_RESITANCE, 	ADKEY_8_TYPE, 	ADKEY_8_LONGTYPE, 	ADKEY_8_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 8
		{ADKEY_9_RESITANCE, 	ADKEY_9_TYPE, 	ADKEY_9_LONGTYPE, 	ADKEY_9_LONGKEYTIME},
	#endif
	#if ADKEY_NUM > 9
		{ADKEY_10_RESITANCE, 	ADKEY_10_TYPE, 	ADKEY_10_LONGTYPE, 	ADKEY_10_LONGKEYTIME},
	#endif
#endif
	},
	.sdcard_timeout			= (DEV_SDC_TIMEOUT)?((APB_CLK/1000)*DEV_SDC_TIMEOUT):((APB_CLK/1000)*1000),
	.sdcard_en				= USER_USE_SDCARD,
	.sdcard_pos				= SDCARD_POS,
	.sdcard_bus_width		= SDCARD_BUS_WIDTH,
	.ir_led_en				= USER_USE_IR,
	.ir_led_ch				= IR_LED_CH,
	.ir_led_pin				= IR_LED_PIN,
	.usb_dev_en				= USER_USE_USB_DEV,
	.usb_host_en			= USER_USE_USB_HOST,
	.usb_host_double_bond	= (USB_HOST_CH == USB20_CH)? 1 : 0,
	.usb_host_ch			= USB_HOST_CH,
	.usb_host_pwr_io		= USB_HOST_POWER_IO,	//IO1_CH_NONE: POWER常开， IO1_CH_PD1,IO1_CH_PA7：通过这两个IO口控制
	.usb_host_dect_ctrl		= USB_HOST_DECTCTRL, //1: 需要硬件DECT,    0:  使用软件DECT
	.usb_host_dect_ch		= USB_HOST_DET_CH,
	.usb_host_dect_pin		= USB_HOST_DET_PIN,

	.gsensor_en				= USER_USE_GSENSOR,
	.gsensor_iic_pos		= GSENSOR_IIC_POS,
	.battery_en				= USER_USE_BATTERY,
	//spi1
	.spi1_baudrate          = SPI1_BAURATE,
	.spi1_pos				= SPI1_POS,
	.spi1_busmode			= SPI1_BUS_MODE,
	.spi1_clk_idle			= SPI1_CLK_IDLE,
	.spi1_sample_edge		= SPI1_SAMPLE_EDGE,
	
	.boot_uart_en			= BOOT_UART_DEBG,
	.boot_save_sdram		= SAVE_SDRAM,
	// touchpanel
	.tp_en					= TOUCHPANEL_EN,
	.tp_reset_en			= TOUCHPANEL_RESET_EN,
	.tp_reset_valid			= TOUCHPANEL_RESET_VALID,
	.tp_iic_delay			= (APB_CLK/TOUCHPANEL_IIC_BAUDRATE)/(30),
	.tp_reset_ch			= TOUCHPANEL_RESET_CH,
	.tp_reset_pin			= TOUCHPANEL_RESET_PIN,
	.tp_iic_scl_ch			= TOUCHPANEL_IIC_SCL_CH,
	.tp_iic_scl_pin			= TOUCHPANEL_IIC_SCL_PIN,
	.tp_iic_sda_ch			= TOUCHPANEL_IIC_SDA_CH,
	.tp_iic_sda_pin			= TOUCHPANEL_IIC_SDA_PIN,

	.led_pwm_en				= USER_USE_PWM_LED,
	.led_pwm_valid			= USER_LED_PWM_VALID,
	.led_pwm_ch				= USER_LED_PWM_CH,
	.led_pwm_pin			= USER_LED_PWM_PIN,
	.led_pwn_timer			= LED_PWN_TIMER,
	.led_pwm_timer_ch		= LED_PWN_TIMER_CH,	
	.led_on_off_ch			= LED_ON_OFF_CH,
	.led_on_off_pin			= LED_ON_OFF_PIN,

	.led_on_off2_ch			= LED_ON_OFF2_CH,
	.led_on_off2_pin		= LED_ON_OFF2_PIN,


};


