/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_PROGRESS_BAR_VER_H
#define UI_WIN_PROGRESS_BAR_VER_H

typedef struct
{
	uiWidgetObj widget;
	u32 	  rate;
	uiColor   color;
	uiColor   rimcolor;
	u8 		  align;
}uiProgressBarVerObj;
/*******************************************************************************
* Function Name  : uiProgressBarCreate
* Description    : uiProgressBarCreate
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiProgressBarVerCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id, u8 align);
/*******************************************************************************
* Function Name  : uiProgressBarCreate
* Description    : uiProgressBarCreate
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiProgressBarVerCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
