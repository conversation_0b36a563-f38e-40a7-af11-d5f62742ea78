/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_MCU_SPFD5420

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()
    CMD(0x00),CMD(0x00),DAT(0x00),DAT(0x00),
    CMD(0x00),<PERSON><PERSON>(0x01),DAT(0x00),DAT(0x00),
    //SM = 0 && GS = 0 : scan order G1->G432
    CMD(0x00),CMD(0x02),DAT(0x01),DAT(0x00),
    CMD(0x00),CMD(0x03),DAT(0x10),DAT(0xa0),
    CMD(0x00),CMD(0x08),DAT(0x08),DAT(0x08),
    CMD(0x00),CMD(0x09),DAT(0x00),DAT(0x01),
    CMD(0x00),CMD(0x0b),DAT(0x00),DAT(0x10),
    CMD(0x00),CMD(0x0c),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x0f),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x07),DAT(0x00),DAT(0x01),
    CMD(0x00),CMD(0x08),DAT(0x08),DAT(0x08),
    CMD(0x00),CMD(0x10),DAT(0x00),DAT(0x17),
    CMD(0x00),CMD(0x11),DAT(0x02),DAT(0x00),
    CMD(0x00),CMD(0x12),DAT(0x03),DAT(0x00),
    CMD(0x00),CMD(0x20),DAT(0x02),DAT(0x1e),
    CMD(0x00),CMD(0x21),DAT(0x02),DAT(0x02),
    CMD(0x00),CMD(0x22),DAT(0x01),DAT(0x00),
    CMD(0x00),CMD(0x90),DAT(0x80),DAT(0x08),

    CMD(0x01),CMD(0x00),DAT(0x17),DAT(0xb0),
    DLY(1),
    CMD(0x01),CMD(0x01),DAT(0x01),DAT(0x47),
    DLY(1),
    CMD(0x01),CMD(0x02),DAT(0x01),DAT(0x38),
    DLY(1),
    CMD(0x01),CMD(0x03),DAT(0x2b),DAT(0x00),
    DLY(1),
    CMD(0x01),CMD(0x07),DAT(0x00),DAT(0x00),
    DLY(1),
    CMD(0x01),CMD(0x10),DAT(0x00),DAT(0x01),
    DLY(1),
    CMD(0x02),CMD(0x80),DAT(0x00),DAT(0x00),
    CMD(0x02),CMD(0x81),DAT(0x00),DAT(0x08),    //12
    CMD(0x02),CMD(0x82),DAT(0x00),DAT(0x00),
    DLY(1),

    CMD(0x03),CMD(0x00),DAT(0x10),DAT(0x11), //1011
    CMD(0x03),CMD(0x01),DAT(0x25),DAT(0x24), //2524
    CMD(0x03),CMD(0x02),DAT(0x2f),DAT(0x20), //20
    CMD(0x03),CMD(0x03),DAT(0x20),DAT(0x2f),
    CMD(0x03),CMD(0x04),DAT(0x23),DAT(0x25),
    CMD(0x03),CMD(0x05),DAT(0x11),DAT(0x10),
    CMD(0x03),CMD(0x06),DAT(0x0e),DAT(0x04),
    CMD(0x03),CMD(0x07),DAT(0x04),DAT(0x0e),
    CMD(0x03),CMD(0x08),DAT(0x00),DAT(0x05),
    CMD(0x03),CMD(0x09),DAT(0x00),DAT(0x03),
    CMD(0x03),CMD(0x0a),DAT(0x0f),DAT(0x04),
    CMD(0x03),CMD(0x0b),DAT(0x0f),DAT(0x00),
    CMD(0x03),CMD(0x0c),DAT(0x00),DAT(0x0f),
    CMD(0x03),CMD(0x0d),DAT(0x04),DAT(0x0f),
    CMD(0x03),CMD(0x0e),DAT(0x03),DAT(0x00),
    CMD(0x03),CMD(0x0f),DAT(0x05),DAT(0x00),

/*
    CMD(0x03),CMD(0x00),DAT(0x10),DAT(0x11),  // 0000
    CMD(0x03),CMD(0x01),DAT(0x25),DAT(0x07),  // 0507
    CMD(0x03),CMD(0x02),DAT(0x02),DAT(0x03),  // 0203

    CMD(0x03),CMD(0x03),DAT(0x20),DAT(0x2f),  // add
    CMD(0x03),CMD(0x04),DAT(0x23),DAT(0x25),  // add

    CMD(0x03),CMD(0x05),DAT(0x11),DAT(0x10),  // 0105
    CMD(0x03),CMD(0x06),DAT(0x00),DAT(0x0f),  // 000f
    CMD(0x03),CMD(0x07),DAT(0x06),DAT(0x05),  // 0605
    CMD(0x03),CMD(0x08),DAT(0x03),DAT(0x05),  // 0305
    CMD(0x03),CMD(0x09),DAT(0x07),DAT(0x07),  // 0707

    CMD(0x03),CMD(0x0a),DAT(0x0f),DAT(0x04),  // add
    CMD(0x03),CMD(0x0b),DAT(0x0f),DAT(0x00),  // add

    CMD(0x03),CMD(0x0C),DAT(0x05),DAT(0x02),  // 0502
    CMD(0x03),CMD(0x0D),DAT(0x04),DAT(0x0f),  // 040e

    CMD(0x03),CMD(0x0e),DAT(0x03),DAT(0x00),  // add
    CMD(0x03),CMD(0x0f),DAT(0x05),DAT(0x00),  // add
*/

    CMD(0x02),CMD(0x00),DAT(0x00),DAT(0xef), // 0xef
    CMD(0x02),CMD(0x01),DAT(0x01),DAT(0x8f), // 0x18f
    CMD(0x02),CMD(0x10),DAT(0x00),DAT(0x00),
    CMD(0x02),CMD(0x11),DAT(0x00),DAT(0xef),
    CMD(0x02),CMD(0x12),DAT(0x00),DAT(0x00),
    CMD(0x02),CMD(0x13),DAT(0x01),DAT(0x8f),

    CMD(0x04),CMD(0x00),DAT(0x35),DAT(0x00),  // 0x35,0x00,0x31,0x04
    CMD(0x04),CMD(0x01),DAT(0x00),DAT(0x01),
    CMD(0x04),CMD(0x04),DAT(0x00),DAT(0x00),

    CMD(0x06),CMD(0x00),DAT(0x00),DAT(0x00),
    CMD(0x06),CMD(0x06),DAT(0x00),DAT(0x00),
    CMD(0x06),CMD(0xf0),DAT(0x00),DAT(0x00),

    CMD(0x07),CMD(0xf0),DAT(0x54),DAT(0x20),
    CMD(0x07),CMD(0xde),DAT(0x00),DAT(0x00),
    CMD(0x07),CMD(0xf2),DAT(0x00),DAT(0xde),
    CMD(0x07),CMD(0xf3),DAT(0x28),DAT(0x8a),
    CMD(0x07),CMD(0xf4),DAT(0x00),DAT(0x22),
    CMD(0x07),CMD(0xf5),DAT(0x00),DAT(0x21),
    CMD(0x07),CMD(0xf0),DAT(0x00),DAT(0x00), // 0x0000



    CMD(0x00),CMD(0x07),DAT(0x01),DAT(0x73),

    DLY(5),
    CMD(0x02),CMD(0x02),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_SPFD5420",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(240*400*2*47),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,// 400,
    .screen_h 		= 400,// 240,

    .video_w  		= 400,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




