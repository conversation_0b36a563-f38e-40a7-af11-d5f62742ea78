
#ifndef __USB_UAC_H
#define __USB_UAC_H
/*******************************************************************************
* Function Name  : timer_Timer1_Stop
* Description    : timer1 stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_epx_cfg(void);

/*******************************************************************************
* Function Name  : uac_get_volume
* Description    : uac_get_volume
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_get_volume(u8 index);

/*******************************************************************************
* Function Name  : uac_set_volume
* Description    : uac_set_volume
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_set_volume(u8 * rxbuf);

/*******************************************************************************
* Function Name  : uac_set_mute
* Description    : uac_set_mute
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_set_mute(u8 * rxbuf);

/*******************************************************************************
* Function Name  : uac_unit_ctl_hal
* Description    : uac_unit_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_unit_ctl_hal(u8 val, u8 rqu, u8 len);

/*******************************************************************************
* Function Name  : UacReceiveSetSamplingFreqCallback
* Description    : UacReceiveSetSamplingFreqCallback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool UacReceiveSetSamplingFreqCallback(u8* rxbuf);

/*******************************************************************************
* Function Name  : UacHandleToStreaming
* Description    : UacHandleToStreaming
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool UacHandleToStreaming(u8 val, u8 rqu, u8 len);

/*******************************************************************************
* Function Name  : uac_start
* Description    : uac_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_start(void);

/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_stop(void);

/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_isr_process(void);
#endif /* DAC_H_ */

