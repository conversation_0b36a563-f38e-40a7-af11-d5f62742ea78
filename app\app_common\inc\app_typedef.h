/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  APP_TYPEDEF_H
    #define  APP_TYPEDEF_H

					

typedef enum
{
    LCDSHOW_ONLYWINA = 0,
    LCDSHOW_ONLYWINB,
	LCDSHOW_MAIN_WINA,
	LCDSHOW_MAIN_WINB,
	LCDSHOW_WINAB,
	LCDSHOW_WINBA,
	LCDSHOW_WINBMAX,
	LCDSHOW_WIN_DISABLE,
}LCDSHOW_WINAB_MODE;
typedef enum
{
    SENSOR_FILTER_CHANGE_NONE = 0,
    SENSOR_FILTER_CHANGE_NEXT,
	SENSOR_FILTER_CHANGE_PREV,	
}SENSOR_FILTER_STAT;
typedef struct LCDSHOW_CTRL_S
{
	INT16U video_layer_en;
	INT16U ui_layer_en;

}LCDSHOW_CTRL_T;



typedef struct System_Ctrl_S
{
	//dev handler fd
	INT8S dev_fd_battery;  		// dev fd for battery check
	INT8S dev_fd_gsensor; 		// dev fd for gsensor check
	INT8S dev_fd_ir;			// dev fd for ir ctrl
	INT8S dev_fd_key;      		// dev fd for key check
		
	INT8S dev_fd_lcd;      		// dev fd for lcd ctrl	
	INT8S dev_fd_led;      		// dev fd for led ctrl
	INT8S dev_fd_sensor;		// dev fd for sensor
	INT8S dev_fd_sdc;   		// dev fd for sd card check
	
	INT8S dev_fd_dusb;      	// dev fd for usb dev check
	INT8S dev_fd_husb;      	// dev fd for usb host check	
	INT8S dev_fd_tp;      	    // dev fd for touch panel check	
	//dev stat 
	INT8U dev_stat_power;		// power on flag.  key/dcin/gsensor/RTC/...

	INT8U dev_stat_battery; 	// battery state;   
	INT8U dev_stat_gsensorlock; // g sensor active flag
	INT8U dev_stat_ir;			//ir flag: 0: ir close , 1: ir auto 
	//INT8U dev_stat_keycheck;
	INT8U dev_stat_keysound;	//keysound flag:0: keysound off, 1: keysound play

	INT8U dev_stat_lcd;			//0: lcd sreen off, 1: lcd sreen on
	INT8U dev_stat_led;			//0: led off, 1: led on
	INT8U dev_stat_sensor;
	INT8U dev_stat_sdc;			// sdcard stat.no sdc,sdc unstable,sdc error,sdc full,sdc normal

	INT8U dev_dusb_stat;        // usb dev stat. no usb,dcin,usb-pc
	INT8U dev_husb_stat;        // usb host stat. no usb, power pre on, power on, in, show, astern
	INT8U dev_husb_ch;			// usb host ch: null, USB1.1, USB2.0
	INT8U lcdshow_win_mode;			// lcd show win mode

	INT8U lcdshow_win_mode_save;
	INT8U sys_reserv8[3];


	INT32 dev_dusb_out;			//software disable usb20 func
	XMsgQ 	*sysQ;
	XWork_T *recordIncrease1S;
	INT32U poweroffTime;		// sys auto power off time: sec
	INT32U powerOnTime;			// sys power on time: sec
	INT32U rec_remain_time;	    // rec remain time:sec
	INT32U rec_looptime;		// rec loop time: sec	
	INT32U rec_show_time;       // rec cur show time: sec
	INT32U rec_md_time;			// motion rec time
	
	INT32U play_total_time;		//play total time:msec
	INT32U play_cur_time;		//play cur time: msec
	INT32U play_last_time;		//play last time: msec
	
	INT32U sdc_freesize;  		//sdc free size KB
	INT32U fs_clustsize; 		// fs cluster size
	INT32S file_cnt;  		// file index for playback
	INT32S file_index;  // file index for playback
	int    file_type;
	
	
    INT32S avi_list;
	INT32S avia_list;
	INT32S avib_list;
	INT32S jpg_list;  // avi & jpg file list handle
	INT32S wav_list;        // wav file list
	INT32S mp3_list;     // mp3 list
	INT32S nes_list;     // nes list
	INT32S spi_jpg_list;
	//INT32S mp3dir_list;     // mp3 dir list
	//INT32S mp3file_list;     // mp3 file list
	
	INT32U	file_premalloc;
	FILELIST_NAME_T new_fname;
	FILELIST_NAME_T old_fname;
	FILELIST_NAME_T jpg_fname;
	char   file_fullname[FILE_PATH_LEN+FILE_NAME_LEN+1];
	char   version_str[32];	

	INT32S sensor_filter_index;
	INT32U lcd_scaler_level;
	INT32U lcd_scaler_level_max;
	INT16U lcd_scaler_w_step;
	INT16U lcd_scaler_h_step;

	INT32U play_scaler_level;
	INT32U play_scaler_level_max;
	INT16U play_scaler_w_step;
	INT16U play_scaler_h_step;
	INT8S dev_fd_led_pwm;
	int led_pwm_level;
	INT32U lcd_scaler_level_uishow;
	INT32U start_rec_or_takephoto_enable;//0:disable	1:rec	2:photo
	int tips_insert_sd_icon_count;

	INT8U file_del_ok;
	int low_power_tips;

	INT8U poweronFirst;

	INT32U led_onoff_sw;


	int af_value;
	int af_complete;
}System_Ctrl_T;

















#endif

