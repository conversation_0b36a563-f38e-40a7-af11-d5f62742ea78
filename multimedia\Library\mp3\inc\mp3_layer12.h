/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_LAYER12_H
       #define  MP3_LAYER12_H
/*******************************************************************************
* Function Name  : mp3_layer1_dec
* Description    : mp3_layer1_dec: decode a single Layer I frame
* Input          : MP3_BSIO_BIT_T *bit_op, u32 nb
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_layer1_dec(void);
/*******************************************************************************
* Function Name  : mp3_layer2_dec
* Description    : mp3_layer2_dec: decode a single Layer II frame
* Input          : MP3_BSIO_BIT_T *bit_op, u32 nb
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_layer2_dec(void);
#endif
