/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef DUSB_TOOL_API_H
#define DUSB_TOOL_API_H

#define CS_VENDOR_RAM                   0x0001
#define CS_VENDOR_RUN                   0x0002
#define CS_VENDOR_RESET                 0x0003
#define CS_VENDOR_READ_IC_INF           0x0004
#define CS_VENDOR_WR_ADDR               0x0006
#define CS_VENDOR_READ_SDK_INF          0x0007

/*******************************************************************************
* Function Name  : pqtool_get_sdk_info
* Description    : 
* Input          : none
* Output         : none                                            
* Return         : info pointer
*******************************************************************************/
u8* pqtool_get_sdk_info(void);

#endif
