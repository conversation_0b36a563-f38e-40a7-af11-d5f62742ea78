/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_RGB_OTA5182

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DAT(x)    LCD_CMD_RGB_DAT(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0x000f),
    <PERSON><PERSON>(0x0005),
    <PERSON><PERSON>(50),
    <PERSON><PERSON>(0x000f),
    <PERSON><PERSON>(0x0005),
    DLY(50),
    // <PERSON><PERSON>(0x000f),
    // C<PERSON>(0xf546),
    // CMD(0x9060),
    // CMD(0x5000),			//vcom
    // DLY(50),
    CMD(0x3008),
    CMD(0x703a),		//default 0x7040
    CMD(0xC005),
    CMD(0xE013),
    CMD(0x6001),		// NTSC
    DLY(150),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "RGB_OTA5182",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_0,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 4,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_RISING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 12,
    .vfp 			= 8,

    .hlw 			= 4,
    .hbp 			= 63,
    .hfp 			= 6,

    .spi_cpol		= 1,
    .spi_cpha		= 1,
    .spi_order		= 0,
    .spi_bits		= 16,

    .data_mode = 0x00708428,
    .data_mode1 = 0x01000000,

    .screen_w 		= 320,
    .screen_h 		= 240,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation 	= LCD_SATURATION_160,

    .contra_index 	= 6,

    .gamma_index 	= {4, 5, 6},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()


#endif








