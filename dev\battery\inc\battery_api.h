/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef BATTERY_API_H
#define BATTERY_API_H


/*******************************************************************************
* Function Name  : dev_battery_init
* Description    : dev_battery_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_battery_ioctrl(u32 op, u32 para);
/*******************************************************************************
* Function Name  : dev_battery_init
* Description    : dev_battery_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_battery_init(void);

#endif
