/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiWidgetManageProc
* Description    : uiWidgetManageProc
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiTipsProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiTipsObj *ptips;
	uiWinObj  *pWin;
	if(uiWidgetProc(msg))
		return;
	hWin  = msg->curWin;
	ptips = (uiTipsObj*)uiHandleToPtr(hWin);
	pWin  = &(ptips->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("tips win create\n");
			return;
		case MSG_WIN_PAINT:
			if(ptips->string.visible == 1) 
				uiWinDrawString(&pWin->rect,(uiRect*)(msg->para.p),&ptips->string);
			else if(pWin->bgColor!=INVALID_COLOR)
				uiWinDrawRect((uiRect*)(msg->para.p),pWin->bgColor);
			return;
		case MSG_WIN_UPDATE_RESID:
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_CHANGE_RESID:
			if(!RES_ID_IS_RAM(ptips->string.id)&& ptips->string.id == msg->para.v)
				return;
			ptips->string.id = msg->para.v;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_CHANGE_FG_COLOR:
			if(ptips->string.bgColor == msg->para.v)
				return;
			ptips->string.bgColor = msg->para.v;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			break;
		case MSG_WIN_VISIBLE_SET:
			if(pWin->bgColor==INVALID_COLOR)
				break;
			if(msg->para.v)
				ptips->string.visible = 1;
			else
				ptips->string.visible = 0;
			uiWinUpdateInvalid(hWin);
			return;
		case MSG_WIN_VISIBLE_GET:
			if(pWin->bgColor==INVALID_COLOR)
				break;
			if(ptips->string.visible)
				msg->para.v = 1;
			else
				msg->para.v = 0;
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiTipsCreate
* Description    : uiTipsCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiTipsCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle htips;
	uiTipsObj *ptips;
	htips = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiTipsProc,sizeof(uiTipsObj),WIN_WIDGET|infor->style);
	if(htips != INVALID_HANDLE)
	{
		ptips		   			= (uiTipsObj*)uiHandleToPtr(htips);
		ptips->string.id 		= infor->str;
		ptips->string.style		= infor->style;
		ptips->string.strAlign	= infor->strAlign;
		ptips->string.font		= infor->font;
		ptips->string.fontColor = infor->fontColor;
		ptips->string.bgColor 	= infor->bgColor;
		ptips->string.rimColor 	= infor->rimColor;
		ptips->string.visible	= 1;
		uiWidgetSetId(htips,infor->id);
		uiWinSetbgColor(htips, infor->bgColor);
	}
	return htips;
}