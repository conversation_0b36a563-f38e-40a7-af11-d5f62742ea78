/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

/*******************************************************************************
* Function Name  : main
* Description    :
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
u8 flash_success;

int main(void)
{
//--------------power on--------
    app_init();       // system power on configure
    #if (ENCRYPT_FUNC_SWITCH == 1)
    	flash_success = spi_flash_check_md5();
	#endif
#if FUN_BATTERY_CHARGE_SHOW
	if(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN)
	{
		deg_Printf("[main]--app_taskStart(TASK_BAT_CHARGE,1);--\r\n");
		app_taskStart(TASK_BAT_CHARGE,1);
	}
	else
#endif
	{
	//----fireware upgrade
		taskSdUpdateProcess();

		//hal_timerPWMStart(TIMER2,TMR2_PWM_POS_PD0,10000, 30);
	// start default task
		app_taskStart(TASK_RECORD_PHOTO,0);
	}
	//XMsgQFlush(SysCtrl.sysQ);
	app_taskService();
	return 2; // for usb upgrade
}



