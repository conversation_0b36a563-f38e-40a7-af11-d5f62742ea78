/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num2 :16*32
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
const unsigned char ascii_num2_32[]= // ' '
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_33[]= // '!'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xc0,0x03,0xc0,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_35[]= // '#'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x38,0x0e,0x38,0x0e,0x38,
   0x0e,0x38,0x0e,0x30,0x7f,0xfe,0x7f,0xfe,0x0e,0x70,0x0c,0x70,0x1c,0x70,0x1c,0x70,
   0x1c,0x70,0x7f,0xfe,0x7f,0xfe,0x1c,0xe0,0x18,0xe0,0x18,0xe0,0x38,0xe0,0x38,0xe0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_36[]= // '$'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x0f,0xe0,0x1f,0xf0,0x3f,0xf8,
   0x3b,0xf8,0x3b,0xb8,0x3b,0x80,0x3f,0x80,0x1f,0x80,0x0f,0x80,0x07,0xe0,0x03,0xf0,
   0x03,0xf8,0x03,0xf8,0x7b,0xb8,0x3b,0xb8,0x3b,0xb8,0x3f,0xf8,0x3f,0xf8,0x1f,0xf0,
   0x07,0xc0,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_37[]= // '%'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x18,0x7e,0x30,0x7e,0x30,
   0x7e,0x60,0x76,0x60,0x76,0xe0,0x7e,0xc0,0x7e,0xc0,0x7f,0x80,0x3d,0xb0,0x03,0x7c,
   0x03,0xfc,0x06,0xec,0x06,0xec,0x0c,0xec,0x0c,0xec,0x0c,0xec,0x18,0xfc,0x18,0x7c,
   0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_38[]= // '&'
{
   0x10,0x20,
   0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,
   0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,
   0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,
   0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x7f,0xff,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_39[]= // '''
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x07,0x00,0x07,0x00,
   0x07,0x00,0x07,0x00,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_40[]= // '('
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x3c,0x00,0x38,0x00,0x70,0x00,0xe0,
   0x00,0xe0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,
   0x01,0x80,0x01,0x80,0x01,0xc0,0x01,0xc0,0x00,0xc0,0x00,0xe0,0x00,0x70,0x00,0x70,
   0x00,0x38,0x00,0x1c,0x00,0x0c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_41[]= // ')'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x38,0x00,0x3c,0x00,0x1c,0x00,0x0e,0x00,
   0x0e,0x00,0x07,0x00,0x07,0x00,0x03,0x00,0x03,0x00,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x0e,0x00,0x0e,0x00,0x1c,0x00,
   0x38,0x00,0x78,0x00,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_42[]= // '*'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x03,0x80,
   0x7b,0x9c,0x7f,0xfe,0x1f,0xf8,0x07,0xe0,0x0f,0xe0,0x3f,0xf8,0x7f,0xbe,0x73,0x9c,
   0x03,0x80,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_43[]= // '+'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x7f,0xfe,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_44[]= // ','
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x3c,0x00,
   0x1c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_45[]= // '-'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xfc,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_46[]= // '.'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x3c,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_47[]= // '/'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0e,0x00,0x1c,0x00,0x18,0x00,0x38,
   0x00,0x30,0x00,0x70,0x00,0x60,0x00,0xe0,0x00,0xc0,0x01,0x80,0x03,0x80,0x03,0x00,
   0x07,0x00,0x06,0x00,0x0e,0x00,0x0c,0x00,0x1c,0x00,0x38,0x00,0x30,0x00,0x70,0x00,
   0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_48[]= // '0'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x1c,0x78,
   0x3c,0x38,0x38,0x38,0x78,0x3c,0x78,0x1c,0x70,0x1c,0x70,0x1c,0x70,0x1c,0x70,0x1c,
   0x70,0x1c,0x70,0x1c,0x78,0x3c,0x38,0x3c,0x38,0x38,0x3c,0x78,0x1f,0xf0,0x0f,0xe0,
   0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_49[]= // '1'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xc0,0x03,0xc0,0x07,0xc0,
   0x0f,0xc0,0x1f,0xc0,0x1b,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,
   0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,0x03,0xc0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_50[]= // '2'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3c,0x78,
   0x78,0x38,0x70,0x3c,0x00,0x3c,0x00,0x38,0x00,0x78,0x00,0x70,0x00,0xf0,0x01,0xe0,
   0x01,0xc0,0x03,0xc0,0x07,0x80,0x0f,0x00,0x1e,0x00,0x3c,0x00,0x3f,0xfc,0x7f,0xfc,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_51[]= // '3'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf8,0x3c,0x78,
   0x38,0x38,0x78,0x3c,0x00,0x38,0x00,0x38,0x00,0x78,0x03,0xf0,0x03,0xe0,0x00,0xf8,
   0x00,0x38,0x00,0x3c,0x00,0x3c,0x70,0x3c,0x78,0x3c,0x3c,0x78,0x3f,0xf8,0x0f,0xe0,
   0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_52[]= // '4'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0xf0,0x00,0xf0,
   0x01,0xf0,0x03,0xf0,0x03,0xf0,0x07,0x70,0x0f,0x70,0x0e,0x70,0x1c,0x70,0x3c,0x70,
   0x78,0x70,0x70,0x70,0xff,0xfe,0xff,0xfe,0x00,0x70,0x00,0x70,0x00,0x70,0x00,0x70,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_53[]= // '5'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xf8,0x1f,0xf8,0x1c,0x00,
   0x3c,0x00,0x38,0x00,0x38,0x00,0x3f,0x80,0x3f,0xf0,0x7c,0xf8,0x78,0x78,0x00,0x3c,
   0x00,0x3c,0x00,0x3c,0x00,0x3c,0x70,0x3c,0x70,0x38,0x78,0x78,0x3f,0xf0,0x1f,0xe0,
   0x07,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_54[]= // '6'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe0,0x01,0xc0,0x03,0xc0,
   0x03,0x80,0x07,0x80,0x0f,0x00,0x0e,0x00,0x1f,0xf0,0x3f,0xf8,0x3c,0x3c,0x38,0x1c,
   0x78,0x1e,0x78,0x1e,0x78,0x1e,0x78,0x1e,0x78,0x1c,0x3c,0x3c,0x3f,0x78,0x1f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_55[]= // '7'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x00,0x1c,
   0x00,0x3c,0x00,0x38,0x00,0x70,0x00,0x70,0x00,0xe0,0x00,0xe0,0x01,0xc0,0x01,0xc0,
   0x03,0xc0,0x03,0x80,0x03,0x80,0x07,0x80,0x07,0x00,0x07,0x00,0x0f,0x00,0x0f,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_56[]= // '8'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3c,0x78,
   0x38,0x38,0x78,0x38,0x78,0x38,0x38,0x38,0x3c,0x78,0x1f,0xf0,0x1f,0xf0,0x3e,0xf8,
   0x78,0x3c,0x70,0x1c,0x70,0x1c,0x70,0x1c,0x70,0x3c,0x78,0x3c,0x3f,0xf8,0x1f,0xf0,
   0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_57[]= // '9'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x3f,0xf0,0x3c,0x78,
   0x78,0x3c,0x70,0x3c,0x70,0x3c,0x70,0x3c,0x70,0x3c,0x78,0x78,0x78,0x78,0x3f,0xf0,
   0x1f,0xf0,0x01,0xe0,0x01,0xe0,0x01,0xc0,0x03,0xc0,0x07,0x80,0x07,0x00,0x0f,0x00,
   0x0e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_58[]= // ':'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_59[]= // ';'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,
   0x01,0x80,0x03,0x80,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_60[]= // '<'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x38,0x00,0x70,
   0x00,0xe0,0x01,0xc0,0x03,0x80,0x0f,0x00,0x1e,0x00,0x3c,0x00,0x70,0x00,0x38,0x00,
   0x1c,0x00,0x0e,0x00,0x07,0x00,0x03,0x80,0x01,0xe0,0x00,0xf0,0x00,0x78,0x00,0x1c,
   0x00,0x0c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_61[]= // '='
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_62[]= // '>'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x38,0x00,0x1c,0x00,
   0x0f,0x00,0x07,0x80,0x03,0xc0,0x01,0xe0,0x00,0x70,0x00,0x38,0x00,0x1c,0x00,0x3c,
   0x00,0x70,0x00,0xe0,0x01,0xc0,0x03,0x80,0x07,0x00,0x0e,0x00,0x3c,0x00,0x78,0x00,
   0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_63[]= // '?'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3c,0x78,
   0x3c,0x78,0x38,0x38,0x38,0x38,0x00,0x78,0x00,0x78,0x00,0xf0,0x01,0xe0,0x03,0xc0,
   0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_64[]= // '@'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xe0,0x0c,0x38,0x18,0x18,
   0x30,0x0c,0x33,0xfc,0x67,0x7c,0x66,0x6e,0x6e,0x6e,0x6c,0xee,0x7c,0xee,0x7c,0xec,
   0x7c,0xec,0x7c,0xcc,0x7f,0xfc,0x6f,0xf8,0x70,0x00,0x30,0x00,0x18,0x38,0x0e,0x70,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_65[]= // 'A'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0xc0,0x07,0xc0,
   0x07,0xc0,0x07,0xe0,0x0f,0xe0,0x0e,0xe0,0x0e,0xe0,0x1e,0xf0,0x1e,0x70,0x1c,0x70,
   0x1f,0xf8,0x3f,0xf8,0x3c,0x38,0x38,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x1c,0x70,0x1e,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_66[]= // 'B'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xe0,0x7f,0xf0,0x78,0x78,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x78,0x7f,0xf0,0x7f,0xe0,0x78,0xf8,
   0x78,0x3c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x3c,0x78,0x3c,0x7f,0xf8,0x7f,0xf0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_67[]= // 'C'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xe0,0x0f,0xf8,0x1e,0x78,
   0x3c,0x3c,0x38,0x1c,0x38,0x1c,0x78,0x1c,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x78,0x00,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x3c,0x3c,0x3c,0x3c,0x1f,0xf8,0x0f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_68[]= // 'D'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x80,0x7f,0xe0,0x79,0xf0,
   0x78,0x78,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x1c,0x78,0x1c,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x78,0x78,0xf8,0x7f,0xf0,0x7f,0xc0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_69[]= // 'E'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xfc,0x3f,0xfc,0x38,0x00,
   0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x3f,0xf8,0x3f,0xf8,0x38,0x00,
   0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x3f,0xfc,0x3f,0xfc,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_70[]= // 'F'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x78,0x00,
   0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x7f,0xf0,0x7f,0xf0,
   0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_71[]= // 'G'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xe0,0x1f,0xf0,0x1e,0x78,
   0x3c,0x38,0x38,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x00,0x78,0x00,0x78,0x00,0x79,0xfc,
   0x79,0xfc,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x3c,0x3c,0x3c,0x3c,0x1f,0xfc,0x0f,0xfc,
   0x03,0x9c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_72[]= // 'H'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x78,0x3c,0x78,0x3c,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x7f,0xfc,0x7f,0xfc,0x78,0x3c,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_73[]= // 'I'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_74[]= // 'J'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x3c,0x00,0x3c,
   0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x3c,
   0x00,0x3c,0x78,0x3c,0x78,0x3c,0x38,0x3c,0x38,0x38,0x3c,0x78,0x1f,0xf8,0x1f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_75[]= // 'K'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x78,0x78,0x78,0xf0,
   0x78,0xf0,0x79,0xe0,0x7b,0xc0,0x7f,0x80,0x7f,0x00,0x7f,0x80,0x7f,0x80,0x7f,0xc0,
   0x79,0xc0,0x79,0xe0,0x78,0xf0,0x78,0xf0,0x78,0x78,0x78,0x78,0x78,0x3c,0x78,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_76[]= // 'L'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x7f,0xfc,0x7f,0xfc,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_77[]= // 'M'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x3c,0x7c,0x7c,0x7c,0x7c,
   0x7c,0x7c,0x7c,0x7c,0x7e,0x7c,0x7e,0xfc,0x7e,0xfc,0x7e,0xfc,0x7e,0xfc,0x7f,0xfc,
   0x77,0xfc,0x77,0xdc,0x77,0xdc,0x77,0xdc,0x77,0xdc,0x73,0xdc,0x73,0x9c,0x73,0x9c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_78[]= // 'N'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x7c,0x3c,0x7c,0x3c,
   0x7c,0x3c,0x7e,0x3c,0x7e,0x3c,0x7f,0x3c,0x7f,0x3c,0x7f,0xbc,0x7b,0xbc,0x7b,0xfc,
   0x79,0xfc,0x79,0xfc,0x79,0xfc,0x78,0xfc,0x78,0xfc,0x78,0x7c,0x78,0x7c,0x78,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_79[]= // 'O'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3e,0x78,
   0x3c,0x38,0x78,0x3c,0x78,0x3c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,
   0x78,0x1c,0x78,0x1c,0x78,0x3c,0x78,0x3c,0x38,0x3c,0x3c,0x78,0x1f,0xf8,0x0f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_80[]= // 'P'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xe0,0x7f,0xf8,0x78,0x78,
   0x78,0x3c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x3c,0x78,0xf8,0x7f,0xf8,
   0x7f,0xe0,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_81[]= // 'Q'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3e,0x78,
   0x3c,0x38,0x78,0x3c,0x78,0x3c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,
   0x78,0x1c,0x78,0xdc,0x79,0xfc,0x78,0xfc,0x38,0xfc,0x3c,0x78,0x1f,0xf8,0x0f,0xf8,
   0x03,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_82[]= // 'R'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xe0,0x7f,0xf8,0x78,0xf8,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x78,0x7f,0xf8,0x7f,0xe0,
   0x79,0xe0,0x78,0xe0,0x78,0xf0,0x78,0xf0,0x78,0x78,0x78,0x78,0x78,0x3c,0x78,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_83[]= // 'S'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf0,0x3c,0x78,
   0x38,0x38,0x38,0x3c,0x78,0x00,0x3c,0x00,0x3e,0x00,0x1f,0x80,0x07,0xe0,0x01,0xf8,
   0x00,0x78,0x00,0x3c,0x78,0x3c,0x78,0x1c,0x78,0x3c,0x3c,0x3c,0x3f,0xf8,0x1f,0xf0,
   0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_84[]= // 'T'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xfc,0x7f,0xfc,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_85[]= // 'U'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x78,0x3c,0x78,0x3c,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x3c,0x3c,0x3f,0xf8,0x1f,0xf0,
   0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_86[]= // 'V'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf0,0x1e,0x70,0x1c,0x78,0x3c,
   0x78,0x3c,0x38,0x3c,0x38,0x38,0x3c,0x78,0x3c,0x78,0x1c,0x70,0x1c,0x70,0x1e,0xf0,
   0x0e,0xf0,0x0e,0xe0,0x0f,0xe0,0x0f,0xe0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x03,0x80,
   0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_87[]= // 'W'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf3,0x9e,0xf3,0x9e,0xf7,0xde,
   0x77,0xdc,0x77,0xdc,0x77,0xdc,0x77,0xdc,0x77,0xdc,0x76,0xdc,0x7e,0xfc,0x7e,0xfc,
   0x3e,0xf8,0x3e,0xf8,0x3e,0xf8,0x3e,0xf8,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,
   0x1c,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_88[]= // 'X'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x38,0x38,0x3c,0x78,
   0x1c,0x70,0x1e,0xf0,0x0e,0xe0,0x0f,0xe0,0x07,0xc0,0x07,0xc0,0x03,0x80,0x07,0xc0,
   0x07,0xc0,0x0f,0xe0,0x0e,0xe0,0x1e,0xf0,0x1c,0x78,0x3c,0x78,0x78,0x3c,0x78,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_89[]= // 'Y'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x1c,0x78,0x3c,0x38,0x38,
   0x3c,0x78,0x1c,0x70,0x1e,0xf0,0x0e,0xe0,0x0f,0xe0,0x07,0xc0,0x07,0xc0,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_90[]= // 'Z'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xfc,0x3f,0xfc,0x00,0x3c,
   0x00,0x78,0x00,0x70,0x00,0xf0,0x00,0xe0,0x01,0xe0,0x03,0xc0,0x03,0x80,0x07,0x80,
   0x07,0x00,0x0f,0x00,0x1e,0x00,0x1c,0x00,0x3c,0x00,0x78,0x00,0x7f,0xfc,0x7f,0xfc,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_91[]= // '['
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x03,0xfc,0x03,0xfc,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_92[]= // '\'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x38,0x00,0x38,0x00,0x1c,0x00,
   0x1c,0x00,0x0c,0x00,0x0e,0x00,0x0e,0x00,0x07,0x00,0x07,0x00,0x03,0x00,0x03,0x80,
   0x03,0x80,0x01,0xc0,0x01,0xc0,0x00,0xc0,0x00,0xe0,0x00,0xe0,0x00,0x70,0x00,0x70,
   0x00,0x30,0x00,0x38,0x00,0x38,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_93[]= // ']'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x7f,0xc0,0x7f,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,
   0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,
   0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,0x01,0xc0,
   0x01,0xc0,0x01,0xc0,0x7f,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_94[]= // '^'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x07,0xc0,0x0f,0xe0,0x1e,0xf0,0x3c,0x78,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_95[]= // '_'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xfe,0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_96[]= // '`'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x0f,0x80,0x07,0x80,0x03,0xc0,0x01,0xc0,0x00,0xe0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_97[]= // 'a'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf0,0x1f,0xf8,0x3c,0x38,0x00,0x38,
   0x00,0x78,0x0f,0xf8,0x3f,0xb8,0x38,0x38,0x78,0x38,0x78,0x78,0x7d,0xf8,0x3f,0xf8,
   0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_98[]= // 'b'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x78,0x00,0x78,0x00,
   0x78,0x00,0x78,0x00,0x78,0x00,0x7b,0x80,0x7f,0xf0,0x7e,0xf8,0x7c,0x78,0x78,0x38,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x7c,0x78,0x7e,0xf8,0x7f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_99[]= // 'c'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf0,0x1f,0xf8,0x3c,0x38,0x78,0x3c,
   0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x3c,0x78,0x3c,0x3c,0x3c,0x3e,0xf8,0x0f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_100[]= // 'd'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x38,0x00,0x38,
   0x00,0x38,0x00,0x38,0x00,0x38,0x07,0x38,0x1f,0xf8,0x3f,0xf8,0x3c,0x78,0x78,0x38,
   0x78,0x38,0x78,0x38,0x78,0x38,0x78,0x38,0x78,0x38,0x38,0x78,0x3e,0xf8,0x1f,0xf8,
   0x07,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_101[]= // 'e'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf0,0x1e,0xf8,0x3c,0x38,0x38,0x3c,
   0x7f,0xfc,0x7f,0xfc,0x78,0x00,0x78,0x00,0x78,0x3c,0x3c,0x3c,0x1e,0xf8,0x0f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_102[]= // 'f'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xfc,0x03,0xfc,0x07,0x80,
   0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x7f,0xf8,0x7f,0xf8,0x07,0x00,0x07,0x00,
   0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_103[]= // 'g'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfc,0x1f,0xf0,0x3c,0x70,0x38,0x70,
   0x38,0x70,0x3c,0x70,0x1f,0xe0,0x1f,0xc0,0x38,0x00,0x38,0x00,0x3f,0xf0,0x1f,0xfc,
   0x38,0x3c,0x70,0x1c,0x78,0x3c,0x3f,0xf8,0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_104[]= // 'h'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x38,0x00,0x38,0x00,
   0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x3f,0xf8,0x3f,0x78,0x3c,0x3c,0x3c,0x3c,
   0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_105[]= // 'i'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_106[]= // 'j'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe0,0x00,0xe0,0x00,0xe0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,
   0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,
   0x00,0xe0,0x00,0xe0,0x1d,0xe0,0x1f,0xc0,0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_107[]= // 'k'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x38,0x00,0x38,0x00,
   0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x78,0x38,0xf0,0x39,0xe0,0x3b,0xc0,
   0x3f,0x80,0x3f,0xc0,0x3f,0xe0,0x3c,0xe0,0x38,0xf0,0x38,0x70,0x38,0x38,0x38,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_108[]= // 'l'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_109[]= // 'm'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xfc,0xff,0xfe,0xf3,0xde,0xf3,0x8e,
   0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,0xf3,0x8e,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_110[]= // 'n'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xf8,0x3f,0x78,0x3c,0x3c,0x3c,0x3c,
   0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_111[]= // 'o'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xe0,0x1f,0xf8,0x3c,0x78,0x78,0x3c,
   0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x1c,0x78,0x3c,0x3c,0x38,0x1e,0xf8,0x0f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_112[]= // 'p'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0xe0,0x7f,0xf8,0x7c,0x78,0x78,0x38,
   0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x78,0x3c,0x7c,0x78,0x7e,0xf8,0x7f,0xf0,
   0x7b,0xc0,0x78,0x00,0x78,0x00,0x78,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_113[]= // 'q'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xf8,0x3f,0xf8,0x3c,0x78,0x78,0x38,
   0x78,0x38,0x78,0x38,0x78,0x38,0x78,0x38,0x78,0x38,0x38,0x78,0x3e,0xf8,0x1f,0xf8,
   0x07,0xb8,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x38,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_114[]= // 'r'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0xf0,0x1f,0xf0,0x1f,0x80,0x1f,0x00,
   0x1e,0x00,0x1c,0x00,0x1c,0x00,0x1c,0x00,0x1c,0x00,0x1c,0x00,0x1c,0x00,0x1c,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_115[]= // 's'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf0,0x1e,0xf8,0x3c,0x38,0x3c,0x00,
   0x3c,0x00,0x1f,0xe0,0x07,0xf0,0x00,0x78,0x38,0x38,0x38,0x38,0x3e,0x78,0x1f,0xf0,
   0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_116[]= // 't'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x00,
   0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x7f,0xf8,0x7f,0xf8,0x07,0x00,0x07,0x00,
   0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x00,0x07,0x98,0x03,0xf8,
   0x00,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_117[]= // 'u'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,
   0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x3c,0x38,0x7c,0x3c,0xfc,0x1f,0xfc,
   0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_118[]= // 'v'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x38,0x38,0x38,0x38,0x3c,0x78,
   0x1c,0x70,0x1c,0x70,0x0e,0xf0,0x0e,0xe0,0x0f,0xe0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_119[]= // 'w'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf3,0x9e,0xf7,0xde,0x77,0xdc,0x77,0xdc,
   0x77,0xdc,0x76,0xdc,0x3e,0xf8,0x3e,0xf8,0x3e,0xf8,0x3e,0xf8,0x3c,0x78,0x1c,0x70,
   0x1c,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_120[]= // 'x'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x38,0x1c,0x70,0x1e,0xf0,0x0e,0xe0,
   0x07,0xc0,0x07,0xc0,0x07,0xc0,0x07,0xc0,0x0e,0xe0,0x1e,0xf0,0x3c,0x78,0x38,0x38,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_121[]= // 'y'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x3c,0x38,0x38,0x38,0x38,0x3c,0x78,
   0x1c,0x70,0x1c,0x70,0x1e,0xf0,0x0e,0xe0,0x0f,0xe0,0x07,0xc0,0x07,0xc0,0x07,0xc0,
   0x03,0x80,0x03,0x80,0x3f,0x80,0x3f,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_122[]= // 'z'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xf8,0x3f,0xf8,0x00,0xf0,0x00,0xe0,
   0x01,0xe0,0x03,0xc0,0x07,0x80,0x0f,0x00,0x1e,0x00,0x1c,0x00,0x38,0x00,0x3f,0xfc,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_123[]= // '{'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x00,0x78,0x00,0xf0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,
   0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x03,0xe0,0x03,0xe0,
   0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,0x00,0xe0,
   0x00,0xe0,0x00,0xe0,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_124[]= // '|'
{
   0x10,0x20,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,
   0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x03,0x80,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_125[]= // '}'
{
   0x10,0x20,
   0x00,0x00,0x00,0x00,0x3c,0x00,0x1e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,
   0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x07,0x80,0x0f,0x80,
   0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,0x0e,0x00,
   0x0e,0x00,0x0e,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num2_126[]= // '~'
{
   0x10,0x20,
   0x0c,0x00,0x1f,0x0c,0x3f,0x9c,0x39,0xf8,0x30,0xf8,0x00,0x70,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
//-------------------------ascii table----------------------
ALIGNED(4) const unsigned char * const ascii_num2_table[]=
{
   ascii_num2_32,// ' '
   ascii_num2_33,// '!'
   ascii_num2_35,// '#'
   ascii_num2_36,// '$'
   ascii_num2_37,// '%'
   ascii_num2_38,// '&'
   ascii_num2_39,// '''
   ascii_num2_40,// '('
   ascii_num2_41,// ')'
   ascii_num2_42,// '*'
   ascii_num2_43,// '+'
   ascii_num2_44,// ','
   ascii_num2_45,// '-'
   ascii_num2_46,// '.'
   ascii_num2_47,// '/'
   ascii_num2_48,// '0'
   ascii_num2_49,// '1'
   ascii_num2_50,// '2'
   ascii_num2_51,// '3'
   ascii_num2_52,// '4'
   ascii_num2_53,// '5'
   ascii_num2_54,// '6'
   ascii_num2_55,// '7'
   ascii_num2_56,// '8'
   ascii_num2_57,// '9'
   ascii_num2_58,// ':'
   ascii_num2_59,// ';'
   ascii_num2_60,// '<'
   ascii_num2_61,// '='
   ascii_num2_62,// '>'
   ascii_num2_63,// '?'
   ascii_num2_64,// '@'
   ascii_num2_65,// 'A'
   ascii_num2_66,// 'B'
   ascii_num2_67,// 'C'
   ascii_num2_68,// 'D'
   ascii_num2_69,// 'E'
   ascii_num2_70,// 'F'
   ascii_num2_71,// 'G'
   ascii_num2_72,// 'H'
   ascii_num2_73,// 'I'
   ascii_num2_74,// 'J'
   ascii_num2_75,// 'K'
   ascii_num2_76,// 'L'
   ascii_num2_77,// 'M'
   ascii_num2_78,// 'N'
   ascii_num2_79,// 'O'
   ascii_num2_80,// 'P'
   ascii_num2_81,// 'Q'
   ascii_num2_82,// 'R'
   ascii_num2_83,// 'S'
   ascii_num2_84,// 'T'
   ascii_num2_85,// 'U'
   ascii_num2_86,// 'V'
   ascii_num2_87,// 'W'
   ascii_num2_88,// 'X'
   ascii_num2_89,// 'Y'
   ascii_num2_90,// 'Z'
   ascii_num2_91,// '['
   ascii_num2_92,// '\'
   ascii_num2_93,// ']'
   ascii_num2_94,// '^'
   ascii_num2_95,// '_'
   ascii_num2_96,// '`'
   ascii_num2_97,// 'a'
   ascii_num2_98,// 'b'
   ascii_num2_99,// 'c'
   ascii_num2_100,// 'd'
   ascii_num2_101,// 'e'
   ascii_num2_102,// 'f'
   ascii_num2_103,// 'g'
   ascii_num2_104,// 'h'
   ascii_num2_105,// 'i'
   ascii_num2_106,// 'j'
   ascii_num2_107,// 'k'
   ascii_num2_108,// 'l'
   ascii_num2_109,// 'm'
   ascii_num2_110,// 'n'
   ascii_num2_111,// 'o'
   ascii_num2_112,// 'p'
   ascii_num2_113,// 'q'
   ascii_num2_114,// 'r'
   ascii_num2_115,// 's'
   ascii_num2_116,// 't'
   ascii_num2_117,// 'u'
   ascii_num2_118,// 'v'
   ascii_num2_119,// 'w'
   ascii_num2_120,// 'x'
   ascii_num2_121,// 'y'
   ascii_num2_122,// 'z'
   ascii_num2_123,// '{'
   ascii_num2_124,// '|'
   ascii_num2_125,// '}'
   ascii_num2_126,// '~'
};