/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef HAL_DMAUART_H
#define HAL_DMAUART_H
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartRxOverWait(void);
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartRxDataOut(void);
/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dmaUartIRQHandler(u8 sta);
/*******************************************************************************
* Function Name  : hal_dmauartInit
* Description    : hal layer.dma uart initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartInit(void);
/*******************************************************************************
* Function Name  : hal_dmauartInit
* Description    : hal layer.dma uart initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dmauartTest(void);
















#endif
