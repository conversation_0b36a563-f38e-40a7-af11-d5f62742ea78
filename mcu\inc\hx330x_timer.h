/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_TIMER_H
    #define HX330X_TIMER_H

enum
{
	TIMER0=0,
	TIMER1,
	TIMER2,
	TIMER3,

	TIMER_MAX
};

#define  TIMER_FRQ_MIN         20   // min frq hz	
/*******************************************************************************
* Function Name  : hx330x_timer0IRQHandler
* Description    : timer0 irq handler
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timer0IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_timer1IRQHandler
* Description    : timer1 irq handler
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timer1IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_timer2IRQHandler
* Description    : timer2 irq handler
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timer2IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_timer3IRQHandler
* Description    : timer3 irq handler
* Input          : 

* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timer3IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_timerISRRegister
* Description    : timer isr register
* Input          :  u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
                       void (*isr)(void) : callback function
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_timerISRRegister(u8 timer,void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_timerStart
* Description    : timer start
* Input          : u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
                      u32 frq  : frq .>20hz
                      void (*isr)(void) : callback function
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_timerStart(u8 timer,u32 frq,void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_timeStop
* Description    : timer stop
* Input          : u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_timerStop(u8 timer);
/*******************************************************************************
* Function Name  : hx330x_timerEnable
* Description    : timer enable
* Input          : u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_timerEnable(u8 timer);
/*******************************************************************************
* Function Name  : hx330x_timerDisable
* Description    : timer disable
* Input          : u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_timerDisable(u8 timer);
/*******************************************************************************
* Function Name  : hx330x_timerTickStart
* Description    : tick timer start
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timerTickStart(void);
/*******************************************************************************
* Function Name  : hx330x_timerTickStop
* Description    : tick timer stop
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_timerTickStop(void);
/*******************************************************************************
* Function Name  : hx330x_timerTickCount
* Description    : tick timer counter get
* Input          : 
* Output         : None
* Return         : count
*******************************************************************************/
u32 hx330x_timerTickCount(void);

/*******************************************************************************
* Function Name  : hx330x_timerPWMStart
* Description    : timer PWM start
* Input          : timer: timer select
* 				   pwm_map: PWM output group select
* 				   frq:     PWM frequency configure
* 				   percent: PWM duty rate(<100)
* Output         : None
* Return         : true:sucess, false: fail
*******************************************************************************/
bool hx330x_timerPWMStart(u8 timer,u8 pwm_map,u32 frq, u8 percent);
/*******************************************************************************
* Function Name  : hx330x_timerPWMStop
* Description    : timer PWM stop
* Input          : timer: timer select
* Output         : None
* Return         : NONE
*******************************************************************************/
void  hx330x_timerPWMStop(u8 timer);

#endif
