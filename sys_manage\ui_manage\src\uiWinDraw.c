/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

ALIGNED(4) UIDRAW_CTRL_T ui_draw_ctrl;
/*******************************************************************************
* Function Name  : uiWinDrawInit
* Description    : uiWinDrawInit
* Input          : lcdshow_frame_t * drawframe
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawInit(lcdshow_frame_t * drawframe)
{
    if(drawframe)
	{
		drawframe->data_size	= drawframe->buf_size;
		ui_draw_ctrl.drawframe  = drawframe;
		if(hardware_setup.ui_need_softrotate == 0)
		{
			ui_draw_ctrl.width  	= drawframe->w;
			ui_draw_ctrl.height 	= drawframe->h;
		}else{
			ui_draw_ctrl.width  	= drawframe->h;
			ui_draw_ctrl.height 	= drawframe->w;
		}
		
		ui_draw_ctrl.bufStart   = drawframe->y_addr;
		ui_draw_ctrl.bufEnd     = drawframe->y_addr + drawframe->buf_size;
	}
   
}
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawUpdate(void)
{	
	if(ui_draw_ctrl.drawframe)
	{
		hx330x_sysDcacheWback((u32)ui_draw_ctrl.bufStart,ui_draw_ctrl.drawframe->buf_size);
		hal_lcdUiKickWait((lcdshow_frame_t *)ui_draw_ctrl.drawframe);
		hal_lcdUiSetBufferWaitDone(UI_LAYER0);
		hal_lcdUiKick((lcdshow_frame_t *)ui_draw_ctrl.drawframe);
	}
}
/*******************************************************************************
* Function Name  : uidraw_Service
* Description    : uidraw_Service
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static u16 uiWinDraw_calPhoneticPos(u8* srcAddr,u16 charW,u16 charH)
{
	u8 findBlack = 0;
	u16 i,j;
	u8* curAddr;
	charW = (charW+7)>>3;
	for(i = charH-3;i>0;i--)
	{
		curAddr = srcAddr + i*charW;
		for(j=0;j<charW;j++)
		{
			findBlack = *curAddr++;
			if(findBlack)
				break;
		}
		if(findBlack)
			break;
	}
	if(i>4)
		i=i-4;
	return i;
}
/*******************************************************************************
* Function Name  : uiWinDrawStartAddrCal
* Description    : uiWinDrawStartAddrCal
* Input          : s32 scan_mode, s16 sx, s16 sy
* Output         : none                                            
* Return         : none 
*******************************************************************************/

#define uiWinDrawNoRotateStartAddrCal(sx, sy) 	ui_draw_ctrl.bufStart + (sy)*ui_draw_ctrl.width + (sx)
#define uiWinDrawRotateStartAddrCal(sx, sy) 	ui_draw_ctrl.bufStart + (ui_draw_ctrl.width-(sx)-1)*ui_draw_ctrl.height + (sy)
/*uiColor* uiWinDrawStartAddrCal(s16 sx, s16 sy)
{
	if(hardware_setup.ui_need_softrotate)
	{
		return ui_draw_ctrl.bufStart + (ui_draw_ctrl.width-sx-1)*ui_draw_ctrl.height + sy;
		
	}else
	{
		return ui_draw_ctrl.bufStart + (sy)*ui_draw_ctrl.width + (sx);
	}
}*/
/*******************************************************************************
* Function Name  : uiWinDrawAddrReCal
* Description    : uiWinDrawAddrReCal
* Input          : s16 sx, s16 sy
* Output         : none                                            
* Return         : none 
*******************************************************************************/
#define uiWinDrawNoRotateAddrReCal(baseAddr,x, y)	 ((baseAddr) + (y)*ui_draw_ctrl.width + (x))
#define uiWinDrawRotateAddrReCal(baseAddr,x, y)		((baseAddr) - (x)*ui_draw_ctrl.height + (y))
/*uiColor* uiWinDrawAddrReCal(uiColor* baseAddr,s16 x, s16 y)	
{
	if(hardware_setup.ui_need_softrotate)
	{
		return baseAddr - x*ui_draw_ctrl.height + y;
	}else
	{
		return (baseAddr + (y)*ui_draw_ctrl.width + (x));
	}
}*/
/*******************************************************************************
* Function Name  : uiWinDrawLine
* Description    : uiWinDrawLine
* Input          : LINE_DRAW_T *line
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawLine(LINE_DRAW_T *line)
{
	u16 i,j;

	uiColor *dest;
	uiColor *dest_start;      		//start point of each block
	u16 block_width,block_height;  //the size of block which is needed to move
	s32 hdeta,wdeta,intg,cnt;
	if(ui_draw_ctrl.bufStart == NULL)
		return;

	if(!(line->style & WIN_NOT_ZOOM))
	{
		line->sx 	= USER_Rx(line->sx);
		line->sy 	= USER_Ry(line->sy);
		line->ex 	= USER_Rx(line->ex);
		line->ey 	= USER_Ry(line->ey);
		line->width = USER_Rw(line->width);
	}
	if(line->sx >= USER_UI_WIDTH)
		line->sx = USER_UI_WIDTH - 1;
	if(line->sy >= USER_UI_HEIGHT)
		line->sy = USER_UI_HEIGHT - 1;
	if(line->ex >= USER_UI_WIDTH)
		line->ex = USER_UI_WIDTH - 1;	
	if(line->ey >= USER_UI_HEIGHT)
		line->ey = USER_UI_HEIGHT - 1;	

	block_width 	= line->width;
	block_height	= line->ey - line->sy;
	hdeta = 0;
	wdeta = 0;
	intg = 0;
	if(block_height == 0)
	{
		block_height = line->width;
		block_width  = line->ex - line->sx;
	}
    else
    {
		cnt = intg = line->ex - line->sx;
		if(intg < 0)
			cnt = 0 - cnt;
		if(cnt)
		{
			wdeta =  cnt/block_height;
			if(wdeta == 0)
				hdeta = (block_height*10)/cnt;
			else
				wdeta =  (cnt*10)/block_height;
		}
    }
	//deg_Printf("line:[%d, %d, %d, %d, %d]\n", line->sx, line->sy, line->ex, line->ey, line->width);
	//deg_Printf("line:[%d, %d] wdeta:%d, hdeta:%d, intg:%d\n",block_width,block_height,wdeta, hdeta, intg);
	if(hardware_setup.ui_need_softrotate)
	{
		dest_start =  uiWinDrawRotateStartAddrCal(line->sx, line->sy);
	}else
	{
		dest_start =  uiWinDrawNoRotateStartAddrCal(line->sx, line->sy);
	}
	
	if(dest_start == NULL)
		return;
    cnt = 0;
	if(hardware_setup.ui_need_softrotate)
	{
		for(i=0;i<block_height;i++)
		{
			dest = dest_start;
			for(j=0;j < block_width;j++)
			{
				if(dest >= ui_draw_ctrl.bufStart && dest < ui_draw_ctrl.bufEnd)
					*dest = (uiColor)line->fill_color;
				else
					deg_err("memeory operation out of bounds!\n");
				dest = uiWinDrawRotateAddrReCal(dest, 1, 0);
			}
			dest_start = uiWinDrawRotateAddrReCal(dest_start, 0, 1);
			if(hdeta || wdeta)
			{
				cnt += 10;
				if(hdeta)
				{
					if(intg<0)
					{
						dest_start = uiWinDrawRotateAddrReCal(dest_start, -cnt/hdeta, 0);
					}
					else
					{
						dest_start = uiWinDrawRotateAddrReCal(dest_start, cnt/hdeta, 0);
					}
					if(cnt >= hdeta)
						cnt -=hdeta;
				}
				else
				{
					if(intg < 0)
					{
						dest_start = uiWinDrawRotateAddrReCal(dest_start, -cnt/wdeta, 0);
					}
					else
					{
						dest_start = uiWinDrawRotateAddrReCal(dest_start, cnt/wdeta, 0);	
					}
					if(cnt >= wdeta)
						cnt -= wdeta;
				}
			}
		}
	}else
	{
		for(i=0;i<block_height;i++)
		{
			dest = dest_start;
			for(j=0;j < block_width;j++)
			{
				if(dest >= ui_draw_ctrl.bufStart && dest < ui_draw_ctrl.bufEnd)
					*dest = (uiColor)line->fill_color;
				else
					deg_err("memeory operation out of bounds!\n");
				dest = uiWinDrawNoRotateAddrReCal(dest, 1, 0);
			}
			dest_start = uiWinDrawNoRotateAddrReCal(dest_start, 0, 1);
			if(hdeta || wdeta)
			{
				cnt += 10;
				if(hdeta)
				{
					if(intg<0)
					{
						dest_start = uiWinDrawNoRotateAddrReCal(dest_start, -cnt/hdeta, 0);
					}
					else
					{
						dest_start = uiWinDrawNoRotateAddrReCal(dest_start, cnt/hdeta, 0);
					}
					if(cnt >= hdeta)
						cnt -=hdeta;
				}
				else
				{
					if(intg < 0)
					{
						dest_start = uiWinDrawNoRotateAddrReCal(dest_start, -cnt/wdeta, 0);
					}
					else
					{
						dest_start = uiWinDrawNoRotateAddrReCal(dest_start, cnt/wdeta, 0);	
					}
					if(cnt >= wdeta)
						cnt -= wdeta;
				}
			}
		}
	}
}
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawRect(uiRect* rect,uiColor color)
{
	s16 x,y;
	uiColor* dest;
	uiColor* destStart;
	if(ui_draw_ctrl.bufStart == NULL)
		return;

	if(rect->x0 >= USER_UI_WIDTH)
		rect->x0 = USER_UI_WIDTH - 1;
	if(rect->y0 >= USER_UI_HEIGHT)
		rect->y0 = USER_UI_HEIGHT - 1;
	if(rect->x1 >= USER_UI_WIDTH)
		rect->x1 = USER_UI_WIDTH - 1;
	if(rect->y1 >= USER_UI_HEIGHT)
		rect->y1 = USER_UI_HEIGHT - 1;
	if(hardware_setup.ui_need_softrotate)
		destStart = uiWinDrawRotateStartAddrCal(rect->x0, rect->y0);
	else
		destStart = uiWinDrawNoRotateStartAddrCal(rect->x0, rect->y0);
	if(destStart == NULL)
		return;
	if(hardware_setup.ui_need_softrotate)
	{
		for(y = rect->y0; y <= rect->y1; y++)
		{
			dest = destStart;
			for(x = rect->x0; x <= rect->x1; x++)
			{
				*dest = color;
				dest = uiWinDrawRotateAddrReCal(dest,1, 0);		
			}
			destStart = uiWinDrawRotateAddrReCal(destStart, 0, 1);
		}
	}else
	{
		for(y = rect->y0; y <= rect->y1; y++)
		{
			dest = destStart;
			for(x = rect->x0; x <= rect->x1; x++)
			{
				*dest = color;
				dest = uiWinDrawNoRotateAddrReCal(dest,1, 0);		
			}
			destStart = uiWinDrawNoRotateAddrReCal(destStart, 0, 1);
		}
	}
}
void uiWinDrawPoint(uiRect* rect,int x,int y,int cnt, uiColor color)
{
	//deg_Printf("11 [%d,%d,%d]\n", x,y,cnt);
	if(y < rect->y0 || y > rect->y1)
	{
		return;
	}
	if(x > rect->x1)
	{
		return;
	}
	if(x < rect->x0)
	{
		x = rect->x0;
	}
	

	if(cnt == 0)
	{
		cnt = 1;
	}
	if((rect->x1 - x + 1) < cnt)
		cnt = (rect->x1 - x + 1);
	//deg_Printf("[%d,%d,%d]\n", x,y,cnt);
	uiColor* dest;
	if(hardware_setup.ui_need_softrotate)
		dest = uiWinDrawRotateStartAddrCal(x, y);
	else
		dest = uiWinDrawNoRotateStartAddrCal(x, y);
	while(cnt--)
	{
		*dest = color;
		if(hardware_setup.ui_need_softrotate)
		{
			dest = uiWinDrawRotateAddrReCal(dest,1, 0);
		}else
		{
			dest = uiWinDrawNoRotateAddrReCal(dest,1, 0);
		}
	}

	
}
/*******************************************************************************
* Function Name  : uiWinDrawCircle
* Description    : uiWinDrawCircle
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawCircle(uiCycle *cycle,uiColor bgcolor, uiColor fontcolor)
{
	int a,b;
	int di;
	int x0, y0;
	uiColor* dest;
	uiColor* destStart;
	if(ui_draw_ctrl.bufStart == NULL)
		return;
	if(cycle->radius <= 0)
	{
		return;
	}

	if(cycle->x_center > cycle->rect.x1)
	{
		cycle->x_center = cycle->rect.x1;
	}
	if(cycle->y_center > cycle->rect.y1)
	{
		cycle->y_center = cycle->rect.y1;
	}	
	if(bgcolor != INVALID_COLOR)
		uiWinDrawRect(&cycle->rect,bgcolor);
	if(fontcolor == INVALID_COLOR)
	{
		return;
	}
	x0 = cycle->x_center;
	y0 = cycle->y_center;
	a  = 0;
	b  = cycle->radius;
	di = 3 -  cycle->radius * 2;
	//deg_Printf("bgcolor:%x, foncolor:%x, x0:%d, y0:%d, b:%d, di:%d\n", bgcolor,fontcolor, x0, y0, b, di);
	while(a<=b)
	{
		uiWinDrawPoint(&cycle->rect,x0-a,y0-b,2*a,fontcolor);             //2
		//uiWinDrawPoint(&cycle->rect,x0+a,y0-b,fontcolor);             //5

		uiWinDrawPoint(&cycle->rect,x0-a,y0+b,2*a, fontcolor);             //1
		//uiWinDrawPoint(&cycle->rect,x0+a,y0+b,fontcolor);             //6
		
		
		uiWinDrawPoint(&cycle->rect,x0-b,y0-a,2*b, fontcolor);             //7
 		//uiWinDrawPoint(&cycle->rect,x0+b,y0-a, fontcolor);             //0

		uiWinDrawPoint(&cycle->rect,x0-b,y0+a,2*b,fontcolor);
		//uiWinDrawPoint(&cycle->rect,x0+b,y0+a,fontcolor);             //4
		

 		
  		
		a++;
		// 计算下一个点的参数值
		if(di<0)di +=4*a+6;
		else
		{
			di+=10+4*(a-b);
			b--;
		}
	}
}
/*******************************************************************************
* Function Name  : res_draw_RectNotInter
* Description    : res_draw_RectNotInter
* Input          : uiRect* father,uiRect* child,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiWinDrawRectNotInter(uiRect* father,uiRect* child,uiColor color)
{
	uiRect invalidRect;
	if(father->x0 < child->x0)
	{
		invalidRect.x0 = father->x0;
		invalidRect.x1 = child->x0-1;
		invalidRect.y0 = father->y0;
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->y0 < child->y0)
	{
		invalidRect.x0 = hx330x_max(father->x0,child->x0);
		invalidRect.x1 = father->x1;
		invalidRect.y0 = father->y0;
		invalidRect.y1 = child->y0-1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->x1 > child->x1)
	{
		invalidRect.x0 = child->x1+1;
		invalidRect.x1 = father->x1;
		invalidRect.y0 = hx330x_max(father->y0,child->y0);
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->y1 > child->y1)
	{
		invalidRect.x0 = hx330x_max(father->x0,child->x0);
		invalidRect.x1 = hx330x_min(father->x1,child->x1);
		invalidRect.y0 = child->y1+1;
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawIcon
* Description    : res_draw_Icon
* Input          : uiRect* winRect,uiRect* drawRect,resID id,u8 alignment,uiColor bgColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawIcon(uiRect* winRect,uiRect* drawRect,ICON_DRAW_T *icon)
{
	u16 iconWidth  	= 0;
	u16 iconHeight 	= 0;
	uiColor* resAddr;
	uiColor *dest;
	uiColor *dest_start;      		//start point of each block
	uiColor* resData;
	u16 i,j,width,height;
	uiRect resRect;
	s16 resX,resY;
	uiColor transfer_Color;

	if(ui_draw_ctrl.bufStart == NULL)
		return;	
	resAddr = (uiColor*)res_icon_GetAddrAndSize(icon->id,&iconWidth,&iconHeight);
	if(resAddr == NULL)
		return;

	if(icon->iconAlign & ALIGNMENT_LEFT)
	{
		resX = resRect.x0 = winRect->x0;
		resRect.x1 = resRect.x0 + iconWidth - 1;
	}
	else if(icon->iconAlign & ALIGNMENT_RIGHT)
	{
		resRect.x1 = winRect->x1;
		resX = resRect.x0 = winRect->x1 - iconWidth + 1;
	}
	else
	{
		resX = resRect.x0 = (winRect->x0 + winRect->x1 - iconWidth)/2;
		resRect.x1 = resRect.x0 + iconWidth - 1;
	}
	resY = resRect.y0 = (winRect->y0 + winRect->y1 - iconHeight)/2;
	resRect.y1 = resRect.y0 + iconHeight - 1;
	
	if(uiWinOverlapCmp(&resRect,drawRect) < 0)
	{
		if(icon->bgColor != INVALID_COLOR)
			uiWinDrawRect(drawRect,icon->bgColor);
		return;
	}
	if(icon->bgColor != INVALID_COLOR)
		uiWinDrawRectNotInter(drawRect,&resRect,icon->bgColor);
	uiWinInterSection(drawRect,drawRect,&resRect);
	resRect.x0 = drawRect->x0 - resX;  //
	resRect.y0 = drawRect->y0 - resY;
	resRect.x1 = drawRect->x1 - resX;
	resRect.y1 = drawRect->y1 - resY;
	if(hardware_setup.ui_need_softrotate)
		dest_start = uiWinDrawRotateStartAddrCal(drawRect->x0, drawRect->y0);
	else
		dest_start = uiWinDrawNoRotateStartAddrCal(drawRect->x0, drawRect->y0);
	if(dest_start == NULL)
		return;

	width  = resRect.x1 - resRect.x0 + 1;
	height = resRect.y1 - resRect.y0 + 1;
	resAddr += iconWidth*resRect.y0 + resRect.x0*sizeof(uiColor);
	transfer_Color = (uiColor)res_icon_GetTColor(icon->id);
	//deg_Printf("id:%x, bgColor:%x, transparentColor:%x\n", icon->id,icon->bgColor,transfer_Color);
	if(hardware_setup.ui_need_softrotate)
	{
		for(i=0;i<height;i++)
		{
			resData = res_iconGetData(icon->id,(u32)resAddr,width);
			resAddr += iconWidth;
			if(resData == NULL)
				break;
			dest = dest_start;
			for(j=0;j<width;j++)
			{
				if(*resData != transfer_Color)
				{
					if(icon->iconColor != INVALID_COLOR)
					{
						*dest = icon->iconColor;
					}else
					{
						*dest = *resData;
					}	

				}
					
				else
				{
					if(icon->bgColor != INVALID_COLOR)
						*dest = icon->bgColor;
				}
				dest = uiWinDrawRotateAddrReCal(dest,1, 0);		
				resData++;
			}
			dest_start = uiWinDrawRotateAddrReCal(dest_start,0, 1);	
		}
	}else
	{
		for(i=0;i<height;i++)
		{
			resData = res_iconGetData(icon->id,(u32)resAddr,width);
			resAddr += iconWidth;
			if(resData == NULL)
				break;
			dest = dest_start;
			for(j=0;j<width;j++)
			{
				if(*resData != transfer_Color)
				{
					if(icon->iconColor != INVALID_COLOR)
					{
						*dest = icon->iconColor;
					}else
					{
						*dest = *resData;
					}
				}

				else
				{
					if(icon->bgColor != INVALID_COLOR)
						*dest = icon->bgColor;
				}
				dest = uiWinDrawNoRotateAddrReCal(dest,1, 0);		
				resData++;
			}
			dest_start = uiWinDrawNoRotateAddrReCal(dest_start,0, 1);	
		}
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawChar
* Description    : uiWinDrawChar
* Input          : u8* destAddr,u8* srcAddr,uiRect* srcRect,u16 charW,u16 charH,uiColor fontColor,uiColor bgColor,uiColor rimColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiWinDrawChar(u8* destAddr,u8* srcAddr,CHAR_DRAW_T *char_draw)
{
	u8 temp,mask,lastDraw = 2;
	u16 i,j,drawLine;
	u8* drawAddr,*upPointAddr,*charAddr,*temAddr;
	u16 destW, destH;
	charAddr = srcAddr;
	destW = (char_draw->charW + 7)>>3; 
	srcAddr +=  char_draw->charRect.y0 * destW;
	destH = char_draw->charRect.y1 - char_draw->charRect.y0 + 1;

	if(hardware_setup.ui_need_softrotate)
	{
		for(i = 0;i < destH; i++)
		{
			drawLine = 0;
			drawAddr = destAddr;
			for(j=0;j < destW; j++)
			{
				if(srcAddr - destW >= charAddr)
					upPointAddr = srcAddr - destW;
				else
					upPointAddr = NULL;
				if(i < char_draw->charH)
					temp = *srcAddr++;
				else
					temp = 0;
				for(mask=0; mask < 8; mask++)
				{
					if(drawLine >= char_draw->charRect.x0 && drawLine <= char_draw->charRect.x1)
					{
						if(temp&(0x80>>mask))
						{
							if(char_draw->rimColor != INVALID_COLOR)
							{
								if(lastDraw == 0)
								{
									temAddr = uiWinDrawRotateAddrReCal(destAddr,-1, 0);
									//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
										*temAddr = char_draw->rimColor;
								}
								if(upPointAddr)
								{
									if((*upPointAddr&(0x80>>mask)) == 0)
									{
										temAddr = uiWinDrawRotateAddrReCal(destAddr, 0, -1);
										//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
											*temAddr = char_draw->rimColor;
									}
								}
							}
							*destAddr = char_draw->fontColor;
							lastDraw = 1;
						}
						else
						{
							if(char_draw->rimColor != INVALID_COLOR)
							{
								if(lastDraw == 1)
									*destAddr = char_draw->rimColor;
								else if(upPointAddr&&(*upPointAddr&(0x80>>mask)))
									*destAddr = char_draw->rimColor;
								else
								{
									if(char_draw->bgColor != INVALID_COLOR)
										*destAddr = char_draw->bgColor;
								}
							}
							else
							{
								if(char_draw->bgColor != INVALID_COLOR)
									*destAddr = char_draw->bgColor;
							}
							lastDraw = 0;
						}
						destAddr = uiWinDrawRotateAddrReCal(destAddr, 1, 0);
					}
					drawLine++;
				}
			}
			destAddr = uiWinDrawRotateAddrReCal(drawAddr, 0, 1);
		}		
	}else
	{
		for(i = 0;i < destH; i++)
		{
			drawLine = 0;
			drawAddr = destAddr;
			for(j=0;j < destW; j++)
			{
				if(srcAddr - destW >= charAddr)
					upPointAddr = srcAddr - destW;
				else
					upPointAddr = NULL;
				if(i < char_draw->charH)
					temp = *srcAddr++;
				else
					temp = 0;
				for(mask=0; mask < 8; mask++)
				{
					if(drawLine >= char_draw->charRect.x0 && drawLine <= char_draw->charRect.x1)
					{
						if(temp&(0x80>>mask))
						{
							if(char_draw->rimColor != INVALID_COLOR)
							{
								if(lastDraw == 0)
								{
									temAddr = uiWinDrawNoRotateAddrReCal(destAddr,-1, 0);
									//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
										*temAddr = char_draw->rimColor;
								}
								if(upPointAddr)
								{
									if((*upPointAddr&(0x80>>mask)) == 0)
									{
										temAddr = uiWinDrawNoRotateAddrReCal(destAddr, 0, -1);
										//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
											*temAddr = char_draw->rimColor;
									}
								}
							}
							*destAddr = char_draw->fontColor;
							lastDraw = 1;
						}
						else
						{
							if(char_draw->rimColor != INVALID_COLOR)
							{
								if(lastDraw == 1)
									*destAddr = char_draw->rimColor;
								else if(upPointAddr&&(*upPointAddr&(0x80>>mask)))
									*destAddr = char_draw->rimColor;
								else
								{
									if(char_draw->bgColor != INVALID_COLOR)
										*destAddr = char_draw->bgColor;
								}
							}
							else
							{
								if(char_draw->bgColor != INVALID_COLOR)
									*destAddr = char_draw->bgColor;
							}
							lastDraw = 0;
						}
						destAddr = uiWinDrawNoRotateAddrReCal(destAddr, 1, 0);
					}
					drawLine++;
				}
			}
			destAddr = uiWinDrawNoRotateAddrReCal(drawAddr, 0, 1);
		}	
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawString
* Description    : res_draw_Char
* Input          : uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawString(uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw)
{
	u32 strNum;	
	s16 resX,resY;
	u16 i,width;
	u8* destAddr;
	u8* lastAddr;
	u8* secSpecialAddr;
	u8* lastSecSpecialAddr;
	u8* lowPhoneticAddr;
	u8* lastLowPhoneticAddr;
	u8* charBuff;
	uiRect drawRect, resRect;
	CHAR_DRAW_T char_draw;

	u8 special,lastSpecial=0;
	
	strNum = res_GetStringInfor(string_draw->id,&char_draw.charW,&char_draw.charH,string_draw->font);
	if(strNum==0)
	{
		//deg_Printf("find string error!!!,0x%x\n",string_draw->id);
		return;
	}

	drawRect.x0 = invalidRect->x0;
	drawRect.y0 = invalidRect->y0;
	drawRect.x1 = invalidRect->x1;
	drawRect.y1 = invalidRect->y1;
	if(string_draw->strAlign & ALIGNMENT_LEFT)
	{
		resX = winRect->x0;
		//resRect.x1 = resRect.x0 + char_draw.charW - 1;
	}
	else if(string_draw->strAlign & ALIGNMENT_RIGHT)
	{
		//resRect.x1 = winRect->x1;
		resX = winRect->x1 - char_draw.charW + 1;
	}
	else
	{
		resX = (winRect->x0 + winRect->x1 - char_draw.charW)>>1;
		//resRect.x1 = resRect.x0 + char_draw.charW - 1;
	}
	if(resX < winRect->x0)
	{
		resX = winRect->x0;
	}
	resRect.x0 = resX;
	resRect.x1 = resX + char_draw.charW - 1;
	
	resY = resRect.y0 = (winRect->y0 + winRect->y1 - char_draw.charH)>>1;
	if(!RES_ID_IS_RAM(string_draw->id))
		resY = resRect.y0 = resRect.y0 + 3;
	resRect.y1 = resRect.y0 + char_draw.charH - 1;
	if(uiWinOverlapCmp(&resRect,&drawRect) < 0)
	{
		if(string_draw->bgColor!=INVALID_COLOR)
			uiWinDrawRect(&drawRect,string_draw->bgColor);
		return;
	}
	if(string_draw->bgColor != INVALID_COLOR)
		uiWinDrawRectNotInter(&drawRect, &resRect,string_draw->bgColor);
	uiWinInterSection(&drawRect,&drawRect,&resRect);
	resRect.x0 = drawRect.x0 - resX;
	resRect.y0 = drawRect.y0 - resY;
	resRect.x1 = drawRect.x1 - resX;
	resRect.y1 = drawRect.y1 - resY;
	secSpecialAddr = NULL;
	if(hardware_setup.ui_need_softrotate)
	{
		destAddr = uiWinDrawRotateStartAddrCal(drawRect.x0, drawRect.y0);
		if(destAddr == NULL)
			return;
		if(resY > winRect->y0)
		{
			if(resY - winRect->y0 > 5)
				secSpecialAddr = uiWinDrawRotateStartAddrCal(drawRect.x0, (resY-5));
			else
				secSpecialAddr = uiWinDrawRotateStartAddrCal(drawRect.x0, winRect->y0);
		}
		lastSecSpecialAddr = secSpecialAddr;
		lowPhoneticAddr = NULL;
		if(resY + (s16)char_draw.charH-7 <= winRect->y1)
		{
			if(resY + (s16)char_draw.charH >= 11)
				lowPhoneticAddr = uiWinDrawRotateStartAddrCal(drawRect.x0, (resY+(int16)char_draw.charH-11));
		}
		lastLowPhoneticAddr = lowPhoneticAddr;
		width = 0;
		lastAddr = destAddr;
		
		char_draw.fontColor = string_draw->fontColor;
		char_draw.bgColor   = string_draw->bgColor;
		char_draw.rimColor	= string_draw->rimColor;
		for(i=0;i<strNum;i++)
		{
			charBuff = res_GetCharInfor(string_draw->id, i, &char_draw.charW, &char_draw.charH,string_draw->font,&special);
			if(charBuff == NULL)
				break;
			if(special == 0) //限制了字符串起始必须是标准字体
				width += char_draw.charW;
			if(width > resRect.x0)
			{
				if(special)
				{
					if(lastSpecial == special)
					{
						char_draw.charRect.y0 = 0;
						char_draw.charRect.y1 = char_draw.charH-1;
					}
					else if(special==2)
					{
						char_draw.charRect.y0 = uiWinDraw_calPhoneticPos(charBuff,char_draw.charW,char_draw.charH);//resHeight*4;
						char_draw.charRect.y1 = char_draw.charRect.y0 + 4;
					}
					else
					{
						if(resRect.y0 >= char_draw.charH)
							continue;
						char_draw.charRect.y0 = resRect.y0;
						char_draw.charRect.y1 = char_draw.charH - 1;
					}
				}
				else
				{
					if(width - resRect.x0 < char_draw.charW)
						char_draw.charRect.x0 = char_draw.charW - (width - resRect.x0);
					else
						char_draw.charRect.x0 = 0;
					if(width > resRect.x1+1)
						char_draw.charRect.x1 = (char_draw.charW-(width-(resRect.x1+1))-char_draw.charRect.x0)-1;
					else
						char_draw.charRect.x1 = char_draw.charW-1;
						//charRect.x1=char_draw.charW-charRect.x0-1;
					char_draw.charRect.y0 = resRect.y0;
					char_draw.charRect.y1 = resRect.y1;
				}
				if(special)
				{
					if(lastSpecial == special)
					{
						if(lastSecSpecialAddr)
							uiWinDrawChar(lastSecSpecialAddr,charBuff,&char_draw);

					}
					else
					{
						if(special == 1)
							uiWinDrawChar(lastAddr,charBuff,&char_draw);
						else if(special==2 && lastLowPhoneticAddr)
							uiWinDrawChar(lastLowPhoneticAddr,charBuff,&char_draw);
					}
				}
				else
					uiWinDrawChar(destAddr,charBuff,&char_draw);
				lastSpecial = special;
				if(special == 0)
				{
					lastAddr  = destAddr;

					destAddr = uiWinDrawRotateAddrReCal(destAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					if(secSpecialAddr)
					{
						lastSecSpecialAddr 	= secSpecialAddr;
						secSpecialAddr 		= uiWinDrawRotateAddrReCal(secSpecialAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					}
					if(lowPhoneticAddr)
					{
						lastLowPhoneticAddr = lowPhoneticAddr;
						lowPhoneticAddr 	= uiWinDrawRotateAddrReCal(lowPhoneticAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					}
				}
				if(width > resRect.x1)
				{
					if(i < strNum-1)
					{
						res_GetCharInfor(string_draw->id, i+1, NULL, NULL,string_draw->font,&special);
						if(special)
							continue;
					}
					break;
				}
			}
		}
	}	
	else
	{
		destAddr = uiWinDrawNoRotateStartAddrCal(drawRect.x0, drawRect.y0);
		if(destAddr == NULL)
			return;	
		if(resY > winRect->y0)
		{
			if(resY - winRect->y0 > 5)
				secSpecialAddr = uiWinDrawNoRotateStartAddrCal(drawRect.x0, (resY-5));
			else
				secSpecialAddr = uiWinDrawNoRotateStartAddrCal(drawRect.x0, winRect->y0);
		}	
		lastSecSpecialAddr = secSpecialAddr;
		lowPhoneticAddr = NULL;
		if(resY + (s16)char_draw.charH-7 <= winRect->y1)
		{
			if(resY + (s16)char_draw.charH >= 11)
				lowPhoneticAddr = uiWinDrawNoRotateStartAddrCal(drawRect.x0, (resY+(int16)char_draw.charH-11));
		}
		lastLowPhoneticAddr = lowPhoneticAddr;
		width = 0;
		lastAddr = destAddr;
		
		char_draw.fontColor = string_draw->fontColor;
		char_draw.bgColor   = string_draw->bgColor;
		char_draw.rimColor	= string_draw->rimColor;
		for(i=0;i<strNum;i++)
		{
			charBuff = res_GetCharInfor(string_draw->id, i, &char_draw.charW, &char_draw.charH,string_draw->font,&special);
			if(charBuff == NULL)
				break;
			if(special == 0) //限制了字符串起始必须是标准字体
				width += char_draw.charW;
			if(width > resRect.x0)
			{
				if(special)
				{
					if(lastSpecial == special)
					{
						char_draw.charRect.y0 = 0;
						char_draw.charRect.y1 = char_draw.charH-1;
					}
					else if(special==2)
					{
						char_draw.charRect.y0 = uiWinDraw_calPhoneticPos(charBuff,char_draw.charW,char_draw.charH);//resHeight*4;
						char_draw.charRect.y1 = char_draw.charRect.y0 + 4;
					}
					else
					{
						if(resRect.y0 >= char_draw.charH)
							continue;
						char_draw.charRect.y0 = resRect.y0;
						char_draw.charRect.y1 = char_draw.charH - 1;
					}
				}
				else
				{
					if(width - resRect.x0 < char_draw.charW)
						char_draw.charRect.x0 = char_draw.charW - (width - resRect.x0);
					else
						char_draw.charRect.x0 = 0;
					if(width > resRect.x1+1)
						char_draw.charRect.x1 = (char_draw.charW-(width-(resRect.x1+1))-char_draw.charRect.x0)-1;
					else
						char_draw.charRect.x1 = char_draw.charW-1;
						//charRect.x1=char_draw.charW-charRect.x0-1;
					char_draw.charRect.y0 = resRect.y0;
					char_draw.charRect.y1 = resRect.y1;
				}
				if(special)
				{
					if(lastSpecial == special)
					{
						if(lastSecSpecialAddr)
							uiWinDrawChar(lastSecSpecialAddr,charBuff,&char_draw);

					}
					else
					{
						if(special == 1)
							uiWinDrawChar(lastAddr,charBuff,&char_draw);
						else if(special==2 && lastLowPhoneticAddr)
							uiWinDrawChar(lastLowPhoneticAddr,charBuff,&char_draw);
					}
				}
				else
					uiWinDrawChar(destAddr,charBuff,&char_draw);
				lastSpecial = special;
				if(special == 0)
				{
					lastAddr  = destAddr;

					destAddr = uiWinDrawNoRotateAddrReCal(destAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					if(secSpecialAddr)
					{
						lastSecSpecialAddr 	= secSpecialAddr;
						secSpecialAddr 		= uiWinDrawNoRotateAddrReCal(secSpecialAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					}
					if(lowPhoneticAddr)
					{
						lastLowPhoneticAddr = lowPhoneticAddr;
						lowPhoneticAddr 	= uiWinDrawNoRotateAddrReCal(lowPhoneticAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
					}
				}
				if(width > resRect.x1)
				{
					if(i < strNum-1)
					{
						res_GetCharInfor(string_draw->id, i+1, NULL, NULL,string_draw->font,&special);
						if(special)
							continue;
					}
					break;
				}
			}
		}
	}
		
}
