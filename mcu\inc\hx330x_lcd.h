/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef HX330X_LCD_H
#define HX330X_LCD_H

#define  LCD_BUS_RGB         	0   // rgb ,srgb,prgb
#define  LCD_BUS_MCU         	1   // i80-mcu-cpu
#define  LCD_BUS_SPI         	2   // spi

#define  LCD_IRQ_TE       		20   //TE signal interrupt enable
#define  LCD_IRQ_DEND    		19   //Data end interrupt enable
#define  LCD_IRQ_LEND    		18   //Line end interrupt enable
#define  LCD_IRQ_FEND    		17   //Frame end interrupt enable
#define  LCD_IRQ_FSRT    		16	 //Frame start interrupt enable


/*******************************************************************************
* Function Name  : hx330x_lcdReset
* Description    : lcd reset
* Input          : NONE
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdReset(void);
/*******************************************************************************
* Function Name  : hx330x_lcdSPIMode
* Description    : lcd spi mode set
* Input          : u8 cpol :cpol 0/1
*                  u8 cpha : cpha 0/1
*                  u8 order: 0/1
*                  u8 bits :
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSPIMode(u8 cpol,u8 cpha,u8 order,u8 bits);
/*******************************************************************************
* Function Name  : hx330x_lcdRGBIOConfig
* Description    : lcd io configure
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdRGBIOConfig(u8 pos, void * lcd_desc);
/*******************************************************************************
* Function Name  : hx330x_lcdMCUIOConfig
* Description    : lcd io configure
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMCUIOConfig(u8 pos, void * lcd_desc);
/*******************************************************************************
* Function Name  : hx330x_lcdSPIInit
* Description    : lcd spi initial GPIO
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSPIInit(void);
/*******************************************************************************
* Function Name  : hx330x_lcdSPIUninit
* Description    : lcd spi uninitial release GPIO
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSPIUninit(void);
/*******************************************************************************
* Function Name  : hx330x_lcdSPISendData
* Description    : lcd spi send data
* Input          : u32 data : data
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSPISendData(u32 data);
/*******************************************************************************
* Function Name  : hx330x_lcdMcuSendCmd
* Description    : lcd i80 send command
* Input          : u32 cmd : cmdcommand
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMcuSendCmd(u32 cmd);
/*******************************************************************************
* Function Name  : hx330x_lcdMcuSendData
* Description    : lcd i80 send data
* Input          : u32 data : data
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMcuSendData(u32 data);
/*******************************************************************************
* Function Name  : hx330x_lcdMcuSendCmd16
* Description    : lcd i80 send command
* Input          : u32 cmd : cmdcommand
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMcuSendCmd16(u32 cmd);
/*******************************************************************************
* Function Name  : hx330x_lcdMcuSendData
* Description    : lcd i80 send data
* Input          : u32 data : data
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMcuSendData16(u32 data);
/*******************************************************************************
* Function Name  : hx330x_lcdInit
* Description    : lcd initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdInit(void);
/*******************************************************************************
* Function Name  : hx330x_lcdIRQEnable
* Description    : lcd IRQ enable,TE_IRQ table
* Input          : u8 itype : irq ttype
*                  u8 en    : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdIRQEnable(u8 itype,u8 en);
/*******************************************************************************
* Function Name  : hx330x_lcdPreLineSet
* Description    : lcd pre line conifg,for DE get ready
* Input          : u32 line : pre line
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdPreLineSet(u32 line);
/*******************************************************************************
* Function Name  : hx330x_lcdSignalSet
* Description    : lcd signal set for rgb mode
* Input          : u8 vsync : vsync inactive
*                  u8 inv_pclk : pclk invactive 1->inv,0->normarl
*                  u8 inv_de   : data enable invactive
*                  u8 inv_hs   : hsync invactive
*                  u8 inv_vs   : vsync invactive
*                  u8 en_de    : data en sigable enable
*                  u8 en_hs    : hsync signal enable
*                  u8 en_vs    : vcyns signal enable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSignalSet(u8 vsync,u8 inv_pclk,u8 inv_de,u8 inv_hs,u8 inv_vs,u8 en_de,u8 en_hs,u8 en_vs);
/*******************************************************************************
* Function Name  : hx330x_lcdBusWidth
* Description    : lcd bus width set
* Input          : u32 width :
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdBusWidth(u32 width);
/*******************************************************************************
* Function Name  : hx330x_lcdBusEnable
* Description    : lcd bus enable
* Input          : u32 mask : mask word
				   u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdBusEnable(u32 mask,u8 en);
/*******************************************************************************
* Function Name  : hx330x_lcdClkSet
* Description    : lcd clk set
* Input          : u32 width :
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdClkSet(u32 clk_div);
/*******************************************************************************
* Function Name  : hx330x_lcdSyncSet
* Description    : lcd sync signal info
* Input          : u32 hsfp : hsync front phase
*                  u32 hsbp : hsync back phase
*                  u32 vsfp : vsync front phase
*                  u32 hsbp : vsync back phase
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdSyncSet(u32 hsfp,u32 hsbp,u32 vsfp,u32 vsbp);
/*******************************************************************************
* Function Name  : hx330x_lcdDESignalSet
* Description    : lcd data enable signal info
* Input          : u32 defp : data front phase
*                  u32 debp : data back phase
*                  u32 destart : data start
*                  u32 deend : data end
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdDESignalSet(u32 defp,u32 debp,u32 destart,u32 deend);
/*******************************************************************************
* Function Name  : hx330x_lcdPositionSet
* Description    : lcd position
* Input          : u32 x : x
*                  u32 y : y
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdPositionSet(u32 x,u32 y);
/*******************************************************************************
* Function Name  : hx330x_lcdResolutionSet
* Description    : lcd reslution
* Input          : u32 width : width
*                  u32 height : height
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdResolutionSet(u32 width,u32 height);
/*******************************************************************************
* Function Name  : hx330x_lcdWindowSizeSet
* Description    : lcd window size
* Input          : u32 width : width
*                  u32 height : height
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdWindowSizeSet(u32 width,u32 height);
/*******************************************************************************
* Function Name  : hx330x_lcdDataModeSet
* Description    : lcd color coding,fifo mode,shfit
* Input          : u32 mode : mode
*                  u32 mode : mode1
*                  u8 odd_order : RGB order,line 1,3,5...
*                  u8 even_order: RGB order,line 0,2,4...
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdDataModeSet(u32 mode,u32 mode1,u8 even_order,u8 odd_order);
/*******************************************************************************
* Function Name  : hx330x_lcdClkNumberSet
* Description    : lcd total pixel set
* Input          : u32 num : pixels
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdClkNumberSet(u32 num);
/*******************************************************************************
* Function Name  : hx330x_lcdEndLineSet
* Description    : lcd enable line
* Input          : u32 eline : end line
*                  u32 aline : active line
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdEndLineSet(u32 eline,u32 aline);
/*******************************************************************************
* Function Name  : hx330x_lcdPanelMode
* Description    : lcd interface type
* Input          : u8 panel : 0-RGB,1-MCU
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdPanelMode(u8 panel);
/*******************************************************************************
* Function Name  : hx330x_lcdEnable
* Description    : lcd enable
* Input          : u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_lcdTeMode
* Description    : lcd TE MODE
* Input          : u8 mode : mode
*                : u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdTeMode(u8 mode,u8 en);
/*******************************************************************************
* Function Name  : hx330x_lcdTeCheck
* Description    : check lcd te ready
* Input          : u32 timeout
* Output         : 0 : te not ready
* Return         : None
*******************************************************************************/
bool hx330x_lcdTeCheck(u32 timeout);
/*******************************************************************************
* Function Name  : hx330x_lcdKick
* Description    : lcd start frame show once
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdKick(void);
/*******************************************************************************
* Function Name  : hx330x_lcdIRQHandler
* Description    : lcd IRQ Handler
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_lcdISRRegister
* Description    : lcd isr register
* Input          : u8 type : irq .LCD_IRQ_FSRT~LCD_IRQ_TE
				   void (*isr)(void) : irq callback
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdISRRegister(u8 type,void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_lcdMCUTimimgInit
* Description    :
* Input          : lcddev_t * lcd_desc
*
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdMCUTimimgInit(void * lcd_desc);
/*******************************************************************************
* Function Name  : hx330x_lcdRGBTimimgInit
* Description    :
* Input          : lcddev_t * lcd_desc
*
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdRGBTimimgInit(void * lcd_desc);

#endif
