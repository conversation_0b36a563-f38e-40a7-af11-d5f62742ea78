/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

typedef struct TASK_COMMON_PARA_S{
	u32 sreen_mtime;
	u32 poweroff_mtime;
	u32 ir_led_cnt;
	u32 sdc_check_time;
	u32 sdc_scan_flag;		//BIT(1):scan fs, BIT(0):wait stable
	u32 bat_check_cnt;
	u32 usbhost_check_time;
	u32 tp_check_time;
	u32 tp_check_interval;
	u32 tp_quickResponse;
	u32 tp_moveth;
}TASK_COMMON_PARA_T;

ALIGNED(4) static TASK_COMMON_PARA_T tComPara;
/*******************************************************************************
* Function Name  : task_com_para_init
* Description    : task com para init
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_para_init(void)
{
	tComPara.sreen_mtime 		= XOSTimeGet();
	tComPara.poweroff_mtime 	= XOSTimeGet();
	tComPara.ir_led_cnt			= 100;
	tComPara.sdc_check_time 	= 0;
	tComPara.sdc_scan_flag  	= 0;
	tComPara.bat_check_cnt		= 0;
	tComPara.usbhost_check_time = 0;
	tComPara.tp_check_time      = 0;
	tComPara.tp_check_interval  = 20;
	tComPara.tp_quickResponse   = 0;
	tComPara.tp_moveth          = (tComPara.tp_quickResponse)?100:8;
}
/*******************************************************************************
* Function Name  : task_com_key_check
* Description    : key check value
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_key_check(void)
{
	if(SysCtrl.dev_fd_key < 0)
		return;
	int adcValue;
	if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_AD_READ, (INT32U )&adcValue)>=0)
	{
		if(adcValue) 
		{
			if(SysCtrl.dev_fd_lcd >= 0 && SysCtrl.dev_stat_lcd == 0 && KEY_EVENT_POWER != getType(adcValue))
			{
				task_com_sreen_check(SREEN_RESET_AUTOOFF);
			}else{
				XMsgQPost(SysCtrl.sysQ,(void*)adcValue);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usbhost_set(u32 stat)
{
	int ret;
	if(SysCtrl.dev_husb_stat != stat)
	{
		switch(stat)
		{
			case USBHOST_STAT_NULL: 
			case USBHOST_STAT_OUT: 
				dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_POWER_CTRL, HUSB_POWER_OFF);
				SysCtrl.dev_husb_ch	  = USBNONE_CH;
				//SysCtrl.rec_show_time = 0;
				//deg_Printf("11111111\n");
				if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW || SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN)
				{
					XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_NULL));
					SysCtrl.rec_show_time = 0;
					deg_Printf("[COM] usbhost out:%d\n",stat);
				}
				break;	
			case USBHOST_STAT_WAIT_STABLE:
				//deg_Printf("[COM] USB HOST WAIT STABLE\n");
				break;
			case USBHOST_STAT_PWR_ON:
				dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_POWER_CTRL, HUSB_POWER_ON);
				break;
			case USBHOST_STAT_IN:
				ret = dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_TYPE_CHECK, 0);
				if(ret > 0)
				{
					dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_INIT, ret);
					stat = USBHOST_STAT_IN;
					SysCtrl.dev_husb_ch = ret;
					deg_Printf("[COM] USBHOST IN:%d\n",SysCtrl.dev_husb_ch);
					XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_IN));
					if(hardware_setup.usb_host_double_bond  && SysCtrl.dev_dusb_stat >= USBDEV_STAT_DEVIN)
					{
						SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					}

				}else
				{
					dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_POWER_CTRL, HUSB_POWER_OFF);
					stat = USBHOST_STAT_NULL;
					SysCtrl.dev_husb_ch	  = USBNONE_CH;
				}			
				break;
			case USBHOST_STAT_SHOW:
				SysCtrl.rec_show_time = 0;
				deg_Printf("[COM] USBHOST SHOW ON:%d\n",SysCtrl.dev_husb_ch);
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_SHOW));
				break;
			case USBHOST_STAT_ASTERN:
				deg_Printf("[COM] USBHOST ASTERN ON:%d\n",SysCtrl.dev_husb_ch);
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_ASTERN));
				break;
			default:break;
		}
		SysCtrl.dev_husb_stat = stat;
		
	}
	tComPara.usbhost_check_time = XOSTimeGet();
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbhost_check(void)
{
	//static u32 lastTime = 0;
	//if((XOSTimeGet()-lastTime) <= 200)
	//	return ;

	//lastTime = XOSTimeGet();

	u32 temp = 0;
	int ret = dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_DET_CHECK, (u32)&temp);
	if(ret >= 0)
	{
		//deg_Printf("[USB HOST]SysCtrl.dev_husb_stat:%d, temp:%d\n",SysCtrl.dev_husb_stat, temp);
		if((SysCtrl.dev_husb_stat == USBHOST_STAT_OUT) || ((temp == 0)&& (SysCtrl.dev_husb_stat != USBHOST_STAT_NULL)))
		{
			if(SysCtrl.dev_husb_stat == USBHOST_STAT_OUT)
			{
				//SysCtrl.dev_husb_stat = USBHOST_STAT_MAX;
				task_com_usbhost_set(USBHOST_STAT_OUT);
			}else
			{
				//deg_Printf("SysCtrl.dev_husb_stat:%d\n",SysCtrl.dev_husb_stat);
				task_com_usbhost_set(USBHOST_STAT_NULL);
			}
		}else if(temp > 0) //HUSB DET
		{
			if(SysCtrl.dev_husb_stat < USBHOST_STAT_IN)
			{
				if(tComPara.usbhost_check_time == 0)	//power on det
				{
					task_com_usbhost_set(USBHOST_STAT_PWR_ON);
					XOSTimeDly(200);
					task_com_usbhost_set(USBHOST_STAT_IN);
				}else
				{
					switch(SysCtrl.dev_husb_stat)
					{
						case USBHOST_STAT_NULL: task_com_usbhost_set(USBHOST_STAT_WAIT_STABLE); return;
						case USBHOST_STAT_WAIT_STABLE: 
							if(tComPara.usbhost_check_time + 100 > XOSTimeGet())
								return;	
							task_com_usbhost_set(USBHOST_STAT_PWR_ON); return;
						case USBHOST_STAT_PWR_ON: 
							if(tComPara.usbhost_check_time + 100 > XOSTimeGet())
								return;	
							task_com_usbhost_set(USBHOST_STAT_IN); return;
					}	
				}
			}else
			{
				husb_api_usensor_linkingLcd();	
				u8 dev_sta = husb_api_devicesta(SysCtrl.dev_husb_ch);
				//if(dev_sta)
					//deg_Printf("dev_sta:%x\n",dev_sta);
				if(dev_sta == USB_NONE)
				{
					//deg_Printf("dev_sta:%x\n",dev_sta);
					task_com_usbhost_set(USBHOST_STAT_NULL);
				}else if((dev_sta & (USB_UVC_TRAN|USB_ASTERN)) == USB_UVC_TRAN)
				{
					task_com_usbhost_set(USBHOST_STAT_SHOW);
				}else if((dev_sta & (USB_UVC_TRAN|USB_ASTERN)) == (USB_UVC_TRAN|USB_ASTERN))
				{
					task_com_usbhost_set(USBHOST_STAT_ASTERN);
				}			
			}
		}
				
	}
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_set(u32 stat)
{
	if(SysCtrl.dev_dusb_stat != stat)
	{
		u32 temp = 0;
		SysCtrl.dev_dusb_stat = stat;
		switch(stat)
		{
			case USBDEV_STAT_NULL: 
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 0); //set offline
				tComPara.bat_check_cnt 	  = 5;// wait stable
				SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_NULL));
				deg_Printf("[COM] usbdev out\n");
				break;
			case USBDEV_STAT_DCIN:
				//deg_Printf("[COM] usbdev %d\n",SysCtrl.dev_husb_stat);
				SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
				deg_Printf("[COM] usbdev in\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DCIN));
				break;
			case USBDEV_STAT_DEVIN_CHECK:
				if(SysCtrl.dev_dusb_out == 0 && (hardware_setup.usb_host_double_bond == 0 || SysCtrl.dev_husb_stat == USBHOST_STAT_NULL ))
				{
					dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_HW_CON_CHECK, (u32)&temp);
					if(temp)
					{
						SysCtrl.dev_dusb_stat = USBDEV_STAT_DEVIN;
						if(hardware_setup.usb_host_double_bond)
							task_com_usbhost_set(USBHOST_STAT_OUT);
						dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_INIT, USB_DEVTYPE_MSC);
						XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DEVIN));
					}else
					{
                        SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					}
				}else
				{
					SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
				}

				break;
			case USBDEV_STAT_PC:
				SysCtrl.dev_dusb_stat = USBDEV_STAT_PC;
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 1); //set online
				deg_Printf("[COM] usbdev PC\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_PC));
				break;
			default: return;
		}
		//SysCtrl.dev_dusb_stat = stat;
		
	}
}
/*******************************************************************************
* Function Name  : task_com_usbdev_check
* Description    : task_com_usbdev_check and bat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_check(void)
{
	int ret, temp;
	//--------------------usb detect------------------------
	ret = dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
	if(ret>=0)
    {
		//deg_Printf("[USB DEV] SysCtrl.dev_dusb_stat:%d, %d\n", SysCtrl.dev_dusb_stat, SysCtrl.dev_husb_stat);
		if((temp == 0) && (SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)) // dc out
		{
			task_com_usbdev_set(USBDEV_STAT_NULL);
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)) // dc in
		{
			task_com_usbdev_set(USBDEV_STAT_DCIN);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_DCIN)) // dc in
		{
			//deg_Printf("temp:%d,SysCtrl.dev_dusb_stat:%d, %d\n",temp,SysCtrl.dev_dusb_stat, SysCtrl.dev_husb_stat);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else 
		{
			temp = 0;
			dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_SW_CON_CHECK, (INT32U)&temp);
			if((SysCtrl.dev_dusb_stat == USBDEV_STAT_DEVIN) && temp )
			{	
				task_com_usbdev_set(USBDEV_STAT_PC);
			}
		}
    }
//----------------------battery detect---------------------------------------	
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) // only dc out check battery
	{
	    ret = dev_ioctrl(SysCtrl.dev_fd_battery,DEV_BATTERY_READ,(INT32U)&temp);  
		if(ret>=0)
		{
			if(SysCtrl.dev_stat_battery != temp && tComPara.bat_check_cnt == 0) // need battery stable
				tComPara.bat_check_cnt = 5;//  3;
			else if(SysCtrl.dev_stat_battery == temp)
			{
				tComPara.bat_check_cnt = 0;
				return ; // no need update
			}
			if(tComPara.bat_check_cnt >0)
				tComPara.bat_check_cnt--;
            
			if(tComPara.bat_check_cnt == 0)
			{
				
				if(temp == 0){
					SysCtrl.low_power_tips = 1;
					//app_taskStart(TASK_POWER_OFF,0);
				}
				if(SysCtrl.dev_stat_battery != BATTERY_STAT_MAX)		
				{
					//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) //不接DC，电池只下降
					{
						if(SysCtrl.dev_stat_battery <= temp)
						{
							return;
						}
					}
					//else //充电时，电池只上升
					//{
					//	if(SysCtrl.dev_stat_battery >= temp)
					//	{
					//		return;		
					//	}
					//}
				}
				SysCtrl.dev_stat_battery = temp;
								
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_BAT,SysCtrl.dev_stat_battery));
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usb_dev_out(u32 out)
{
	SysCtrl.dev_dusb_out = out;
	if(out)
	{
		if(SysCtrl.dev_dusb_stat > USBDEV_STAT_DCIN)
		{
			SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
		} 
	}
}
/*******************************************************************************
* Function Name  : task_com_spijpg_Init
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : 
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_spijpg_Init(u32 unit_force)
{
#if TASK_SCAN_FILE_EVERY_TIME
	if(unit_force || SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		if(SysCtrl.spi_jpg_list >= 0)
		{
			nv_jpg_uinit();
			filelist_api_nodedestory(SysCtrl.spi_jpg_list);
			SysCtrl.spi_jpg_list = -1;
		}
	}else
	{
		if(SysCtrl.spi_jpg_list < 0)
		{
			nv_jpg_init();
			SysCtrl.spi_jpg_list   = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
		}
	}
#endif
}
/*******************************************************************************
* Function Name  : task_com_sdlist_scan
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : u8 unit_force: 1: \
				   u8 type: 0: jpg, 1:avi, 2: jpg + avi
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_sdlist_scan(u8 unit_force, u8 type)
{
#if TASK_SCAN_FILE_EVERY_TIME
	u8 create;
	if(unit_force || (SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && SysCtrl.dev_stat_sdc != SDC_STAT_FULL))
	{
		create = 0;
	}else
	{
		create = 1;
	}
	if(create)
	{
		if(SysCtrl.avi_list < 0) 
		{
			int list;
			if(type == 1)
			{
				list = SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
			}else if(type == 0)
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG, -1);
			}else
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,   -1);
				SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,SysCtrl.jpg_list);
				
			}
			filelist_api_scan(list);
		}	

	}else
	{
		filelist_api_nodedestory(SysCtrl.avi_list);
		filelist_api_nodedestory(SysCtrl.jpg_list);
		SysCtrl.avi_list = -1;
		SysCtrl.jpg_list = -1;
	}
#endif
}

/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sdc_stat_set(u32 stat)
{
	if(SysCtrl.dev_stat_sdc != stat) 
	{
		SysCtrl.dev_stat_sdc = stat;	
		tComPara.sdc_check_time = XOSTimeGet();
		switch(stat)
		{
			case SDC_STAT_NULL: 
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NULL));
				deg_Printf("[COM] : sdc out\n");
				break;
			case SDC_STAT_UNSTABLE:
				deg_Printf("[COM] : sdc wait stable\n");
				break;
			case SDC_STAT_IN:
				deg_Printf("[COM] : sdc in\n");
				break;
			case SDC_STAT_ERROR:
				deg_Printf("[COM] : sdc error\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_ERROR));
				break;
			case SDC_STAT_FULL:
				deg_Printf("[COM] : sdc full\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
				break;
			case SDC_STAT_NORMAL:
				deg_Printf("[COM] : sdc normal\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NORMAL));
				break;
			default: break;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_fs_scan
* Description    : fs scan
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_fs_scan(void)
{
	deg_Printf("[COM] : fs mount start.%d\n",XOSTimeGet());
	
	char string[16];
	
#if FUN_AUDIO_RECORD_EN
	hx330x_str_cpy(string, FILEDIR_WAV);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);
#endif
#if 0//FUN_MP3_PLAY_EN
	hx330x_str_cpy(string, FILEDIR_MP3);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);//FILEDIR_AUDIO);
#endif

#if 0//FUN_NES_GAME_EN
	hx330x_str_cpy(string, FILEDIR_NES);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);//FILEDIR_AUDIO);
#endif
#if USER_USE_USB_HOST
	hx330x_str_cpy(string, FILEDIR_RECA);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);

	hx330x_str_cpy(string, FILEDIR_RECB);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);
#else
	hx330x_str_cpy(string, FILEDIR_REC);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);
#endif

	hx330x_str_cpy(string, FILEDIR_IMG);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);

	sd_api_Stop();

	SysCtrl.fs_clustsize = fs_getclustersize();
	deg_Printf("[COM] : fs cluster size.%d B\n",SysCtrl.fs_clustsize);
	if(SysCtrl.spi_jpg_list >= 0)
	{
	#if TASK_SCAN_FILE_EVERY_TIME	
		nv_jpg_uinit();
	#endif
		filelist_api_nodedestory(SysCtrl.spi_jpg_list);
		SysCtrl.spi_jpg_list = -1;
	}
#if TASK_SCAN_FILE_EVERY_TIME == 0	
	if(SysCtrl.avi_list < 0) // scan file list
	{
	#if USER_USE_USB_HOST	
		SysCtrl.avi_list 	= SysCtrl.avia_list = filelist_api_nodecreate(FILEDIR_RECA,FILELIST_TYPE_AVI,-1);
		SysCtrl.avib_list 	= filelist_api_nodecreate(FILEDIR_RECB, FILELIST_TYPE_AVI|FILELIST_FLAG_AB, SysCtrl.avi_list);
	#else
		SysCtrl.avi_list 	= SysCtrl.avia_list = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
		SysCtrl.avib_list 	= -1;
	#endif
		SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,                  SysCtrl.avi_list);
		filelist_api_scan(SysCtrl.avi_list);
	}
#endif
#if FUN_AUDIO_RECORD_EN
	if(SysCtrl.wav_list<0)
    {
		SysCtrl.wav_list = filelist_api_nodecreate(FILEDIR_WAV, FILELIST_TYPE_WAV,-1);
		filelist_api_scan(SysCtrl.wav_list);
    }
#endif
	task_com_sdc_stat_set(SDC_STAT_NORMAL);
	task_com_sdc_freesize_check();
	//task_video_record_caltime();

	sd_api_unlock();
		
}

/*******************************************************************************
* Function Name  : task_com_sdc_check
* Description    : sd card stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_sdc_check(void)
{
	int temp,res;
    if(SysCtrl.dev_fd_sdc < 0)
		return;
	if(tComPara.sdc_scan_flag & BIT(1))
	{
		task_com_fs_scan();
		tComPara.sdc_scan_flag &= ~BIT(1);
	}
	dev_ioctrl(SysCtrl.dev_fd_sdc, DEV_SDCARD_READ, (INT32U)&temp);  //temp: 1 - sd online or lock, 0 - sd  offline 
	if(SysCtrl.dev_stat_sdc  <= SDC_STAT_UNSTABLE)
	{
		if(sd_api_init(hardware_setup.sdcard_bus_width)>=0)
		{
			if(!(tComPara.sdc_scan_flag & BIT(0)))
			{
				task_com_sdc_stat_set(SDC_STAT_IN);	
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
			{
				task_com_sdc_stat_set(SDC_STAT_UNSTABLE);
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_UNSTABLE)
			{
				if(tComPara.sdc_check_time + 500 > XOSTimeGet())// 500 ms,wait sdc stable
					return;
				task_com_sdc_stat_set(SDC_STAT_IN);	
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_IN)
			{
				res = fs_mount();
				if(res!=FR_OK)
				{
					//sd_api_CardState_Set(SDC_STATE_NULL);
                    if(hardware_setup.sdcard_bus_width != SD_BUS_WIDTH1)
                    {
						sd_api_CardState_Set(SDC_STATE_NULL);
                        if(sd_api_init(SD_BUS_WIDTH1)>=0) // sdc intial 1line
                        {
                            res = fs_mount();
                        }
                    }
				}
				if(res<0)
				{
					task_com_sdc_stat_set(SDC_STAT_ERROR);	
				}
				else
				{
					tComPara.sdc_scan_flag |= BIT(1);
				}	
			}
			temp = 1;
		}
	}
	if(temp == 0) // no sdc dectcted
	{
		if(SysCtrl.dev_stat_sdc >= SDC_STAT_ERROR && SysCtrl.dev_stat_sdc <= SDC_STAT_NORMAL)
		{
			fs_nodeinit();  // initial fs node
			filelist_api_nodedestory(SysCtrl.avi_list);
			filelist_api_nodedestory(SysCtrl.avia_list);
			filelist_api_nodedestory(SysCtrl.avib_list);
			filelist_api_nodedestory(SysCtrl.jpg_list);
		#if (FUN_AUDIO_RECORD_EN == 1)
			filelist_api_nodedestory(SysCtrl.wav_list);
		#endif
		#if 0//(FUN_MP3_PLAY_EN == 1)
			filelist_api_nodedestory(SysCtrl.mp3_list);
		#endif
		#if 0//(FUN_NES_GAME_EN == 1)
			filelist_api_nodedestory(SysCtrl.nes_list);
		#endif
			SysCtrl.avi_list 	= -1;
			SysCtrl.avia_list 	= -1;
			SysCtrl.avib_list 	= -1;
			SysCtrl.jpg_list 	= -1;
			SysCtrl.wav_list 	= -1;
			SysCtrl.mp3_list	= -1;
			SysCtrl.nes_list	= -1;
		#if TASK_SCAN_FILE_EVERY_TIME == 0
			SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
		#endif
			task_com_sdc_stat_set(SDC_STAT_NULL);
			task_com_sdc_freesize_check();
		}	
			
		tComPara.sdc_scan_flag |= BIT(0);
	}
	
}
/*******************************************************************************
* Function Name  : task_common_gsensor_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_common_gsensor_check(void) // 10 ms
{
	int temp,ret;
	if((app_taskCurId() != TASK_RECORD_VIDEO))
		return;
	if((SysCtrl.dev_fd_gsensor > 0) && user_configValue2Int(CONFIG_ID_GSENSOR))
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)	// recording will check Gsensor
		{
			ret = dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_LOCK_READ,(INT32U)&temp);
			if((ret >=0) && temp > 0)
			{
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_LOCK));
				deg_Printf("[COM].gsensor active\n");
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_lcdbk_set
* Description    : lcd sreen on/off
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcdbk_set(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0)
		return;
	if(SysCtrl.dev_stat_lcd != on)
	{
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_WRITE,on);   // screen on
		SysCtrl.dev_stat_lcd = on;
	}
	tComPara.sreen_mtime = XOSTimeGet();  //save sreen on time
}
/*******************************************************************************
* Function Name  : task_com_sreen_save
* Description    : screen save check
* Input          : u32 on: COM_SREEN_OP 0x00: check auto off, 0x01: reset auto off, 0x02:sreen off, 0x03 sreen on
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sreen_check(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0 || app_taskCurId() == TASK_POWER_OFF)
		return;
	//deg_Printf("task_com_sreen_check:%d\n", on);
	if(SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN || (on == SREEN_RESET_AUTOOFF)||(on == SREEN_SET_ON) ) //0x01: reset auto off, 0x03 sreen on
	{
		task_com_lcdbk_set(1);
	}else if(on == SREEN_SET_OFF)   //0x02:sreen off
	{
		task_com_lcdbk_set(0);
	}else //0x00: check auto off SREEN_CHECK_AUTOOFF
	{
		u32 sreensave = user_configValue2Int(CONFIG_ID_SCREENSAVE)*1000;
		if(sreensave != 0)
		{
			if(tComPara.sreen_mtime + sreensave < XOSTimeGet())
			{
				//deg_Printf("tComPara.sreen_mtime:%d,%d,%d\n",tComPara.sreen_mtime,XOSTimeGet(),sreensave);
				task_com_lcdbk_set(0);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_auto_poweroff
* Description    : system auto power off check
* Input          : int reset : 1:reset auto poweroff, 0:no reset
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_auto_poweroff(int reset)
{
	if(app_taskCurId() == TASK_POWER_OFF)
		return;
	if(reset)
	{
		tComPara.poweroff_mtime = XOSTimeGet();
	}else
	{
		u32 autopowerofftime = user_configValue2Int(CONFIG_ID_AUTOOFF)*1000;
		if(autopowerofftime != 0)
		{
			if(tComPara.poweroff_mtime + autopowerofftime <= XOSTimeGet())
			{
				deg_Printf("[COM]Auto power off\n");
				app_taskStart(TASK_POWER_OFF,0);
				tComPara.poweroff_mtime = XOSTimeGet();
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_keysound_play
* Description    : task_com_keysound_play
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_keysound_play(void)
{
	if( (SysCtrl.dev_stat_keysound == 0) || // key sound off
		(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)||// video/audio playing
		(app_taskCurId()  == TASK_POWER_OFF))
	{
		return;
	}
	res_keysound_play();		
}
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_check
* Description    : com get fs free size
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_check(void)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		SysCtrl.sdc_freesize = fs_free_size()>>1;  // kb
		if(SysCtrl.sdc_freesize < 32)
		{
			SysCtrl.sdc_freesize  = 0;
			SysCtrl.dev_stat_sdc = SDC_STAT_FULL; 
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
			deg_Printf("[COM] : sdc normal but free space is 0.set sdc error\n");  // user need format sdc
		}
	}
	else
		SysCtrl.sdc_freesize = 0;
	deg_Printf("[COM] : fs free size = %dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,(SysCtrl.sdc_freesize)&0x3ff);
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_modify
* Description    : com dec size from free size
* Input          : INT8S dec: >=0 add freesize, <0 minus freesize
*                  INT32U size : unit byte
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_modify(INT8S dec,INT32U size)
{
	if(size&(SysCtrl.fs_clustsize-1))
	{
		size = (size&(~(SysCtrl.fs_clustsize-1)))+SysCtrl.fs_clustsize;
		//size+=1024;
	}
	size>>=10;
	if(dec<0)
	{
		if(SysCtrl.sdc_freesize>size)
			SysCtrl.sdc_freesize-=size;
		else
			SysCtrl.sdc_freesize = 0;
	}
	else
	{
		SysCtrl.sdc_freesize+=size;
	}
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_ir_set
* Description    : task_com_ir_set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_ir_set(u32 on)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(SysCtrl.dev_stat_ir != on)
		{
			dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_WRITE,on);
			SysCtrl.dev_stat_ir = on;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_ir_auto_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_ir_auto_check(void)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(user_config_get(CONFIG_ID_IR_LED) != R_ID_STR_IR_AUTO)
			return;	
		if(hal_isp_br_get() < 0xa)		// need ir
		{
			task_com_ir_set(1);
		}	
		else if(hal_isp_br_get() > 0x30)
		{
			tComPara.ir_led_cnt--;
			if(tComPara.ir_led_cnt == 0)// wait for stable
			{
				task_com_ir_set(0);
				tComPara.ir_led_cnt =100;
			}
		}		
	}
}

/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_md_check(void)
{
	if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	{
		if(hal_mdCheck())
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_MD,1));
	}
}
/*******************************************************************************
* Function Name  : task_com_tp_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_tp_check(void)
{

	static u16 lastTpType  = TP_TYPE_NONE;
	TOUCHPANEL_INFO_T *tp_info;
	uiRect	rect;
	if(SysCtrl.dev_fd_tp < 0 || app_taskCurId() == TASK_POWER_OFF)
		return;
	if((XOSTimeGet() - tComPara.tp_check_time) <= tComPara.tp_check_interval)
		return ;
	tComPara.tp_check_time = XOSTimeGet();
	tp_info = (TOUCHPANEL_INFO_T*)dev_ioctrl(SysCtrl.dev_fd_tp, DEV_TOUCHPANEL_READ,(INT32U)tComPara.tp_moveth);
	if(tp_info == NULL)
		return;
	if(tp_info->tp_type == TP_TYPE_PRESS)
	{
		if(tComPara.tp_quickResponse && lastTpType != TP_TYPE_PRESS)
		{
			rect.x0 = (tp_info->x < 2) ? 0 : (tp_info->x - 1);
			rect.y0 = (tp_info->y < 2) ? 0 : (tp_info->y - 1);
			rect.x1 = ((tp_info->x + 1) >= USER_UI_WIDTH) ? (USER_UI_WIDTH - 1) : (tp_info->x + 1);
			rect.y1 = ((tp_info->y + 1) >= USER_UI_HEIGHT) ? (USER_UI_HEIGHT - 1) : (tp_info->y + 1);
			if(uiWinTouchProcess(&rect,TOUCH_PRESS))
			{
				if (!((app_taskCurId() == TASK_PLAY_AUDIO && audioPlaybackGetStatus() != MEDIA_STAT_STOP ) ||
					(app_taskCurId() == TASK_PLAY_VIDEO && videoPlaybackGetStatus() != MEDIA_STAT_STOP)))
				{
					task_com_keysound_play();
				}
			}
		}
		tComPara.tp_check_interval = 60;
	}else if(tp_info->tp_type == TP_TYPE_MOVE)
	{
		if(tp_info->tp_speed > 0 && tComPara.tp_check_interval != 60)
		{
			XMsgQPost(SysCtrl.sysQ,makeMSG(SYS_TOUCH_SLIDE_ON,tp_info->tp_dir));
		}
		if(tComPara.tp_check_interval == 60)
			tComPara.tp_check_interval = 140;
		else
			tComPara.tp_check_interval = 200;
	}else if(tp_info->tp_type == TP_TYPE_NONE)
	{
		if(lastTpType == TP_TYPE_PRESS)
		{
			if(tComPara.tp_quickResponse == 0)
			{
				rect.x0 = (tp_info->x < 2) ? 0 : (tp_info->x - 1);
				rect.y0 = (tp_info->y < 2) ? 0 : (tp_info->y - 1);
				rect.x1 = ((tp_info->x + 1) >= USER_UI_WIDTH) ? (USER_UI_WIDTH - 1) : (tp_info->x + 1);
				rect.y1 = ((tp_info->y + 1) >= USER_UI_HEIGHT) ? (USER_UI_HEIGHT - 1) : (tp_info->y + 1);
				if(uiWinTouchProcess(&rect,TOUCH_PRESS))
				{
					if (!((app_taskCurId() == TASK_PLAY_AUDIO && audioPlaybackGetStatus() != MEDIA_STAT_STOP ) ||
						(app_taskCurId() == TASK_PLAY_VIDEO && videoPlaybackGetStatus() != MEDIA_STAT_STOP)))
					{
						task_com_keysound_play();
					}
				}
				uiWinTouchProcess(NULL,TOUCH_RELEASE);
			}else
			{
				uiWinTouchProcess(NULL,TOUCH_RELEASE);
			}
		}else if(lastTpType == TP_TYPE_MOVE)
		{
			if(tComPara.tp_quickResponse)
			{
				uiWinTouchProcess(NULL,TOUCH_OVER);
			}
			if(tp_info->tp_speed > 0)
			{
				XMsgQPost(SysCtrl.sysQ,makeMSG(SYS_TOUCH_SLIDE_OFF,tp_info->tp_dir));
			}
		}
		if(videoRecordGetStatus() == MEDIA_STAT_START)
		{
			tComPara.tp_check_interval = 60;
		}else
		{
			tComPara.tp_check_interval = 30;
		}
	}
	lastTpType = tp_info->tp_type;
}
/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/

static void task_com_sec(void)
{
	static u8 lastSec		= 255;
	static uint32 lastTime 	= 0;
	HAL_RTC_T* rtcTime = hal_rtcTimeGet();
	if(lastSec != rtcTime->sec)
	{
		lastSec = rtcTime->sec;
		SysCtrl.powerOnTime++;
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_1S,0));

		res_iconBuffTimeUpdate();
	}
	if((XOSTimeGet()-lastTime)<=500)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_500MS,0));
}


static void task_com_100ms(void)
{
	static uint32 lastTime 	= 0;
	if((XOSTimeGet()-lastTime)<=100)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_100MS,0));

}




/*******************************************************************************
* Function Name  : task_com_powerOnTime_str
* Description    : task_com_powerOnTime_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_powerOnTime_str(void)
{
	u32 sec = SysCtrl.powerOnTime;
	static char powerOnTimeStr[]= "00:00";
	hx330x_num2str(powerOnTimeStr, sec/3600, 2);
	powerOnTimeStr[2] = ':';
	hx330x_num2str(&powerOnTimeStr[3], (sec%3600)/60, 2);
	
	return powerOnTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_show_time_str(void)
{
	u32 sec = SysCtrl.rec_show_time;
	/*static char recTimeStr[]="00:00:00";
	hx330x_num2str(recTimeStr, sec/3600, 2); //hour
	recTimeStr[2] = ':';
	hx330x_num2str(&recTimeStr[3], (sec%3600)/60, 2); //min
	recTimeStr[5] = ':';
	hx330x_num2str(&recTimeStr[6], sec%60, 2); //sec*/
	static char recTimeStr[]="00:00";
	//hx330x_num2str(recTimeStr, sec/3600, 2); //hour
	//recTimeStr[2] = ':';
	hx330x_num2str(recTimeStr, (sec%3600)/60, 2); //min
	recTimeStr[2] = ':';
	hx330x_num2str(&recTimeStr[3], sec%60, 2); //sec	
	
	return recTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_remain_time_str(void)
{
	static char recRemainTimeStr[]="00:00:00";
	app_taskRecordVideo_caltime();
	u32 sec = SysCtrl.rec_remain_time;
	hx330x_num2str(recRemainTimeStr, sec/3600, 2); //hour
	recRemainTimeStr[2] = ':';
	hx330x_num2str(&recRemainTimeStr[3], (sec%3600)/60, 2); //min
	recRemainTimeStr[5] = ':';
	hx330x_num2str(&recRemainTimeStr[6], sec%60, 2); //sec
	
	
	return recRemainTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_play_time_str
* Description    : task_com_play_time_str
* Input          : int sel: 1: total time, 0: cur play time
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_play_time_str(int sel)
{	
	char *str;
	static char playCurTimeStr[] = {"00:00:00"};
	static char playTotalTimeStr[] = {"00:00:00"};
	u32 sec;
	u32 playtime  = SysCtrl.play_cur_time/1000;
	u32 totaltime = SysCtrl.play_total_time;

	if(totaltime%1000 > 500)
		totaltime = totaltime/1000 + 1;
	else 
		totaltime = totaltime/1000;
	if(sel)
	{
		sec = totaltime;
		str = playTotalTimeStr;
	}
	else
	{
		sec = playtime;
		str = playCurTimeStr;
	}
	if(sec/3600)
	{
		hx330x_num2str(str, sec/3600, 2); //hour
		str[2] = ':';
		hx330x_num2str(&str[3], (sec%3600)/60, 2); //min
		str[5] = ':';
		hx330x_num2str(&str[6], sec%60, 2); //sec		
	}else
	{
		hx330x_num2str(str, (sec%3600)/60, 2); //min
		str[2] = ':';
		hx330x_num2str(&str[3], sec%60, 2); //sec		
	}


	return str;
}
/*******************************************************************************
/*******************************************************************************
* Function Name  : task_com_tips_show
* Description    : task_com_tips_show
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_tips_show(u32 type)
{
	u32 tips_id;
	switch(type)
	{
		case TIPS_TYPE_SD:
		{
			switch(SysCtrl.dev_stat_sdc)
			{
				case SDC_STAT_NULL:  tips_id = TIPS_SD_NOT_INSERT; break;
				case SDC_STAT_FULL:  tips_id = TIPS_SD_FULL;       break;
				case SDC_STAT_ERROR: tips_id = TIPS_SD_ERROR;	   break;
				default: return;
			}
			uiOpenWindow(&tips1Window,0,2,tips_id, 2);
			break;
		}
		case TIPS_TYPE_SPI:
		{
			uiOpenWindow(&tips1Window,0,2,TIPS_SPI_FULL, 2);
			break;
		}
		case TIPS_TYPE_POWER:
		{
			if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
			{
				if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
				{
					tips_id = TIPS_POWER_LOW;
				}else if(SysCtrl.dev_stat_battery == BATTERY_STAT_0)
				{
					tips_id = TIPS_NO_POWER;
				}else
				{
                    return;
				}
				uiOpenWindow(&tipsWindow,0,2,tips_id,4); //改为提示4秒，2秒感觉有点太快
			}
			break;
		}
		case TIPS_COM_WAITING: 	uiOpenWindow(&tipsWindow,1,2,R_ID_STR_COM_WAITING,5); break;
		case TIPS_COM_SUCCESS: 	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_SUCCESS,2); break;
		case TIPS_COM_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_FAILED,2);  break;
		case TIPS_NO_FILE: 		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FIL_NULL,2);	  break;
		case TIPS_SET_LOCKED:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_SET_LOCKED,2);  break;
		case TIPS_FMT_SUCCESS:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_SUCCESS,2); break;
		case TIPS_FMT_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_FAIL,2);	  break;
		case TIPS_ERROR:		uiOpenWindow(&tipsWindow,0,2,"ERROR",2);	  break;
		case TIPS_VERSION:		uiOpenWindow(&tipsWindow,0,2,SYSTEM_VERSION,5);	  break;
		
		default : break;
	}
}
/*******************************************************************************
* Function Name  : task_com_playscaler_rate
* Description    : task_com_playscaler_rate
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u32 task_com_playscaler_rate(void)
{
	return (((u32)100 - SysCtrl.play_scaler_level) * 100) / (100 - SysCtrl.play_scaler_level_max);
}
/*******************************************************************************
* Function Name  : task_com_service
* Description    : system service in task com
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_service(u32 scanKey)
{
	static u32 lastTime = 0;
	task_com_sec();
	task_com_100ms();
	hal_ispService();
		
	if((XOSTimeGet()-lastTime)<=10*X_TICK)
		return ;
		
	lastTime = XOSTimeGet();
//--------key check -------------------
	if(SysCtrl.dev_stat_power & POWERON_FLAG_KEY) //�ȴ�power key�ͷ�
	{
		u32 adcValue;
		if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_POWER_READ, (INT32U )&adcValue)>=0)
		{
			if(adcValue == 0)
			{
				SysCtrl.dev_stat_power &=~POWERON_FLAG_KEY;
			}
		}
		scanKey = 0;
	}else
	{
		if(scanKey)
			task_com_key_check();      // system key read
	}

//--------usb host check---------------------
#if USER_USE_USB_HOST
	task_com_usbhost_check();
#endif
	//--------usb dev check---------------------
	task_com_usbdev_check();	
    if(app_taskCurId() != TASK_POWER_OFF)
    {
		#if 1//FUNC_AF_ENABLE
		//task_common_af_adjust();	//这个算法在近的场景会变模糊
		task_common_af_adjust_simple();//这个算法精确	
		#endif	
//-------sdc card check-----------------    
	    task_com_sdc_check();  // sdc state check
//--------gsensor check-----------------
	    task_common_gsensor_check(); // gsensor state check
//--------sereen save check ------------	
		task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
//--------auto power off check----------
	    task_com_auto_poweroff(0); //auto power off check 
//--------ir auto check-------------
		task_com_ir_auto_check();
//--------motion dection check----------
		task_com_md_check();
		if(scanKey)
			task_com_tp_check();
    }
}


//static u8 pwm_value[]={0,10,40,70};
u8 pwm_value[]={10,10,30,40};
void task_com_LedPwm_ctrl_Add(){

		
		SysCtrl.led_pwm_level ++;
		if(SysCtrl.led_pwm_level>3)
		SysCtrl.led_pwm_level= 0;
		deg_Printf("[task_com_LedOnOffChange]111111111111111=%d  %d\n",SysCtrl.led_pwm_level,SysCtrl.led_onoff_sw);
		if(SysCtrl.led_pwm_level == 0)
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
		else
		{
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,pwm_value[SysCtrl.led_pwm_level]); 
		}



}

void task_com_LedPwm_ctrl_Dec(){	
		SysCtrl.led_pwm_level --;
		if(SysCtrl.led_pwm_level<0)
		SysCtrl.led_pwm_level= 3;
		if(SysCtrl.led_pwm_level == 0)
		{
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 

		}
		else
		{
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,pwm_value[SysCtrl.led_pwm_level]); 
		}
			

}

void task_com_LedOnOffChange()
{
		
		u32 status;
		//dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_READ,(INT32U)&status); 
		deg_Printf("[task_com_LedOnOffChange]status=%d\n",status);
		if(SysCtrl.led_onoff_sw == 1)
		{
			SysCtrl.led_onoff_sw = 2;
		//	dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_PWM_ADJUST, pwm_value[SysCtrl.led_pwm_level]);
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
		}else if(SysCtrl.led_onoff_sw == 2)
		{
			SysCtrl.led_onoff_sw = 1;
		//	dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_PWM_ADJUST, pwm_value[SysCtrl.led_pwm_level]);
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
		}

}

void task_com_led_on()
{
	
	SysCtrl.led_onoff_sw = 1;
	dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
	//SysCtrl.led_onoff_sw = 2;
	//dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,SysCtrl.led_onoff_sw); 
	dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_PWM_ADJUST, pwm_value[SysCtrl.led_pwm_level]);
}
void task_com_led_off()
{
	dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_PWM_ADJUST, pwm_value[SysCtrl.led_pwm_level]);
	dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
}

void task_com_tips_insert_sd_start()
{
	SysCtrl.tips_insert_sd_icon_count = 11;
	return;
}

void task_com_tips_insert_sd_clear()
{
	SysCtrl.tips_insert_sd_icon_count = 0;
	return;
}

#if 1//FUNC_AF_ENABLE
// unsigned char clarity_buf[480*272];
int clarity_statistics(unsigned char *img, unsigned short img_width, unsigned short img_height, unsigned short win_xs, unsigned short win_width, unsigned short win_ys, unsigned short win_height, unsigned char method, int *clarity)
{
	long long sum;
	int x, y, p;

	if(win_xs < 0 || ((win_xs + win_width) > img_width) ||  win_ys < 0 || ((win_ys + win_height) > img_height)){
		deg_Printf("boundary overflow!\n");
		return -1;
	}

	//for display
	// memcpy(clarity_buf, img, sizeof(clarity_buf));

	if(method == 0){	//eog	320x240:33.2ms 320x180:24.8ms 160x120:8.3ms 160x90:6.3ms 100x100:4.4ms 80x60:2.1ms
		int dx,dy;
		sum = 0;

		for(y = win_ys; y < win_ys + win_height - 1; y++){
			for(x = win_xs; x < win_xs + win_width - 1; x++){
				dx = (int)img[y * img_width + x + 1] - (int)img[y * img_width + x];
				dy = (int)img[(y  + 1) * img_width + x] - (int)img[y * img_width + x];
				p = dx * dx + dy * dy;
				sum +=p;

				//for display
				// int pix = p / 2;
				// if( pix > 255)
				// 	pix = 255;
				// clarity_buf[y * img_width + x] = 255 - pix;
			}
		}
		sum *= 100;
		sum /= (win_width - 1) * (win_height - 1);

		//deg_Printf("clarity eg = %d\n", sum);
	}
	else if(method == 1){ //laplace	320x240:30.8ms 320x180:23.0ms 160x120:7.7ms 160x90:5.6ms 100x100:4.0ms 80x60:1.9ms
		sum = 0;

		for(y = win_ys + 1; y < win_ys + win_height - 1; y++)
		{
			for(x = win_xs + 1; x < win_xs + win_width - 1; x++)
			{
				p = (int)img[(y - 1) * img_width + x] + \
					(int)img[y * img_width + x - 1] + \
					(int)img[y * img_width + x + 1] + \
					(int)img[(y + 1) * img_width + x] - \
					(int)(img[y * img_width + x] << 2);
				p *= p;
				sum +=p;

				//for display
				// int pix = p / 8;
				// if( pix > 255)
				// 	pix = 255;
				// clarity_buf[y * img_width + x] = 255 - pix;
			}
		}
		sum *= 100;
		sum /= (win_width - 2) * (win_height - 2);

		// hx_printk("clarity lp = %d\n", sum);
	}

	//for display
	// memcpy(img, clarity_buf, sizeof(clarity_buf));


	//deg_Printf("clarity 1 = %d\n", sum);

	if(sum > 2147483647LL)
		sum = 2147483647LL;
	else if(sum < -2147483648LL)
		sum = -2147483648LL;

	if(clarity){
		*clarity = (int)(sum);
		//deg_Printf("clarity 2 = %d\n", sum);
	}
	return 0;
}

static int clarity = 0;

enum{
	AF_MOVE_BACKWARD = 0,
	AF_MOVE_FORDWARD,
	AF_MOVE_STANDBY,
};



typedef struct{
	u32 vcm;// current adc value
	int clarity;// clarity of current dac voltage
	u32 time;//clarity calulate time
	u32 adjust_index;
}AF_POINT;

typedef struct{
	u8 adjust_flag;
	u8 adjust_motion;// 0:motion detect stop  1:motion detect start
	u8 adjust_start;
	u8 adjust_state;// 0:idle 1:getting buffer 2:got buffer 3:calculatting clarity 4:calculat done 5:adjust af moto 6:ajust done 7:back to idle
	u16 cur_index;
	u16 cur_offset;
	u16 cur_diretion;// 0:backward 1: forward
	u8* clarity_cal_buf;
	u32 adjust_time;
	AF_POINT clarity_most;// most clarity in current adjust,clear it after adjust
	AF_POINT left_point;
	AF_POINT cent_point;
	AF_POINT right_point;
}AF_ADJUST;

AF_ADJUST af_adapt =
{
	.adjust_flag = 0,// 0: 100 as step   1: 10 as step
	.adjust_motion = 0,
	.adjust_start = 0,
	.adjust_state = 0,
	.cur_index = 0,
	.cur_offset = 0,
	.cur_diretion = 0,
	.clarity_cal_buf = NULL,
	.adjust_time = 0,
	.clarity_most.vcm = 0,
	.clarity_most.clarity = 0,
	.clarity_most.time = 0,
	.left_point.vcm = 0,
	.left_point.clarity = 0,
	.left_point.time = 0,
	.cent_point.vcm = 0,
	.cent_point.clarity = 0,
	.cent_point.time = 0,
	.right_point.vcm = 0,
	.right_point.clarity = 0,
	.right_point.time = 0,
};

u8 * task_common_get_af_handler_buffer(void)
{
	return af_adapt.clarity_cal_buf;
}

u8 task_common_get_af_state(void)
{
	return af_adapt.adjust_state;
}

void task_common_set_af_state(u8 state)
{
	af_adapt.adjust_state = state;
}

u8 task_common_get_af_clarity(void)
{
	return clarity;
}

u8 task_common_find_best_clarity(u8 dir,int *clarity)
{
	static u32 last_clarity = 0;
	if(clarity > last_clarity){
		last_clarity = clarity;
	}
}

#define AF_DEG(...)      	uart_Printf(__VA_ARGS__)
#if 0 
void task_common_af_adjust(void)
{
    static AF_POINT mark_point[5];
	static u8 adjust_flag = 0;
	static int clarity_new = 0;
	static int clarity_b = 0;
	//static u8 buffer_save_flag = 0xff;
	static u32 handle_count = 0;
	static u32 vcm_a= 0;
	static u32 vcm_b = 0;
	static u32 vcm_next = 0;
	static u32 last_vcm = 0;

    u32 slope_left;
    u32 slope_right;

	AF_POINT temp_point;
	u32 temp;
 	u32 task_cur = app_taskCurId();

	u32 temp_time;
	static u32 triggle_time = 0;

	if(XOSTimeGet()<4000)
		return;

	if(TASK_RECORD_PHOTO != task_cur){
		if(TASK_RECORD_VIDEO!= task_cur){
			hal_sysMemFree(af_adapt.clarity_cal_buf);
			af_adapt.clarity_cal_buf = NULL;
			return;
		}
	}
	if(NULL == af_adapt.clarity_cal_buf){
		af_adapt.clarity_cal_buf = hal_sysMemMalloc(320*60);
	}

	if(NULL == af_adapt.clarity_cal_buf){
		return;
	}
//----------------------motion detect start--------------------------//
	if(hal_mdCheck()){
		//XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_MD,1));
		triggle_time = XOSTimeGet();
		af_adapt.adjust_motion = 1;

		af_adapt.adjust_state = AF_FLAG_IDLE;
		af_adapt.cur_index=0;
		af_adapt.left_point.time = 0;
		af_adapt.cent_point.time = 0;
		af_adapt.right_point.time = 0;
	}else{
		if(af_adapt.adjust_motion){
			temp_time = XOSTimeGet();
			if(temp_time - triggle_time > 300){
				if(AF_FLAG_IDLE == af_adapt.adjust_state){
					af_adapt.adjust_state = AF_FLAG_START;
				}
				af_adapt.adjust_motion = 0;
			}
		}
	}
//----------------------motion detect end--------------------------//

	if(AF_FLAG_START == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_GETTING_FIRST_BUFFER;
	}else if(AF_FLAG_GOT_FIRST_BUFFER == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_CALCULATTING_FIRST_BUFFER;
		hx330x_sysDcacheFlush(af_adapt.clarity_cal_buf,320*60);
		clarity_statistics(af_adapt.clarity_cal_buf,320,60,120,80,0,60,0,&clarity_new);
		af_adapt.adjust_state = AF_FLAG_CALCULATTD_FIRST_BUFFER;
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_READ, &temp);
		AF_DEG("111clarity_new = %d,cur_vcm=%d\n",clarity_new,temp);
		af_adapt.adjust_state = AF_FLAG_MOVE_AF1;

		if(0 == af_adapt.left_point.time){//第一次取清晰度，先移动至 0 位置
            //MARK();
			if(0 == af_adapt.cur_index){
                af_adapt.left_point.vcm			    = 0;
                af_adapt.left_point.clarity			= 0;
                af_adapt.left_point.time		    = 0;

                af_adapt.cent_point.vcm			    = 0;
                af_adapt.cent_point.clarity			= 0;
                af_adapt.cent_point.time		    = 0;

                af_adapt.right_point.vcm			= 0;
                af_adapt.right_point.clarity        = 0;
                af_adapt.right_point.time		    = 0;
                vcm_next = 100;
                af_adapt.cur_index =1;//
			}else{//移动过后取数据
			    //MARK();

                af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                af_adapt.left_point.clarity			= clarity_new;
                af_adapt.left_point.time			= XOSTimeGet();
                af_adapt.left_point.adjust_index	= af_adapt.cur_index;

                af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
                af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;
                af_adapt.clarity_most.time			= af_adapt.left_point.time;
                af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;
                af_adapt.cur_offset = 100;//第二个点固定+200
                vcm_next = af_adapt.left_point.vcm + af_adapt.cur_offset;
                af_adapt.left_point.adjust_index	= af_adapt.cur_index;
                af_adapt.cur_diretion = AF_MOVE_FORDWARD;
			}
			AF_DEG("%d---->L:%d,C:%d,R:%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
			dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);
		}else if(0 == af_adapt.cent_point.time){//取第二个点(vcm=200)的清晰度
			af_adapt.cent_point.vcm			    = temp;
			af_adapt.cent_point.clarity			= clarity_new;
			af_adapt.cent_point.time			= XOSTimeGet();
			af_adapt.cent_point.adjust_index	= af_adapt.cur_index;
			af_adapt.cur_offset = 200;//第三个点固定+200至400位置

            vcm_next = af_adapt.clarity_most.vcm + af_adapt.cur_offset;
            af_adapt.cur_diretion = AF_MOVE_FORDWARD;

			dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);
			AF_DEG("%d---->L:%d,C:%d,R:%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
		}else if(0 == af_adapt.right_point.time){//取第三个点(vcm=400)的清晰度
			af_adapt.right_point.vcm			    = temp;
			af_adapt.right_point.clarity		    = clarity_new;
			af_adapt.right_point.time			    = XOSTimeGet();
			af_adapt.right_point.adjust_index	    = af_adapt.cur_index;
			if(af_adapt.left_point.clarity < af_adapt.cent_point.clarity && af_adapt.cent_point.clarity < af_adapt.right_point.clarity){// vcm larger,clarity better
				af_adapt.clarity_most.clarity		= af_adapt.right_point.clarity;//最后一个点最清晰
				af_adapt.clarity_most.vcm			= af_adapt.right_point.vcm;
				af_adapt.clarity_most.time			= af_adapt.right_point.time;
				af_adapt.clarity_most.adjust_index	= af_adapt.cent_point.adjust_index;
				af_adapt.cur_offset = 200;
				af_adapt.cur_diretion = AF_MOVE_FORDWARD;//继续+200,直到最清晰位置
                vcm_next = af_adapt.clarity_most.vcm + af_adapt.cur_offset;
                dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);
                //获取两个点之间斜率,直到斜率最小(波峰)

                slope_left = (af_adapt.cent_point.clarity - af_adapt.left_point.clarity)/(af_adapt.cent_point.vcm - af_adapt.left_point.vcm);
                slope_right = (af_adapt.right_point.clarity - af_adapt.cent_point.clarity)/(af_adapt.right_point.vcm - af_adapt.cent_point.vcm);

                AF_DEG("%d---->L:%d,C:%d,R:%d,rate1=%d,rate2=%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm,\
                           slope_left,slope_right);
			}else if(af_adapt.left_point.clarity > af_adapt.cent_point.clarity && af_adapt.cent_point.clarity > af_adapt.right_point.clarity){// vcm smaller,clarity better
                af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;//最小VCM最清晰,往左边缩小范围
				af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
				af_adapt.clarity_most.time			= af_adapt.left_point.time;
				af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;

                af_adapt.cur_diretion = AF_MOVE_BACKWARD;

                vcm_next = af_adapt.left_point.vcm/2;
                dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);

                slope_left = (af_adapt.left_point.clarity - af_adapt.cent_point.clarity)/(af_adapt.cent_point.vcm - af_adapt.left_point.vcm);
                slope_right = (af_adapt.cent_point.clarity - af_adapt.right_point.clarity)/(af_adapt.right_point.vcm - af_adapt.cent_point.vcm);

                AF_DEG("%d---->L:%d,C:%d,R:%d,rate1=%d,rate2=%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm,\
                           slope_left,slope_right);
			}else if(af_adapt.left_point.clarity < af_adapt.cent_point.clarity && af_adapt.cent_point.clarity > af_adapt.right_point.clarity){// center point has best clarity
                af_adapt.clarity_most.clarity		= af_adapt.cent_point.clarity;
				af_adapt.clarity_most.vcm			= af_adapt.cent_point.vcm;
				af_adapt.clarity_most.time			= af_adapt.cent_point.time;
				af_adapt.clarity_most.adjust_index	= af_adapt.cent_point.adjust_index;
                slope_left = ((af_adapt.cent_point.clarity - af_adapt.left_point.clarity)/(af_adapt.cent_point.vcm - af_adapt.left_point.vcm));
                slope_right = ((af_adapt.right_point.clarity - af_adapt.cent_point.clarity)/(af_adapt.right_point.vcm - af_adapt.cent_point.vcm));

				if(slope_left < slope_right){
                    af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                    vcm_next = af_adapt.cent_point.vcm + (af_adapt.right_point.vcm - af_adapt.cent_point.vcm)/2;
				}else{
                    af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                    vcm_next = af_adapt.cent_point.vcm - (af_adapt.cent_point.vcm - af_adapt.left_point.vcm)/2;
				}
                dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);

                slope_left = (af_adapt.cent_point.clarity - af_adapt.left_point.clarity)/(af_adapt.cent_point.vcm - af_adapt.left_point.vcm);
                slope_right = (af_adapt.cent_point.clarity - af_adapt.right_point.clarity)/(af_adapt.right_point.vcm - af_adapt.cent_point.vcm);

                AF_DEG("%d---->L:%d,C:%d,R:%d,rate1=%d,rate2=%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm,\
                           slope_left,slope_right);
			}else{
				AF_DEG("%d---->L:%d,C:%d,R:%d,rate=%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm,\
                           (af_adapt.right_point.clarity - af_adapt.cent_point.clarity)/(af_adapt.right_point.vcm - af_adapt.cent_point.vcm));

			}
		}else{// already get 3 points  to be compare, finally vcm a <= b <= c
			if(AF_MOVE_FORDWARD == af_adapt.cur_diretion){// vcm move to right
                AF_DEG("%d---->AF_MOVE_FORDWARD\n",__LINE__);
                if(temp > af_adapt.right_point.vcm){// 落在 right 点右边

                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.right_point.clarity){// 移动后的点比上一个点清晰，整体右移
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.right_point.vcm;
                        af_adapt.cent_point.clarity			= af_adapt.right_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.right_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.right_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.right_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.right_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + ((1023 - af_adapt.right_point.vcm)/2);// VCM继续增大

                    }else if(clarity_new < af_adapt.right_point.clarity){// 移动后的点比上一个点模糊，整体左移
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.right_point.vcm;
                        af_adapt.cent_point.clarity			= af_adapt.right_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.right_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.right_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.right_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.right_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.right_point.vcm + ((1023 - af_adapt.right_point.vcm)/2);// VCM开始减小
                    }
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp > af_adapt.cent_point.vcm && temp < af_adapt.right_point.vcm){//落在cent与right点之间


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.cent_point.clarity && clarity < af_adapt.right_point.clarity){//上坡，越右移越清晰
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = temp;//
                        af_adapt.cent_point.clarity		    = clarity_new;
                        af_adapt.cent_point.time			= XOSTimeGet();
                        af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.right_point.vcm			= temp;          最右点不动
                        //af_adapt.right_point.clarity		= clarity_new;
                        //af_adapt.right_point.time			= XOSTimeGet();
                        //af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + (temp - af_adapt.right_point.vcm)/2;//

                    }else if(clarity_new < af_adapt.cent_point.clarity && clarity > af_adapt.right_point.clarity){//下坡，越右移越模糊,保持vcm left < center < right
                        //af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//前面两点不动
                        //af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        //af_adapt.left_point.time			= af_adapt.cent_point.time;
                        //af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        //af_adapt.cent_point.vcm			    = temp;       //
                        //af_adapt.cent_point.clarity		    = clarity_new;
                        //af_adapt.cent_point.time			= XOSTimeGet();
                        //af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.left_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.cent_point.vcm + (temp - af_adapt.cent_point.vcm)/2;//

                    }else{
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp > af_adapt.left_point.vcm && temp < af_adapt.cent_point.vcm){//落在left与cent点之间


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.left_point.clarity && clarity < af_adapt.cent_point.clarity){//上坡，越右移越清晰
                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.cent_point.vcm			    = temp;//
                        //af_adapt.cent_point.clarity		    = clarity_new;
                        //af_adapt.cent_point.time			= XOSTimeGet();
                        //af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.right_point.vcm			= temp;          最右点不动
                        //af_adapt.right_point.clarity		= clarity_new;
                        //af_adapt.right_point.time			= XOSTimeGet();
                        //af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + (temp - af_adapt.right_point.vcm)/2;//

                    }else if(clarity_new < af_adapt.left_point.clarity && clarity > af_adapt.cent_point.clarity){//下坡，越右移越模糊,保持vcm left < center < right
                        //af_adapt.left_point.vcm			= af_adapt.cent_point.vcm;//前面两点不动
                        //af_adapt.left_point.clarity       = af_adapt.cent_point.clarity;
                        //af_adapt.left_point.time			= af_adapt.cent_point.time;
                        //af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = temp;       //
                        af_adapt.cent_point.clarity		    = clarity_new;
                        af_adapt.cent_point.time			= XOSTimeGet();
                        af_adapt.cent_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.left_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.left_point.vcm/2;//

                    }else{
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp < af_adapt.left_point.vcm){//落在left左边


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new < af_adapt.left_point.clarity){//上坡，越右移越清晰

                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;// in the highest point
                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.left_point.vcm;//
                        af_adapt.cent_point.clarity		    = af_adapt.left_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.left_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.left_point.adjust_index;

                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.cur_diretion               = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.left_point.vcm + (af_adapt.cent_point.vcm - af_adapt.left_point.vcm)/2;//

                    }else if(clarity_new > af_adapt.left_point.clarity){//下坡，越右移越模糊,保持vcm left < center < right

                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.clarity		    = af_adapt.left_point.clarity;// in the highest point
                        af_adapt.cent_point.vcm			    = af_adapt.left_point.vcm;
                        af_adapt.cent_point.time			= af_adapt.left_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = (af_adapt.left_point.vcm/2)<100 ? (af_adapt.left_point.vcm/2):100;//

                    }else{
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

                }

				AF_DEG("%d---->L:%d,C:%d,R:%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);





			}else if(AF_MOVE_BACKWARD == af_adapt.cur_diretion){// vcm move to left
                AF_DEG("%d---->AF_MOVE_BACKWARD\n",__LINE__);



                if(temp > af_adapt.right_point.vcm){// 落在 right 点右边


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.right_point.clarity){// 移动后的点比上一个点清晰，整体右移
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.right_point.vcm;
                        af_adapt.cent_point.clarity			= af_adapt.right_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.right_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.right_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.right_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.right_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + ((1023 - af_adapt.right_point.vcm)/2);// VCM继续增大

                    }else if(clarity_new < af_adapt.right_point.clarity){// 移动后的点比上一个点模糊，整体左移
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.right_point.vcm;
                        af_adapt.cent_point.clarity			= af_adapt.right_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.right_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.right_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.right_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.right_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.right_point.adjust_index;

                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.right_point.vcm + ((1023 - af_adapt.right_point.vcm)/2);// VCM开始减小
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp >= af_adapt.cent_point.vcm && temp <= af_adapt.right_point.vcm){//落在cent与right点之间


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.cent_point.clarity && clarity < af_adapt.right_point.clarity){//上坡，越左移越模糊
                        af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        af_adapt.left_point.time			= af_adapt.cent_point.time;
                        af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = temp;//
                        af_adapt.cent_point.clarity		    = clarity_new;
                        af_adapt.cent_point.time			= XOSTimeGet();
                        af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.right_point.vcm			= temp;          最右点不动
                        //af_adapt.right_point.clarity		= clarity_new;
                        //af_adapt.right_point.time			= XOSTimeGet();
                        //af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + (temp - af_adapt.right_point.vcm)/2;//

                    }else if(clarity_new < af_adapt.cent_point.clarity && clarity > af_adapt.right_point.clarity){//下坡，越左移越清晰,保持vcm left < center < right
                        //af_adapt.left_point.vcm			    = af_adapt.cent_point.vcm;//前面两点不动
                        //af_adapt.left_point.clarity			= af_adapt.cent_point.clarity;
                        //af_adapt.left_point.time			= af_adapt.cent_point.time;
                        //af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        //af_adapt.cent_point.vcm			    = temp;       //
                        //af_adapt.cent_point.clarity		    = clarity_new;
                        //af_adapt.cent_point.time			= XOSTimeGet();
                        //af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.right_point.vcm			= temp;
                        af_adapt.right_point.clarity		= clarity_new;
                        af_adapt.right_point.time			= XOSTimeGet();
                        af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.left_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.cent_point.vcm + (temp - af_adapt.cent_point.vcm)/2;//

                    }else{
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp >= af_adapt.left_point.vcm && temp <= af_adapt.cent_point.vcm){//落在left与cent点之间


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new > af_adapt.left_point.clarity && clarity < af_adapt.cent_point.clarity){//上坡，越右移越清晰
                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.cent_point.vcm			    = temp;//
                        //af_adapt.cent_point.clarity		    = clarity_new;
                        //af_adapt.cent_point.time			= XOSTimeGet();
                        //af_adapt.cent_point.adjust_index	= af_adapt.cur_index;

                        //af_adapt.right_point.vcm			= temp;          最右点不动
                        //af_adapt.right_point.clarity		= clarity_new;
                        //af_adapt.right_point.time			= XOSTimeGet();
                        //af_adapt.right_point.adjust_index	= af_adapt.cur_index;

                        af_adapt.cur_diretion = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.right_point.vcm + (temp - af_adapt.right_point.vcm)/2;//

                    }else if(clarity_new < af_adapt.left_point.clarity && clarity > af_adapt.cent_point.clarity){//下坡，越右移越模糊,保持vcm left < center < right
                        //af_adapt.left_point.vcm			= af_adapt.cent_point.vcm;//前面两点不动
                        //af_adapt.left_point.clarity       = af_adapt.cent_point.clarity;
                        //af_adapt.left_point.time			= af_adapt.cent_point.time;
                        //af_adapt.left_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = temp;       //
                        af_adapt.cent_point.clarity		    = clarity_new;
                        af_adapt.cent_point.time			= XOSTimeGet();
                        af_adapt.cent_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.clarity_most.clarity		= af_adapt.left_point.clarity;// in the highest point
                        af_adapt.clarity_most.vcm			= af_adapt.left_point.vcm;
                        af_adapt.clarity_most.time			= af_adapt.left_point.time;
                        af_adapt.clarity_most.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = af_adapt.left_point.vcm/2;//

                    }else{
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


                }else if(temp < af_adapt.left_point.vcm){//落在left左边


                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    if(clarity_new < af_adapt.left_point.clarity){//上坡，越右移越清晰

                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;// in the highest point
                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.vcm			    = af_adapt.left_point.vcm;//
                        af_adapt.cent_point.clarity		    = af_adapt.left_point.clarity;
                        af_adapt.cent_point.time			= af_adapt.left_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.left_point.adjust_index;

                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.cur_diretion               = AF_MOVE_FORDWARD;
                        vcm_next = af_adapt.left_point.vcm + (af_adapt.cent_point.vcm - af_adapt.left_point.vcm)/2;//

                    }else if(clarity_new > af_adapt.left_point.clarity){//下坡，越右移越模糊,保持vcm left < center < right

                        af_adapt.right_point.vcm			= af_adapt.cent_point.vcm;
                        af_adapt.right_point.clarity		= af_adapt.cent_point.clarity;
                        af_adapt.right_point.time			= af_adapt.cent_point.time;
                        af_adapt.right_point.adjust_index	= af_adapt.cent_point.adjust_index;

                        af_adapt.cent_point.clarity		    = af_adapt.left_point.clarity;// in the highest point
                        af_adapt.cent_point.vcm			    = af_adapt.left_point.vcm;
                        af_adapt.cent_point.time			= af_adapt.left_point.time;
                        af_adapt.cent_point.adjust_index	= af_adapt.left_point.adjust_index;


                        af_adapt.left_point.vcm			    = temp;//重新排列,始终保持vcm left < center < right
                        af_adapt.left_point.clarity			= clarity_new;
                        af_adapt.left_point.time			= XOSTimeGet();
                        af_adapt.left_point.adjust_index	= af_adapt.cur_index;


                        af_adapt.cur_diretion = AF_MOVE_BACKWARD;
                        vcm_next = (af_adapt.left_point.vcm/2)<100 ? (af_adapt.left_point.vcm/2):100;//

                    }else{
                        af_adapt.cur_diretion = AF_MOVE_STANDBY;
                        AF_DEG("%d---->AF_MOVE_STANDBY,clarity new=%d,%d,%d,%d\n",__LINE__,clarity_new,af_adapt.left_point.clarity,af_adapt.cent_point.clarity,af_adapt.right_point.clarity);
                        AF_DEG("%d---->AF_MOVE_STANDBY,vcm new=%d,%d,%d,%d\n",__LINE__,temp,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);
                    }
                    MARK();
                    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

                }

				deg_Printf("%d---->L:%d,C:%d,R:%d\n",__LINE__,af_adapt.left_point.vcm,af_adapt.cent_point.vcm,af_adapt.right_point.vcm);


            }else{
                deg_Printf("%d---->AF_MOVE_STANBY\n",__LINE__);
            }


            if(af_adapt.cur_diretion == AF_MOVE_FORDWARD || af_adapt.cur_diretion == AF_MOVE_BACKWARD)
                dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_next);

		}

	}else if(AF_FLAG_MOVE_AF1 == af_adapt.adjust_state){
		af_adapt.cur_index++;
		//af_adapt.adjust_state = AF_FLAG_MOVE_AF1_DONE;
		if(20 == af_adapt.cur_index){
			af_adapt.adjust_state = AF_FLAG_IDLE;
		}else{
			af_adapt.adjust_state = AF_FLAG_START;
		}
		//debgreg(af_adapt.cur_index);
	}else if(AF_FLAG_MOVE_AF1_DONE == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_GETTING_SECOND_BUFFER;
	}else if(AF_FLAG_GETTING_SECOND_BUFFER == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_GOT_SECOND_BUFFER;
	}else if(AF_FLAG_GOT_SECOND_BUFFER == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_CALCULATTING_SECOND_BUFFER;
		clarity_statistics(af_adapt.clarity_cal_buf,320,60,120,80,0,60,0,&clarity_b);
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_READ, &temp);
		AF_DEG("222clarity_b = %d,cur_vcm=%d\n",clarity_b,temp);
		af_adapt.adjust_state = AF_FLAG_CALCULATTD_SECOND_BUFFER;
		if(clarity_b < clarity_new){// worse
			if(vcm_b < vcm_a){// larger,better
				if((clarity_new - clarity_b)<1000){
					vcm_a =  (vcm_b+100)<1000?(vcm_b+100):1000;
				}else{
					vcm_a =  (vcm_b+100)<1000?(vcm_b+100):1000;
				}
				AF_DEG("line %d vcm_a=%d,vcm_b=%d\n",__LINE__,vcm_a,vcm_b);
			}else{// smaller,better
				if((clarity_new - clarity_b)<1000){
					if(vcm_b>=100)
						vcm_a = (vcm_b-100);
					else
						vcm_a = 0;
				}else{
					if(vcm_b>=100)
						vcm_a = (vcm_b-100);
					else
						vcm_a = 0;
				}
				AF_DEG("line %d vcm_a=%d,vcm_b=%d\n",__LINE__,vcm_a,vcm_b);
			}
			//deg_Printf("no to the threadhold,do nothing\n");
		}else{// better
			if(vcm_b < vcm_a){// smaller,better
				if((clarity_b - clarity_new)<1000){
					if(vcm_b>=100)
						vcm_a = (vcm_b-100);
					else
						vcm_a = 0;
				}else{
					if(vcm_b>=100)
						vcm_a = (vcm_b-100);
					else
						vcm_a = 0;
				}
				AF_DEG("line %d vcm_a=%d,vcm_b=%d\n",__LINE__,vcm_a,vcm_b);
			}else{// larger,better
				if((clarity_b - clarity_new)<1000){
					vcm_a =  (vcm_b+100)<1000?(vcm_b+100):1000;
				}else{
					vcm_a =  (vcm_b+100)<1000?(vcm_b+100):1000;
				}
				AF_DEG("line %d vcm_a=%d,vcm_b=%d\n",__LINE__,vcm_a,vcm_b);
			}
		}
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, vcm_a);
		last_vcm = vcm_a;
		af_adapt.adjust_state = AF_FLAG_MOVE_AF2;
	}else if(AF_FLAG_MOVE_AF2 == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_MOVE_AF2_DONE;
	}else if(AF_FLAG_MOVE_AF2_DONE == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_IDLE;
	}
	//debgreg(af_adapt.adjust_state);
}
#endif

void task_common_af_adjust_simple(void)
{
    //static AF_POINT mark_point[11];
	static u8 adjust_flag = 0;
	static int clarity_new = 0;

	static u8 af_handle_step = 0xff;
	static AF_POINT first_step_point[11] = {{0,0,0,0}};
	static AF_POINT second_step_point[11] = {{0,0,0,0}};
	static u16 first_step_index = 0;
	static u16 second_step_index = 0;
	u16 i;
	int max_clarity = 0;

    u32 slope_left;
    u32 slope_right;

	AF_POINT temp_point;
	u32 temp;
 	u32 task_cur = app_taskCurId();

	u32 temp_time;
	static u32 triggle_time = 0;
	static u32 af_move_time = 0;

	if(XOSTimeGet()<4000)
		return;
#if 1
	// if(TASK_RECORD_PHOTO != task_cur){
	// 	if(TASK_RECORD_VIDEO!= task_cur){
	// 		hal_sysMemFree(af_adapt.clarity_cal_buf);
	// 		af_adapt.clarity_cal_buf = NULL;
	// 		return;
	// 	}
	// }
#elif 1	
	if(TASK_RECORD_PHOTO != task_cur){
		hal_sysMemFree(af_adapt.clarity_cal_buf);
		af_adapt.clarity_cal_buf = NULL;
		return;
	}
#endif	

	if(NULL == af_adapt.clarity_cal_buf){

		for(i=0;i<11;i++){
			first_step_point[i].vcm = i*100;
			first_step_point[i].adjust_index = i;
		}
		for(i=0;i<10;i++){
			second_step_point[i].vcm = i*10;
			second_step_point[i].adjust_index = i;
		}
		af_adapt.clarity_cal_buf = hal_sysMemMalloc(320*60);
	}

	if(NULL == af_adapt.clarity_cal_buf){
		return;
	}

//----------------------motion detect start--------------------------//
	if(hal_mdCheck()/*&&!SysCtrl.manual_af*/){
		if(af_move_time){
			if(XOSTimeGet() - af_move_time < 500){
				AF_DEG("1111 md triggle too fast--->\n");
				return;
			}

		}
		//deg_Printf("--0--\n");
		//XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_MD,1));
		triggle_time = XOSTimeGet();
		af_adapt.adjust_motion = 1;

		af_adapt.adjust_state = AF_FLAG_IDLE;
		af_adapt.cur_index=0;
		af_adapt.left_point.time = 0;
		af_adapt.cent_point.time = 0;
		af_adapt.right_point.time = 0;
		//AF_DEG("1111 md start--->\n");
		
	}
	// else if(SysCtrl.manual_af == 1)
	// {
	// 	SysCtrl.manual_af = 2;

	// 	triggle_time = XOSTimeGet();
	// 	af_adapt.adjust_motion = 1;

	// 	af_adapt.adjust_state = AF_FLAG_IDLE;
	// 	af_adapt.cur_index=0;
	// 	af_adapt.left_point.time = 0;
	// 	af_adapt.cent_point.time = 0;
	// 	af_adapt.right_point.time = 0;
	// 	AF_DEG("2222 md start--->\n");
	// }
	else{
		//deg_Printf("--1--\n");
		if(af_adapt.adjust_motion){
			temp_time = XOSTimeGet();
			if(temp_time - triggle_time > 300){
				if(AF_FLAG_IDLE == af_adapt.adjust_state){
					MARK();
					af_adapt.adjust_state = AF_FLAG_START;
					af_adapt.cur_diretion == AF_MOVE_FORDWARD;
					af_handle_step = 0;
					first_step_index = 0;
				}
				af_adapt.adjust_motion = 0;
			}
		}
	}
	//debgreg(af_adapt.adjust_state);
//----------------------motion detect end--------------------------//

	if(AF_FLAG_START == af_adapt.adjust_state){
        //XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_FOCUS_START,1));// start focus
		af_adapt.adjust_state = AF_FLAG_MOVE_AF1;
		af_move_time = XOSTimeGet();
		AF_DEG("111 first_step_point[%d].clarity = %d,time=%d\n",first_step_index,first_step_point[first_step_index].clarity,XOSTimeGet());
        dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, first_step_point[first_step_index].vcm);
		SysCtrl.af_complete = 1;
	}else if(AF_FLAG_MOVE_AF1 == af_adapt.adjust_state){
		//MARK();
		af_adapt.adjust_state = AF_FLAG_MOVE_AF1_DONE;
	}else if(AF_FLAG_MOVE_AF1_DONE == af_adapt.adjust_state){
		af_adapt.adjust_state = AF_FLAG_GETTING_FIRST_BUFFER;
		AF_DEG("start getting frame----------->%d\n",XOSTimeGet());
	}else if(AF_FLAG_GOT_FIRST_BUFFER == af_adapt.adjust_state){
	    AF_DEG("got frame----------->%d\n",XOSTimeGet());
		af_adapt.adjust_state = AF_FLAG_CALCULATTING_FIRST_BUFFER;
		hx330x_sysDcacheFlush(af_adapt.clarity_cal_buf,320*60);
		clarity_statistics(af_adapt.clarity_cal_buf,320,60,120,80,0,60,0,&clarity_new);
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_READ, &temp);
		AF_DEG("111clarity_new = %d,cur_vcm=%d\n",clarity_new,temp);
		if(0 == af_handle_step){
			first_step_point[first_step_index].vcm = temp;
			first_step_point[first_step_index].clarity = clarity_new;
			first_step_point[first_step_index].time = XOSTimeGet();
			first_step_point[first_step_index].adjust_index = first_step_index;
			//AF_DEG("first_step_index = %d\n",first_step_index);

		}else if(1 == af_handle_step){
			first_step_point[second_step_index].vcm = temp;
			first_step_point[second_step_index].clarity = clarity_new;
			first_step_point[second_step_index].time = XOSTimeGet();
			first_step_point[second_step_index].adjust_index = second_step_index;
		}

		if(first_step_index < 11 && af_handle_step == 0){
			//debgreg(first_step_index);
			if(first_step_index > 2){
                if(first_step_point[first_step_index - 2].clarity > first_step_point[first_step_index - 1].clarity && \
                   first_step_point[first_step_index - 1].clarity > first_step_point[first_step_index - 0].clarity){//清晰度越来越低,停止调节
                   for(i=first_step_index;i<11;i++){
                       first_step_point[i].clarity = first_step_point[first_step_index].clarity;
                   }
                   first_step_index = 11;
                }else{
                    first_step_index++;
                }
			}else{
                first_step_index++;
			}

			if(first_step_index == 11){
				af_adapt.clarity_most.vcm = first_step_point[0].vcm;
				af_adapt.clarity_most.clarity = first_step_point[0].clarity;

				for(i=0;i<11;i++){
					AF_DEG("first_step_point[%d].clarity = %d\n",i,first_step_point[i].clarity);
					if(first_step_point[i].clarity >= af_adapt.clarity_most.clarity){
						af_adapt.clarity_most.vcm = first_step_point[i].vcm;
						af_adapt.clarity_most.adjust_index= i;
						af_adapt.clarity_most.clarity = first_step_point[i].clarity;
					}
				}
				AF_DEG("the [%d] point.clarity = %d is best,vcm = %d\n",af_adapt.clarity_most.adjust_index,af_adapt.clarity_most.clarity,af_adapt.clarity_most.vcm);
				af_adapt.adjust_state = AF_FLAG_MOVE_AF2;
				af_move_time = XOSTimeGet();
				first_step_index = 0;
				af_handle_step = 0;
				af_adapt.adjust_motion = 0;
				AF_DEG("find best poin end----------->%d\n",XOSTimeGet());
				 //XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_FOCUS_START,0));// start focus
				for(i=0;i<11;i++){
                    first_step_point[i].clarity = 0;
				}
				return;
			}else{
				af_adapt.adjust_state = AF_FLAG_START;
				//MARK();
			}
		}else if(second_step_index < 11 && af_handle_step == 1){
			second_step_index++;
			if(second_step_index > 10){
				af_handle_step = 10;
			}
			//MARK();
		}

	}else if(AF_FLAG_MOVE_AF2 == af_adapt.adjust_state){
		dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_READ, &temp);
		if(temp > af_adapt.clarity_most.vcm){
			if(temp - af_adapt.clarity_most.vcm > 100){
				temp = af_adapt.clarity_most.vcm + (temp - af_adapt.clarity_most.vcm)/2;
				dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, temp);
				AF_DEG("111 set vcm = %d\n",temp);
			}else{
				dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, af_adapt.clarity_most.vcm);
				af_adapt.adjust_state = AF_FLAG_IDLE;
				AF_DEG("222 set vcm = %d\n",af_adapt.clarity_most.vcm);
				SysCtrl.af_complete = 0;
			}
		}else{
			if(af_adapt.clarity_most.vcm - temp > 100){
				temp = temp + (af_adapt.clarity_most.vcm - temp)/2;
				dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, temp);
				AF_DEG("333 set vcm = %d\n",temp);
			}else{
				dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, af_adapt.clarity_most.vcm);
				af_adapt.adjust_state = AF_FLAG_IDLE;
				AF_DEG("444 set vcm = %d\n",af_adapt.clarity_most.vcm);
				SysCtrl.af_complete = 0;
			}
		}


	}
	//debgreg(af_adapt.adjust_state);
}
#endif



int task_com_AF_ctrl(int temp)
{//测试对焦用，实测有效

if(temp)
{
	SysCtrl.af_value += 20;
	if(SysCtrl.af_value >0x3ff)
		SysCtrl.af_value = 0;//0x3ff;
}else
{
	SysCtrl.af_value -= 20;
	if(SysCtrl.af_value<0)
		SysCtrl.af_value = 0;
}
	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_AF_WRITE, SysCtrl.af_value);

}