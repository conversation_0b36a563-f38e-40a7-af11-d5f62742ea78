/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_GC9307

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)



LCD_INIT_TAB_BEGIN()
	//------end Reset Sequence-----//
	<PERSON><PERSON>(0xfe),
	<PERSON><PERSON>(0xef),
	<PERSON><PERSON>(0x36),
	DAT(0x48),
	C<PERSON>(0x3a),
	DAT(0x05),
	
	
	CMD(0x86),	
	DAT(0x98),
	
	CMD(0x89),	
	DAT(0x13),
	CMD(0x8b),	
	DAT(0x80),	
	
	CMD(0x8d),	
	DAT(0x33),	
	
	CMD(0x8e),	 
	DAT(0x0f),
	
	CMD(0xe8),
	DAT(0x13),
	DAT(0x00),
	
	CMD(0xEC),	
	DAT(0x33),
	DAT(0x07),  
	DAT(0x00),
		
	
	CMD(0xff),
	DAT(0x62),
	
	
	CMD(0x99),	
	DAT(0x3e),
	
	
	
	CMD(0x9d),	
	DAT(0x4b),
	CMD(0x98),  
	DAT(0x3e),
	
	CMD(0x9c),	
	DAT(0x4b),
	
	
	CMD(0xc3),	
	DAT(0x1A),
	CMD(0xc4),	
	DAT(0x30),
	CMD(0xc9),	
	DAT(0x2F),
	
	//???240*320
	CMD(0x2a),   
	DAT(0x00),
	DAT(0x00),
	DAT(0x00),
	DAT(0xEF),
	
	CMD(0x2b),   
	DAT(0x00),
	DAT(0x00),
	DAT(0x01),
	DAT(0x3F),
	
	CMD(0x2c),
	
	///gamma
	CMD(0xF0),
	DAT(0x15),
	DAT(0x17),
	DAT(0x07),
	DAT(0x09),
	DAT(0x07),
	DAT(0x32),
	
	
					
	CMD(0xF2),
	DAT(0x15),
	DAT(0x17),
	DAT(0x07),
	DAT(0x09),
	DAT(0x07),
	DAT(0x3B),
	
	CMD(0xF1),
	DAT(0x45),
	DAT(0x8E),
	DAT(0x95),
	DAT(0x28),
	DAT(0x2A),
	DAT(0x7F),
					
	
					
	CMD(0xF3),
	DAT(0x4E),
	DAT(0x8E),
	DAT(0x95),
	DAT(0x28),
	DAT(0x2A),
	DAT(0x7F),


	CMD(0x35),
	DAT(0x00),
	CMD(0x44),
	DAT(0x00),
	DAT(0x0a),
	CMD(0x11),

    DLY(120),

	CMD(0x29),
	CMD(0x2c),

LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()
    .name 			= "MCU_gc9307",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast   	= LCD_CONTRAST_100,

    .brightness 	= 0,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 4,

    .gamma_index 	= {4, 4, 4},//JPY
	

    .asawtooth_index = {5, 5},
    
    .lcd_ccm         = LCD_CCM_DEFAULT,




    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()



#endif


