/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_HX8357B



#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)



LCD_INIT_TAB_BEGIN()

    CMD(0x11),//
    DLY(200),
    <PERSON><PERSON>(0xB0),
    DAT(0x00),

    C<PERSON>(0xB3),
    DAT(0x02),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),

    CMD(0xB4),
    DAT(0x00),//0x00

    CMD(0xC6),
    DAT(0x02),

    CMD(0xC0),    //set PANEL
    DAT(0x14),   //DAT //REV,SM,GS
    DAT(0x3b),   //DAT
    DAT(0x00),   //DAT
    DAT(0x02),   //DAT
    DAT(0x11),   //DAT

    CMD(0xC1),
    DAT(0x10),   //Bit4:0-->frame inversion,1-->line inversion
    DAT(0x10),
    DAT(0x22),

    CMD(0xC5),//Freme rate
    DAT(0x07),  //0x06==54fps  0x07==59fps

    CMD(0xC8),   //GAMMA
    DAT(0x00), //DAT
    DAT(0x25), //DAT
    DAT(0x11), //DAT
    DAT(0x21), //DAT
    DAT(0x09), //DAT
    DAT(0x0C), //DAT
    DAT(0x66), //DAT
    DAT(0x25), //DAT
    DAT(0x77), //DAT
    DAT(0x12), //DAT
    DAT(0x06), //DAT
    DAT(0x12), //DAT

    CMD(0xD0),   //set power
    DAT(0x43), //DAT//DDVDH,VCI1,VCL
    DAT(0x41), //DAT//VGH,VGL
    DAT(0x0A), //DAT//VREG1  01--4.125V,06--4.625V

    CMD(0xD1),   //set vcom
    DAT(0x35), //DAT//VCOMH  //25
    DAT(0x09), //DAT//VCOML  //09

    CMD(0xD2),    //set NOROW
    DAT(0x05), //DAT //SAP
    DAT(0x12), //DAT //DC10/00

    CMD(0xEA),
    DAT(0x03),
    DAT(0x00),
    DAT(0x00),

    CMD(0xEE),   //SET EQ
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0x00), //DAT

    CMD(0xED),   //SET DIR TIM--Source/gate/vcom timing set
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0xA2), //DAT
    DAT(0xA2), //DAT
    DAT(0xA2), //DAT
    DAT(0xA2), //DAT
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0x00), //DAT
    DAT(0xAE), //DAT
    DAT(0xAE), //DAT
    DAT(0x01), //DAT
    DAT(0xA2), //DAT
    DAT(0x00), //DAT

    CMD(0x53),
    DAT(0xff), //DAT

    CMD(0x35),///TE on
    DAT(0x00),

    CMD(0x3A), //Set Panel
    DAT(0x55),

#if 0 //  TE OFF window=480X320
    CMD(0x36), //Set Panel
    DAT(0xa8),
    CMD(0X2A),
    DAT(0X00),
    DAT(0X00),
    DAT(0x01),
    DAT(0xDF),

    CMD(0X2B),
    DAT(0X00),
    DAT(0X00),
    DAT(0x01),
    DAT(0x3F),
#else  //  TE ON  && window==320X480
    CMD(0x36), //Set Panel
    DAT(0x08),

    CMD(0X2B),
    DAT(0X00),
    DAT(0X00),
    DAT(0x01),
    DAT(0xDF),

    CMD(0X2A),
    DAT(0X00),
    DAT(0X00),
    DAT(0x01),
    DAT(0x3F),
#endif
    CMD(0x29), //Display On
    DLY(20),
    CMD(0x2c), //Display On
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name           = "MCU_HX8357B",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_90,
    .te_mode        = LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div       = LCD_PCLK_DIV(320*480*2*30),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w       = 320,
    .screen_h       = 480,

    .video_w        = 480,
    .video_h        = 320,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -16,

    .saturation     = LCD_SATURATION_130,

    .contra_index   = 9,

    .gamma_index    = {0, 0, 0},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























