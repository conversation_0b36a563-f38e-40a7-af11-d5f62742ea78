/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYMP3FILEOPS_SELECT_ID =0,
};

UNUSED ALIGNED(4) const widgetCreateInfor playMp3FileOpsWin[] =
{
	createFrameWin( 							Rx(120), Ry(50),  Rw(80), Rh(140),R_ID_PALETTE_Black,WIN_ABS_POS),
	createItemManage(PLAYMP3FILEOPS_SELECT_ID,	Rx(0),   <PERSON>y(0),   Rw(80), Rh(140),INVALID_COLOR),
	widgetEnd(),
};



