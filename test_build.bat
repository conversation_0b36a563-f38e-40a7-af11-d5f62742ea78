@echo off
echo Testing HX330x SDK Build Configuration
echo =====================================

echo 1. Checking toolchain...
set TOOLCHAIN_PATH=E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\bin
if exist "%TOOLCHAIN_PATH%\or1k-unknown-elf-gcc.exe" (
    echo [OK] Toolchain found
    "%TOOLCHAIN_PATH%\or1k-unknown-elf-gcc.exe" --version | findstr "gcc version"
) else (
    echo [ERROR] Toolchain not found at: %TOOLCHAIN_PATH%
    goto :error
)

echo.
echo 2. Checking Code::Blocks project...
if exist "app\hx330x_sdk.cbp" (
    echo [OK] Code::Blocks project found
    echo     Project: app\hx330x_sdk.cbp
) else (
    echo [ERROR] Code::Blocks project not found
    goto :error
)

echo.
echo 3. Checking build tools...
where codeblocks >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Code::Blocks found in PATH
    codeblocks --version 2>nul | findstr "Code::Blocks"
) else (
    echo [WARNING] Code::Blocks not in PATH
)

set MAKE_PATH=C:\Program Files (x86)\GnuWin32\bin\make.exe
if exist "%MAKE_PATH%" (
    echo [OK] Make found at GnuWin32
) else (
    echo [INFO] Make not found at GnuWin32
)

echo.
echo 4. Testing build configuration...
if exist "build.bat" (
    echo [OK] build.bat found
) else (
    echo [ERROR] build.bat not found
    goto :error
)

if exist "Makefile" (
    echo [OK] Makefile found
) else (
    echo [WARNING] Makefile not found
)

if exist ".vscode\tasks.json" (
    echo [OK] VSCode tasks configured
) else (
    echo [ERROR] VSCode tasks not configured
    goto :error
)

echo.
echo 5. Testing clean operation...
call build.bat clean
if %ERRORLEVEL% EQU 0 (
    echo [OK] Clean operation successful
) else (
    echo [WARNING] Clean operation had issues
)

echo.
echo [SUCCESS] Configuration test completed!
echo.
echo Available build methods:
echo 1. VSCode: Press Ctrl+Shift+B and select build task
echo 2. Command: build.bat
echo 3. Command: build.bat clean
echo 4. Command: build.bat rebuild
echo.
echo Output will be in: app\bin\Debug\hx330x_sdk.elf
goto :end

:error
echo.
echo [FAILED] Configuration test failed!
echo Please fix the issues above.
pause
exit /b 1

:end
pause
