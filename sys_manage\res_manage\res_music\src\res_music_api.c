/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) static WAV_PARA_T	music_arg;
ALIGNED(4) static WAV_PARA_T	key_arg;
/*******************************************************************************
* Function Name  : res_music_end
* Description    : res_music_end: music end
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void res_music_end(void)
{
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
		audioPlaybackStop();
	if(music_arg.cachebuf)
		hal_sysMemFree((void *)music_arg.cachebuf);
	music_arg.cachebuf = NULL;
	music_arg.cachelen = 0;
}
/*******************************************************************************
* Function Name  : res_music_start
* Description    : res_music_start: start music
* Input          : INT16U idx : resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int res_music_start(INT16U idx, INT8U wait, INT8U volume)
{
	//WAV_PARA_T	music;
	int addr;
	hal_wdtClear();
	addr = nv_open(idx);
	if(addr < 0)
	{
		res_music_end();
		return -1;
	}
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
		audioPlaybackStop();
	res_music_end();
	music_arg.cachelen = nv_size(idx);
	music_arg.cachebuf = (u8*)hal_sysMemMallocLast(music_arg.cachelen);
	if(music_arg.cachebuf == NULL)
	{
		deg_Printf("music show mem fail.\n");
		res_music_end();
		return -1;
	}
	nv_read(addr,(void *)music_arg.cachebuf,music_arg.cachelen);
	music_arg.ch_out	= 1;
	music_arg.type 		= MEDIA_WAV;
	music_arg.src_type 	= MEDIA_SRC_RAM;
	music_arg.volume	= volume;
	music_arg.fd		= idx;
	if(audioPlaybackParse(&music_arg) != STATUS_OK)
	{
		//hal_sysMemFree((void *)music.cachebuf);
		res_music_end();
		return -1;
	}
	if(audioPlaybackStart(&music_arg) != STATUS_OK)
	{
		deg_Printf("music show fail\n");
		//hal_sysMemFree((void *)music.cachebuf);
		res_music_end();
		return -1;
		
	}
	if(wait)
	{
		u32 timeout = 0x3ffffff;
		while((audioPlaybackGetStatus() == MEDIA_STAT_PLAY) && timeout--);
		res_music_end();
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : res_keysound_init
* Description    : res_keysound_init: keysound start
* Input          : INT8U load_auto,INT16U idx,INT8U volume
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_init(INT8U load_auto,INT16U idx,INT8U volume)
{
	key_arg.cachelen = 0;
	if(load_auto)
	{
		int addr = nv_open(idx);
		if(addr >= 0)
		{
			key_arg.cachelen = nv_size(idx);
			key_arg.cachebuf = (u8*)hal_sysMemMalloc(key_arg.cachelen);
			if(key_arg.cachebuf == NULL)
			{
				key_arg.cachelen = 0;
			}else
			{			
				nv_read(addr, (void*)key_arg.cachebuf,(int)key_arg.cachelen);
				key_arg.fd = idx;
				
			}
		}
	}
	if(key_arg.cachelen == 0)
	{
		key_arg.cachelen = sizeof(res_key_music);
		key_arg.cachebuf = (u8*)res_key_music;
		key_arg.fd	 = -1;
	}
	key_arg.ch_out		= 1;
	key_arg.type 		= MEDIA_WAV;
	key_arg.src_type 	= MEDIA_SRC_RAM;
	key_arg.volume		= volume;
	if(audioPlaybackParse(&key_arg) != STATUS_OK)
	{
		if(key_arg.fd >= 0)
			hal_sysMemFree((void *)key_arg.cachebuf);			
		key_arg.cachebuf = NULL;
		key_arg.cachelen = 0;
	}
}
/*******************************************************************************
* Function Name  : res_keysound_play
* Description    : res_keysound_play: keysound start
* Input          : none
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_play(void)
{
	if(key_arg.cachebuf == NULL)
		return;
	audioPlaybackStart(&key_arg);
}
/*******************************************************************************
* Function Name  : res_keysound_stop
* Description    : res_keysound_stop: keysound start
* Input          : none
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_stop(void)
{
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
	{
		audioPlaybackStop();
	}
	if(key_arg.fd >= 0)
		hal_sysMemFree((void *)key_arg.cachebuf);	
	key_arg.fd		 = -1;
	key_arg.cachebuf = NULL;
}
/*******************************************************************************
* Function Name  : res_music_start
* Description    : res_music_start: start music
* Input          : INT16U idx : resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
extern void task_com_usb_dev_out(u32 out);
int res_mp3_start(INT16U idx, INT8U wait, INT8U volume)
{
#if MP3_EN
	//WAV_PARA_T	music;
	int addr;
	int res = 0;
	u32 src_addr, src_size;
	hal_wdtClear();
	addr = nv_open(idx);
	if(addr < 0)
	{
		//res_music_end();
		return -1;
	}
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
		audioPlaybackStop();
	res_music_end();
	hal_sysMemPrint();
	src_size = nv_size(idx);
	src_addr = (u32)hal_sysMemMallocLast(src_size);
	if(src_addr == 0)
	{
		deg_Printf("mp3 show mem fail.\n");
		return -2;
	}
	nv_read(addr,(void *)src_addr,src_size);
	music_arg.ch_out	= 1;
	music_arg.type 		= MEDIA_WAV;
	music_arg.src_type 	= MEDIA_SRC_RAM;
	music_arg.volume	= volume;
	music_arg.fd		= idx;
	//nv_read(addr,(void *)music_arg.cachebuf,music_arg.cachelen);
	task_com_usb_dev_out(1);
	dusb_api_Uninit();
	if(mp3_api_decode_to_wav(src_addr, src_size, &music_arg) < 0)
	{
		res = -3;
		goto MP3_DECODE_END;
	}
	if(audioPlaybackStart(&music_arg) != STATUS_OK)
	{
		deg_Printf("mp3 show fail\n");
		//hal_sysMemFree((void *)music.cachebuf);
		res = -4;
		goto MP3_DECODE_END;
		
	}
	if(wait)
	{
		u32 timeout = 0x3ffffff;
		while((audioPlaybackGetStatus() == MEDIA_STAT_PLAY) && timeout--);
		res_music_end();
	}
MP3_DECODE_END:
	if(src_addr)
	{
		hal_sysMemFree((void*)src_addr);
	}
	if(res < 0)
	res_music_end();
	task_com_usb_dev_out(0);
	hal_sysMemPrint();
	return res;
#else
	return -1;
#endif
}