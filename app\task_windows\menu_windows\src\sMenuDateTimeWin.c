/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DATETIME_SELECT_ID = 0,
};
UNUSED ALIGNED(4) const widgetCreateInfor dateTimeWin[] =
{
	createFrameWin(							Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_GREY_W,WIN_ABS_POS),
	createItemManage(DATETIME_SELECT_ID,	Rx(0),	<PERSON>y(0), 	<PERSON>w(180),<PERSON>h(142),INVALID_COLOR),
	widgetEnd(),
};



