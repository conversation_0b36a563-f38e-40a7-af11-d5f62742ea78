/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_TIPS_H
#define UI_WIN_TIPS_H

typedef struct uiTipsObj_S
{
	uiWidgetObj 	widget;
	STRING_DRAW_T 	string;	
}uiTipsObj;
/*******************************************************************************
* Function Name  : uiTipsCreate
* Description    : uiTipsCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle tipsCreateIndirect(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
