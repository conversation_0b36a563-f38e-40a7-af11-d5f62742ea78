/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiFrameWinCB
* Description    : uiFrameWinCB
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiFrameWinCB(uiWinMsg* msg)
{
	winHandle 		hWin 		= msg->curWin;
	uiFrameWinObj	*pFrameWin	= (uiFrameWinObj*)uiHandleToPtr(hWin);
	uiWinObj		*pWin		= &(pFrameWin->win);
	uiWinObj		*pChild;
	u16   			chileId;
	if(pFrameWin->cb)
		pFrameWin->cb(msg);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("frame win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pWin->bgColor != INVALID_COLOR)
				uiWinDrawRect((uiRect*)(msg->para.p), pWin->bgColor);
			//deg_msg("paint frame win[]:[%d %d %d %d]\n",pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_TOUCH:
			return;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		case MSG_WIN_ADD_WIDGET:
			pChild = (uiWinObj*)uiHandleToPtr(msg->childWin);
			if(pChild != NULL && (pChild->style & WIN_WIDGET))
			{
				chileId = uiWidgetGetId(msg->childWin);
				if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
					pFrameWin->widgetHandle[chileId] = msg->childWin;
			}
			return;
		case MSG_WIN_GET_WIDGET:
			chileId = msg->para.v;
			if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
				msg->childWin = pFrameWin->widgetHandle[chileId];
			else
				msg->childWin = INVALID_HANDLE;
			return;
		case MSG_WIN_WIDGET_DESTROY:
			pChild = (uiWinObj*)uiHandleToPtr(msg->childWin);
			if(pChild != NULL && (pChild->style & WIN_WIDGET))
			{
				chileId = uiWidgetGetId(msg->childWin);
				if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
					pFrameWin->widgetHandle[chileId] = INVALID_HANDLE;
			}
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiFrameWinCreate
* Description    : uiFrameWinCreate
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiFrameWinCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 		hFrameWin;
	uiFrameWinObj	*pFrameWin;
	hFrameWin = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height, parent,uiFrameWinCB,sizeof(uiFrameWinObj),infor->style|WIN_FRAME);
	if(hFrameWin != INVALID_HANDLE)
	{
		pFrameWin 			= (uiFrameWinObj*)uiHandleToPtr(hFrameWin);
		pFrameWin->cb		= cb;
		pFrameWin->prvate	= infor->prvate; //pointer WINDOWS_T
		uiWinSetbgColor(hFrameWin, infor->bgColor);
	}

	return hFrameWin;
}