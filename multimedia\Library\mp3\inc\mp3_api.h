/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_API_H
       #define  MP3_API_H
	   
#define MP3_RODATA_SECTION		MP3CODE_SECTION
#define MP3_TEXT_SECTION		MP3TEXT_SECTION//SDRAM_TEXT_SECTION
#define MP3_DATA_SECTION		MP3DATA_SECTION	   
	   
/****************** conditional debugging ******************************************************/
#define MP3_DEBG_EN				1

#define DEBUG_HUFFMAN			(0&MP3_DEBG_EN)
#define DEBUG_SIDE_INFO			(0&MP3_DEBG_EN)
#define DEBUG_HEADER			(0&MP3_DEBG_EN)
#define DEBUG_TAG				(0&MP3_DEBG_EN)
#define DEBUG_LRC				(0&MP3_DEBG_EN)
#define DEBUG_DAC				(0&MP3_DEBG_EN)

#define   PCM_OUTPUT_SAVE_FILE			0
#define   PCM_OUTPUT_SAVE_FILENAME		"mp3/test.wav"

#define   PCM_OUTPUT_SAVE_MIX			0  //samples[pcm_size*2]: sample[left] - sample[right] - sample[left]-sample[right]...
#define   PCM_OUTPUT_SAVE_SEPARATE		1  //samples[pcm_size*2]: samples_left[pcm_size] + samples_right[pcm_size]

#define   PCM_SAVE_FORMAT    			PCM_OUTPUT_SAVE_SEPARATE

#define   PCM_DECODE_SINGLE				0
#define   PCM_DECODE_DUAL				1	

#define   PCM_DECODE_CHANNEL			PCM_DECODE_SINGLE

#define	  MP3_PCM_BUFFER_NUM			128
#define   MP3_PCM_SIZE					(1152*2)  //default 


#include "mp3_cpu_fixed.h"	  
#include "mp3_bsio.h"	 
#include "mp3_huffman.h"
#include "mp3_frame.h"
#include "mp3_synth.h"
#include "mp3_layer12.h"
#include "mp3_layer3.h"
#include "mp3_lrc.h"
#include "mp3_dec.h"
#include "mp3_dac.h"





typedef struct MP3_DEC_OP_S{
	u8	mp3_dec_sta;
	u8	mp3_dec_frame_sta;
	u8  mp3_dac_sta;
	u8  lrc_dec_sta;
#if PCM_OUTPUT_SAVE_FILE
	WAV_PARA_T 	wav_arg;
#endif
	
	int mp3_fd;
	u32 mp3_src_type;
	
	u32 mp3_head_len;
	u32 total_time;
	u32 play_time;
	
	u8*	dac_buf;
	u32 dac_buf_size;
	u32 save_mode;
	u8* save_buf;
	u32 save_len;
	u32 cur_len;
	u32 src_len;
	MP3_RAM_T		*ram;
	MP3_DAC_CTL_T	*dac_ctl;
	MP3_DEC_CTRL_T	*dec_ctl;
	LRC_DEC_CTL_T	*lrc_ctl;
}MP3_DEC_OP_T;
extern MP3_DEC_OP_T mp3_dec_op;
extern MP3_RAM_T mp3_dec_ram;

/*******************************************************************************
* Function Name  : mp3_api_uinit
* Description    : mp3_dec_uinit: 释放MP3使用的内存空间
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
void mp3_api_uinit(void);
/*******************************************************************************
* Function Name  : mp3_dec_init
* Description    : mp3_dec_init: mp3 decode memory malloc and sta init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
int mp3_api_init(void);
/*******************************************************************************
* Function Name  : mp3_dec_init
* Description    : mp3_dec_init: mp3 decode memory malloc and sta init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_api_start(int mp3_fd, int lrc_fd);
/*******************************************************************************
* Function Name  : mp3_api_stop
* Description    : mp3_api_stop: mp3_api_stop
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_api_stop(void);
/*******************************************************************************
* Function Name  : mp3_api_service
* Description    : mp3_api_service
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_api_service(void);
/*******************************************************************************
* Function Name  : mp3_api_playtime
* Description    : mp3_api_playtime
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
void mp3_api_playtime_get(u32 *total, u32* cur); //ms
/*******************************************************************************
* Function Name  : mp3_lrc_sta
* Description    : mp3_lrc_sta: MP3_LRC_SHOW / MP3_LRC_NONE
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
u8 mp3_lrc_showOn(void);
/*******************************************************************************
* Function Name  : mp3_dec_sta
* Description    : mp3_dec_sta: MP3_DEC_UINT = 0,MP3_DEC_STOP, MP3_DEC_START,
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
u8 mp3_dec_sta(void);
/*******************************************************************************
* Function Name  : mp3_dac_sta
* Description    : mp3_dac_sta: MP3_DAC_STOP , MP3_DAC_START,MP3_DAC_PAUSE
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
u8 mp3_dac_sta(void);
/*******************************************************************************
* Function Name  : mp3_tit2_get
* Description    : mp3_tit2_get: 返回当前mp3歌曲 的标题
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
u8* mp3_title_get(void);
/*******************************************************************************
* Function Name  : mp3_orchestra_get
* Description    : mp3_orchestra_get: 当前mp3歌曲 的作者
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
u8* mp3_orchestra_get(void);
/*******************************************************************************
* Function Name  : mp3_Album_get
* Description    : mp3_Album_get: 返回当前mp3歌曲 的专辑
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
u8* mp3_Album_get(void);
/*******************************************************************************
* Function Name  : mp3_picture_get
* Description    : mp3_picture_get: 返回当前mp3歌曲 的专辑图片
* Input          : u32 *len
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
u8* mp3_picture_get(u32 *len);

/*******************************************************************************
* Function Name  : mp3_api_decode_to_wav
* Description    : mp3_api_decode_to_wav: from ram to ram
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION 
int mp3_api_decode_to_wav(u32 src_addr, u32 src_size, WAV_PARA_T *arg);

#endif
