/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	AUDIO_REC_TIME_ID=0,
};

UNUSED ALIGNED(4) const widgetCreateInfor RecordAudioWin[] =
{
	createFrameWin( 					Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Gray, WIN_ABS_POS),
	createStringIcon(INVALID_WIDGET_ID,	Rx(100), <PERSON>y(70),  <PERSON>w(120), <PERSON>h(25),  RAM_ID_MAKE("Record Audio"), ALIGNMENT_CENTER, R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(AUDIO_REC_TIME_ID,	Rx(100), Ry(105), Rw(120), Rh(25),  RAM_ID_MAKE("00:00:00"),		ALIGNMENT_CENTER, R_ID_PALETTE_White,	DEFAULT_FONT),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : audioRecTimeShow
* Description    : audioRecTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/	
UNUSED static void audioRecTimeShow(winHandle handle)
{
	if(audioRecordGetStatus() == MEDIA_STAT_START)
		uiWinSetStrInfor(winItem(handle,AUDIO_REC_TIME_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Red);
	else	
		uiWinSetStrInfor(winItem(handle,AUDIO_REC_TIME_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White); 
	uiWinSetResid(winItem(handle,AUDIO_REC_TIME_ID),RAM_ID_MAKE(task_com_rec_show_time_str()));
}


