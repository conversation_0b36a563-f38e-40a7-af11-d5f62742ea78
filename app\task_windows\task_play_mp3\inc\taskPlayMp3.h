/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_PLAY_MP3_H
#define  __TASK_PLAY_MP3_H
#if FUN_MP3_PLAY_EN
typedef struct PLAYMP3_OP_S
{
	u32  playmode;
	int  playfirstIndex;		//记录按键播放的文件索引, < 0: 无
	u32  playCurDirIndex;		//记录正在播放的音乐的DIR层数
	u32  openListTotal;			//记录当前列表的文件个数	
	u32  opendirindex;			//记录当前列表的DIR层数
	u32  dirfileindex[10]; 	//记录打开的DIR层的文件索引，用于返回DIR时选择文件
	char playfilename[FILE_NAME_LEN+1];
}PLAYMP3_OP_T;
typedef enum{
	UPDATE_PLAYTIME 		= (1 << 0),
	UPDATE_LRCSHOW			= (1 << 1),
	UPDATE_FILEITEM_SHOW	= (1 << 2),
	UPDATE_NONE				= 0,
}MP3_EVENT_PLAY;
typedef enum
{
	PLAY_MP3_MODE_NONE		= 0,
	PLAY_MP3_MODE_ALLCYCLE	= R_ID_ICON_MUSICMODECYCLE,		//列表循环
	PLAY_MP3_MODE_ONECYCLE	= R_ID_ICON_MUSICMODECONTINUE,		//单曲循环
	PLAY_MP3_MODE_RAND		= R_ID_ICON_MUSICMODEORDER,			//列表随机播放
	PLAY_MP3_MODE_ORDER		= R_ID_ICON_MUSICMODERAND,		//顺序播放
	PLAY_MP3_MODE_MAX,
}PLAY_MP3_MODE;

#define INVALID_PLAY_INDEX		((int)-1)


EXTERN_WINDOW(playMp3MainWindow);
EXTERN_WINDOW(playMp3SubWindow);
EXTERN_WINDOW(playMp3DirOpsWindow);
EXTERN_WINDOW(playMp3FileOpsWindow);


extern PLAYMP3_OP_T task_playMp3_op;
extern sysTask_T taskPlayMp3;

/*******************************************************************************
* Function Name  : taskPlayMp3MainStart
* Description    : taskPlayMp3MainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3MainStart(u32 index);
/*******************************************************************************
* Function Name  : taskPlayMp3ModeChange
* Description    : taskPlayMp3ModeChange
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3ModeChange(void);
/*******************************************************************************
* Function Name  : taskPlayMp3OpenDir
* Description    : taskPlayMp3OpenDir
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3OpenDir(char *dir);
/*******************************************************************************
* Function Name  : taskPlayMp3ParentDirOpen
* Description    : taskPlayMp3ParentDirOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3ParentDirOpen(void);
/*******************************************************************************
* Function Name  : taskPlayMp3ChildDirOpen
* Description    : taskPlayMp3ChildDirOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3ChildDirOpen(u32 index, char *childDir);
/*******************************************************************************
* Function Name  : taskPlayMp3MainStart
* Description    : taskPlayMp3MainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayMp3RootDirOpen(void);

/*******************************************************************************
* Function Name  : taskPlayMp3DirFindFile
* Description    : taskPlayMp3DirFindFile
* Input          : nextOrPrev: 1:next, 0: prev
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayMp3DirFindFile(u32 start_index, int end_index, u32 nextOrPrev);

#endif
#endif
