/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	GREEN_LEFT_LINE0 = 0,
	GREEN_LEFT_LINE1,
	GREEN_LEFT_LINE2,
	YELLOW_LEFT_LINE0,
	YELLOW_LEFT_LINE1,
	YELLOW_LEFT_LINE2,
	RED_LEFT_LINE0,
	RED_LEFT_LINE1,
	RED_LEFT_LINE2,
	GREEN_RIGHT_LINE0,
	<PERSON><PERSON><PERSON>_RIGHT_LINE1,
	<PERSON><PERSON><PERSON>_RIGHT_LINE2,
	<PERSON><PERSON><PERSON><PERSON>_RIGHT_LINE0,
	Y<PERSON><PERSON>OW_RIGHT_LINE1,
	<PERSON><PERSON><PERSON><PERSON>_RIGHT_LINE2,
	RED_RIGHT_LINE0,
	RED_RIGHT_LINE1,
	RED_RIGHT_LINE2,
	STOP_STRING_ID,
};
#define LINE_RATE		3
UNUSED ALIGNED(4) const widgetCreateInfor asternWin[] =
{
	createFrameWin(					Rx(0),   				Ry(0),  	Rw(320), 				Rh(240), 			R_ID_PALETTE_Transparent,WIN_ABS_POS),
	//x = 100, y = 90
	createLine(GREEN_LEFT_LINE0,	Rx(100), 				Ry(90), 	Rx(140), 				Ry(90),  	Rw(5), 	R_ID_PALETTE_Green),
	createLine(GREEN_LEFT_LINE1,	Rx(100), 				Ry(90), 	Rx(100-10/LINE_RATE), 	Ry(100),  	Rw(5), 	R_ID_PALETTE_Green),
	//x = 100 - 15/LINE_RATE, y = 105
	createLine(GREEN_LEFT_LINE2,	Rx(100-15/LINE_RATE), 	Ry(105), 	Rx(100-25/LINE_RATE), 	Ry(115),  	Rw(5), 	R_ID_PALETTE_Green),
	
	//x = 100 - 30/LINE_RATE, y = 120
	createLine(YELLOW_LEFT_LINE0,	Rx(100-30/LINE_RATE), 	Ry(120), 	Rx(140-30/LINE_RATE), 	Ry(120),  	Rw(5), 	R_ID_PALETTE_Yellow),
	createLine(YELLOW_LEFT_LINE1,	Rx(100-30/LINE_RATE), 	Ry(120), 	Rx(100-45/LINE_RATE), 	Ry(135),  	Rw(5), 	R_ID_PALETTE_Yellow),
	//x = 100 - 55/LINE_RATE, y = 145
	createLine(YELLOW_LEFT_LINE2,	Rx(100-55/LINE_RATE), 	Ry(145), 	Rx(100-70/LINE_RATE), 	Ry(160),  	Rw(5), 	R_ID_PALETTE_Yellow),	

	//x = 100 - 80/LINE_RATE, y = 170
	createLine(RED_LEFT_LINE0,		Rx(100-80/LINE_RATE), 	Ry(170), 	Rx(140-80/LINE_RATE), 	Ry(170),  	Rw(5), 	R_ID_PALETTE_Red),
	createLine(RED_LEFT_LINE1,		Rx(100-80/LINE_RATE), 	Ry(170), 	Rx(100-100/LINE_RATE), 	Ry(190),  	Rw(5), 	R_ID_PALETTE_Red),
	//x = 100 - 110/LINE_RATE, y = 200
	createLine(RED_LEFT_LINE2,		Rx(100-110/LINE_RATE), 	Ry(200), 	Rx(100-130/LINE_RATE), 	Ry(220),  	Rw(5), 	R_ID_PALETTE_Red),
	
	//x = 220, y = 90
	createLine(GREEN_RIGHT_LINE0,	Rx(180), 				Ry(90), 	Rx(220), 				Ry(90),  	Rw(5), 	R_ID_PALETTE_Green),
	createLine(GREEN_RIGHT_LINE1,	Rx(220-3), 				Ry(90), 	Rx(220-3+10/LINE_RATE), Ry(100),  	Rw(5), 	R_ID_PALETTE_Green),
	//x = 220 + 15/LINE_RATE, y = 105
	createLine(GREEN_RIGHT_LINE2,	Rx(220-3+15/LINE_RATE), Ry(105), 	Rx(220-3+25/LINE_RATE), Ry(115),  	Rw(5), 	R_ID_PALETTE_Green),
	

	//x = 220 + 30/LINE_RATE, y = 120
	createLine(YELLOW_RIGHT_LINE0,	Rx(180+30/LINE_RATE), 	Ry(120), 	Rx(220+30/LINE_RATE), 	Ry(120),  	Rw(5), 	R_ID_PALETTE_Yellow),
	createLine(YELLOW_RIGHT_LINE1,	Rx(220-3+30/LINE_RATE), Ry(120), 	Rx(220-3+45/LINE_RATE), Ry(135),  	Rw(5), 	R_ID_PALETTE_Yellow),
	//x = 220 + 55/LINE_RATE, y = 145
	createLine(YELLOW_RIGHT_LINE2,	Rx(220-3+55/LINE_RATE), Ry(145), 	Rx(220-3+70/LINE_RATE), Ry(160),  	Rw(5), 	R_ID_PALETTE_Yellow),
	
	//x = 220 + 80/LINE_RATE, y = 170
	createLine(RED_RIGHT_LINE0,		Rx(180+80/LINE_RATE), 	Ry(170), 	Rx(220+80/LINE_RATE), 	Ry(170),  	Rw(5), 	R_ID_PALETTE_Red),
	createLine(RED_RIGHT_LINE1,		Rx(220-3+80/LINE_RATE), Ry(170), 	Rx(220-3+100/LINE_RATE),Ry(190),  	Rw(5), 	R_ID_PALETTE_Red),
	//x = 220 + 110/LINE_RATE, y = 200
	createLine(RED_RIGHT_LINE2,		Rx(220-3+110/LINE_RATE),Ry(200), 	Rx(220-3+130/LINE_RATE),Ry(220),  	Rw(5), 	R_ID_PALETTE_Red),
	createStringIcon(STOP_STRING_ID,Rx(140-80/LINE_RATE),	Ry(170-15), Rw(40+160/LINE_RATE),	Rh(40),		"STOP",ALIGNMENT_CENTER, R_ID_PALETTE_Yellow,RES_FONT_NUM2),

	widgetEnd(),
};



