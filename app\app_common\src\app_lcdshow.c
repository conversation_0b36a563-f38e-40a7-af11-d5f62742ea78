/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

ALIGNED(4) static LCDSHOW_CTRL_T  app_lcdshow_ctrl;

#if 1
ALIGNED(4) static const u32 display_crop_tab[] =  {98,80,70,60};//{98,80,60,40};//{100,90,80,70,60,50,40}; // min :40%


/*******************************************************************************
* Function Name  : app_lcdVideoShowScaler_cfg
* Description    : set lcd show crop scaler(from csi img)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdVideoShowScaler_cfg(int step)
{

    static u8 crop_index = 0xff;
	static u16 crop_w_save = 0, crop_h_save = 0;
	u8  crop_index_temp;
	u16 csi_w,csi_h,crop_w,crop_h;
	u16 csi_ratio_w, csi_ratio_h;
	u16 video_w, video_h;
	u32 crop_level;
	crop_index_temp = crop_index;
	hal_SensorResolutionGet(&csi_w,&csi_h);
	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	hal_SensorRatioResolutionGet(&csi_ratio_w, &csi_ratio_h);
	//deg_Printf("csi_w:%d,csi_h:%d\n", csi_w, csi_h);
	//deg_Printf("video_w:%d,video_h:%d\n", video_w, video_h);
	video_w = hx330x_min(csi_ratio_w, video_w);
	video_h = hx330x_min(csi_ratio_h, video_h);

	if(step == 0)
	{
		crop_index_temp = 0;
		SysCtrl.lcd_scaler_level_max = (u32)video_w*video_h* 100/((u32)csi_w*csi_h);
	}else if(step > 0)
	{
		if(crop_index_temp < (ARRAY_NUM(display_crop_tab)-1) )
			crop_index_temp++;
	}else{
		if(crop_index_temp >= 1)
			crop_index_temp--;
	}
	crop_level = display_crop_tab[crop_index_temp];
	crop_w = (csi_ratio_w * crop_level/100) & ~1;
	crop_h = (csi_ratio_h * crop_level/100) & ~1;
	//deg_Printf("%d, crop_w:%d, crop_h:%d\n", crop_index, crop_w, crop_h);
	if(crop_w < video_w || crop_h < video_h)
	{
		crop_w = video_w;
		crop_h = video_h;	
	}
	if(crop_w_save == crop_w && crop_h_save == crop_h)
	{
		return -1;
	}else
	{
		crop_w_save = crop_w;
		crop_h_save = crop_h;
	}
	crop_index = crop_index_temp;
	SysCtrl.lcd_scaler_level_uishow = crop_index;

	//deg_Printf("11 %d, crop_w:%d, crop_h:%d\n", crop_index, crop_w, crop_h);
    hal_lcdSetCsiCrop((csi_w-crop_w)/2, (csi_h-crop_h)/2, (csi_w+crop_w)/2,  (csi_h+crop_h)/2);
	SysCtrl.lcd_scaler_level  = (u32)crop_w*crop_h* 100/((u32)csi_w*csi_h);
	return 0;
}
#else
/*******************************************************************************
** 实现无极放大效果 
调用示例：
*     while(hal_lcdWinUpdataCheckDone()) hal_wdtClear; //等待上一次缩放生效完成
*	  app_lcdVideoShowScaler_cfg(1);
* Function Name  : app_lcdVideoShowScaler_cfg
* Description    : set lcd show crop scaler(from csi img)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdVideoShowScaler_cfg(int step)
{
	static u16 crop_w_save = 0, crop_h_save = 0;
	u16 csi_w,csi_h,video_w,video_h;
	u16 csi_ratio_w, csi_ratio_h;
	u16 crop_w, crop_h;
	hal_SensorResolutionGet(&csi_w,&csi_h);
	hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	hal_SensorRatioResolutionGet(&csi_ratio_w, &csi_ratio_h);
	video_w = hx330x_min(csi_ratio_w, video_w);
	video_h = hx330x_min(csi_ratio_h, video_h);

	if(step == 0)
	{
		u32 step = hx330x_greatest_divisor(csi_ratio_w, csi_ratio_h);
		SysCtrl.lcd_scaler_level_max = (u32)video_w*video_h* 100/((u32)csi_ratio_w*csi_ratio_h);
		SysCtrl.lcd_scaler_w_step = csi_ratio_w / step * 4;
		SysCtrl.lcd_scaler_h_step = csi_ratio_h / step * 4;
		crop_w = csi_ratio_w;
		crop_h = csi_ratio_h;
		crop_w_save = crop_h_save = 0;
	}else if(step > 0)
	{
		crop_w = crop_w_save - SysCtrl.lcd_scaler_w_step;
		crop_h = crop_h_save - SysCtrl.lcd_scaler_h_step;
	}else{
		crop_w = crop_w_save + SysCtrl.lcd_scaler_w_step;
		crop_h = crop_h_save + SysCtrl.lcd_scaler_h_step;
	}
	//deg_Printf("%d, crop_w:%d, crop_h:%d\n", crop_index, crop_w, crop_h);
	if(crop_w < video_w || crop_h < video_h)
	{
		crop_w = video_w;
		crop_h = video_h;	
	}
	if(crop_w > csi_ratio_w || crop_h > csi_ratio_h)
	{
		crop_w = csi_ratio_w;
		crop_h = csi_ratio_h;	
	}
	if(crop_w_save == crop_w && crop_h_save == crop_h)
	{
		return -1;
	}
	crop_w_save = crop_w;
	crop_h_save = crop_h;
	SysCtrl.lcd_scaler_level  = (u32)crop_w*crop_h* 100/((u32)csi_ratio_w*csi_ratio_h);

	//deg_Printf("level %d, crop_w:%d, crop_h:%d\n", SysCtrl.lcd_scaler_level, crop_w, crop_h);
    hal_lcdSetCsiCrop((csi_w-crop_w)/2, (csi_h-crop_h)/2, (csi_w+crop_w)/2,  (csi_h+crop_h)/2);
	return 0;
}
#endif
/*******************************************************************************
* Function Name  : app_lcdPlayShowScaler_cfg
* Description    : set lcd play scaler(from playback)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
				   u8 type: PLAY_SCALER_STAT_KICK, updata without hal_lcdVideoSetFrame
                            PLAY_SCALER_STAT_KICKBUF, updata after hal_lcdVideoSetFrame
				   u8 use_ratio: 1: scaler ref by ratio
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdPlayShowScaler_cfg(int step, u8 type, u8 use_ratio)
{
	#define PLAY_MAX_LEVEL		4
	static u16 play_w_save = 0, play_h_save = 0;
	u16 video_w,video_h;
	u16 video_w_min,video_h_min;
	u16 play_w, play_h;
	if(type != PLAY_SCALER_STAT_KICK && type != PLAY_SCALER_STAT_KICKBUF)
	{
		return -1;
	}
	if(1)//(use_ratio)
		hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	else
		hal_lcdGetVideoResolution(&video_w,&video_h);
	video_w_min = (video_w/(PLAY_MAX_LEVEL/2))&~3;
	video_h_min = (video_h/(PLAY_MAX_LEVEL/2))&~3;


	if(step == 0)
	{
		u32 step = hx330x_greatest_divisor(video_w, video_h);
		SysCtrl.play_scaler_level_max = (u32)video_w_min*video_h_min* 100/((u32)video_w*video_h);;
		SysCtrl.play_scaler_w_step = (video_w / step * 4);
		SysCtrl.play_scaler_h_step = (video_h / step * 4);
		
		play_w = video_w;
		play_h = video_h;
		play_w_save = play_h_save = 0;
	}else if(step > 0)
	{
		play_w = play_w_save - SysCtrl.play_scaler_w_step;
		play_h = play_h_save - SysCtrl.play_scaler_h_step;
	}else{
		play_w = play_w_save + SysCtrl.play_scaler_w_step;
		play_h = play_h_save + SysCtrl.play_scaler_h_step;
	}
	if(play_w < video_w_min || play_h < video_h_min)
	{
		play_w = video_w_min;
		play_h = video_h_min;	
	}
	if(play_w > video_w || play_h > video_h)
	{
		play_w = video_w;
		play_h = video_h;	
	}
	if(play_w_save == play_w && play_h_save == play_h)
	{
		return -2;
	}
	play_w_save = play_w;
	play_h_save = play_h;
	SysCtrl.play_scaler_level  = (u32)play_w*play_h* 100/((u32)video_w*video_h);

	//deg_Printf("play level %d, play_w:%d, play_h:%d\n", SysCtrl.play_scaler_level, play_w, play_h);
	//deg_Printf("play_scaler_w_step %d, play_scaler_h_step:%d\n", SysCtrl.play_scaler_w_step, SysCtrl.play_scaler_h_step);
	//deg_Printf("video_w_min %d, video_h_min:%d\n", video_w_min, video_h_min);
	//hal_lcdSetPlayScaler(type, (video_w-play_w)/2, (video_h-play_h)/2, (video_w+play_w)/2,  (video_h+play_h)/2);
	//hal_lcdSetPlayScaler(type, 0, 0, play_w,  play_h);
	if(hal_lcdVideoNeedRotateGet())
	{
		if(hal_lcdVideoScanModeGet() & LCD_DISPLAY_ROTATE_180 )
		{
			hal_lcdSetPlayScaler(type, (video_w-play_w)/2, video_h - play_h, (video_w+play_w)/2,  video_h);
		}else{
			hal_lcdSetPlayScaler(type, (video_w-play_w)/2, 0, (video_w+play_w)/2,  play_h);
		}
		//
		
	}else
	{
		hal_lcdSetPlayScaler(type, 0, (video_h-play_h)/2, play_w,  (video_h+play_h)/2);
	}
	
	return 0;
}

/*******************************************************************************
* Function Name  : app_lcdVideoShowRatio_cfg
* Description    : user cfg display ratio ,only effect to video show
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : none
* Return         :0
*******************************************************************************/
void app_lcdVideoShowRatio_cfg(u16 ratio)
{
	hal_lcdSetRatio(ratio);
	if(app_lcdshow_ctrl.video_layer_en)
	{
		app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	}
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStart
* Description    : lcd video show start for showing csi, will reset scaler
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdCsiVideoShowStart(void)
{
	if(app_lcdshow_ctrl.video_layer_en)
		return 0;
	hal_lcdSetRatio(hardware_setup.lcd_ratio_mode);
	app_lcdVideoShowScaler_cfg(0);
	app_lcdPlayShowScaler_cfg(0,PLAY_SCALER_STAT_KICKBUF, 1);
	hal_lcdCsiShowStart();
	app_lcdshow_ctrl.video_layer_en = 1;

	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdCsiVideoShowStop(void)
{
	if(app_lcdshow_ctrl.video_layer_en)
		hal_lcdCsiShowStop();
	hx330x_lcdShowWaitDone();
	app_lcdshow_ctrl.video_layer_en = 0;
}

/*******************************************************************************
* Function Name  : app_lcdVideoIdleFrameGet
* Description    : lcd video show idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdVideoIdleFrameGet(void)
{
    return (void *) hal_lcdVideoIdleFrameMalloc();
}

/*******************************************************************************
* Function Name  : app_lcdUiShowInit
* Description    : lcd ui show init
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdUiShowInit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en != 0)
		return 0;
	u16	ui_w, ui_h, sreen_w,sreen_h;
	u16 pos_x, pos_y;
	if(hal_lcdGetUiResolution(&ui_w,&ui_h) < 0)
		return -2;
	if(hal_lcdGetUiPosition(&pos_x,&pos_y) < 0)
			return -3;
	if(hal_lcdGetSreenResolution(&sreen_w,&sreen_h) < 0)
		return -4;
    u32 palette 	= (u32)hal_sysMemMalloc(256 * 4);
	if(palette == 0)
		return -6;
    INT32 scanmode = hal_lcdUiScanModeGet();
    // load palette
	if(res_iconGetPalette(R_ID_BIN_PALETTE, (u8*)palette) <= 0)
	{
		hal_sysMemFree((void *)palette);
		palette = 0;
	}
    hal_uiLzoInit();
	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,pos_y,	palette,scanmode & 0x03);
#if 0
	switch(scanmode & 0x03)
	{
		case LCD_DISPLAY_ROTATE_0:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,		         pos_y,	               palette,	scanmode & 0x03); 	break;
		case LCD_DISPLAY_ROTATE_90:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_y,	             sreen_w-pos_x-ui_w,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_180:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_w-pos_x-ui_w,   sreen_h-pos_y-ui_h,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_270:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_h-pos_y-ui_h,   pos_x,	               palette,	scanmode & 0x03);	break;
		default: 				hal_sysMemFree((void *)palette); return -5;//
	}
#endif
    hal_lcdUiEnable(UI_LAYER0,1);
    hal_sysMemFree((void *)palette);
	app_lcdshow_ctrl.ui_layer_en = 1;
	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdUiShowUinit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en)
		hal_lcdUiEnable(UI_LAYER0,0);
	app_lcdshow_ctrl.ui_layer_en = 0;
}
/*******************************************************************************
* Function Name  : app_lcdUiDrawIdleFrameGet
* Description    : lcd ui draw idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdUiDrawIdleFrameGet(void)
{
    return (void *) hal_uiDrawBufMalloc(UI_LAYER0);
}
/*******************************************************************************
* Function Name  : app_lcdShowWinModeCfg
* Description    : config lcdshow WINAB mode
* Input          : INT8U mode : mode index
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdShowWinModeCfg(INT8U mode)
{
    u16 video_w,video_h;
	u16 winA_x, winA_y,winA_w, winA_h;
	u16 winB_x, winB_y,winB_w, winB_h;
	u8  winA_layer, winB_layer, winB_en;
	u8  win_sub_step_w, win_sub_step_h;
    hal_lcdGetVideoRatioResolution(&video_w,&video_h);
	if(video_w > video_h)
	{
		win_sub_step_w = 3;
		win_sub_step_h = 2;
	}else
	{
		win_sub_step_w = 2;
		win_sub_step_h = 3;
	}
	deg_Printf("WIN:%d [%d-%d]\n", mode,video_w, video_h);
	switch(mode)
	{
    	case LCDSHOW_ONLYWINA:
			winA_layer = LCDWIN_TOP_LAYER;
			winB_layer = LCDWIN_BOT_LAYER;
			winA_x = 0; winA_y = 0; winA_w = video_w; winA_h = video_h;
			winB_x = 0; winB_y = 0; winB_w = video_w; winB_h = video_h;
			hx330x_dmaNocWinA();
			hal_lcdWinEnablePreSet(1);
			break;
    	case LCDSHOW_ONLYWINB:
			winA_layer = LCDWIN_BOT_LAYER;
			winB_layer = LCDWIN_TOP_LAYER;
			winA_x = 0; winA_y = 0; winA_w = video_w; winA_h = video_h;
			winB_x = 0; winB_y = 0; winB_w = video_w; winB_h = video_h;
			hx330x_dmaNocDefault();
			hal_lcdWinEnablePreSet(1);
			break;
		case LCDSHOW_MAIN_WINA:
			winA_layer = LCDWIN_BOT_LAYER;
			winB_layer = LCDWIN_TOP_LAYER;
			winA_x = 0; winA_y = 0; winA_w = video_w; winA_h = video_h;
			winB_x = (video_w - video_w/win_sub_step_w)&~7; winB_y = 0;
			winB_w = video_w - winB_x; winB_h = (video_h/win_sub_step_h)&~1;
			hx330x_dmaNocDefault();
			hal_lcdWinEnablePreSet(1);
			break;
		case LCDSHOW_MAIN_WINB:
			winA_layer = LCDWIN_TOP_LAYER;
			winB_layer = LCDWIN_BOT_LAYER;
			winA_x = (video_w - video_w/win_sub_step_w)&~7; winA_y = 0;
			winA_w = video_w - winA_x; winA_h = (video_h/win_sub_step_h)&~1;
			winB_x = 0; winB_y = 0; winB_w = video_w; winB_h = video_h;
			hx330x_dmaNocWinB();
			hal_lcdWinEnablePreSet(1);
			break;
		case LCDSHOW_WINAB:
			winA_layer = LCDWIN_BOT_LAYER;
			winB_layer = LCDWIN_TOP_LAYER;
			winA_x = 0; winA_y = 0;
			winA_w = (video_w/2)&~7; winA_h = video_h;
			winB_x = winA_w; winB_y = 0; winB_w = video_w - winB_x; winB_h = video_h;
			hx330x_dmaNocDefault();
			hal_lcdWinEnablePreSet(0);
			break;
		case LCDSHOW_WINBA:
			winA_layer = LCDWIN_BOT_LAYER;
			winB_layer = LCDWIN_TOP_LAYER;
			winB_x = 0; winB_y = 0;
			winB_w = (video_w/2)&~7; winB_h = video_h;
			winA_x = winB_w; winA_y = 0; winA_w = video_w - winA_x; winA_h = video_h;
			hx330x_dmaNocDefault();
			hal_lcdWinEnablePreSet(0);
			break;
		case LCDSHOW_WIN_DISABLE: hal_lcdSetWinEnable(0);hx330x_dmaNocPlayBack(); return 0;
		default:	return -1;
	}
	if(husb_api_usensor_tran_sta()){
		winB_en = WINAB_EN;
	}else
	{
		winB_en = WINAB_DIS;
	}
	//deg_Printf("WINA [%d] pos[%d:%d] wh[%d:%d]\n", winA_layer, winA_x,winA_y,winA_w,winA_h);
	//deg_Printf("WINB [%d] pos[%d:%d] wh[%d:%d]\n", winB_layer, winB_x,winB_y,winB_w,winB_h);
    hal_lcdSetWINAB(LCDWIN_A,winA_layer,winA_x,winA_y,winA_w,winA_h,WINAB_EN);
	hal_lcdSetWINAB(LCDWIN_B,winB_layer,winB_x,winB_y,winB_w,winB_h,winB_en);
	return 0;

}
/*******************************************************************************
* Function Name  : app_Cmos_Sensor_Switch
* Description    : app_Cmos_Sensor_Switch: video record should stop
* Input          : 
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_Cmos_Sensor_Switch(void)
{
	
	if(app_taskCurId() != TASK_RECORD_PHOTO && app_taskCurId() != TASK_RECORD_VIDEO)
		return -1;
	if(SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN)
		return -1;
	if(hardware_setup.cmos_sensor_switch_en == 0)
		return -1;
	if(videoRecordGetStatus() == MEDIA_STAT_START)
		app_taskRecordVideo_stop();
	app_lcdCsiVideoShowStop();
	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_UINIT, hardware_setup.cmos_sensor_sel);
	hardware_setup.cmos_sensor_sel ^=1;
	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);
	app_lcdCsiVideoShowStart();
	app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	return 0;
}

/*******************************************************************************
* Function Name  : app_lcdVideoShowRotate_cfg
* Description    : user cfg display video rotate
* Input          : rotate_180: 1: add 180,  0: not add 180
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdVideoShowRotate180(void)
{
	static u8 rotate_180 = 1;
	hal_lcdVideoSetRotate180(rotate_180);
	rotate_180 ^= 1;
}















