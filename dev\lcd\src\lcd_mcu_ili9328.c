/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ILI9328

#define CMD(x)    LCD_CMD_MCU_CMD16(x)
#define DAT(x)    LCD_CMD_MCU_DAT16(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0x00E3),DAT(0x3008),
    CMD(0x00E7),DAT(0x0012),
    C<PERSON>(0x00EF),DAT(0x1231),

    CMD(0x0001),DAT(0x0100),// set SS and SM bit (S720 -> S1)
    CMD(0x0002),DAT(0x0700),// set 1 line inversion
    CMD(0x0003),DAT(0x1028), // set GRAM write direction and BGR=1. 65K
    CMD(0x0004),DAT(0x0000), // Resize register
    CMD(0x0008),DAT(0x0207),// set the back porch and front porch
    CMD(0x0009),DAT(0x0000),// set non-display area refresh cycle ISC[3:0]
    CMD(0x000A),DAT(0x0000), // FMARK function
    CMD(0x000C),DAT(0x0000), // RGB interface setting
    CMD(0x000D),DAT(0x0000), // Frame marker Position
    CMD(0x000F),DAT(0x0000),// RGB interface polarity
                                                                 //Power On sequence
    CMD(0x0010),DAT(0x0000), // SAP),DAT( BT[3:0]),DAT( AP),DAT( DSTB),DAT( SLP),DAT( STB
    CMD(0x0011),DAT(0x0007), // DC1[2:0]),DAT( DC0[2:0]),DAT( VC[2:0]
    CMD(0x0012),DAT(0x0000), // VREG1OUT voltage
    CMD(0x0013),DAT(0x0000), // VDV[4:0] for VCOM amplitude
    //CMD(0x0007),DAT(0x0001),
    DLY(50),              // Dis-charge capacitor power voltage
    CMD(0x0010),DAT(0x1290), // SAPF1, BT[3:0]F1, APF1, DSTBF1, SLPF1, STB
    CMD(0x0011),DAT(0x0227), // DC1[2:0]F1, DC0[2:0]F1, VC[2:0]
    DLY(50),
    CMD(0x0012),DAT(0x0019), //001C// Internal reference voltage= Vci;
    DLY(50),
    CMD(0x0013),DAT(0x0900), //Set VDV[4:0] for VCOM amplitude
    CMD(0x0029),DAT(0x0000), //Set VCM[5:0] for VCOMH
    CMD(0x002B),DAT(0x000c), // Set Frame Rate   000C
    DLY(50),
    CMD(0x0020),DAT(0x00ef), // GRAM horizontal Address
    CMD(0x0021),DAT(0x0000), // GRAM Vertical Address
                                                                // Adjust the Gamma Curve
    CMD(0x0030),DAT(0x0000),
    CMD(0x0031),DAT(0x0603),
    CMD(0x0032),DAT(0x0206),
    CMD(0x0035),DAT(0x0206),
    CMD(0x0036),DAT(0x0004),
    CMD(0x0037),DAT(0x0105),
    CMD(0x0038),DAT(0x0401),
    CMD(0x0039),DAT(0x0707),
    CMD(0x003C),DAT(0x0602),
    CMD(0x003D),DAT(0x0004),

                                                             //Set GRAM area
    CMD(0x0050),DAT(0x0000), // Horizontal GRAM Start Address
    CMD(0x0051),DAT(0x00EF),// Horizontal GRAM End Address
    CMD(0x0052),DAT(0x0000), // Vertical GRAM Start Address
    CMD(0x0053),DAT(0x013F), // Vertical GRAM Start Address
    CMD(0x0060),DAT(0xA700), // Gate Scan Line
    CMD(0x0061),DAT(0x0001),// NDL),DAT(VLE),DAT( REV
    CMD(0x006A),DAT(0x0000), // set scrolling line

                                                                //Partial Display Control
    CMD(0x0080),DAT(0x0000),
    CMD(0x0081),DAT(0x0000),
    CMD(0x0082),DAT(0x0000),
    CMD(0x0083),DAT(0x0000),
    CMD(0x0084),DAT(0x0000),
    CMD(0x0085),DAT(0x0000),
                                                                //Panel Control
    CMD(0x0090),DAT(0x0010),
    CMD(0x0092),DAT(0x0600),
    CMD(0x0093),DAT(0x0003),
    //CMD(0x0095),DAT(0x0110),
    //CMD(0x0097),DAT(0x0000),
    //CMD(0x0098),DAT(0x0000),
    CMD(0x0007),DAT(0x0133), // 262K color and display ON
    CMD(0x0022),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_ili9328",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_0,
    .te_mode 		= LCD_MCU_TE_DISABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 320,
    .screen_h 		= 240,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= 4,

    .saturation 	= LCD_SATURATION_160,

    .contra_index 	= 2,

    .gamma_index 	= {1, 1, 1},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()



#endif


