/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"


typedef struct RES_GIF_DEC_S{
	u16 video_posx, video_posy;
	u16 video_ratiow, video_ratioh;
	u16 video_destw, video_desth;
	u32 gif_delay; //ms
	u32 gif_cur_time;
	u32 dec_state; //0 : STOP, 1: START
}RES_GIF_DEC_T;
ALIGNED(4) static RES_GIF_DEC_T res_gif_dec;
/*******************************************************************************
* Function Name  : res_gif_dec_one_frame
* Description    : res_gif_dec_one_frame
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
static int res_gif_dec_one_frame(void)
{
	int res = 0;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	if(res_gif_dec.dec_state == 0)
	{
		return -1;
	}
	if(gif_Dec_state() <= 0)
	{
		return -1;
	}
	do {
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer == NULL);
	hal_lcdVideoFrameFlush(p_lcd_buffer, res_gif_dec.video_posx, 	res_gif_dec.video_posy, 
										 res_gif_dec.video_ratiow, 	res_gif_dec.video_ratioh, 
										 res_gif_dec.video_destw, 	res_gif_dec.video_desth);	
	res = gif_get_frame(p_lcd_buffer);
	if(res <= 0)
	{
		if(res == 0)
		{
			while((res_gif_dec.gif_cur_time + res_gif_dec.gif_delay) > XOSTimeGet()) hal_wdtClear();
		}
		hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);
	}else
	{
		if(res_gif_dec.gif_delay)
		{
			if((res_gif_dec.gif_cur_time + res_gif_dec.gif_delay) > XOSTimeGet())
				deg_Printf("delay:%dms\n", res_gif_dec.gif_cur_time + res_gif_dec.gif_delay - XOSTimeGet());
			while((res_gif_dec.gif_cur_time + res_gif_dec.gif_delay) > XOSTimeGet()) hal_wdtClear();
		}
		hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
		hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
		res_gif_dec.gif_cur_time = XOSTimeGet();
		res_gif_dec.gif_delay 	 = gif_delay_get();
	}
	return res;

}
/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_gif_show_start(INT32U idx, INT32U uidisable)
{
	int res;
    int addr, size;
	hx330x_bytes_memset((u8*)&res_gif_dec, 0, sizeof(res_gif_dec));
	hal_wdtClear();
	addr = nv_open(idx);
    if(addr < 0)
    {
        return -1;
    }
    size = nv_size(idx);
    if(size <= 0)
    {
        return -2;
    }
	deg_Printf("addr:%x, size:%x\n", addr, size);
	
    if(gif_Dec_Start(MEDIA_SRC_NVFS, addr, size) < 0)
    {
        return -3;
    }
#if 1 //按最大video size显示
	hal_lcdSetRatio(0);
	app_lcdPlayShowScaler_cfg(0,PLAY_SCALER_STAT_KICKBUF, 1);
	hal_lcdGetVideoPos(&res_gif_dec.video_posx,			&res_gif_dec.video_posy);
	hal_lcdGetVideoResolution(&res_gif_dec.video_ratiow,&res_gif_dec.video_ratioh);
	hal_lcdGetVideoResolution(&res_gif_dec.video_destw,	&res_gif_dec.video_desth);
#else  //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&res_gif_dec.video_posx,			&res_gif_dec.video_posy);
	hal_lcdGetVideoRatioResolution(&res_gif_dec.video_ratiow,	&res_gif_dec.video_ratioh);
	hal_lcdGetVideoRatioDestResolution(&res_gif_dec.video_destw,&res_gif_dec.video_desth);
#endif
	res_gif_dec.dec_state = 1;
	
	if(uidisable)
		hal_lcdUiEnable(UI_LAYER0,0);
	if(res_gif_dec_one_frame() <= 0)
	{
		gif_Dec_Stop();
		res_gif_dec.dec_state = 0;
		return -3;
	}else
	{
		return 0;
	}
	
}
/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_gif_show_process(void)
{
	int res = res_gif_dec_one_frame();
#if 0 //循环播放
	if(res == 0)//trailer
	{
		if(gif_loopcnt_get() > 1)
		{
			gif_rewind();
			res = res_gif_dec_one_frame();
		}
	}
#endif
	if(res <= 0)
	{
		res_gif_dec.dec_state = 0;
		gif_Dec_Stop();
		return -1;
	}else{
		return 0;
	}	
}
/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
void res_gif_show_stop(void)
{
	gif_Dec_Stop();
	res_gif_dec.dec_state = 0;
}