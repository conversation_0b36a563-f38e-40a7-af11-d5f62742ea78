/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  IMAGE_DECODE_H
   #define IMAGE_DECODE_H

/*******************************************************************************
* Function Name  : imageDecodeStart
* Description    : imageDecodeStart
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeStart(JPG_DEC_ARG *arg);
/*******************************************************************************
* Function Name  : imageDecodeSpiStart
* Description    : imageDecodeSpiStart
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeSpiStart(JPG_DEC_ARG *arg);
/*******************************************************************************
* Function Name  : imageDecodeGetResolution
* Description	 : image decode get jpeg resolution
* Input 		 : INT16U *width,INT16U *height
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageDecodeGetResolution(INT16U *width,INT16U *height);

/*******************************************************************************
* Function Name  : imageDecodeDirect
* Description    : imageDecodeDirect : should after hal_mjpHeaderParse 
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeDirect(u16 dst_width, u16 dst_height, u8* src_jpg_buf, u8* dst_start_addr);


#endif
