/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayVideoThumbnallWin.c"
#define  PLAYVIDEOTHUMB_WAIT_KEYSOUND_END           while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY){XOSTimeDly(1);}
/*******************************************************************************
* Function Name  : playVideoThumbnallDrawById
* Description    : playVideoThumbnallDrawById
* Input          : winHandle handle, int index,thumbnallID id, lcdshow_frame_t* p_lcd_buffer
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallDrawById(winHandle handle, int index,thumbnallID id, lcdshow_frame_t* p_lcd_buffer)
{
	uiRect rect;
	u16 width, height;
	uiWinGetPos(winItem(handle,id),&rect);

	hal_lcdGetUiResolution(&width,&height);
	if(!(p_lcd_buffer->w == width && p_lcd_buffer->h == height))
	{
		rect.x0 = rect.x0 * p_lcd_buffer->w / width;
		rect.x1 = rect.x1 * p_lcd_buffer->w / width;
		rect.y0 = rect.y0 * p_lcd_buffer->h / height;
		rect.y1 = rect.y1 * p_lcd_buffer->h / height;
	}
	rect.x0 = (rect.x0)&~0x01;
	rect.x1 = (rect.x1 + 2)&~0x01;
	rect.y0 = (rect.y0)&~0x01;
	rect.y1 = (rect.y1 + 2)&~0x01;	
	return taskPlayVideoThumbnallDrawImage(p_lcd_buffer,index,&rect);
}
/*******************************************************************************
* Function Name  : playVideoThumbnallShow
* Description    : playVideoThumbnallShow
* Input          : winHandle handle,int indexStart,u32 num
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoThumbnallPageImgShow(winHandle handle,int indexStart,u32 num)
{
	u16 i;
	u16 x, y, width, height, dest_w, dest_h;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	if(num > THUMBNALL_NUM)
		num = THUMBNALL_NUM;
	do{
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer==NULL);
		
#if 0 //按最大video size显示
	hal_lcdSetRatio(0);
	app_lcdPlayShowScaler_cfg(0,PLAY_SCALER_STAT_KICKBUF, 1);
	hal_lcdGetVideoPos(&x,&y);
	hal_lcdGetVideoResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&x,&y);
	hal_lcdGetVideoRatioResolution(&width,&height);
	hal_lcdGetVideoRatioDestResolution(&dest_w,&dest_h);
#endif
	hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);
	//deg_Printf("playVideoThumbnallPageImgShow:%d\n",num);

	hal_lcdSetBufYUV(p_lcd_buffer,0,0x80,0x80);
	for(i=0;i<num;i++)
	{
		if(playVideoThumbnallDrawById(handle, indexStart+i,THUMBNALL_RECTID(i), p_lcd_buffer) < 0)
			uiWinSetResid(winItem(handle,THUMBNALL_STRID(i)),	RAM_ID_MAKE("ERR"));
		else if(SysCtrl.file_type & FILELIST_TYPE_JPG)
			uiWinSetResid(winItem(handle,THUMBNALL_STRID(i)),	RAM_ID_MAKE("JPG"));
		else
			uiWinSetResid(winItem(handle,THUMBNALL_STRID(i)),	RAM_ID_MAKE("AVI"));
	}
	while(num < THUMBNALL_NUM)
		uiWinSetResid(winItem(handle,THUMBNALL_STRID(num++)), RAM_ID_MAKE(" "));
	hal_lcdVideoSetFrame((void *)p_lcd_buffer);
}
/*******************************************************************************
* Function Name  : playVideoThumbnallShowValid
* Description    : playVideoThumbnallShowValid
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoThumbnallPageShow(winHandle handle)
{
	INT32S indexStart;
	int file_index_temp;
	if(SysCtrl.file_cnt < 1)
		return;
	file_index_temp = SysCtrl.file_index;
	indexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
	playVideoThumbnallPageNumShow(handle, THUMBNALL_PAGEID(SysCtrl.file_index), THUMBNALL_PAGESUM(SysCtrl.file_cnt));
	playVideoThumbnallPageImgShow(handle,indexStart, SysCtrl.file_cnt - indexStart);
	SysCtrl.file_index = file_index_temp;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallKeyMsgOk
* Description    : playVideoThumbnallKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		PLAYVIDEOTHUMB_WAIT_KEYSOUND_END;
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallKeyMsgUp
* Description    : playVideoThumbnallKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	int fileNum,lastIndexStart,curIndexStart;
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		PLAYVIDEOTHUMB_WAIT_KEYSOUND_END;
		if(SysCtrl.file_cnt <= 3)
			return 0;
		lastIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
		playVideoThumbnallSelect(handle,SysCtrl.file_index - lastIndexStart, 0);
		if(SysCtrl.file_index < 3)
		{
			SysCtrl.file_index = SysCtrl.file_cnt - 3  + SysCtrl.file_index;
		}else
		{
			SysCtrl.file_index -= 3;
		}
		curIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
		if(curIndexStart != lastIndexStart)
		{
			playVideoThumbnallPageShow(handle);
		}
		playVideoThumbnallSelect(handle, SysCtrl.file_index - curIndexStart, 1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallKeyMsgDown
* Description    : playVideoThumbnallKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	int lastIndexStart,curIndexStart;
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		task_com_keysound_play();
		PLAYVIDEOTHUMB_WAIT_KEYSOUND_END;
		if(SysCtrl.file_cnt <= 3)
		{
			return 0;
		}
		lastIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
		playVideoThumbnallSelect(handle,SysCtrl.file_index - lastIndexStart, 0);
		if(SysCtrl.file_index >= SysCtrl.file_cnt - 3)
		{
			SysCtrl.file_index = SysCtrl.file_index + 3 - SysCtrl.file_cnt;
		}else
		{
			SysCtrl.file_index += 3;
		}
		curIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
		if(curIndexStart != lastIndexStart)
		{
			playVideoThumbnallPageShow(handle);
		}
		playVideoThumbnallSelect(handle, SysCtrl.file_index - curIndexStart, 1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallKeyMsgMenu
* Description    : playVideoThumbnallKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		PLAYVIDEOTHUMB_WAIT_KEYSOUND_END;	
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallKeyMsgMode
* Description    : playVideoThumbnallKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallKeyMsgMode(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		PLAYVIDEOTHUMB_WAIT_KEYSOUND_END;		
		app_taskChange();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallSysMsgSD
* Description    : playVideoThumbnallSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;

	if(SysCtrl.file_cnt <= 0) // sdc out when recording
	{
		uiOpenWindow(&noFileWindow, 0, 0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallSysMsgUSB
* Description    : playVideoThumbnallSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallSysMsgBattery
* Description    : playVideoThumbnallSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{

	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallSysMsg1S
* Description    : playVideoThumbnallSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallOpenWin
* Description    : playVideoThumbnallOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	u32 i;
	int indexStart;
	playVideoOp.playMode = PLAYVIDEO_THUMBNALL;
	indexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	for(i=0;i < THUMBNALL_NUM;i++)
		playVideoThumbnallSelect(handle, i, 0);
	playVideoThumbnallPageShow(handle);
	playVideoThumbnallSelect(handle, SysCtrl.file_index - indexStart, 1);
	deg_Printf("[WIN]thumbnallOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallCloseWin
* Description    : playVideoThumbnallCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoThumbnallCloseWin\n");
	playVideoOp.playMode = PLAYVIDEO_MAIN;
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallWinChildClose
* Description    : playVideoThumbnallWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	u32 i;
	int indexStart,tem;
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	if(SysCtrl.file_index >= SysCtrl.file_cnt)
		SysCtrl.file_index = SysCtrl.file_cnt -1;
	if(SysCtrl.file_cnt < 1)
	{
		uiOpenWindow(&noFileWindow, 0, 0);
		return 0;
	}
	playVideoOp.playMode = PLAYVIDEO_THUMBNALL;
	tem = SysCtrl.file_index;
	indexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index); 
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	for(i=0;i<THUMBNALL_NUM;i++)
		playVideoThumbnallSelect(handle,i,0);
	playVideoThumbnallPageShow(handle);
	SysCtrl.file_index = tem;
	playVideoThumbnallSelect(handle,SysCtrl.file_index - indexStart, 1);
	deg_Printf("[WIN]playVideoThumbnallWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallTouchWin
* Description    : playVideoThumbnallTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	u32 touchItem=0;
	int curIndexStart;
	if(parameNum!=3)
	{
		//deg_Printf("playVideoThumbnallTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		touchItem = THUMBNALL_TOUCHITEM(parame[0]);
		if(touchItem >= THUMBNALL_NUM)
			return 0;
		curIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
		SysCtrl.file_index = curIndexStart + touchItem;
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoThumbnallTouchSlideOff
* Description    : playVideoThumbnallTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoThumbnallTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	int fileNum,lastIndexStart,curIndexStart;
	if(parameNum!=1)
	{
		deg_Printf("thumbnallSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	fileNum = filelist_api_CountGet(SysCtrl.avi_list);
	if(fileNum < THUMBNALL_NUM)
		return 0;
	lastIndexStart = THUMBNALL_PAGESTARTID(SysCtrl.file_index);
	playVideoThumbnallSelect(handle,SysCtrl.file_index - lastIndexStart, 0);
	playVideoThumbnallSelect(handle,0, 0);
	if(parame[0]== TP_DIR_UP)
	{
		if(lastIndexStart + THUMBNALL_NUM >= fileNum)
			curIndexStart = 0;
		else
			curIndexStart = lastIndexStart + THUMBNALL_NUM;
		SysCtrl.file_index = curIndexStart;
		playVideoThumbnallPageShow(handle);
	}else if(parame[0]== TP_DIR_DOWN)
	{
		if(lastIndexStart < THUMBNALL_NUM)
			curIndexStart = THUMBNALL_PAGESTARTID(fileNum); 
		else
			curIndexStart = lastIndexStart - THUMBNALL_NUM;
		SysCtrl.file_index = curIndexStart;
		playVideoThumbnallPageShow(handle);		
	}

	return 0;
}

ALIGNED(4) msgDealInfor playVideoThumbnallMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	playVideoThumbnallOpenWin},
	{SYS_CLOSE_WINDOW,	playVideoThumbnallCloseWin},
	{SYS_CHILE_COLSE,	playVideoThumbnallWinChildClose},
	{SYS_TOUCH_WINDOW,  playVideoThumbnallTouchWin},
	{SYS_TOUCH_SLIDE_OFF,playVideoThumbnallTouchSlideOff},	
	{KEY_EVENT_OK,		playVideoThumbnallKeyMsgOk},
	{KEY_EVENT_UP,		playVideoThumbnallKeyMsgUp},
	{KEY_EVENT_DOWN,	playVideoThumbnallKeyMsgDown},
	{KEY_EVENT_MENU,	playVideoThumbnallKeyMsgMenu},
	{KEY_EVENT_MODE,	playVideoThumbnallKeyMsgMode},
	{SYS_EVENT_SDC,		playVideoThumbnallSysMsgSD},
	{SYS_EVENT_USBDEV,	playVideoThumbnallSysMsgUSB},
	{SYS_EVENT_BAT,		playVideoThumbnallSysMsgBattery},
	{SYS_EVENT_1S,		playVideoThumbnallSysMsg1S},
	{EVENT_MAX,NULL},
};

WINDOW(playVideoThumbnallWindow,playVideoThumbnallMsgDeal,playVideoThumbnallWin)



