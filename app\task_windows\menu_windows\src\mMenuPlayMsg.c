/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


/*******************************************************************************
* Function Name  : menuProcDelCur
* Description    : menuProcDelCur
* Input          : winHandle handle,uint32 parameNum,uint32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcDelCur(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&delCurWindow, 0, 0);
}
/*******************************************************************************
* Function Name  : menuProcDelCur
* Description    : menuProcDelCur
* Input          : winHandle handle,uint32 parameNum,uint32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcDelAll(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&delAllWindow, 0, 0);
}
/*******************************************************************************
* Function Name  : menuProcLockCur
* Description    : menuProcLockCur
* Input          : winHandle handle,uint32 parameNum,uint32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcLockCur(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&lockCurWindow,0,0);
}
/*******************************************************************************
* Function Name  : menuProcUnlockCur
* Description    : menuProcUnlockCur
* Input          : winHandle handle,uint32 parameNum,uint32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcUnlockCur(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&unlockCurWindow,0,0);
}
/*******************************************************************************
* Function Name  : menuProcUnlockAll
* Description    : menuProcUnlockAll
* Input          : winHandle handle,uint32 parameNum,uint32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcUnlockAll(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&unlockAllWindow,0,0);
}






