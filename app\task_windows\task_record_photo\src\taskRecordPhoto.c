/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) taskRecordPhotoOp recordPhotoOp;

/*******************************************************************************
* Function Name  : filelist_remain
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void taskRecordPhotoRemainCal(void)
{
	u16 dst_width, dst_height;
	u32 file_size; //kb
	u32 remain_size; //kb
	u32 reamin_file_max;
	u32 filelist_remain;
	u32 quli;
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		dst_width 	= user_configValue2Int(CONFIG_ID_PRESLUTION)>>16;
		dst_height 	= user_configValue2Int(CONFIG_ID_PRESLUTION)&0xffff;
		remain_size = SysCtrl.sdc_freesize;
		filelist_remain = filelist_api_MaxCountGet(SysCtrl.jpg_list) - filelist_api_CountGet(SysCtrl.jpg_list);
		quli = 8;
	}else if( SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		recordPhotoOp.file_remain = 0;
		return;
	}else
	{
		//hal_SensorResolutionGet(&dst_width,&dst_height);
		hal_lcdGetVideoRatioResolution(&dst_width,&dst_height);
		if(SysCtrl.spi_jpg_list < 0)
		{
			recordPhotoOp.file_remain = 0;
			return;
		}else
		{
			remain_size     = nvjpg_free_size()/1024;
			filelist_remain = filelist_api_MaxCountGet(SysCtrl.spi_jpg_list) - filelist_api_CountGet(SysCtrl.spi_jpg_list);
			if(filelist_remain > nvjpg_free_dir())
				filelist_remain = nvjpg_free_dir();
		}
		quli = 22;
	}
	file_size = (u32)dst_width*dst_height*quli/(110*1024);
	reamin_file_max = remain_size/file_size;
	deg_Printf("file_size:%dkb, remain_size:%dkb\n", file_size, remain_size);
	recordPhotoOp.file_remain = (reamin_file_max > filelist_remain)?filelist_remain: reamin_file_max;
	deg_Printf("taskRecordPhotoRemainCal:reamin_file_max: %d, filelist_remain:%d\n", reamin_file_max, filelist_remain);

}
/*******************************************************************************
* Function Name  : app_taskRecordPhoto_callback
* Description    : APP LAYER: app_taskRecordPhoto
* Input          : INT32U channel,INT32U cmd,INT32U para
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int app_taskRecordPhoto_callback(INT32U channel,INT32U cmd,INT32U para)
{
	int *fdt = (int *)para;
	int ret;
	char *name;
	deg_Printf("CMD:%d\n",cmd);
	

	if(SysCtrl.spi_jpg_list < 0)
	{
		if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
		{
			//sd_api_unlock();
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
			return -1;
		}
	}
	if(cmd == CMD_PHOTO_RECORD_START)
	{
		SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
		if(recordPhotoOp.file_remain <= 0)
		{
			if(SysCtrl.spi_jpg_list < 0)
			{
				task_com_sdc_stat_set(SDC_STAT_FULL);
				return -5;
			}else
			{
				task_com_tips_show(TIPS_TYPE_SPI);
				return -6;	
			}
		}
		if(SysCtrl.spi_jpg_list < 0)
		{
		ret = filelist_SpaceCheck(SysCtrl.jpg_list,&SysCtrl.sdc_freesize,1024L*5); //5M
		if(ret<0)
		{
			task_com_sdc_stat_set(SDC_STAT_FULL);
			return -2;
		}
	//---------creater file name
		name = filelist_createNewFileFullName(SysCtrl.jpg_list, &SysCtrl.jpg_fname); //try to create new file name
		if(name == NULL)
		{
			deg_Printf("photo : create file name fail.\n");
			task_com_sdc_stat_set(SDC_STAT_FULL);
			return -3;
		}
		hx330x_str_cpy(SysCtrl.file_fullname,name);
	//---------open file from file system
		//sd_api_lock();
		*fdt = fs_open(name,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
		if(*fdt < 0)
		{
			deg_Printf("photo : open file fail.%s\n",name);
			return -4;
			}	
		}else{
			name = filelist_createNewFileFullName(SysCtrl.spi_jpg_list, &SysCtrl.jpg_fname); //try to create new file name
			if(name == NULL)
			{
				deg_Printf("photo : create file name fail.\n");
				task_com_tips_show(TIPS_TYPE_SPI);
				return -3;
			}	
			hx330x_str_cpy(SysCtrl.file_fullname,name);
			deg_Printf("photo : open file.%s\n",name);
			if(NV_OK != nv_jpg_open(SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK,NVFA_CREATE_ALWAYS | NVFA_WRITE | NVFA_READ))
			{
				*fdt = -1;
				deg_Printf("photo : open file fail.%s\n",name);
				task_com_tips_show(TIPS_TYPE_SPI);
				return -4;
			}
			else
			{
				*fdt = SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK;
			}		
		}
	}else if(cmd == CMD_PHOTO_RECORD_STOP || cmd == CMD_COM_ERROR)
	{
		if((*fdt >= 0) && !(SysCtrl.jpg_fname.index & FILELIST_FLAG_IVL))
		{
			if(SysCtrl.spi_jpg_list < 0)
			{
			u32 filesize = fs_size(*fdt);
			fs_close(*fdt);
			if(cmd == CMD_PHOTO_RECORD_STOP)
			{
				task_com_sdc_freesize_modify(-1,filesize);
				filenode_addFileByFname(SysCtrl.jpg_list, &SysCtrl.jpg_fname);
				deg_Printf("photo  %d: take photo ok.<%s>\n",channel,SysCtrl.file_fullname);
			}else
			{
				f_unlink(SysCtrl.file_fullname);
				deg_Printf("photo  %d: take photo fail.<%s>\n",channel,SysCtrl.file_fullname);
					SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
			}
				//SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
			*fdt = -1;
			}else
			{
				nv_jpg_close();
				if(cmd == CMD_PHOTO_RECORD_STOP)
				{
					filenode_addFileByFname(SysCtrl.spi_jpg_list, &SysCtrl.jpg_fname);
					deg_Printf("photo : take photo ok.<%s>\n",SysCtrl.file_fullname);
				}else
				{
					nv_jpgfile_delete(SysCtrl.jpg_fname.index & FILELIST_INDEX_MASK);
					deg_Printf("photo  : take photo fail.<%s>\n",SysCtrl.file_fullname);
					SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;				
				}
			}

			//sd_api_unlock();
		}
	}
	return 0;
}


/*******************************************************************************
* Function Name  : taskRecordPhotoProcess
* Description    : take a photo by user config
* Input          : none
* Output         : none                                            
* Return         : int <0 fail
*******************************************************************************/
int taskRecordPhotoProcess(void)
{
	JPG_ENC_ARG jpg_arg;
	int ret = 0;
	static u32 index_test = 0;
	jpg_arg.fd 			= -1;
	jpg_arg.dst_width 	= user_configValue2Int(CONFIG_ID_PRESLUTION)>>16;
	jpg_arg.dst_height 	= user_configValue2Int(CONFIG_ID_PRESLUTION)&0xffff;
	jpg_arg.img_Q		= JPEG_Q_75;
	jpg_arg.timestamp	= user_configValue2Int(CONFIG_ID_TIMESTAMP);
	jpg_arg.buf			= NULL;
	jpg_arg.size		= 0;
	if(SysCtrl.spi_jpg_list < 0)
	{
		sd_api_lock();
	}else
	{
		hal_lcdGetVideoRatioResolution(&jpg_arg.dst_width,&jpg_arg.dst_height);
		//hal_SensorResolutionGet(&jpg_arg.dst_width,&jpg_arg.dst_height);
		//hal_SensorRatioResolutionGet(&jpg_arg.dst_width,&jpg_arg.dst_height);
		jpg_arg.img_Q		= JPEG_Q_75;//JPEG_Q_40; //JPEG_Q_27
	}
//-------------channel A
    if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
    {
		ret = app_taskRecordPhoto_callback(VIDEO_CH_A,CMD_PHOTO_RECORD_START,(INT32U)&jpg_arg.fd);
		if(ret < 0)
			goto TAKE_PHOTO_END;
		if(SysCtrl.spi_jpg_list < 0)
		{
		deg_Printf("take photoA : [%d:%d]\n",jpg_arg.dst_width,jpg_arg.dst_height);
		ret = imageEncodeStart(&jpg_arg);
		}else
		{
			deg_Printf("take photo SPI: [%d:%d]\n",jpg_arg.dst_width,jpg_arg.dst_height);
			ret = imageEncodeToSpi(&jpg_arg, 0);
		}
		deg_Printf("take photoA : %d\n",ret);
		if(ret<0)
		{
			app_taskRecordPhoto_callback(VIDEO_CH_A,CMD_COM_ERROR,(INT32U)&jpg_arg.fd);
			goto TAKE_PHOTO_END;
		}
		app_taskRecordPhoto_callback(VIDEO_CH_A,CMD_PHOTO_RECORD_STOP,(INT32U)&jpg_arg.fd);
    }
//--------channel B
    if(!husb_api_usensor_tran_sta())
		goto TAKE_PHOTO_END;
	if( SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINB )
	{
		ret = app_taskRecordPhoto_callback(VIDEO_CH_B,CMD_PHOTO_RECORD_START,(INT32U)&jpg_arg.fd);
		if(ret < 0)
			goto TAKE_PHOTO_END;		
		husb_api_usensor_res_get(&jpg_arg.dst_width , &jpg_arg.dst_height);	
		
		deg_Printf("take photoB : [%d:%d]\n",jpg_arg.dst_width,jpg_arg.dst_height);
		ret = imageEncodeStartB(&jpg_arg);
		if(ret<0)
		{
			app_taskRecordPhoto_callback(VIDEO_CH_B,CMD_COM_ERROR,(INT32U)&jpg_arg.fd);
			goto TAKE_PHOTO_END;
		}
	//-------close file & add to file list
	   app_taskRecordPhoto_callback(VIDEO_CH_B,CMD_PHOTO_RECORD_STOP,(INT32U)&jpg_arg.fd);
	}
 	if(SysCtrl.spi_jpg_list < 0)
    	sd_api_Stop();
    ret = 0;
TAKE_PHOTO_END:
	if(ret >= 0)
	{
		recordPhotoOp.file_remain--;	
	}
	if(SysCtrl.spi_jpg_list < 0)
	sd_api_unlock();
	return ret;
}
/*******************************************************************************
* Function Name  : taskRecordPhotoOpen
* Description    : taskRecordPhotoOpen
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskRecordPhotoOpen(u32 arg)
{
	//deg_Printf("[TASK OPEN]<RecordPhoto>\n");
    imageEncodeInit();
	app_lcdCsiVideoShowStart();
	SysCtrl.lcdshow_win_mode_save = SysCtrl.lcdshow_win_mode;
	SysCtrl.lcdshow_win_mode 	 = LCDSHOW_ONLYWINA;
    app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	while(hardware_setup.lcd_first_drop) //等待sensor OK
	{
		hal_wdtClear();
	}
	uiOpenWindow(&recordPhotoWindow,0,0);	
}
/*******************************************************************************
* Function Name  : taskRecordPhotoClose
* Description    : taskRecordPhotoClose
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskRecordPhotoClose(u32 arg)
{
	SysCtrl.lcdshow_win_mode = SysCtrl.lcdshow_win_mode_save;
	app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);

	//app_lcdCsiVideoShowStop();
	imageEncodeUninit();
	//videoRecordUninit();
	if(SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		SysCtrl.dev_stat_sdc = SDC_STAT_NORMAL;
	}
	task_com_spijpg_Init(1);
	task_com_sdlist_scan(1, 0);
	//deg_Printf("[TASK CLOSE]<RecordPhoto>\n");
}


ALIGNED(4) sysTask_T taskRecordPhoto =
{
	"Record Photo",
	0,
	taskRecordPhotoOpen,
	taskRecordPhotoClose,
	NULL,
};


