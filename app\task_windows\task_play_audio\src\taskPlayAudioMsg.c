/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayAudioWin.c"
#define  PLAYAUDIO_WAIT_KEYSOUND_END           while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY){XOSTimeDly(1);}
static char (*play_audio_fullname)[FILE_FULLNAME_LEN];
/*******************************************************************************
* Function Name  : getPlayAudioResInfor
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getPlayAudioResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= INVALID_RES_ID;
	if(str)
	{
		char *name = filelist_GetFileFullNameByIndex(SysCtrl.wav_list,item,NULL);
		if(play_audio_fullname)
		{
			if(name)
			{
				hx330x_str_cpy(play_audio_fullname[item%MAX_ROW_NUM],name);
				*str = (u32)play_audio_fullname[item%MAX_ROW_NUM];
			}else
			{
				*str = (u32)0;
			}
		}else
		{
			*str = (u32)name;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioOpenWin
* Description    : playAudioOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	int sum = filelist_api_CountGet(SysCtrl.wav_list);
	if(sum <= 0)
		sum = 0;
	//if(play_audio_fullname)
		play_audio_fullname = (char (*)[FILE_FULLNAME_LEN])hal_sysMemMalloc(FILE_FULLNAME_LEN*MAX_ROW_NUM);
	if(play_audio_fullname == NULL)
	{
		deg_Printf("play_audio_fullname malloc fail\n");
	}
	playAudioPlayTimeShow(handle);
	playAudioSDShow(handle);
	playAudioBaterryShow(handle);
	uiItemManageSetHeightAvgGap(winItem(handle,PLAYAUDIO_SELECT_ID),	Rh(33));
	uiItemManageCreateItem(		winItem(handle,PLAYAUDIO_SELECT_ID),	uiItemCreateMenuOption,	getPlayAudioResInfor, sum);
	uiItemManageSetCharInfor(	winItem(handle,PLAYAUDIO_SELECT_ID),	DEFAULT_FONT,	ALIGNMENT_LEFT,		R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,PLAYAUDIO_SELECT_ID),	R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,PLAYAUDIO_SELECT_ID),	R_ID_PALETTE_Gray);
	uiItemManageSetCurItem(		winItem(handle,PLAYAUDIO_SELECT_ID),	SysCtrl.file_index);
	if(sum == 0)
		uiOpenWindow(&noFileWindow,0, 1, "no audio file");
	else
		app_taskPlayAudio_start(SysCtrl.file_index);
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioCloseWin
* Description    : playAudioCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(play_audio_fullname)
		hal_sysMemFree(play_audio_fullname);
	play_audio_fullname = NULL;
	deg_Printf("[WIN]playAudioCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioWinChildClose
* Description    : playAudioWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	int sum = filelist_api_CountGet(SysCtrl.wav_list);
	if(sum <= 0)
	{
		uiOpenWindow(&noFileWindow, 0, 1, "no audio file");
		return 0;
	}
	if(SysCtrl.file_index >= sum)
		SysCtrl.file_index = 0;
	uiItemManageUpdateRes(winItem(handle,PLAYAUDIO_SELECT_ID),sum,SysCtrl.file_index);
	app_taskPlayAudio_start(SysCtrl.file_index);
	//playAudioPlayTimeShow(handle);
	playAudioSDShow(handle);
	playAudioBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioKeyMsgOk
* Description    : playAudioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(audioPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			u32 item = uiItemManageGetCurrentItem(winItem(handle,PLAYAUDIO_SELECT_ID));
			if(SysCtrl.file_index == item)
			{
				app_taskPlayAudio_start(SysCtrl.file_index);
			}
			audioPlaybackResume();
		}
		else if(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)
		{
			audioPlaybackPause();
			PLAYAUDIO_WAIT_KEYSOUND_END;
			//task_com_keysound_play();
		}else
		{
			//task_com_keysound_play();
			//PLAYAUDIO_WAIT_KEYSOUND_END;
			audioPlaybackResume();
		}
		playAudioPlayTimeShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioKeyMsgUp
* Description    : playAudioKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,PLAYAUDIO_SELECT_ID));
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYAUDIO_SELECT_ID));
		if(SysCtrl.file_index != item)
		{
			if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
				audioPlaybackStop();
			}
			task_com_keysound_play();
			PLAYAUDIO_WAIT_KEYSOUND_END;
			SysCtrl.file_index = item;
			app_taskPlayAudio_start(SysCtrl.file_index);
			//task_com_keysound_play();
		}

	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioKeyMsgDown
* Description    : playAudioKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,PLAYAUDIO_SELECT_ID));
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYAUDIO_SELECT_ID));
		if(SysCtrl.file_index != item)
		{
			if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
				audioPlaybackStop();
			}
			task_com_keysound_play();
			PLAYAUDIO_WAIT_KEYSOUND_END;
			SysCtrl.file_index = item;
			app_taskPlayAudio_start(SysCtrl.file_index);
			//task_com_keysound_play();
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgMenu
* Description    : playVideoMainKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if((audioPlaybackGetStatus() != MEDIA_STAT_START)&&(filelist_api_CountGet(SysCtrl.wav_list)>0))
		{
			audioPlaybackStop();
			task_com_keysound_play();
            uiOpenWindow(&menuItemWindow, 0, 1,(u32)&MENU(playBack));
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioKeyMsgMode
* Description    : playAudioKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(audioPlaybackGetStatus() != MEDIA_STAT_PLAY)
		{
			if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
				audioPlaybackStop();
			task_com_keysound_play();
			PLAYAUDIO_WAIT_KEYSOUND_END;
			app_taskChange();
		}


	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgSD
* Description    : playAudioSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	int sum;
	deg_Printf("[Play Audio] sdc stat ->%d\n",SysCtrl.dev_stat_sdc);
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL))
	{
		if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
			audioPlaybackStop();
		uiOpenWindow(&noFileWindow, 0, 1, "no audio file");
	}
	else
	{
		sum = filelist_api_CountGet(SysCtrl.wav_list);
		if(sum <= 0)
		{
			uiOpenWindow(&noFileWindow, 0, 1, "no audio file");
			return 0;
		}
		if(SysCtrl.file_index >= sum)
			SysCtrl.file_index = 0;
		uiItemManageUpdateRes(winItem(handle,PLAYAUDIO_SELECT_ID),sum,SysCtrl.file_index);
		app_taskPlayAudio_start(SysCtrl.file_index);
		playAudioSDShow(handle);
		playAudioPlayTimeShow(handle);

	}

	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgUSB
* Description    : playAudioSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	playAudioBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgBattery
* Description    : playAudioSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		playAudioBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsg1S
* Description    : playAudioSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if(uiWinIsVisible(winItem(handle,PLAYAUDIO_BATERRY_ID)))
			uiWinSetVisible(winItem(handle,PLAYAUDIO_BATERRY_ID),0);
		else
		{
			uiWinSetVisible(winItem(handle,PLAYAUDIO_BATERRY_ID),1);
			uiWinSetResid(winItem(handle,PLAYAUDIO_BATERRY_ID),R_ID_ICON_MTBATTERY5);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgTimeUpdate
* Description    : playAudioSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playAudioSysMsgTimeUpdate(winHandle handle,uint32 parameNum,uint32* parame)
{
	playAudioPlayTimeShow(handle);
	return 0;
}


ALIGNED(4) msgDealInfor playAudioMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playAudioOpenWin},
	{SYS_CLOSE_WINDOW,		playAudioCloseWin},
	{SYS_CHILE_COLSE,		playAudioWinChildClose},
	{KEY_EVENT_OK,			playAudioKeyMsgOk},
	{KEY_EVENT_UP,			playAudioKeyMsgUp},
	{KEY_EVENT_DOWN,		playAudioKeyMsgDown},
	{KEY_EVENT_MENU,		playAudioKeyMsgMenu},
	{KEY_EVENT_MODE,		playAudioKeyMsgMode},
	{SYS_EVENT_SDC,			playAudioSysMsgSD},
	{SYS_EVENT_USBDEV,		playAudioSysMsgUSB},
	{SYS_EVENT_BAT,			playAudioSysMsgBattery},
	{SYS_EVENT_1S,			playAudioSysMsg1S},
	{SYS_EVENT_TIME_UPDATE,	playAudioSysMsgTimeUpdate},
	{EVENT_MAX,NULL},
};

WINDOW(playAudioWindow,playAudioMsgDeal,playAudioWin)


