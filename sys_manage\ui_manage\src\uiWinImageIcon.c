/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiImageIconProc
* Description    : uiImageIconProc
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiImageIconProc(uiWinMsg* msg)
{
	winHandle 		hWin;
	uiImageIconObj  *pimageIcon;
	uiWinObj		*pWin;
	uiResInfo* 		resInfor;
	if(uiWidgetProc(msg))
		return;
	hWin 		= msg->curWin;
	pimageIcon 	= (uiImageIconObj*)uiHandleToPtr(hWin);
	pWin		= &(pimageIcon->widget.win);

	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("imageIcon win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pimageIcon->image.id != INVALID_RES_ID && pimageIcon->image.visible == 1)
			{
				if(pimageIcon->select)
				{
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pimageIcon->imageSelect); //pWin->bgColor
					
				}else
				{
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pimageIcon->image); //pWin->bgColor					
				}
			}
				
			else if(pWin->bgColor != INVALID_COLOR)
				uiWinDrawRect((uiRect*)(msg->para.p),pWin->bgColor);
			//deg_msg("paint imageIcon [%d]:[%d %d %d %d]\n",pimageIcon->widget.id,pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_CHANGE_RESID:
			if(pimageIcon->image.id == msg->para.v)
				return;
			pimageIcon->image.id 		= msg->para.v;
			pimageIcon->imageSelect.id 	= msg->para.v;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_VISIBLE_SET:
			if(pWin->bgColor == INVALID_COLOR)
				break;
			pimageIcon->image.visible = msg->para.v;
			uiWinUpdateInvalid(hWin);
			return;
		case MSG_WIN_VISIBLE_GET:
			if(pWin->bgColor == INVALID_COLOR)
				break;
			msg->para.v = pimageIcon->image.visible;
			return;
		case MSG_WIN_UNSELECT:
			if(pimageIcon->select == 0)
				return;
			pimageIcon->select = 0;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_SELECT:
			if(pimageIcon->select == 1)
				return;
			pimageIcon->select = 1;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			resInfor = (uiResInfo*)(msg->para.p);	
			pimageIcon->imageSelect.iconColor   = resInfor->fontColor;	
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			resInfor = (uiResInfo*)(msg->para.p);	
			pimageIcon->image.iconColor   		= resInfor->fontColor;
			return;
		case MSG_WIN_CHANGE_FG_COLOR:
			if(pimageIcon->image.iconColor == msg->para.v)
				return;
			pimageIcon->image.iconColor= msg->para.v;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin		= pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle	= hWin;
			((touchInfor *)(msg->para.p))->touchID		= pimageIcon->widget.id;
			((touchInfor *)(msg->para.p))->touchItem	= 0;
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : imageIconCreate
* Description    : imageIconCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiImageIconCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id)
{
	winHandle 		himageIcon;
	uiImageIconObj	*pimageIcon;
	himageIcon	= uiWinCreate(x0,y0,width,height,parent,uiImageIconProc,sizeof(uiImageIconObj),WIN_WIDGET|style);
	if(himageIcon!=INVALID_HANDLE)
	{
		uiWidgetSetId(himageIcon,id);
		pimageIcon = (uiImageIconObj*)uiHandleToPtr(himageIcon);
		pimageIcon->image.id 		= INVALID_RES_ID;
		pimageIcon->image.iconAlign	= ALIGNMENT_CENTER;
		pimageIcon->image.bgColor	= INVALID_COLOR;
		pimageIcon->image.iconColor	= INVALID_COLOR;
		pimageIcon->image.visible	= 1;
		pimageIcon->imageSelect.id 			= INVALID_RES_ID;
		pimageIcon->imageSelect.iconAlign	= ALIGNMENT_CENTER;
		pimageIcon->imageSelect.bgColor		= INVALID_COLOR;
		pimageIcon->imageSelect.iconColor	= INVALID_COLOR;
		pimageIcon->imageSelect.visible		= 1;
		pimageIcon->select			= 0;
		uiWinSetbgColor(himageIcon,INVALID_COLOR);
	}
	return himageIcon;
}
/*******************************************************************************
* Function Name  : imageIconCreate
* Description    : imageIconCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiImageIconCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 		himageIcon;
	uiImageIconObj* pimageIcon;
	himageIcon = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiImageIconProc,sizeof(uiImageIconObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	if(himageIcon!=INVALID_HANDLE)
	{
		pimageIcon = (uiImageIconObj*)uiHandleToPtr(himageIcon);
		pimageIcon->image.id 		= infor->image;
		pimageIcon->image.iconAlign	= infor->imageAlign;
		pimageIcon->image.bgColor	= infor->bgColor;
		pimageIcon->image.iconColor	= INVALID_COLOR;
		pimageIcon->image.visible	= 1;
		pimageIcon->imageSelect.id 			= infor->image;
		pimageIcon->imageSelect.iconAlign	= infor->imageAlign;
		pimageIcon->imageSelect.bgColor		= infor->bgColor;
		pimageIcon->imageSelect.iconColor	= INVALID_COLOR;
		pimageIcon->imageSelect.visible		= 1;
		pimageIcon->select			= 1;		
		uiWidgetSetId(himageIcon,infor->id);
		uiWinSetbgColor(himageIcon,infor->bgColor);
	}	

	return himageIcon;
}