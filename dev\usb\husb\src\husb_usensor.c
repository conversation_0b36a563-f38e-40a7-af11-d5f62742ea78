/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) HUSB_HANDLE* usensor_handle;

/*******************************************************************************
* Function Name  : husb_api_handle_reg
* Description    : husb_api_handle_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_handle_reg(void *handle)
{
	usensor_handle = (HUSB_HANDLE*)handle;
}

/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_tran_sta(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_UVC_TRAN)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_atech_sta(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_UVC_ATECH)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
s32 husb_api_usensor_res_get(u16* width, u16* height)
{
	//*width  = 0;
	//*height = 0;
	if(usensor_handle)
	{
		*width  = usensor_handle->usbsta.usensor_res.width;
		*height = usensor_handle->usbsta.usensor_res.height;
		return 0;
	}
	return -1;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_mjp(void)
{

	if(usensor_handle)
	{
		if(usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
		{
			return 1;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_yuv(void)
{

	if(usensor_handle)
	{
		if(usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_YUV)
		{
			return 1;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : husb_api_astern_set
* Description    : husb_api_astern_set
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_astern_set(bool sta)
{
	if(usensor_handle)
	{
		if(sta)
			usensor_handle->usbsta.device_sta |= USB_ASTERN;
		else
			usensor_handle->usbsta.device_sta &= ~USB_ASTERN;
	}
}
/*******************************************************************************
* Function Name  : husb_api_astern_get
* Description    : husb_api_astern_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_astern_get(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_ASTERN)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_detech_check
* Description    : husb_api_detech_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_detech_check(void)
{
	if((usensor_handle)&&(usensor_handle->usbsta.device_sta & USB_DTECH))
		return true;
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_linkingLcd
* Description    : husb_api_usensor_linkingLcd:check fisrt frame and link to LCD SHOW
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_linkingLcd(void)
{
	husb_uvc_linking(usensor_handle);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_relinkLcd_reg
* Description    : husb_api_usensor_relinkLcd_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_relinkLcd_reg(void)
{
	husb_uvc_relink_register(usensor_handle);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_dcdown
* Description    : husb_api_usensor_dcdown
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_dcdown(u8 err)
{
	if(usensor_handle)
		huvc_cache_dcd_down(usensor_handle,err);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_detech
* Description    : husb_api_usensor_detech: for software remove usb
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_detech(void)
{
	if(!husb_api_usensor_tran_sta()){
		return;
	}
	if(usensor_handle->usbsta.ch == USB20_CH)
	{
		husb_api_u20_remove();
	}else if(usensor_handle->usbsta.ch == USB11_CH)
	{
		husb_api_u11_remove();
	}
	usensor_handle->usbsta.device_sta = USB_DTECH;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asterncheck(void)
{
	if(usensor_handle)
	{
		husb_api_ep0_asterncheck_kick(usensor_handle);
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asternset(u32 astern)
{
	if(usensor_handle)
	{
		if(astern)
			usensor_handle->usbsta.device_sta |= USB_ASTERN;
		else
			usensor_handle->usbsta.device_sta &= ~USB_ASTERN;
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_frame_read
* Description    : husb_api_usensor_frame_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 *husb_api_usensor_frame_read(u32 *len)
{
	static u8 *pbuf;
	if(husb_api_usensor_tran_sta())
	{
		if(husb_uvc_frame_read(usensor_handle, &pbuf, len))
		{
			//if(hal_mjpDecodeParse((u8 *)pbuf,usensor_handle->usbsta.usensor_res.width,usensor_handle->usbsta.usensor_res.height) < 0)
			//{
			//	huvc_cache_dcd_down(usensor_handle,1);
			//	return NULL;
			//}
			return (u8 *)pbuf;
		}
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_notran_check
* Description    : husb_api_usensor_notran_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_notran_check(void)
{
	//static u32 cnt_out = 0;
	if(husb_api_usensor_tran_sta())
	{
		//deg_Printf("USENSOR fps:%d\n",usensor_handle->usbsta.uvc_fstack.jUcnt);
		if(usensor_handle->usbsta.uvc_fstack.jUcnt == 0)
		{
			usensor_handle->usbsta.uvc_fstack.notran_cnt++;
			if(usensor_handle->usbsta.uvc_fstack.notran_cnt >= 1)
			{
				usensor_handle->usbsta.uvc_fstack.notran_cnt = 0;
				deg_Printf("UVC[%d] no transfer!!\n",usensor_handle->usbsta.ch);
				if(usensor_handle->usbsta.ch == USB20_CH)
				{
					husb_api_u20_remove();
				}else if(usensor_handle->usbsta.ch == USB11_CH)
				{
					husb_api_u11_remove();
				}
			}
		}else
		{
			usensor_handle->usbsta.uvc_fstack.notran_cnt = 0;
		}

		usensor_handle->usbsta.uvc_fstack.jUcnt = 0;
	}
}

