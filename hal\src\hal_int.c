/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"


/*******************************************************************************
* Function Name  : hal_intInit
* Description    : int initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_intInit(void)
{
//===for example===
	hal_intEnable(IRQ_GPIO,0);			// gpio int disable
	hal_int_priority(IRQ_GPIO,1);		// set  gpio int is high priority

}




