/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiButtonProc
* Description    : uiButtonProc
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiButtonProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiButtonObj* pButton;
	uiWinObj* pWin;
	if(uiWidgetProc(msg))
		return;
	hWin 	= msg->curWin;
	pButton = (uiButtonObj*)uiHandleToPtr(hWin);
	pWin	= &(pButton->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			deg_msg("button win create\n");
			return;
		case MSG_WIN_PAINT:
			uiWinDrawRect((uiRect*)(msg->para.p),pWin->bgColor);
			if(pButton->image.id != INVALID_RES_ID)
				uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pButton->image);
			if(pButton->string.id != INVALID_RES_ID)
				uiWinDrawString(&pWin->rect,(uiRect*)(msg->para.p),&pButton->string);
			//deg_msg("paint button [%d]:[%d %d %d %d]\n",pButton->widget.id,pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiButtonCreate
* Description    : uiButtonCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiButtonCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 	hButton;
	uiButtonObj *pButton;
	hButton =  uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiButtonProc,sizeof(uiButtonObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	if(hButton != INVALID_HANDLE)
	{
		pButton = (uiButtonObj*)uiHandleToPtr(hButton);
		pButton->image.id 			= infor->image;
		pButton->image.iconAlign 	= infor->imageAlign;
		pButton->image.bgColor		= infor->bgColor;
		pButton->image.iconColor	= INVALID_COLOR;
		pButton->image.visible		= 1;
		pButton->string.id			= infor->str;
		pButton->string.style		= infor->style;
		pButton->string.strAlign	= infor->strAlign;
		pButton->string.font		= infor->font;
		pButton->string.fontColor 	= infor->fontColor;
		pButton->string.bgColor 	= infor->bgColor;
		pButton->string.rimColor 	= infor->rimColor;	
		pButton->string.visible		= 1;
		uiWidgetSetId(hButton,infor->id);
		if(infor->bgColor != INVALID_COLOR)
			uiWinSetbgColor(hButton, infor->bgColor);
	}
	return hButton;
}