
extern R_STRING_T User_String_Table[];

enum r_str_id_e {
    R_ID_STR_LAN_ENGLISH = RES_ID_TYPE_STR,
    R_ID_STR_LAN_SCHINESE,
    R_ID_STR_LAN_TCHINESE,
    R_ID_STR_LAN_JAPANESE,
    R_ID_STR_LAN_KOERA,
    R_ID_STR_LAN_RUSSIAN,
    R_ID_STR_LAN_TURKEY,
    R_ID_STR_LAN_TAI,
    R_ID_STR_LAN_CZECH,
    R_ID_STR_LAN_UKRAINIAN,
    R_ID_STR_LAN_GERMAN,
    R_ID_STR_LAN_FRECH,
    R_ID_STR_LAN_ITALIAN,
    R_ID_STR_LAN_PORTUGUESE,
    R_ID_STR_LAN_DUTCH,
    R_ID_STR_LAN_HEBREW,
    R_ID_STR_LAN_POLISH,
    R_ID_STR_UPDATE_START,
    R_ID_STR_UPDATE_END,
    R_ID_STR_COM_OFF,
    R_ID_STR_COM_ON,
    R_ID_STR_COM_OK,
    R_ID_STR_COM_CANCEL,
    R_ID_STR_COM_YES,
    R_ID_STR_DC_OUT,
    R_ID_STR_COM_NO,
    R_ID_STR_COM_LOW,
    R_ID_STR_COM_MIDDLE,
    R_ID_STR_COM_HIGH,
    R_ID_STR_COM_WAITING,
    R_ID_STR_COM_50HZ,
    R_ID_STR_COM_60HZ,
    R_ID_STR_COM_P2_0,
    R_ID_STR_COM_P1_0,
    R_ID_STR_COM_P0_0,
    R_ID_STR_COM_N1_0,
    R_ID_STR_COM_N2_0,
    R_ID_STR_COM_ALWAYSON,
    R_ID_STR_COM_ECONOMIC,
    R_ID_STR_COM_NORMAL,
    R_ID_STR_COM_FINE,
    R_ID_STR_TIM_1MIN,
    R_ID_STR_TIM_2MIN,
    R_ID_STR_TIM_3MIN,
    R_ID_STR_TIM_5MIN,
    R_ID_STR_TIM_10MIN,
    R_ID_STR_TIM_15MIN,
    R_ID_STR_TIM_2SEC,
    R_ID_STR_TIM_5SEC,
    R_ID_STR_TIM_30SEC,
    R_ID_STR_SET_DATETIME,
    R_ID_STR_SET_AUTOOFF,
    R_ID_STR_SET_LANGUAGE,
    R_ID_STR_SET_SCREENOFF,
    R_ID_STR_SET_VIDEOROTATE,
    R_ID_STR_SET_RESET,
    R_ID_STR_SET_FORMAT,
    R_ID_STR_SET_VERSION,
    R_ID_STR_SET_RESOLUTION,
    R_ID_STR_SET_LOOPRECORD,
    R_ID_STR_SET_MOTIONDET,
    R_ID_STR_SET_AUDIOREC,
    R_ID_STR_SET_TIMESTRAMP,
    R_ID_STR_SET_FILLIGHT,
    R_ID_STR_IR_AUTO,
    R_ID_STR_SET_GSENSOR,
    R_ID_STR_SET_PARKMODE,
    R_ID_STR_SET_FASTVIEW,
    R_ID_STR_SET_DELETECUR,
    R_ID_STR_SET_DELETEALL,
    R_ID_STR_SET_LOCKCUR,
    R_ID_STR_SET_LOCKALL,
    R_ID_STR_SET_UNLOCKCUR,
    R_ID_STR_SET_UNLOCKALL,
    R_ID_STR_SET_LOCKED,
    R_ID_STR_SET_LOCK,
    R_ID_STR_SET_DELETE,
    R_ID_STR_SET_PLAYMODE,
    R_ID_STR_SET_REPEATALL,
    R_ID_STR_SET_REPEATRAD,
    R_ID_STR_SET_REPEATSIG,
    R_ID_STR_SET_AUDIOPLAY,
    R_ID_STR_SET_VOLUME,
    R_ID_STR_SET_THUMBNAIL,
    R_ID_STR_SET_SETTING,
    R_ID_STR_SET_VIDEO,
    R_ID_STR_SET_PHOTO,
    R_ID_STR_SET_PLAY,
    R_ID_STR_SET_MUSIC,
    R_ID_STR_SET_AUDIOMODE,
    R_ID_STR_SET_USBMASS,
    R_ID_STR_SET_USBCAM,
    R_ID_STR_SET_BEEPSOUND,
    R_ID_STR_SET_FREQUENCY,
    R_ID_STR_SET_QUALITY,
    R_ID_STR_SET_PROMT,
    R_ID_STR_RES_240P,
    R_ID_STR_RES_480P,
    R_ID_STR_RES_480FHD,
    R_ID_STR_RES_720P,
    R_ID_STR_RES_1024P,
    R_ID_STR_RES_1080P,
    R_ID_STR_RES_1080FHD,
    R_ID_STR_RES_1440P,
    R_ID_STR_RES_3024P,
    R_ID_STR_RES_QVGA,
    R_ID_STR_RES_VGA,
    R_ID_STR_RES_HD,
    R_ID_STR_RES_FHD,
    R_ID_STR_RES_12M,
    R_ID_STR_RES_10M,
    R_ID_STR_RES_8M,
    R_ID_STR_RES_5M,
    R_ID_STR_RES_3M,
    R_ID_STR_RES_2M,
    R_ID_STR_RES_1M,
    R_ID_STR_SDC_NULL,
    R_ID_STR_SDC_NULL1,
    R_ID_STR_SDC_FULL,
    R_ID_STR_SDC_PLSINSERTSD,
    R_ID_STR_SDC_ERROR,
    R_ID_STR_FIL_NULL,
    R_ID_STR_FIL_LOCKED,
    R_ID_STR_FMT_ING,
    R_ID_STR_FMT_SUCCESS,
    R_ID_STR_FMT_FAIL,
    R_ID_STR_FMT_RESET,
    R_ID_STR_FMT_FORMAT,
    R_ID_STR_FMT_FORMAT1,
    R_ID_STR_FMT_FORMAT2,
    R_ID_STR_FMT_DELETE,
    R_ID_STR_PWR_LOW,
    R_ID_STR_PWR_NO,
    R_ID_STR_PWR_BACKLOW,
    R_ID_STR_PWR_CHARGELOW,
    R_ID_STR_ISP_WHITEBL,
    R_ID_STR_ISP_ISO,
    R_ID_STR_ISP_ANTISHANK,
    R_ID_STR_ISP_AUTO,
    R_ID_STR_ISP_SOFT,
    R_ID_STR_ISP_STRONG,
    R_ID_STR_ISP_SUNLIGHT,
    R_ID_STR_ISP_CLOUDY,
    R_ID_STR_ISP_TUNGSTEN,
    R_ID_STR_ISP_FLUORESCENT,
    R_ID_STR_ISP_BLACKWHITE,
    R_ID_STR_ISP_SEPIA,
    R_ID_STR_ISP_ISO100,
    R_ID_STR_ISP_ISO200,
    R_ID_STR_ISP_ISO400,
    R_ID_STR_ISP_WDR,
    R_ID_STR_ISP_EXPOSURE,
    R_ID_STR_COM_SUCCESS,
    R_ID_STR_COM_FAILED,
    R_STR_MAX
};

enum r_lan_id_e {
    LAN_ENGLISH,
    LAN_SCHINESE,
    LAN_TCHINESE,
    LAN_JAPANESE,
    LAN_KOERA,
    LAN_RUSSIAN,
    LAN_TURKEY,
    LAN_TAI,
    LAN_CZECH,
    LAN_UKRAINIAN,
    LAN_GERMAN,
    LAN_FRENCH,
    LAN_ITALIAN,
    LAN_PORTUGUESE,
    LAN_DUTCH,
    LAN_HEBREW,
    LAN_POLISH,
    LAN_MAX
};
