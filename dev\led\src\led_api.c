/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : dev_led_init
* Description    : dev_led_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_init(void)
{
	if(hardware_setup.led_en)
	{
		
	}
	return 0;
}

/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_ioctrl(u32 op, u32 para)
{
	if(hardware_setup.led_en)
	{
		static u32 dev_led_state = 0;
		switch(op)
		{
			case DEV_LED_READ:
				if(para)
					*(u32*)para = dev_led_state;
				break;
			case DEV_LED_WRITE:
				if((hal_uartIOShare()==0)||(hal_iic1IOShare()==0))
				{
					hal_gpioInit(hardware_setup.led_ch, hardware_setup.led_pin, GPIO_OUTPUT,GPIO_PULL_UP);
				}
				dev_led_state = para;
				if(hardware_setup.led_valid == 0)
				{
					para = (para^1)&1;
				}
				if(para)
					hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,GPIO_HIGH);
				else
					hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,GPIO_LOW);
				break;
		}
	}
	return 0;
}

