/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_FRAME_H
       #define  MP3_FRAME_H
//MP3 DATA FRAME HEADER
//SYNC: 11bit 同步信息，恒为0x7ff
//VER: 2bit MP3_VER
//LAYER: 2bit MP3_LAYER
//CRC:  1bit MP3_CRC
//bit_rate: 4bit
//sampleFreq: 2bit
//padding: 1bit
//private_bit: 1bit
//mode: 2bit
//mode_extension : 2bit
//copyright: 1bit
//original/copy: 1bit
//emphasis:2bit

typedef enum  {
	MP3_VER_MPEG_2_5	= 0,
	MP3_VER_UNDEFINED	= 1,
	MP3_VER_MPEG_2		= 2,
	MP3_VER_MPEG_1		= 3
}MP3_VER;
typedef enum  {
	MP3_LAYER_UNDEFINED = 4,
	MP3_LAYER_III   	= 3,                    /* Layer I */
	MP3_LAYER_II  		= 2,                    /* Layer II */
	MP3_LAYER_I 		= 1                     /* Layer III */
}MP3_LAYER;
typedef enum  {
	MP3_CRC_ON 			= 0,
	MP3_CRC_OFF   		= 1
}MP3_CRC;
typedef enum  {
	MP3_MODE_SINGLE_CHANNEL = 0,          /* single channel */
	MP3_MODE_DUAL_CHANNEL   = 1,          /* dual channel */
	MP3_MODE_JOINT_STEREO   = 2,          /* joint (MS/intensity) stereo */
	MP3_MODE_STEREO         = 3           /* normal LR stereo */
}MP3_MODE;
typedef enum  {
	MP3_EXT_MODE_I_STEREO 	= 0x1,         	 
	MP3_EXT_MODE_MS_STEREO 	= 0x2,          
}MP3_EXT_MODE;

typedef enum {
	MP3_EMPHASIS_NONE       = 0,          /* no emphasis */
	MP3_EMPHASIS_50_15_US   = 1,          /* 50/15 microseconds emphasis */
	MP3_EMPHASIS_CCITT_J_17 = 3,          /* CCITT J.17 emphasis */
	MP3_EMPHASIS_RESERVED   = 2           /* unknown emphasis */
}MP3_EMPHASIS;

typedef enum {
	MP3_FLAG_NPRIVATE_III = 0x0007,       /* number of Layer III private bits *//*BIT[2:0] save layer3 sideinfo private bit len*/
	MP3_FLAG_INCOMPLETE   = 0x0008,       /* header but not data is decoded */

	MP3_FLAG_PROTECTION   = 0x0010,       /* frame has CRC protection */
	MP3_FLAG_COPYRIGHT    = 0x0020,       /* frame is copyright */
	MP3_FLAG_ORIGINAL     = 0x0040,       /* frame is original (else copy) */
	MP3_FLAG_PADDING      = 0x0080,       /* frame has additional slot */

	MP3_FLAG_I_STEREO     = 0x0100,       /* uses intensity joint stereo */
	MP3_FLAG_MS_STEREO    = 0x0200,       /* uses middle/side joint stereo */
	MP3_FLAG_FREEFORMAT   = 0x0400,       /* uses free format bitrate */

	MP3_FLAG_LSF_EXT      = 0x1000,       /* lower sampling freq. extension */
	MP3_FLAG_MC_EXT       = 0x2000,       /* multichannel audio extension */
	MP3_FLAG_MPEG_2_5_EXT = 0x4000        /* MPEG 2.5 (unofficial) extension */
}MP3_FLAG;

typedef enum  {
    MP3_SAMPLE_FREQ_48000,
    MP3_SAMPLE_FREQ_44100,
    MP3_SAMPLE_FREQ_32000,
    MP3_SAMPLE_FREQ_24000,
    MP3_SAMPLE_FREQ_22050,
    MP3_SAMPLE_FREQ_16000,
    MP3_SAMPLE_FREQ_12000,
    MP3_SAMPLE_FREQ_11025,
    MP3_SAMPLE_FREQ_8000,

    MP3_SAMPLE_FREQ_64000,
    MP3_SAMPLE_FREQ_88200,
    MP3_SAMPLE_FREQ_96000,
    MP3_SAMPLE_FREQ_128000,
    MP3_SAMPLE_FREQ_176400,
    MP3_SAMPLE_FREQ_192000,
}MP3_SAMPLE_FREQ;

typedef enum {
	MP3_PRIVATE_HEADER    = 0x0100,       /* header private bit */
	MP3_PRIVATE_III       = 0x001f        /* Layer III private bits (up to 5) */
}MP3_PRIVATE;

typedef enum {
	MP3_ERROR_NONE           = 0x00,    /* no error */
	MP3_ERROR_LOSTSYNC       = 0x01,    /* lost synchronization */
	MP3_ERROR_BADLAYER       = 0x02,    /* reserved header layer value */
	MP3_ERROR_BADBITRATE     = 0x03,    /* forbidden bitrate value */
	MP3_ERROR_BADSAMPLERATE  = 0x04,    /* reserved sample frequency value */
	MP3_ERROR_BADEMPHASIS    = 0x05,    /* reserved emphasis value */

	MP3_ERROR_BADCRC         = 0x06,    /* CRC check failed */
	MP3_ERROR_BADBITALLOC    = 0x07,    /* forbidden bit allocation value */
	MP3_ERROR_BADSCALEFACTOR = 0x08,    /* bad scalefactor index */
	MP3_ERROR_BADMODE        = 0x09,    /* bad bitrate/mode combination */
	MP3_ERROR_BADFRAMELEN    = 0x0a,    /* bad frame length */
	MP3_ERROR_BADBIGVALUES   = 0x0b,    /* bad big_values count */
	MP3_ERROR_BADBLOCKTYPE   = 0x0c,    /* reserved block_type */
	MP3_ERROR_BADSCFSI       = 0x0d,    /* bad scalefactor selection info */
	MP3_ERROR_BADDATAPTR     = 0x0e,    /* bad main_data_begin pointer */
	MP3_ERROR_BADPART3LEN    = 0x0f,    /* bad audio data length */
	MP3_ERROR_BADHUFFTABLE   = 0x10,    /* bad Huffman table select */
	MP3_ERROR_BADHUFFDATA    = 0x11,    /* Huffman data overrun */
	MP3_ERROR_BADSTEREO      = 0x12,    /* incompatible block_type for JS */
	MP3_ERROR_NOMEM          = 0x13
}MP3_ERROR;


typedef enum{	
	MP3_DEC_FRAME_SYNC = 0,
	MP3_DEC_FRAME_GET_HEADER,
	MP3_DEC_FRAME_SYNC_RECHECK,
	MP3_DEC_FRAME_GET_SIDEINFO,
	//MP3_DEC_FRAME_HEAD_OK,
	//MP3_DEC_FRAME_SUB_QSKIP,
	MP3_DEC_FRAME_SUB_MAIN_START,
	MP3_DEC_FRAME_LAYER_DEC,
	//MP3_DEC_FRAME_LAYER1_DEC,
	//MP3_DEC_FRAME_LAYER2_DEC,
	//MP3_DEC_FRAME_LAYER3_DEC,
	MP3_DEC_FRAME_END,
	MP3_DEC_FRAME_SYNC_FAIL,
}MP3_DEC_FRAME_STA;

typedef struct SIDE_INFO_CH_S {
	/* from side info */
	u16 part2_3_length;
	u16 big_values;
	u16 global_gain;
	u16 scalefac_compress;

	u8 flags;
	u8 block_type;
	u8 table_select[3];
	u8 subblock_gain[3];
	
	u16 region0_count;
	u16 region1_count;
	
	/* from main_data */
	u8 scalefac[39 + 1];       /* scalefac_l and/or scalefac_s */
} SIDE_INFO_CH_T;
typedef struct SIDE_INFO_GR_S {
	SIDE_INFO_CH_T	ch[2];
} SIDE_INFO_GR_T;
typedef struct  MP3_SIDEINFO_S
{										
	u16 side_len;						//MPEG-1: (CH = 0) 17bytes, (CH = 0,1) 32bytes; MPEG-2: (CH = 0) 9bytes, (CH = 0,1) 17bytes
	u16 main_data_begin;				//
	u16 private_bits;
	u8 	scfsi[2];
	SIDE_INFO_GR_T gr[2];
}MP3_SIDEINFO_T;

typedef struct MP3_HEADER_S {	
	u8  layer;                 	/* MP3_LAYER: audio layer (1, 2, or 3) */
	u8  mode;                  	/* MP3_MODE : channel mode  */
	u8  mode_extension;        	/* MP3_EXT_MODE: additional mode info */
	u8 	emphasis;              	/* MP3_EMPHASIS de-emphasis to use */
	u16 flags;					/* MP3_FLAG */	
	u16 private_bits;           /* MP3_PRIVATE private bits (see below) */
	u16 crc_check;             /* frame CRC accumulator */
	u16	crc_target;            /* final target CRC checksum */
	
    u16 frame_sample;			/*sample point per frame*/
    u16 sampleFreq;			   	/*samplerate index*/                      
    u32 samplerate;             /*samplerate */  
    u32 bit_rate;
	
	u32 header_len;
	u32 frame_len;  			
}MP3_HEADER_T;
typedef struct MP3_FRAME_S{

	MP3_HEADER_T	header;				/* MPEG audio header */          
	MP3_SIDEINFO_T 	sideInfo;


	u8 				mp3_sta;			/*MP3_DEC_STA*/
	u8 				sync_err_cnt;
	u8 				err_cnt;
	u8 				sync_flag;			/*sync found*/
	u8 				sync_word[2];
	u16 			mp3_error;			/* MP3_ERROR */
	u32				decode_fcnt;		
	mad_fixed_t 	*sbsample[2];       /* synthesis subband filter samples */
	mad_fixed_t 	(*overlap)[2][32][18]; /* Layer III block overlap data */
	
}MP3_FRAME_T;
#define MP3_NCHANNELS(header)   ((header)->mode ? 2 : 1)

/*******************************************************************************
* Function Name  : mp3_id3v2_header_check
* Description    : mp3_id3v2_header_check: if header check ok, return header len
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_frame_decode(void);
#endif
