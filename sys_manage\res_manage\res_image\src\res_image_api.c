/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : res_image_show
* Description    : res_image_show
* Input          : INT16U idx resource id
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_image_show(INT32U idx)
{
	JPG_DEC_ARG arg;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	u16 pos_x, pos_y;
	u16 width, height;
	u16 dest_w, dest_h;
	//HAL_CRITICAL_INIT();
    //HAL_CRITICAL_ENTER();

	arg.type 		= MEDIA_FILE_JPG;
	arg.wait 		= 1;
	arg.fd 			= idx;
	arg.src_type	= MEDIA_SRC_NVFS;
	do {
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer == NULL);

#if 1 //按最大video size显示
	hal_lcdSetRatio(0);
	app_lcdPlayShowScaler_cfg(0,PLAY_SCALER_STAT_KICKBUF, 1);
	hal_lcdGetVideoPos(&pos_x,&pos_y);
	hal_lcdGetVideoResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else  //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&pos_x,&pos_y);
	hal_lcdGetVideoRatioResolution(&width,&height);
	hal_lcdGetVideoRatioDestResolution(&dest_w,&dest_h);
#endif


	hal_lcdVideoFrameFlush(p_lcd_buffer, pos_x, pos_y, width, height, dest_w, dest_h);
	arg.dst_width	= width;
	arg.dst_height	= height;
	arg.yout  		= p_lcd_buffer->y_addr;
	arg.uvout 		= p_lcd_buffer->uv_addr;
	arg.step_yout 	= NULL;
	arg.p_lcd_buffer = NULL;
	deg_Printf("res_image_show[%d,%d, %d, %d]\n", pos_x, pos_y, arg.dst_width, arg.dst_height);
	hal_lcdSetWinEnable(0);
	if(imageDecodeStart(&arg)<0)
	{
		deg_Printf("logo : image decode fail.\n");
        hal_dispframeFree(p_lcd_buffer);
		//HAL_CRITICAL_EXIT();
		return -1;
	}
	hal_lcdUiEnable(UI_LAYER0,0);
	hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
	hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
	hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
	//HAL_CRITICAL_EXIT();
	return 0;
}
/*******************************************************************************
* Function Name  : res_image_decode
* Description    : res_image_show
* Input          : INT16U idx resource id
* 				   INT8U *buffer
*                  INT16U dst_width
*                  INT16U dst_height
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_image_decode(INT32U idx, INT8U *buffer, INT16U dst_width, INT16U dst_height)
{
	JPG_DEC_ARG arg;
	HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();

	arg.type 		= MEDIA_FILE_JPG;
	arg.wait 		= 1;
	arg.fd 			= idx;
	arg.src_type	= MEDIA_SRC_NVFS;
	arg.dst_width	= dst_width;
	arg.dst_height  = dst_height;
	arg.yout  		= buffer;
	arg.uvout 		= &buffer[((dst_width+0x1f)&~0x1f)*dst_height];
	arg.step_yout 	= NULL;
	arg.p_lcd_buffer = NULL;
	//deg_Printf("res_image_show[%d,%d, %d, %d]\n", pos_x, pos_y, arg.dst_width, arg.dst_height);

	if(imageDecodeStart(&arg)<0)
	{
		deg_Printf("logo : image decode fail.\n");
		HAL_CRITICAL_EXIT();
		return -1;
	}
	HAL_CRITICAL_EXIT();
	return 0;
}

