/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  LCD_API_H
    #define  LCD_API_H

#include "lcd_typedef.h"

//--------------------------LCD INTERFACE TYPE DEFINE--------------------
#define LCD_RGB_ST7282  		            (LCD_IF_TYPE_RGB | 0)  //480*272

#define LCD_RGB_ILI8961			            (LCD_IF_TYPE_RGB | 3)  //320*240
#define LCD_RGB_ILI9342C		            (LCD_IF_TYPE_RGB | 4)  //320*240
#define LCD_RGB_OTA5182			            (LCD_IF_TYPE_RGB | 5)  //320*240


#define LCD_MCU_SPFD5420		            (LCD_IF_TYPE_MCU | 50) //240*400
#define LCD_MCU_HX8352B			            (LCD_IF_TYPE_MCU | 51) //240*400
#define LCD_MCU_HX8352C			            (LCD_IF_TYPE_MCU | 52) //240*400

#define LCD_MCU_ILI9328			            (LCD_IF_TYPE_MCU | 54) //320*240
#define LCD_MCU_ILI9225G		            (LCD_IF_TYPE_MCU | 55) //220*176
#define LCD_MCU_LGDP4532		            (LCD_IF_TYPE_MCU | 56) //240*320
#define LCD_MCU_R61509V			            (LCD_IF_TYPE_MCU | 57) //240*400
#define LCD_MCU_ST7789			            (LCD_IF_TYPE_MCU | 58) //240*320
#define LCD_MCU_ST7789V			            (LCD_IF_TYPE_MCU | 59) //240*320
#define LCD_MCU_ILI9335			            (LCD_IF_TYPE_MCU | 60) //240*320
#define LCD_MCU_ILI9486_T35_H43_86          (LCD_IF_TYPE_MCU | 61) //320*480
#define LCD_MCU_JD9851                      (LCD_IF_TYPE_MCU | 62) //240*320

#define LCD_MCU_3030B						(LCD_IF_TYPE_MCU | 63)//240*320
#define LCD_MCU_GC9307						(LCD_IF_TYPE_MCU | 64)//240*320
#define LCD_MCU_JD9853                      (LCD_IF_TYPE_MCU | 65) //240*320

#define LCD_SPI_ILI9341			            (LCD_IF_TYPE_SPI | 100) //240*320

#define LCD_NONE                            0xFFFFFFFF             //no lcd

//--------------------------LCD CFG --------------------
#define LCD_TAG_SELECT                      LCD_MCU_3030B
#if (LCD_TAG_SELECT == LCD_MCU_3030B)
#define LCD3030_CH			0//畅航
#define LCD3030_YX_SC			1//钰显

#define LCD_3030_CUSTOMER_SELECT	LCD3030_YX_SC

#endif

#if (LCD_TAG_SELECT == LCD_MCU_ST7789V)
#define LCD7789_QX			0//庆显
#define LCD7789_CH			1//畅航
#define LCD7789_YX			2//钰显
#define LCD7789_RX			3//睿显BOE
#define LCD7789_JPY			4//精品源
#define LCD_7789_CUSTOMER_SELECT	LCD7789_RX


//#define LCD7789_YX_BOE_T3	0//BOE玻璃 T3
//#define LCD7789_YX_BOE_P3	1//BOE玻璃P3
//#define LCD7789_YX_CTC_P3	2//深超玻璃P3
//#define LCD7789_

#endif

/*******************************************************************************
* Function Name  : lcd_isp_config
* Description    : lcd isp config
* Input          : lcddev_t *p_lcddev : lcd op node
* Output         : none
* Return         : none
*******************************************************************************/
void lcd_isp_config(void);
/*******************************************************************************
* Function Name  : lcd_initTab_config
* Description    : lcd inittab config
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void lcd_initTab_config(u32 *tab);

/*******************************************************************************
* Function Name  : LcdGetName
* Description    : get lcd name
* Input          :
* Output         : none
* Return         : char *
*******************************************************************************/
const char *LcdGetName(void);

/*******************************************************************************
* Function Name  : dev_ir_init
* Description    : dev_ir_init
* Input          : NONE
* Output         : none
* Return         : none
*******************************************************************************/
int dev_lcd_init(void);

/*******************************************************************************
* Function Name  : dev_lcd_ioctrl
* Description    : dev_lcd_ioctrl
* Input          : NONE
* Output         : none
* Return         : none
*******************************************************************************/
int dev_lcd_ioctrl(u32 op, u32 para);




#endif

