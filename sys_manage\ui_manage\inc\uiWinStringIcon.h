/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_STRINGICON_H
#define UI_WIN_STRINGICON_H

typedef struct
{
	uiWidgetObj 	widget;
	STRING_DRAW_T	string;
	STRING_DRAW_T	stringSelect;
	u32				select;
}uiStringIconObj;
/*******************************************************************************
* Function Name  : uiStringIconCreateDirect
* Description    : uiStringIconCreateDirect
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringIconCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id);
/*******************************************************************************
* Function Name  : uiStringIconCreate
* Description    : uiStringIconCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringIconCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
