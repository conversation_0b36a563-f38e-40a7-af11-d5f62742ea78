/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PLAYVIDEOMAIN_MODE_ID=0,
	PLAYVIDEOMAIN_PLAY_TIME_ID,
	PLAYVIDEOMAIN_RESOLUTION_ID,

	PLAYVIDEOMAIN_POWERON_TIME_ID,
	PLAYVIDEOMAIN_BATERRY_ID,

	PLAYVIDEOMAIN_FILE_NAME_ID,
	PLAYVIDEOMAIN_FILE_INDEX_ID,
	PLAYVI<PERSON>OMAIN_ERROR_ID,


	PLAYVIDEOMAIN_IRLED_ID,
	PLAYVI<PERSON>OMAIN_MONITOR_ID, // parking monitoring

	PLAYVIDEOMAIN_LOCK_ID,
	PLAYVIDEOMAIN_SD_ID,
	PLAYVIDEOMAIN_TIMEBAR_ID,
	PLAYVIDEOMAIN_PLAY_ICON_ID,
	PLAYVIDEOMAIN_MAX_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor playVideoMainWin[] =
{
	createFrameWin(									Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, 		WIN_ABS_POS),
	createImageIcon(PLAYVIDEOMAIN_PLAY_ICON_ID,     Rx(0),   Ry(220),   Rw(64),  Rh(32),  R_ID_ICON_SPLAY, 	ALIGNMENT_LEFT),
	createImageIcon(PLAYVIDEOMAIN_MODE_ID,      	Rx(0),   Ry(0),   Rw(25),  Rh(25),  R_ID_ICON_MTPLAY2, 	ALIGNMENT_LEFT),
	createStringIcon(PLAYVIDEOMAIN_PLAY_TIME_ID,    Rx(43),  Ry(220),   Rw(60), Rh(25),	RAM_ID_MAKE(" "),	ALIGNMENT_LEFT, 	R_ID_PALETTE_Red,DEFAULT_FONT),
	//createStringIcon(PLAYVIDEOMAIN_RESOLUTION_ID,	Rx(140), Ry(0),   Rw(40),  Rh(25),	RAM_ID_MAKE(" "),	ALIGNMENT_CENTER, 	R_ID_PALETTE_White,DEFAULT_FONT),

	//createStringIcon(PLAYVIDEOMAIN_POWERON_TIME_ID,	Rx(250), Ry(0),   Rw(45),  Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(PLAYVIDEOMAIN_BATERRY_ID,    	Rx(295), Ry(0),   Rw(25),  Rh(25), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),

	createStringIcon(PLAYVIDEOMAIN_FILE_NAME_ID,    Rx(95),   Ry(0),  Rw(160), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_FILE_INDEX_ID,   Rx(190), Ry(25),  Rw(120), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_ERROR_ID,      	Rx(110), Ry(100), Rw(80),  Rh(40),	RAM_ID_MAKE("ERROR"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Red,	DEFAULT_FONT),


	//createImageIcon(PLAYVIDEOMAIN_IRLED_ID,     	Rx(270), Ry(165), Rw(25),  Rh(25), 	R_ID_ICON_MTIROFF,		ALIGNMENT_CENTER),

	//createImageIcon(PLAYVIDEOMAIN_MONITOR_ID,   	Rx(270), Ry(215), Rw(25),  Rh(25), 	R_ID_ICON_MTPARKOFF,	ALIGNMENT_CENTER),

	createImageIcon(PLAYVIDEOMAIN_LOCK_ID,      	Rx(295), Ry(165), Rw(25),  Rh(25), 	R_ID_ICON_MTLOCK,		ALIGNMENT_CENTER),
	createImageIcon(PLAYVIDEOMAIN_SD_ID,        	Rx(265), Ry(0), Rw(25),  Rh(25), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
#if FUN_VIDEO_PLAY_SPEED	
	createProgressBar(PLAYVIDEOMAIN_TIMEBAR_ID,     Rx(0),   Ry(235), Rw(320), Rh(5),R_ID_PALETTE_DarkGray,R_ID_PALETTE_Blue, ALIGNMENT_LEFT),
#endif
	widgetEnd(),
};

/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainPlayTimeShow(winHandle handle)
{
	if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetStrInfor(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_Red);
	}
	else
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
		uiWinSetStrInfor(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_White);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_ERROR_ID),		0);
	}
	if(SysCtrl.file_type & FILELIST_TYPE_JPG)
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),0);
	#if FUN_VIDEO_PLAY_SPEED
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID),0);
	#endif
	}else if(SysCtrl.file_type & FILELIST_TYPE_SPI)
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),0);
	}
	else{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),1);
	#if FUN_VIDEO_PLAY_SPEED
		if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), 100);
		}else
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), (SysCtrl.play_cur_time *100) / SysCtrl.play_total_time);
		}
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID),1);
	#endif
	}
}
/*******************************************************************************
* Function Name  : playVideoMainResolutionShow
* Description    : playVideoMainResolutionShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainResolutionShow(winHandle handle)
{
	u16 width,height;
	if(SysCtrl.file_type & FILELIST_TYPE_JPG)
	{
		imageDecodeGetResolution(&width,&height);
		switch((width<<16)|height)
		{
			case ((320<<16)|240)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("QVGA"));break;
			case ((640<<16)|480)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("VGA")); break;
			case ((1280<<16)|720)	:
			case ((1280<<16)|960)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("1M"));	break;
			case ((1920<<16)|1080)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("2M"));	break;
			case ((2560<<16)|1440)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("3M"));	break;
			case ((3200<<16)|1800)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("5M"));	break;
			case ((3800<<16)|2160)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("8M"));	break;
			case ((4032<<16)|2520)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("10M"));	break;
			case ((4032<<16)|2880)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("12M"));	break;
			default:					uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("???"));	break;
		}
	}
	else if(SysCtrl.file_type & FILELIST_TYPE_AVI)
	{
		AVI_DEC_ARG *arg = videoPlaybabkGetArg();
		width 	= arg->width;
		height 	= arg->height;
		switch((width<<16)|height)
		{
			case ((320<<16)|240)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("QVGA"));	break;
			case ((640<<16)|480)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("VGA"));	break;
			case ((1280<<16)|720)	:
			case ((1280<<16)|960)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("HD"));		break;
			case ((1920<<16)|1080)	:	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("FHD"));	break;
			default:					uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE("???"));	break;
		}
	}
}
/*******************************************************************************
* Function Name  : playVideoMainPoweOnTimeShow
* Description    : playVideoPoweOnTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainPoweOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));
}
/*******************************************************************************
* Function Name  : playVideoMainBaterryShow
* Description    : playVideoMainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : playVideoMainFileNameShow
* Description    : playVideoMainFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainFileNameShow(winHandle handle)
{
	static char fileindexstr[] = {"0000/0000"};
	char* name = filelist_GetFileFullNameByIndex(playVideoOp.list,SysCtrl.file_index,NULL);
	if(name)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(name));
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(" "));
	hx330x_num2str(fileindexstr, SysCtrl.file_index + 1, 4);
	fileindexstr[4] = '/';
	hx330x_num2str(fileindexstr + 5, filelist_api_CountGet(playVideoOp.list), 4);
	if(SysCtrl.file_index >= 0)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_INDEX_ID),RAM_ID_MAKE(fileindexstr));
}
/*******************************************************************************
* Function Name  : playVideoMainIrLedShow
* Description    : playVideoMainIrLedShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainIrLedShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_IRLED_ID),0);
	}
}
/*******************************************************************************
* Function Name  : playVideoMainMonitorShow
* Description    : playVideoMainMonitorShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainMonitorShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_PARKMODE) == R_ID_STR_COM_ON)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_MONITOR_ID), R_ID_ICON_MTPARKON);
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_MONITOR_ID), R_ID_ICON_MTPARKOFF);
}
/*******************************************************************************
* Function Name  : playVideoMainLockShow
* Description    : playVideoMainLockShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainLockShow(winHandle handle)
{
	if(filelist_fnameChecklockByIndex(SysCtrl.avi_list,SysCtrl.file_index) > 0)
	{
	    uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),1);
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),R_ID_ICON_MTLOCK);

	}
	else
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),0);
}
/*******************************************************************************
* Function Name  : playVideoMainSDShow
* Description    : playVideoMainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNULL);
}

static void playVideoMainPlayIconShow(winHandle handle)
{
	resID iconId = R_ID_ICON_SPLAY; // 默认播放图标

	// 先判断文件类型
	if((SysCtrl.file_type & FILELIST_TYPE_JPG) || (SysCtrl.file_type & FILELIST_TYPE_SPI))
	{
		// JPG文件不显示播放图标
		uiWinSetVisible(winItem(handle, PLAYVIDEOMAIN_PLAY_ICON_ID), 0);
		return;
	}
	else if(SysCtrl.file_type & FILELIST_TYPE_AVI)
	{
		// AVI文件显示播放图标
		uiWinSetVisible(winItem(handle, PLAYVIDEOMAIN_PLAY_ICON_ID), 1);

		// 根据播放状态设置图标
		if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
		{
			iconId = R_ID_ICON_SPAUSE; // 播放中显示暂停图标
		}
		else if(videoPlaybackGetStatus() == MEDIA_STAT_PAUSE)
		{
			iconId = R_ID_ICON_SPLAY; // 暂停时显示播放图标
		}
		else if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			iconId = R_ID_ICON_SPLAY; // 停止时显示播放图标
		}
	}

	uiWinSetResid(winItem(handle, PLAYVIDEOMAIN_PLAY_ICON_ID), iconId);
}




