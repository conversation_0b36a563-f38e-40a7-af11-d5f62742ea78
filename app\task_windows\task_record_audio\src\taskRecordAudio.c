/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
/*******************************************************************************
* Function Name  : app_taskRecordAudio_callback
* Description    : APP LAYER: app_taskRecordAudio_callback
* Input          : INT32U cmd,INT32U para
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int app_taskRecordAudio_callback(INT32U cmd,INT32U para)
{
	int *fdt = (int *)para;
    char *name;
	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
	{
		sd_api_unlock();
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
		return -1;
	}
	if(cmd == CMD_AUDIO_RECORD_START)
	{
		SysCtrl.rec_show_time = 0;
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		SysCtrl.new_fname.index = FILELIST_FLAG_IVL;
		name = filelist_createNewFileFullName(SysCtrl.wav_list, &SysCtrl.new_fname);
		if(name == NULL)
		{
			deg_Printf("[AUDIO REC] create file name fail.\n");
			task_com_sdc_stat_set(SDC_STAT_FULL);
			return -1;
		}
		*fdt = fs_open(name,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
		if(*fdt < 0)
		{
			deg_Printf("[AUDIO REC] open file fail<%s>,%d\n",name,fdt);
			return -1;
		}
		hx330x_str_cpy(SysCtrl.file_fullname,name);
		deg_Printf("[AUDIO REC]: open file ok<%s>\n",name);
		
	}
	else if(cmd == CMD_AUDIO_RECORD_STOP || cmd == CMD_COM_ERROR)
	{
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		
		if((*fdt >= 0) && !(SysCtrl.new_fname.index & FILELIST_FLAG_IVL))
		{
			u32 filesize = fs_size(*fdt);
			fs_close(*fdt);
			if(cmd == CMD_AUDIO_RECORD_STOP)
			{
				task_com_sdc_freesize_modify(-1,filesize);
				filenode_addFileByFname(SysCtrl.wav_list, &SysCtrl.new_fname);
				deg_Printf("[AUDIO REC]: stop file ok<%s>\n",SysCtrl.file_fullname);
			}else
			{
				f_unlink(SysCtrl.file_fullname);
				deg_Printf("[AUDIO REC]: err, delete <%s>\n",SysCtrl.file_fullname);
			}
			SysCtrl.new_fname.index = FILELIST_FLAG_IVL;
			*fdt = -1;
		}
	}
	else if(cmd == CMD_COM_CHECK) // check sdc sapce
	{
		if(filelist_SpaceCheck(SysCtrl.wav_list,&SysCtrl.sdc_freesize,para) < 0)
		{
			task_com_sdc_stat_set(SDC_STAT_FULL);
			return -1;
		}
	}

    return 0;
}
/*******************************************************************************
* Function Name  : app_taskRecordAudioOpen
* Description    : APP LAYER: app_taskRecordAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static void app_taskRecordAudioOpen(u32 arg)
{
	AUIDO_REC_ARG_T audio_arg;
	
	audio_arg.wav_arg.volume		= MEDIA_CFG_MIC_VOLUME;
	audio_arg.wav_arg.ch_in  		= 1;
	audio_arg.wav_arg.ch_out 		= 1;
	audio_arg.wav_arg.samplerate 	= MEDIA_CFG_AUDSRATE;
	audio_arg.wav_arg.type			= MEDIA_WAV;
	audio_arg.wav_arg.sub_type		= WAV_TYPE_PCM;
	
	audio_arg.wav_arg.media_ch		= -1;
	audio_arg.wav_arg.fd			= -1;
	audio_arg.looprec				= AUDIO_CFG_REC_LOOP;
	audio_arg.looptime 				= AUIDO_CFG_REC_TIME; //second
	audio_arg.callback 				= app_taskRecordAudio_callback;

	SysCtrl.rec_show_time			= 0;
    audioRecordInit(&audio_arg);
	uiOpenWindow(&RecordAudioWindow, 0, 0);
}
/*******************************************************************************
* Function Name  : app_taskRecordAudioClose
* Description    : APP LAYER: app_taskRecordAudioClose
* Input          : u32 arg
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static void app_taskRecordAudioClose(u32 arg)
{
	if(audioRecordGetStatus() == MEDIA_STAT_START)
		audioRecordStop(0);
	audioRecordUninit();
	if(SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		task_com_sdc_stat_set(SDC_STAT_NORMAL);
}
/*******************************************************************************
* Function Name  : app_taskRecordAudioService
* Description    : APP LAYER: app_taskRecordAudioService
* Input          : u32 arg
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static void app_taskRecordAudioService(u32 arg)
{
	audioRecordService();
	if(audioRecordGetStatus() == MEDIA_STAT_START && SysCtrl.rec_show_time != audioRecordGetTime())
	{
		SysCtrl.rec_show_time = audioRecordGetTime();
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
	}
}

ALIGNED(4) sysTask_T taskRecordAudio =
{
	"Record Audio",
	0,
	app_taskRecordAudioOpen,
	app_taskRecordAudioClose,
	app_taskRecordAudioService,
};



