/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef IR_API_H
#define IR_API_H
/*******************************************************************************
* Function Name  : dev_ir_init
* Description    : dev_ir_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_ir_init(void);
/*******************************************************************************
* Function Name  : dev_ir_ioctrl
* Description    : dev_ir_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_ir_ioctrl(u32 op, u32 para);

#endif
