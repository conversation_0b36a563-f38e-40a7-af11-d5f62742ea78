{"C_Cpp.default.configurationProvider": "ms-vscode.cpptools", "C_Cpp.default.compilerPath": "E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin/or1k-unknown-elf-gcc.exe", "C_Cpp.default.cStandard": "c99", "C_Cpp.default.intelliSenseMode": "gcc-x64", "files.associations": {"*.h": "c", "*.c": "c"}, "files.encoding": "utf8", "files.autoGuessEncoding": true, "terminal.integrated.env.windows": {"PATH": "E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin;C:/Program Files (x86)/GnuWin32/bin;${env:PATH}"}, "code-runner.executorMap": {"c": "cd $dir && \"E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin/or1k-unknown-elf-gcc.exe\" -Wall -Os -mno-delay -mhard-div $fileName -o $fileNameWithoutExt.elf", "makefile": "cd $workspaceRoot && \"C:/Program Files (x86)/GnuWin32/bin/make.exe\""}, "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "code-runner.clearPreviousOutput": true}