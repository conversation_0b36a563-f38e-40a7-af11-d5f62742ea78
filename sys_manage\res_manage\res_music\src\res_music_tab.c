/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

//-----------------------------channel-1,sample-16k,16-bit pcm--------------------------------------------------------
const u8 res_key_music[1820] =
{
	0x52, 0x49, 0x46, 0x46, 0x14, 0x07, 0x00, 0x00, 0x57, 0x41, 0x56, 0x45, 0x66, 0x6D, 0x74, 0x20, 
	0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x80, 0x3E, 0x00, 0x00, 0x00, 0x7D, 0x00, 0x00, 
	0x02, 0x00, 0x10, 0x00, 0x64, 0x61, 0x74, 0x61, 0xF0, 0x06, 0x00, 0x00, 0x02, 0x00, 0xFF, 0xFF, 
	0x02, 0x00, 0xFD, 0xFF, 0x04, 0x00, 0xFC, 0xFF, 0x06, 0x00, 0xF7, 0xFF, 0x0D, 0x00, 0xFD, 0xFF, 
	0x0F, 0x03, 0xF7, 0x06, 0x55, 0x04, 0xC4, 0xFC, 0x75, 0xF8, 0x17, 0xFC, 0xE8, 0x00, 0x2C, 0xFC, 
	0xC7, 0xED, 0x71, 0xE5, 0x54, 0xF4, 0x47, 0x18, 0x4A, 0x37, 0x8F, 0x34, 0xDB, 0x0C, 0x28, 0xDD, 
	0xF0, 0xC9, 0x08, 0xDD, 0xF2, 0xFC, 0x88, 0x07, 0xEE, 0xF6, 0xCE, 0xE8, 0xEF, 0xFE, 0x02, 0x34, 
	0x2F, 0x58, 0x1E, 0x42, 0x64, 0xF8, 0xDC, 0xAF, 0x39, 0x9E, 0x22, 0xCB, 0xD1, 0x0B, 0xA2, 0x2C, 
	0xC7, 0x1F, 0xD3, 0x04, 0x85, 0x03, 0x7A, 0x20, 0x8E, 0x37, 0xD3, 0x21, 0x15, 0xE1, 0x76, 0xA4, 
	0xA7, 0x9F, 0xBB, 0xDA, 0x31, 0x29, 0x1A, 0x51, 0xBC, 0x3D, 0xBC, 0x0D, 0x5F, 0xF1, 0xBD, 0xFA, 
	0x48, 0x0F, 0x79, 0x07, 0xF5, 0xDB, 0xA6, 0xB1, 0x7A, 0xB7, 0x91, 0xF6, 0x77, 0x44, 0x57, 0x66, 
	0xED, 0x44, 0x03, 0x00, 0xAB, 0xCF, 0xFF, 0xD0, 0xAD, 0xEF, 0x11, 0x00, 0x7C, 0xEE, 0xA1, 0xD3, 
	0xF7, 0xD9, 0xCA, 0x0E, 0xCE, 0x4E, 0x4A, 0x63, 0x1F, 0x35, 0x64, 0xE4, 0x6E, 0xAD, 0x9C, 0xB3, 
	0xDE, 0xE4, 0x69, 0x0F, 0x03, 0x13, 0x65, 0xFD, 0x08, 0xF7, 0x28, 0x15, 0x1B, 0x40, 0xAD, 0x48, 
	0x1A, 0x18, 0x06, 0xCB, 0x9D, 0x9B, 0x35, 0xAE, 0x1C, 0xF1, 0xBF, 0x2D, 0x96, 0x3A, 0xD6, 0x1D, 
	0x2E, 0x01, 0x78, 0x04, 0x5C, 0x1D, 0xE0, 0x23, 0xC3, 0xFE, 0x55, 0xC2, 0xCA, 0xA1, 0x9F, 0xBF, 
	0xFA, 0x0A, 0x29, 0x4C, 0x47, 0x55, 0x41, 0x29, 0x07, 0xF5, 0xEB, 0xE2, 0x27, 0xF4, 0x27, 0x05, 
	0x93, 0xF6, 0xC9, 0xD0, 0x80, 0xBD, 0x51, 0xDD, 0x4D, 0x23, 0xBA, 0x5B, 0x49, 0x59, 0x30, 0x1E, 
	0x00, 0xDA, 0x71, 0xBE, 0x49, 0xD4, 0xDF, 0xF8, 0x65, 0x04, 0x2D, 0xF2, 0xF4, 0xE2, 0xE4, 0xF7, 
	0x14, 0x2C, 0x5B, 0x54, 0x00, 0x47, 0x64, 0x05, 0x6B, 0xBE, 0x3B, 0xA6, 0xDF, 0xC8, 0x55, 0x02, 
	0x92, 0x22, 0xCA, 0x19, 0xAD, 0x02, 0x3B, 0x02, 0xE1, 0x1E, 0x44, 0x38, 0x7D, 0x28, 0x73, 0xED, 
	0xAE, 0xB0, 0xE5, 0xA3, 0x84, 0xD3, 0x92, 0x1A, 0xCC, 0x43, 0x64, 0x38, 0x8C, 0x10, 0x6E, 0xF7, 
	0x8A, 0xFF, 0xBA, 0x12, 0x99, 0x0C, 0xF6, 0xE3, 0xBF, 0xB8, 0x36, 0xB7, 0xF1, 0xEB, 0xE9, 0x33, 
	0x68, 0x59, 0x23, 0x43, 0x32, 0x09, 0xB1, 0xDC, 0x2C, 0xDA, 0xA8, 0xF2, 0x1F, 0x00, 0x5A, 0xEF, 
	0xA6, 0xD4, 0x95, 0xD6, 0x19, 0x04, 0x68, 0x40, 0x13, 0x5A, 0x4F, 0x38, 0x1E, 0xF3, 0xB6, 0xBE, 
	0x77, 0xBD, 0xDD, 0xE3, 0x08, 0x08, 0x20, 0x0C, 0xA9, 0xF9, 0xAC, 0xF3, 0xCB, 0x0E, 0x43, 0x38, 
	0xD5, 0x45, 0xC0, 0x1F, 0x69, 0xDB, 0x93, 0xAB, 0xEF, 0xB3, 0xD0, 0xE9, 0x83, 0x1F, 0xC1, 0x2E, 
	0x68, 0x19, 0x10, 0x02, 0xA9, 0x05, 0x33, 0x1D, 0xD6, 0x25, 0xF4, 0x06, 0x88, 0xCF, 0x69, 0xAC, 
	0x6A, 0xBF, 0x69, 0xFE, 0x82, 0x3A, 0x26, 0x49, 0x6B, 0x28, 0xBE, 0xFC, 0xD5, 0xEB, 0x63, 0xF9, 
	0xDB, 0x07, 0xB8, 0xFA, 0x20, 0xD7, 0x48, 0xC1, 0x75, 0xD8, 0x22, 0x15, 0x74, 0x4B, 0x1B, 0x51, 
	0x2F, 0x23, 0x23, 0xE8, 0x3B, 0xCC, 0xAB, 0xDA, 0xFB, 0xF7, 0x5F, 0x01, 0xBD, 0xF0, 0x87, 0xE1, 
	0x1F, 0xF2, 0x03, 0x21, 0x84, 0x49, 0xBB, 0x44, 0x6A, 0x0F, 0xB9, 0xCF, 0x63, 0xB4, 0x1C, 0xCC, 
	0x2D, 0xFB, 0x1B, 0x18, 0xB7, 0x12, 0xE8, 0xFF, 0x03, 0x00, 0x8A, 0x1A, 0x74, 0x34, 0x10, 0x2B, 
	0x14, 0xF9, 0x80, 0xC0, 0x29, 0xAE, 0x41, 0xD1, 0x6D, 0x0D, 0x92, 0x34, 0xD0, 0x2F, 0xBC, 0x10, 
	0xDD, 0xFB, 0xEE, 0x02, 0x7C, 0x14, 0x80, 0x10, 0xAC, 0xEC, 0x0F, 0xC3, 0x7B, 0xBB, 0xBE, 0xE4, 
	0xCB, 0x23, 0xD9, 0x49, 0x55, 0x3D, 0x0E, 0x0F, 0x2D, 0xE8, 0x51, 0xE3, 0x48, 0xF6, 0x0C, 0x01, 
	0x8F, 0xF1, 0xC5, 0xD7, 0xB6, 0xD5, 0x2D, 0xFB, 0xD4, 0x31, 0x66, 0x4E, 0xC5, 0x37, 0x96, 0xFE, 
	0xBB, 0xCE, 0x52, 0xC8, 0x20, 0xE5, 0xDC, 0x02, 0xBD, 0x06, 0xE2, 0xF6, 0xFA, 0xF0, 0x7D, 0x08, 
	0x31, 0x2F, 0x48, 0x40, 0x21, 0x24, 0xA9, 0xE9, 0xDA, 0xBB, 0x42, 0xBC, 0xEA, 0xE5, 0xBE, 0x13, 
	0xA2, 0x23, 0x4B, 0x14, 0xB8, 0x01, 0x80, 0x05, 0x66, 0x1B, 0xBF, 0x25, 0x4B, 0x0D, 0x41, 0xDC, 
	0xB9, 0xB8, 0x93, 0xC2, 0x37, 0xF5, 0x76, 0x2A, 0x38, 0x3C, 0x26, 0x25, 0xE4, 0x01, 0xFB, 0xF2, 
	0xB9, 0xFD, 0x1D, 0x0A, 0xDA, 0xFE, 0x6B, 0xDE, 0x53, 0xC7, 0x9A, 0xD6, 0x3F, 0x09, 0x46, 0x3B, 
	0x93, 0x46, 0xA5, 0x24, 0x6B, 0xF3, 0xFF, 0xD8, 0xB1, 0xE1, 0x82, 0xF8, 0xD0, 0xFF, 0xC4, 0xF0, 
	0xC7, 0xE1, 0xF8, 0xED, 0x8C, 0x16, 0x59, 0x3D, 0x55, 0x3F, 0xF2, 0x15, 0xFF, 0xDE, 0xF5, 0xC2, 
	0xB5, 0xD1, 0xD5, 0xF6, 0x9D, 0x0F, 0x7B, 0x0C, 0x4D, 0xFD, 0x90, 0xFD, 0x55, 0x15, 0xB1, 0x2E, 
	0xDD, 0x2A, 0x51, 0x02, 0xCE, 0xCF, 0x5A, 0xBA, 0x70, 0xD2, 0x64, 0x03, 0xB3, 0x26, 0xA3, 0x26, 
	0x41, 0x0F, 0x91, 0xFE, 0xDA, 0x04, 0xCD, 0x14, 0x03, 0x13, 0xB1, 0xF4, 0x50, 0xCE, 0x7D, 0xC2, 
	0x01, 0xE1, 0x06, 0x16, 0x40, 0x3A, 0x4B, 0x35, 0xE1, 0x11, 0x5B, 0xF1, 0x7D, 0xEB, 0xEB, 0xF9, 
	0x6C, 0x02, 0x9D, 0xF4, 0x89, 0xDC, 0x46, 0xD7, 0x9B, 0xF4, 0x16, 0x24, 0x38, 0x41, 0xEF, 0x33, 
	0xAE, 0x06, 0xFF, 0xDC, 0x92, 0xD3, 0x2D, 0xE8, 0xAE, 0xFF, 0xEA, 0x02, 0x56, 0xF5, 0x61, 0xEF, 
	0xD1, 0x02, 0x79, 0x25, 0x62, 0x38, 0x33, 0x25, 0x51, 0xF5, 0xB6, 0xCB, 0x8C, 0xC6, 0x27, 0xE5, 
	0x96, 0x0A, 0xAE, 0x19, 0x11, 0x0F, 0xAA, 0x00, 0x64, 0x04, 0x2A, 0x18, 0x8E, 0x23, 0x6B, 0x11, 
	0xE4, 0xE7, 0x1A, 0xC6, 0xC8, 0xC8, 0x87, 0xEF, 0x8D, 0x1C, 0x2A, 0x2F, 0x0C, 0x20, 0xC3, 0x04, 
	0x63, 0xF8, 0xED, 0x00, 0x8C, 0x0B, 0x81, 0x02, 0x27, 0xE6, 0x51, 0xCF, 0xD0, 0xD7, 0x1A, 0x00, 
	0xEB, 0x2B, 0x69, 0x3A, 0xF5, 0x22, 0xBF, 0xFB, 0x4E, 0xE4, 0xD8, 0xE8, 0xFB, 0xF9, 0x67, 0xFF, 
	0x21, 0xF2, 0xCF, 0xE3, 0xD3, 0xEB, 0x50, 0x0D, 0x87, 0x30, 0x3C, 0x37, 0xF0, 0x18, 0xC5, 0xEB, 
	0x47, 0xD1, 0x12, 0xD9, 0xFD, 0xF4, 0x25, 0x09, 0x67, 0x07, 0x49, 0xFB, 0x67, 0xFB, 0xC0, 0x0F, 
	0x55, 0x27, 0xEA, 0x27, 0xC5, 0x08, 0xE6, 0xDD, 0xC8, 0xC7, 0xB3, 0xD6, 0x93, 0xFC, 0xA3, 0x1A, 
	0x74, 0x1D, 0xA4, 0x0C, 0xE4, 0xFF, 0x70, 0x05, 0x9C, 0x13, 0xCB, 0x13, 0x78, 0xFB, 0xDF, 0xD9, 
	0xCB, 0xCB, 0xB3, 0xE0, 0x09, 0x0B, 0x53, 0x2B, 0xAE, 0x2B, 0x09, 0x12, 0x39, 0xF8, 0x67, 0xF2, 
	0x21, 0xFD, 0xC3, 0x03, 0x12, 0xF8, 0x9D, 0xE2, 0x3F, 0xDB, 0xBA, 0xF0, 0xD8, 0x17, 0x3D, 0x33, 
	0x3A, 0x2D, 0x62, 0x0B, 0x16, 0xE9, 0x9D, 0xDE, 0x78, 0xEC, 0x29, 0xFE, 0x8C, 0x00, 0x22, 0xF5, 
	0x3A, 0xEF, 0x4D, 0xFE, 0xB5, 0x1B, 0x99, 0x2E, 0x0A, 0x23, 0xFD, 0xFD, 0x7A, 0xDA, 0x25, 0xD2, 
	0x27, 0xE7, 0x13, 0x04, 0x34, 0x11, 0x2C, 0x0A, 0x63, 0xFF, 0xC0, 0x02, 0xCE, 0x13, 0x51, 0x1F, 
	0x08, 0x13, 0xD6, 0xF1, 0xDD, 0xD3, 0x8C, 0xD1, 0x4F, 0xED, 0x2F, 0x11, 0xA4, 0x22, 0xB5, 0x19, 
	0xBA, 0x05, 0x22, 0xFC, 0xDD, 0x02, 0xD2, 0x0B, 0x2B, 0x05, 0xCB, 0xED, 0xCF, 0xD8, 0xFC, 0xDB, 
	0x08, 0xFA, 0x0F, 0x1E, 0x50, 0x2D, 0x8B, 0x1E, 0x2C, 0x01, 0xD5, 0xED, 0xA1, 0xEF, 0xED, 0xFB, 
	0xBB, 0xFF, 0x8C, 0xF4, 0x94, 0xE7, 0xF0, 0xEB, 0xD7, 0x05, 0xB9, 0x23, 0xF8, 0x2C, 0x84, 0x18, 
	0xB0, 0xF5, 0xB1, 0xDE, 0x92, 0xE1, 0x43, 0xF5, 0x98, 0x04, 0x9F, 0x03, 0x32, 0xFA, 0xFB, 0xF9, 
	0x53, 0x0A, 0xD2, 0x1E, 0x5D, 0x22, 0x32, 0x0C, 0x2D, 0xEA, 0xBB, 0xD5, 0x88, 0xDD, 0xE4, 0xF8, 
	0xB2, 0x10, 0xCA, 0x14, 0x67, 0x09, 0x39, 0x00, 0xEE, 0x04, 0xF0, 0x10, 0x9E, 0x12, 0x81, 0x00, 
	0x18, 0xE5, 0xE0, 0xD6, 0xAA, 0xE3, 0x28, 0x03, 0xB5, 0x1D, 0x26, 0x21, 0xF4, 0x0F, 0xDE, 0xFC, 
	0xE3, 0xF7, 0x94, 0xFF, 0xA3, 0x04, 0x72, 0xFB, 0x9B, 0xE9, 0x73, 0xE1, 0xC3, 0xEF, 0xB0, 0x0D, 
	0x2D, 0x25, 0x39, 0x24, 0xD9, 0x0C, 0xB5, 0xF2, 0xE9, 0xE8, 0x78, 0xF1, 0xE4, 0xFD, 0x6B, 0xFF, 
	0x43, 0xF6, 0xB8, 0xF0, 0x66, 0xFB, 0x82, 0x12, 0x77, 0x23, 0xDE, 0x1D, 0x6C, 0x03, 0x8A, 0xE7, 
	0x58, 0xDE, 0x69, 0xEB, 0x10, 0x00, 0x66, 0x0A, 0x01, 0x06, 0x54, 0xFE, 0x07, 0x01, 0xAF, 0x0E, 
	0x35, 0x19, 0xF6, 0x11, 0x90, 0xF9, 0x4A, 0xE1, 0x49, 0xDC, 0x5B, 0xEE, 0x9E, 0x08, 0x30, 0x17, 
	0xB6, 0x12, 0x3B, 0x05, 0x6F, 0xFE, 0x8A, 0x03, 0xB9, 0x0A, 0x6E, 0x06, 0xC1, 0xF4, 0x45, 0xE3, 
	0xD7, 0xE2, 0x37, 0xF7, 0x45, 0x12, 0xFE, 0x1F, 0xF6, 0x17, 0xDB, 0x03, 0x67, 0xF5, 0xA4, 0xF5, 
	0xDB, 0xFD, 0x57, 0x00, 0xAC, 0xF7, 0xEB, 0xEC, 0x73, 0xEE, 0xA2, 0x00, 0x9F, 0x17, 0x21, 0x21, 
	0xEC, 0x14, 0x8C, 0xFC, 0xAE, 0xEA, 0x95, 0xEA, 0x2F, 0xF7, 0xC3, 0x01, 0x29, 0x01, 0x44, 0xFA, 
	0xB7, 0xF9, 0x95, 0x05, 0xA9, 0x15, 0x80, 0x1A, 0x71, 0x0C, 0x14, 0xF4, 0x78, 0xE3, 0x56, 0xE6, 
	0x1E, 0xF8, 0x0A, 0x09, 0x0E, 0x0D, 0x0D, 0x06, 0x05, 0x00, 0xA6, 0x03, 0xF1, 0x0C, 0x60, 0x0F, 
	0x5E, 0x03, 0x55, 0xEF, 0x19, 0xE3, 0x99, 0xE9, 0x85, 0xFE, 0xEE, 0x11, 0x5B, 0x16, 0x24, 0x0C, 
	0x88, 0xFF, 0xE4, 0xFB, 0x01, 0x01, 0xA1, 0x04, 0x3D, 0xFE, 0x01, 0xF1, 0x97, 0xE9, 0xC8, 0xF1, 
	0x17, 0x06, 0xC0, 0x17, 0x86, 0x19, 0x53, 0x0B, 0xB6, 0xF9, 0x04, 0xF2, 0x9E, 0xF6, 0x67, 0xFE, 
	0x34, 0xFF, 0x92, 0xF8, 0xF8, 0xF3, 0x81, 0xFA, 0x78, 0x0A, 0x9A, 0x17, 0x05, 0x16, 0x89, 0x05, 
	0x63, 0xF2, 0x79, 0xEA, 0x64, 0xF1, 0x4E, 0xFE, 0x50, 0x05, 0xD5, 0x02, 0xE5, 0xFD, 0xBA, 0xFF, 
	0x49, 0x09, 0x83, 0x11, 0x2F, 0x0E, 0xA5, 0xFE, 0xAD, 0xED, 0x56, 0xE8, 0x58, 0xF2, 0xF8, 0x02, 
	0x42, 0x0D, 0x9F, 0x0B, 0xB8, 0x03, 0x95, 0xFF, 0x0B, 0x03, 0x26, 0x08, 0xEE, 0x05, 0x7B, 0xFA, 
	0x16, 0xEE, 0x03, 0xEC, 0xB6, 0xF7, 0x05, 0x09, 0x27, 0x13, 0xC9, 0x0F, 0x17, 0x04, 0xF4, 0xFA, 
	0x95, 0xFA, 0x5A, 0xFF, 0xCB, 0x00, 0x15, 0xFB, 0x87, 0xF3, 0x59, 0xF3, 0x0A, 0xFE, 0xE4, 0x0C, 
	0x62, 0x14, 0x8C, 0x0E, 0x43, 0x00, 0xC2, 0xF4, 0x85, 0xF3, 0x3F, 0xFA, 0x55, 0x00, 0xF1, 0xFF, 
	0x9C, 0xFB, 0xEF, 0xFA, 0x0A, 0x02, 0x71, 0x0C, 0xB7, 0x10, 0x88, 0x09, 0x34, 0xFB, 0x54, 0xF0, 
	0x7F, 0xF0, 0xE8, 0xF9, 0xB8, 0x03, 0x94, 0x06, 0x04, 0x03, 0xBD, 0xFF, 0x02, 0x02, 0xDF, 0x07, 
	0x12, 0x0A, 0xBC, 0x03, 0xF7, 0xF7, 0xCB, 0xEF, 0x11, 0xF2, 0x29, 0xFD, 0x6B, 0x08, 0xE9, 0x0B, 
	0x25, 0x07, 0x84, 0x00, 0x78, 0xFE, 0x48, 0x01, 0x6D, 0x03, 0xF6, 0xFF, 0x45, 0xF8, 0x40, 0xF3, 
	0xB9, 0xF6, 0x6E, 0x01, 0xA3, 0x0B, 0xCF, 0x0D, 0x36, 0x07, 0x15, 0xFE, 0x96, 0xF9, 0x77, 0xFB, 
	0x3C, 0xFF, 0x88, 0xFF, 0xD3, 0xFB, 0xF4, 0xF8, 0xE3, 0xFB, 0x2C, 0x04, 0xAE, 0x0B, 0xF9, 0x0B, 
	0x5B, 0x04, 0x9F, 0xFA, 0xE5, 0xF5, 0x7F, 0xF8, 0x70, 0xFE, 0xE4, 0x01, 0xD0, 0x00, 0x6C, 0xFE, 
	0x48, 0xFF, 0x1B, 0x04, 0xA7, 0x08, 0xC9, 0x07, 0xC0, 0x00, 0x65, 0xF8, 0x05, 0xF5, 0xD4, 0xF8, 
	0x32, 0x00, 0x2F, 0x05, 0xEE, 0x04, 0xB3, 0x01, 0xF8, 0xFF, 0x9C, 0x01, 0x1C, 0x04, 0x6C, 0x03, 
	0x71, 0xFE, 0x9E, 0xF8, 0xFC, 0xF6, 0x64, 0xFB, 0xAB, 0x02, 0x68, 0x07, 0xA9, 0x06, 0x40, 0x02, 
	0x90, 0xFE, 0x43, 0xFE, 0x14, 0x00, 0xA1, 0x00, 0x48, 0xFE, 0x06, 0xFB, 0x84, 0xFA, 0x58, 0xFE, 
	0x1C, 0x04, 0x74, 0x07, 0xDE, 0x05, 0xEC, 0x00, 0xA3, 0xFC, 0xD8, 0xFB, 0xED, 0xFD, 0xEF, 0xFF, 
	0xC5, 0xFF, 0x37, 0xFE, 0xDC, 0xFD, 0x2D, 0x00, 0xC5, 0x03, 0x89, 0x05, 0x9F, 0x03, 0x3C, 0xFF, 
	0xAA, 0xFB, 0x55, 0xFB, 0xD8, 0xFD, 0xA5, 0x00, 0x8F, 0x01, 0xA9, 0x00, 0xCF, 0xFF, 0x74, 0x00, 
	0x1E, 0x02, 0xDD, 0x02, 0x65, 0x01, 0x72, 0xFE, 0x42, 0xFC, 0x87, 0xFC, 0xED, 0xFE, 0x77, 0x01, 
	0x5F, 0x02, 0x82, 0x01, 0x3A, 0x00, 0xD3, 0xFF, 0x5E, 0x00, 0xC9, 0x00, 0x2D, 0x00, 0xD3, 0xFE, 
	0xEA, 0xFD, 0x5F, 0xFE, 0xEE, 0xFF, 0x68, 0x01, 0xC5, 0x01, 0xFD, 0x00, 0xEE, 0xFF, 0x70, 0xFF, 
	0x9C, 0xFF, 0xF2, 0xFF, 0xF8, 0xFF, 0xB0, 0xFF, 0x85, 0xFF, 0xB7, 0xFF, 0x1F, 0x00, 0x5F, 0x00, 
	0x4C, 0x00, 0x14, 0x00, 0xFD, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 
	0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF, 
	0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFE, 0xFF, 0x00, 0x00
};

