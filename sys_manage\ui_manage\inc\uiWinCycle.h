/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_CYCLE_H
#define UI_WIN_CYCLE_H

typedef struct
{
	uiWidgetObj widget;
	uiCycle		cycle;
	uiColor 	bgcolor;
	uiColor 	fontColor;
	u16 		select;
}uiCycleObj;
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiCycleCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id);
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiCycleCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);


#endif
