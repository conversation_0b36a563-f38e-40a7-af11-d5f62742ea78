/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  JPG_TYPEDEF_H
#define  JPG_TYPEDEF_H
//SMALL_ENDIAN low addr store Low bytes
//JPEG TAG
#define  JPEG_SOI		0xD8FF	//Start Of Image
#define  JPEG_SOF0		0xC0FF	//Start Of Frame 0
#define  JPEG_SOF2		0xC2FF	//Start of Frame 2
#define  JPEG_DHT		0xC4FF	//Define Huffman Table(s)
#define  JPEG_DQT		0xDBFF	//Define Quantization Table(s)
#define  JPEG_DRI		0xDDFF	//Define Restart Interval	
#define  JPEG_SOS		0xDAFF	//Start of Scan	
#define  JPEG_RST0		0xD0FF	//Restart 0
#define  JPEG_RST1		0xD1FF	//Restart 1 
#define  JPEG_RST2		0xD2FF	//Restart 2
#define  JPEG_RST3		0xD3FF	//Restart 3
#define  JPEG_RST4		0xD4FF	//Restart 4
#define  JPEG_RST5		0xD5FF	//Restart 5
#define  JPEG_RST6		0xD6FF	//Restart 6	 
#define  JPEG_RST7		0xD7FF	//Restart 7	
#define  JPEG_APP0		0xE0FF	//Application-sepcific 0
#define  JPEG_APP1		0xE1FF	//Application-sepcific 1
#define  JPEG_APP2		0xE2FF	//Application-sepcific 2	
#define  JPEG_APP3		0xE3FF	//Application-sepcific 3
#define  JPEG_APP4		0xE4FF	//Application-sepcific 4
#define  JPEG_APP5		0xE5FF	//Application-sepcific 5
#define  JPEG_APP6		0xE6FF	//Application-sepcific 6
#define  JPEG_APP7		0xE7FF	//Application-sepcific 7
#define  JPEG_APP8		0xE8FF	//Application-sepcific 8
#define  JPEG_APP9		0xE9FF	//Application-sepcific 9	
#define  JPEG_APP10		0xEAFF	//Application-sepcific 10
#define  JPEG_APP11		0xEBFF	//Application-sepcific 11
#define  JPEG_APP12		0xECFF	//Application-sepcific 12
#define  JPEG_APP13		0xEDFF	//Application-sepcific 13
#define  JPEG_APP14		0xEEFF	//Application-sepcific 14
#define  JPEG_APP15		0xEFFF	//Application-sepcific 15
#define  JPEG_COM		0xFEFF	//Comment
#define  JPEG_EOI		0xD9FF	//End of Image

//Component Type
#define IFD_COM_INT8U			1	//1 BYTES
#define IFD_COM_STRING			2   //1 BYTES
#define IFD_COM_INT16U			3	//3 BYTES
#define IFD_COM_INT32U			4	//4 BYTES
#define IFD_COM_INT64U			5   //8 BYTES
#define IFD_COM_INT8S			6	//1 BYTES
#define IFD_COM_UNDEFINED		7   //1 BYTES
#define IFD_COM_INT16S			8	//2 BYTES
#define IFD_COM_INT32S			9	//3 BYTES
#define IFD_COM_INT64S			10	//8 BYTES
#define IFD_COM_FLOAT			11	//4 BYTES
#define IFD_COM_DOUBLE_FLOAT	12  //8 BYTES

//EXIF Tags
#define EXIF_Make							0x010f //Group: IFD0 , Writable: IFD_COM_STRING
#define EXIF_Model							0x0110 //Group: IFD0 , Writable: IFD_COM_STRING
#define EXIF_Orientation					0x0112 //Group: IFD0 , Writable: IFD_COM_INT16U
#define EXIF_XResolution 					0x011a //Group: IFD0 , Writable: IFD_COM_INT64U
#define EXIF_YResolution 					0x011b //Group: IFD0 , Writable: IFD_COM_INT64U
#define EXIF_ResolutionUnit					0x0128 //Group: IFD0 , Writable: IFD_COM_INT16U
#define EXIF_Software						0x0131 //Group: IFD0 , Writable: IFD_COM_STRING
#define EXIF_ModifyDate						0x0132 //Group: IFD0 , Writable: IFD_COM_STRING
#define EXIF_YCbCrPositioning  				0x0213 //Group: IFD0 , Writable: IFD_COM_INT16U
#define EXIF_ExposureTime					0x829a //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_FNumber						0x829d //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ExifOffset						0x8769 //Group: IFD0 , Writable: -
#define EXIF_ExposureProgram				0x8822 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ISO							0x8827 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ExifVersion					0x9000 //Group: ExifIFD , Writable: IFD_COM_UNDEFINED
#define EXIF_DateTimeOriginal				0x9003 //Group: ExifIFD , Writable: IFD_COM_STRING
#define EXIF_CreateDate						0x9004 //Group: ExifIFD , Writable: IFD_COM_STRING
#define EXIF_ComponentsConfiguration		0x9101 //Group: ExifIFD , Writable: IFD_COM_UNDEFINED
#define EXIF_CompressedBitsPerPixel			0x9102 //Group: ExifIFD , Writable: IFD_COM_INT64U
#define EXIF_BrightnessValue				0x9203 //Group: ExifIFD , Writable: IFD_COM_INT64S
#define EXIF_ExposureCompensation			0x9204 //Group: ExifIFD , Writable: IFD_COM_INT64S
#define EXIF_MaxApertureValue				0x9205 //Group: ExifIFD , Writable: IFD_COM_INT64U
#define EXIF_MeteringMode					0x9207 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_LightSource					0x9208 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_Flash							0x9209 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_FocalLength					0x920a //Group: ExifIFD , Writable: IFD_COM_INT64U
#define EXIF_SubjectArea					0x9214 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_FlashpixVersion				0xa000 //Group: ExifIFD , Writable: IFD_COM_UNDEFINED
#define EXIF_ColorSpace						0xa001 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ExifImageWidth					0xa002 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ExifImageHeight				0xa003 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_InteropOffset					0xa005 //Group: ExifIFD , Writable: -
#define EXIF_FileSource						0xa300 //Group: ExifIFD , Writable: IFD_COM_UNDEFINED
#define EXIF_SceneType						0xa301 //Group: ExifIFD , Writable: IFD_COM_UNDEFINED
#define EXIF_CustomRendered					0xa401 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_ExposureMode					0xa402 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_WhiteBalance					0xa403 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_DigitalZoomRatio				0xa404 //Group: ExifIFD , Writable: IFD_COM_INT64U
#define EXIF_FocalLengthIn35mmFormat		0xa405 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_SceneCaptureType				0xa406 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_GainControl					0xa407 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_Contrast						0xa408 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_Saturation						0xa409 //Group: ExifIFD , Writable: IFD_COM_INT16U
#define EXIF_Sharpness						0xa40a //Group: ExifIFD , Writable: IFD_COM_INT16U




#define  IFD0_ENTRY_CNT  					10
#define  SUBIFD0_ENTRY_CNT  				34


typedef struct TIFF_HEAD_S{
	u8   byte_oder[2];		//"II" --intel small endian, "MM" - motorola big endian
	u16  tag_marker;    //0x002a / 0x2a00
	u32  offset_next_ifd;   //  offset: TIFF_HEAD_T start to first IFD 根据 “Offset to next IFD”是否为0x00000000来判断当前节点是不是最后一个节点。
}__attribute__((packed)) TIFF_HEAD_T;
typedef struct IFD_ENTRY_S{
	u16  tag;	//0x010F
	u16  type;  //IFD_COMPONENT_TYPE
	u32  cnt;	// 存储的是 Directory Entry 对应的Component的数量
	u32  value_off;//存储的可能是Entry对应的值，Entry对应的值的长度超过四个字节，那么存储的是对应的值的偏移地址（该偏移寻址的基址也是TIFFHEADER的起始位置）。
}__attribute__((packed)) IFD_ENTRY_T;
typedef struct EXIF_IFD_S{
	u16	 		entry_num; //IFD0_ENTRY_CNT
	IFD_ENTRY_T	entry[IFD0_ENTRY_CNT];
	u32			offset_next_ifd;
	u8			entry0_data[8];
	u8			entry3_data[8];
	u8			entry4_data[8];
	u8			entry6_data[16];
	u8			entry7_data[20];
}__attribute__((packed)) EXIF_IFD_T;
typedef struct EXIF_SUBIFD_S{
	u16	 		entry_num; //IFD0_ENTRY_CNT
	IFD_ENTRY_T	entry[SUBIFD0_ENTRY_CNT];
	u32			offset_next_ifd;
	u8			entry0_data[8];
	u8			entry1_data[8];
	u8			entry5_data[20];
	u8			entry6_data[20];
	u8			entry8_data[8];
	u8			entry9_data[8];
	u8			entry10_data[8];
	u8			entry11_data[8];
	u8			entry15_data[8];
	u8			entry16_data[8];
	u8			entry27_data[8];
}__attribute__((packed)) EXIF_SUBIFD_T;

typedef struct EXIF_HEADER_S{
	u16 			soi_maker;
	u16 			app1_marker;  //JPEG_APP1
	u16 			app1_size;
	u8  			app1_tag[6]; //"Exif  "
	TIFF_HEAD_T 	tiff_head;
	EXIF_IFD_T		ifd0;
	EXIF_SUBIFD_T	sub_ifd0;
}__attribute__((packed))  EXIF_HEADER_T;





#endif
