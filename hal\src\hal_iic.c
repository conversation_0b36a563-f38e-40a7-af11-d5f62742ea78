/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"

#if HAL_CFG_EN_IIC0
//static volatile u8 halIIC0Lock = 0;
#define  HAL_IIC0_LOCK()       //if(halIIC0Lock){return ;}halIIC0Lock =1;
#define  HAL_IIC0_UNLOCK()    //halIIC0Lock=0;
#endif



/*******************************************************************************
* Function Name  : hal_iicInit
* Description    : hal layer .iic0 initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic0Init(void)
{
#if HAL_CFG_EN_IIC0
	hx330x_iic0Init(hardware_setup.cmos_sensor_iic_baudrate);

	hx330x_iic0Stop();
#endif
}
/*******************************************************************************
* Function Name  : hal_iicUninit
* Description    : hal layer .iic0 uninitial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic0Uninit(void)
{
#if HAL_CFG_EN_IIC0
     hx330x_iic0Uninit();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic8bitAddrWriteData
* Description    : hal layer .iic0 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrWriteData(u8 slaveid,u8 addr,u8 data)
{
	static u8 i;
#if HAL_CFG_EN_IIC0
	 HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid

	i=hx330x_iic0RecvACK();            // recv ack singal
	 deg_Printf("00000000000000IIC Write Status:%d\n",i);

    hx330x_iic0SendByte(addr);     // send address

    i=hx330x_iic0RecvACK();            // recv ack singal
	 deg_Printf("111111111111IIC Write Status:%d\n",i);
   	hx330x_iic0SendByte(data);
    i=hx330x_iic0RecvACK();            // recv ack singal
	 deg_Printf("2222222222222IIC Write Status:%d\n",i);

	 hx330x_iic0Stop();

	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic8bitAddrReadData
* Description    : hal layer .iic0 read data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrReadData(u8 slaveid,u8 addr,u8 *data)
{
#if HAL_CFG_EN_IIC0
     u8 temp;	
	 HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal

	 hx330x_iic0SendByte(addr);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0Stop();                 // stop

	 
	 hx330x_iic0Start();				 // send Start singal

	 hx330x_iic0SendByte(slaveid|1);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal


   	 temp = hx330x_iic0RecvByte();
//	 hx330x_iic1SendACK();

	 hx330x_iic0Stop();	
    
     if(data)
	 	*data = temp;
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic8bitAddrWrite
* Description    : hal layer .iic0 write data for 8bit address slave
* Input          : u8 slaveid : slave id
                      u8 addr    : slave addr
                      u8 *data  : data buffer
                      u8 len      : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrWrite(u8 slaveid,u8 addr,u8 *data,u8 len)
{
#if HAL_CFG_EN_IIC0
     int i;	
	 //deg_Printf("id:%x, addr:%x, data:%x\n",slaveid,addr, data[0]);
	 HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid

	 hx330x_iic0RecvACK();             // recv ack singal

     hx330x_iic0SendByte(addr);     // send address

     hx330x_iic0RecvACK();            // recv ack singal
	 for(i=0;i<len;i++)
	 {
	 	hx330x_iic0SendByte(data[i]);    // send data
		hx330x_iic0RecvACK();              // recv ack
	 }

	 hx330x_iic0Stop();                   // send stop singal	
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic8bitAddrRead
* Description    : hal layer .iic0 read data for 8bit address slave
* Input          : u8 slaveid : slave id
                      u8 addr    : slave addr
                      u8 *data  : data buffer
                      u8 len      : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrRead(u8 slaveid,u8 addr,u8  *data,u8 len)
{
#if HAL_CFG_EN_IIC0
     int i;	
     HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal

     hx330x_iic0SendByte(addr);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0Stop();                 // stop

	 
	 hx330x_iic0Start();				 // send Start singal

	 hx330x_iic0SendByte(slaveid|1);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal
	 
	 for(i=0;i<len;i++)
	 {
	 	data[i] = hx330x_iic0RecvByte();    // send data
		//hx330x_iic0SendACK();              // recv ack
	 }

	 hx330x_iic0Stop();                   // send stop singal
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrWriteData
* Description    : hal layer .iic write data for 16bit address slave
* Input          : u8 slaveid : slave id
                      u8 addr    : slave addr
                      u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrWriteData(u8 slaveid,u16 addr,u8 data)
{
#if HAL_CFG_EN_IIC0
     HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid

	 hx330x_iic0RecvACK();             // recv ack singal

     hx330x_iic0SendByte(addr>>8);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0SendByte(addr&0xff);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

   	 hx330x_iic0SendByte(data);
	 hx330x_iic0RecvACK();

	 hx330x_iic0Stop();
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrReadData
* Description    : hal layer .iic0 read data for 16bit address slave
* Input          : u8 slaveid : slave id
                      u16 addr    : slave addr
                      u8 *data  : data buffer
                      u8 len      : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrReadData(u8 slaveid,u16 addr,u8 *data)
{
#if HAL_CFG_EN_IIC0
     u8 temp;	
     HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal

	 hx330x_iic0SendByte(addr>>8);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0SendByte(addr&0xff);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0Stop();                 // stop

	 
	 hx330x_iic0Start();				 // send Start singal

	 hx330x_iic0SendByte(slaveid|1);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal


   	 temp = hx330x_iic0RecvByte();
//	 hx330x_iic1SendACK();

	 hx330x_iic0Stop();
	 
	 if(data)
		 *data =temp;
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrWrite
* Description    : hal layer .iic0 write data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrWrite(u8 slaveid,u16 addr,u8 *data,u8 len)
{
#if HAL_CFG_EN_IIC0
     int i;	
	 HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal

	 hx330x_iic0SendByte(addr>>8);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0SendByte(addr&0xff);     // send address
     hx330x_iic0RecvACK();            // recv ack singal
     

	 for(i=0;i<len;i++)
	 {
	 	hx330x_iic0SendByte(data[i]);    // send data
		hx330x_iic0RecvACK();              // recv ack
	 }

	 hx330x_iic0Stop();                   // send stop singal
	 HAL_IIC0_UNLOCK();
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrRead
* Description    : hal layer .iic0 read data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrRead(u8 slaveid,u16 addr,u8  *data,u8 len)
{
#if HAL_CFG_EN_IIC0
     int i;	
	 HAL_IIC0_LOCK();
     hx330x_iic0Start();                 // send Start singal

	 hx330x_iic0SendByte(slaveid);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal

	 hx330x_iic0SendByte(addr>>8);     // send address
     hx330x_iic0RecvACK();            // recv ack singal

	 hx330x_iic0SendByte(addr&0xff);     // send address
     hx330x_iic0RecvACK();            // recv ack singal


	 hx330x_iic0Stop();                 // stop

	 
	 hx330x_iic0Start();				 // send Start singal

	 hx330x_iic0SendByte(slaveid|1);  // send slaveid
	 hx330x_iic0RecvACK();             // recv ack singal
	 
	 for(i=0;i<len;i++)
	 {
	 	data[i] = hx330x_iic0RecvByte();    // send data
	//	hx330x_iic1SendACK();              // recv ack
	 }

	 hx330x_iic0Stop();                   // send stop singal
	 HAL_IIC0_UNLOCK();
#endif
}

static u8 halIIC1ShareFlag = 0;
/*******************************************************************************
* Function Name  : hal_iic1IOShare
* Description    : hal layer .iic1 io share
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u8 hal_iic1IOShare(void)
{
#if HAL_CFG_EN_IIC1
	u8 temp;

	temp = halIIC1ShareFlag;

	halIIC1ShareFlag = 1;

	return temp;
#else
	return 1;
#endif
}
/*******************************************************************************
* Function Name  : hal_uartIOShareCheck
* Description    : hal layer.uart io share flag check
* Input          : none
* Output         : None
* Return         : 
*******************************************************************************/
void hal_iic1IOShareCheck(void)
{
#if HAL_CFG_EN_IIC1	
	if(halIIC1ShareFlag)
	{
		hx330x_iic1Init(HAL_CFG_IIC1_BAUDRATE);
	    halIIC1ShareFlag = 0;
	}
#endif
}
/*******************************************************************************
* Function Name  : hal_iicInit
* Description    : hal layer .iic1 initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic1Init(void)
{
#if HAL_CFG_EN_IIC1
	halIIC1ShareFlag = 0;
	hx330x_iic1Init(HAL_CFG_IIC1_BAUDRATE);

	hx330x_iic1Stop();
#endif
}


/*******************************************************************************
* Function Name  : hal_iicUninit
* Description    : hal layer .iic1 uninitial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic1Uninit(void)
{
#if HAL_CFG_EN_IIC1
	hx330x_iic1Uninit();
	halIIC1ShareFlag = 0;
#endif
}


/*******************************************************************************
* Function Name  : hal_iic8bitAddrWriteData
* Description    : hal layer .iic1 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrWriteData(u8 slaveid,u8 addr,u8 data)
{
#if HAL_CFG_EN_IIC1
	hal_iic1IOShareCheck();
	hx330x_iic1Start(); 				// send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid

	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1SendByte(addr);	   // send address

	hx330x_iic1RecvACK();			 // recv ack singal

	hx330x_iic1SendByte(data);
	hx330x_iic1RecvACK();

	hx330x_iic1Stop();
#endif
}

/*******************************************************************************
* Function Name  : hal_iic8bitAddrReadData
* Description    : hal layer .iic1 read data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrReadData(u8 slaveid,u8 addr,u8 *data)
{
#if HAL_CFG_EN_IIC1
	u8 temp;	
	hal_iic1IOShareCheck();
	hx330x_iic1Start();                 // send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid
	hx330x_iic1RecvACK();             // recv ack singal

	hx330x_iic1SendByte(addr);     // send address
	hx330x_iic1RecvACK();            // recv ack singal

	hx330x_iic1Stop();                 // stop

	 
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid|1);  // send slaveid
	hx330x_iic1RecvACK();             // recv ack singal


	temp = hx330x_iic1RecvByte();
//	hx330x_iic1SendACK();

	hx330x_iic1Stop();	
    
	if(data)
		*data = temp;
#endif
}

/*******************************************************************************
* Function Name  : hal_iic8bitAddrWrite
* Description    : hal layer .iic1 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrWrite(u8 slaveid,u8 addr,u8 *data,u8 len)
{
#if HAL_CFG_EN_IIC1
	int i;	
	hal_iic1IOShareCheck();

	hx330x_iic1Start(); 				// send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid

	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1SendByte(addr);	   // send address

	hx330x_iic1RecvACK();			 // recv ack singal
	for(i=0;i<len;i++)
	{
	   hx330x_iic1SendByte(data[i]);	// send data
	   hx330x_iic1RecvACK();			  // recv ack
	}

	hx330x_iic1Stop();					 // send stop singal	

#endif
}

/*******************************************************************************
* Function Name  : hal_iic8bitAddrRead
* Description	 : hal layer .iic1 read data for 8bit address slave
* Input		     : u8 slaveid : slave id
				   u8 addr	  : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic18bitAddrRead(u8 slaveid,u8 addr,u8  *data,u8 len)
{
#if HAL_CFG_EN_IIC1
	int i;
	hal_iic1IOShareCheck();
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid);	// send slaveid
	hx330x_iic1RecvACK();			   // recv ack singal

	hx330x_iic1SendByte(addr); 	// send address
	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1Stop(); 				// stop

	
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid|1);  // send slaveid
	hx330x_iic1RecvACK();			   // recv ack singal
	 
	for(i=0;i<len;i++)
	{
		data[i] = hx330x_iic1RecvByte();	// send data
	//	hx330x_iic1SendACK();			   // recv ack
	}

	hx330x_iic1Stop(); 				  // send stop singal	
#endif
}

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWriteData
* Description	 : hal layer .iic write data for 16bit address slave
* Input 		 : u8 slaveid : slave id
				   u8 addr	  : slave addr
				   u8 data	  : data
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic116bitAddrWriteData(u8 slaveid,u16 addr,u8 data)
{
#if HAL_CFG_EN_IIC1
	hal_iic1IOShareCheck();
	hx330x_iic1Start(); 				// send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid

	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1SendByte(addr>>8);	  // send address
	hx330x_iic1RecvACK();			 // recv ack singal

	hx330x_iic1SendByte(addr&0xff); 	// send address
	hx330x_iic1RecvACK();			 // recv ack singal

	hx330x_iic1SendByte(data);
	hx330x_iic1RecvACK();

	hx330x_iic1Stop();		
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrReadData
* Description	 : hal layer .iic1 read data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic116bitAddrReadData(u8 slaveid,u16 addr,u8 *data)
{
#if HAL_CFG_EN_IIC1
 	u8 temp;
	hal_iic1IOShareCheck();
	
	hx330x_iic1Start();                 // send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid
	hx330x_iic1RecvACK();             // recv ack singal

	hx330x_iic1SendByte(addr>>8);     // send address
	hx330x_iic1RecvACK();            // recv ack singal

	hx330x_iic1SendByte(addr&0xff);     // send address
	hx330x_iic1RecvACK();            // recv ack singal

	hx330x_iic1Stop();                 // stop

	
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid|1);  // send slaveid
	hx330x_iic1RecvACK();             // recv ack singal


	temp = hx330x_iic1RecvByte();
//	hx330x_iic1SendACK();

	hx330x_iic1Stop();

	if(data)
	   *data = temp;
#endif
}

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWrite
* Description	 : hal layer .iic1 write data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		  : None
* Return		  : none
*******************************************************************************/
void hal_iic116bitAddrWrite(u8 slaveid,u16 addr,u8 *data,u8 len)
{
#if HAL_CFG_EN_IIC1
	int i;
	hal_iic1IOShareCheck();	
	hx330x_iic1Start(); 				// send Start singal

	hx330x_iic1SendByte(slaveid);  // send slaveid
	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1SendByte(addr>>8);	  // send address
	hx330x_iic1RecvACK();			 // recv ack singal

	hx330x_iic1SendByte(addr&0xff); 	// send address
	hx330x_iic1RecvACK();			 // recv ack singal


	for(i=0;i<len;i++)
	{
	   hx330x_iic1SendByte(data[i]);	// send data
	   hx330x_iic1RecvACK();			  // recv ack
	}

	hx330x_iic1Stop();					 // send stop singal
#endif
}
/*******************************************************************************
* Function Name  : hal_iic16bitAddrRead
* Description	 : hal layer .iic1 read data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		  : None
* Return		  : none
*******************************************************************************/
void hal_iic116bitAddrRead(u8 slaveid,u16 addr,u8  *data,u8 len)
{
#if HAL_CFG_EN_IIC1
 	int i;
	hal_iic1IOShareCheck();	
	
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid);	// send slaveid
	hx330x_iic1RecvACK();			   // recv ack singal

	hx330x_iic1SendByte(addr>>8);	   // send address
	hx330x_iic1RecvACK();			  // recv ack singal

	hx330x_iic1SendByte(addr&0xff);	 // send address
	hx330x_iic1RecvACK();			  // recv ack singal


	hx330x_iic1Stop(); 				// stop

	
	hx330x_iic1Start();				 // send Start singal

	hx330x_iic1SendByte(slaveid|1);  // send slaveid
	hx330x_iic1RecvACK();			   // recv ack singal
	
	for(i=0;i<len;i++)
	{
		data[i] = hx330x_iic1RecvByte();	// send data
	//	hx330x_iic1SendACK();			   // recv ack
	}

	hx330x_iic1Stop(); 				  // send stop singal
#endif

}




