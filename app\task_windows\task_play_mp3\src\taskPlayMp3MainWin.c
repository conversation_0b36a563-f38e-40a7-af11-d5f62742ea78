/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYMP3MAIN_MODE_ID = 0,
	PLAYMP3MAIN_PLAYTIME_ID,
	PLAYMP3MAIN_PLAYMODE_ID,
	PLAYMP3MAIN_SD_ID,
	PLAYMP3MAIN_BATERRY_ID,
	PLAYMP3MAIN_SELECT_ID,
	
};

UNUSED ALIGNED(4) const widgetCreateInfor playMp3MainWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Black,WIN_ABS_POS),
	
	createImageIcon(PLAYMP3MAIN_MODE_ID,    	Rx(5),   Ry(0),   Rw(25),  Rh(25),  R_ID_ICON_MTPLAY2, 	ALIGNMENT_LEFT),
	createStringIcon(PLAYMP3MAIN_PLAYTIME_ID,	Rx(30),  Ry(0),   Rw(110), Rh(25),	RAM_ID_MAKE(" "),	ALIGNMENT_CENTER, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(PLAYMP3MAIN_PLAYMODE_ID,    Rx(230), Ry(0),   Rw(40),  Rh(25), 	R_ID_ICON_MUSICMODECONTINUE,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_SD_ID,        	Rx(270), Ry(0),   Rw(25),  Rh(25), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createImageIcon(PLAYMP3MAIN_BATERRY_ID,   	Rx(295), Ry(0),   Rw(25),  Rh(25), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),	
	
	createItemManage(PLAYMP3MAIN_SELECT_ID,		Rx(0),   Ry(25),  Rw(320), Rh(215),INVALID_COLOR),
	widgetEnd(),
};


/*******************************************************************************
* Function Name  : playMp3MainPlayTimeShow
* Description    : playMp3MainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainPlayTimeShow(winHandle handle)
{
	if(mp3_dec_sta() >= MP3_DEC_START)
	{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetStrInfor(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID), 0, ALIGNMENT_CENTER, R_ID_PALETTE_Red);
	}
	else
	{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
		uiWinSetStrInfor(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID), 0, ALIGNMENT_CENTER, R_ID_PALETTE_White);
	}
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_PLAYTIME_ID),1);
	
}
/*******************************************************************************
* Function Name  : playMp3MainSDShow
* Description    : playMp3MainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainPlayModeShow(winHandle handle)
{
	if(task_playMp3_op.playmode == PLAY_MP3_MODE_NONE)
	{
		uiWinSetVisible(winItem(handle,PLAYMP3MAIN_PLAYMODE_ID),0);
	}else
	{
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_PLAYMODE_ID),task_playMp3_op.playmode);
		uiWinSetVisible(winItem(handle,PLAYMP3MAIN_PLAYMODE_ID),1);
	}
	
}

/*******************************************************************************
* Function Name  : playMp3MainSDShow
* Description    : playMp3MainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3MainSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PLAYMP3MAIN_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : playMp3MainBaterryShow
* Description    : playMp3MainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/	
static void playMp3MainBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4: 
			//case BATTERY_STAT_5: 
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetResid(winItem(handle,PLAYMP3MAIN_BATERRY_ID),batid);	
	uiWinSetVisible(winItem(handle,PLAYMP3MAIN_BATERRY_ID),1);
}