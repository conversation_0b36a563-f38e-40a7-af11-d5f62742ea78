/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_TMINF_H
   #define HX330X_TMINF_H

typedef struct
{
	u32 ctl;
	u32 size0;
	u32 size1;
	u32 size2;
	u32 pos0;
	u32 pos1;
	u32 pos2;
	u32 adr0;
	u32 adr1;
	u32 adr2;
}MJPTMINF_CTL;



enum
{
	MJPEG_TIMEINFO_LAYER0=0,
	MJPEG_TIMEINFO_LAYER1,
	MJPEG_TIMEINFO_LAYER2,

	MJPEG_TIMEINFO_MAX
};


/*******************************************************************************
* Function Name  : hx330x_mjpA_TimeinfoEnable
* Description    : mjpeg time info en
* Input          : u8 layer : layer
				   u8 en : 0->disable,
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_TimeinfoEnable(u8 layer,u8 en);
/*******************************************************************************
* Function Name  : hx330x_mjpB_TimeinfoEnable
* Description    : mjpeg time info en
* Input          : u8 layer : layer
				   u8 en : 0->disable,
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpB_TimeinfoEnable(u8 layer,u8 en);
/*******************************************************************************
* Function Name  : hx330x_mjpegTimeinfoColor
* Description    : mjpeg time info color in yuv422
* Input          : u32 y : y-byte
				   u32 u : u-bye
				   u32 v : v-byte
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_TimeinfoColor(u32 y,u32 u,u32 v);
void hx330x_mjpB_TimeinfoColor(u32 y,u32 u,u32 v);
/*******************************************************************************
* Function Name  : hx330x_mjpA_TimeinfoSize
* Description    : mjpeg time info ram size
* Input          : u8 layer : timeinfo layer
				   u32 width    : width -byte
				   u32 height   : height-byte
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_TimeinfoSize(u8 layer,u32 width,u32 height);
void hx330x_mjpB_TimeinfoSize(u8 layer,u32 width,u32 height);
/*******************************************************************************
* Function Name  : hx330x_mjpA_TimeinfoPos
* Description    : mjpeg time info position
* Input          : u8 layer : timeinfo layer
				   u32 x    : x position
				   u32 y   : y posotion
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_TimeinfoPos(u8 layer,u32 x,u32 y);
void hx330x_mjpB_TimeinfoPos(u8 layer,u32 x,u32 y);
/*******************************************************************************
* Function Name  : hx330x_mjpA_TimeinfoAddr
* Description    : mjpeg time info adrr
* Input          : u8 layer : timeinfo layer
				   u32 addr: ram addrress
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_mjpA_TimeinfoAddr(u8 layer,u32 addr);
void hx330x_mjpB_TimeinfoAddr(u8 layer,u32 addr);
/*******************************************************************************
* Function Name  : hx330x_recfg_mjpb_tminf
* Description    : hx330x_recfg_mjpb_tminf
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_recfg_mjpb_tminf(void);




#endif
