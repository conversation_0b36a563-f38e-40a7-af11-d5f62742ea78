/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../app_common/inc/app_api.h"
#define    WINDOWS_MAX_ARG       5
/*******************************************************************************
* Function Name  : dialogCB
* Description    : dialogCB
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void dialogCB(uiWinMsg* msg)
{
	winHandle hWin = msg->curWin;
	uiFrameWinObj* pFrameWin = (uiFrameWinObj*)uiHandleToPtr(hWin);
	uiFrameWinObj* pParent;
	u32* argv;
	WINDOWS_T* win;
	u32 argv1[3];
	switch(msg->id)
	{
		case MSG_DIALOG_INIT:
			win = (WINDOWS_T*)(pFrameWin->prvate);
			if(win)
			{
				pParent = (uiFrameWinObj*)uiHandleToPtr(pFrameWin->win.parent);
				if(pParent)
				{
					if(pParent->win.style & WIN_FRAME)
					{
						if(pParent->prvate)
							app_msgDealByInfor(((WINDOWS_T*)(pParent->prvate))->msgDeal,SYS_CHILE_OPEN,pFrameWin->win.parent,0,NULL);
					}
					else
					{
						deg_msg("parent window is not frame\n");
					}
				}
				argv = msg->para.p;
				app_msgDealByInfor(win->msgDeal,SYS_OPEN_WINDOW,hWin,argv[0],argv+1);
				//msgDealByType(SYS_OPEN_WINDOW,hWin,argv[0],argv+1);
			}
			else
				deg_err("window open error,can not find registered msg\n");
			break;
		case MSG_WIN_CHILE_DESTROY:
			win=(WINDOWS_T*)(pFrameWin->prvate);
			if(win)
			{
				taskmsgFuncRegister(win->msgDeal);
				app_msgDealByType(SYS_CHILE_COLSE,hWin,0,NULL);
			}
			else
				deg_err("child window close error,can not find registered msg\n");
			break;
		case MSG_DIALOG_CLOSE:
			win=(WINDOWS_T*)(pFrameWin->prvate);
			if(win)
			{
				app_msgDealByInfor(win->msgDeal,SYS_CLOSE_WINDOW,hWin,0,NULL);
				win->handle=INVALID_HANDLE;
			}
			else
				deg_err("window close error,can not find registered msg\n\n");
			break;
		case MSG_WIN_PARENT_DEAL:
			win = (WINDOWS_T*)(pFrameWin->prvate);
			app_msgDealByInfor(win->msgDeal,msg->para.v,hWin,0,NULL);
			break;
		case MSG_WIN_TOUCH:
			argv1[0]=((touchInfor *)(msg->para.p))->touchID;
			argv1[1]=((touchInfor *)(msg->para.p))->touchItem;
			argv1[2]=((touchInfor *)(msg->para.p))->touchState;
			app_msgDealByType(SYS_TOUCH_WINDOW,hWin,3,argv1);
			break;
		default:
			break;
	}
}
/*******************************************************************************
* Function Name  : uiParentDealMsg
* Description    : uiParentDealMsg
* Input          : winHandle handle,u32 parentMsg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiParentDealMsg(winHandle handle,u32 parentMsg)
{
	uiWinMsg msg;
	msg.id 		= MSG_WIN_PARENT_DEAL;
	msg.para.v 	= parentMsg;
	uiWinSendMsgToParent(handle,&msg);
}

/*******************************************************************************
* Function Name  : uiOpenWindow
* Description    : uiOpenWindow
* Input          : WINDOWS_T* winInfor,u32 show,u32 argc,...
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiOpenWindow(WINDOWS_T* winInfor,u32 show,u32 argc,...)
{
	uiWinMsg msg;
	winHandle hDialog;
	u32 argv[WINDOWS_MAX_ARG + 1];
	u32 i;
	va_list arglist;
	va_start(arglist, argc);
	if(argc > WINDOWS_MAX_ARG)
		argc = WINDOWS_MAX_ARG;
	argv[0] = argc;
	for(i = 0;i < argc; i++)	
		argv[i+1] = va_arg(arglist, uint32);
	va_end(arglist);
	if(winInfor->handle != INVALID_HANDLE)
	{
		if(winInfor->repeateOpenSupport == 0)
		{
			deg_msg("window has opened,do not surport open a window repeatedly\n");
			return INVALID_HANDLE;
		}
		deg_msg("open a window repeatedly\n");
		goto SEND_MSG;
	}

	//if(winInfor->widgetInfor)
	//	winInfor->widgetInfor->prvate = winInfor;
	hDialog = uiDialogCreate(winInfor->widgetInfor,dialogCB,INVALID_HANDLE,(void*)winInfor);
	if(hDialog == INVALID_HANDLE)
	{
		deg_err("create window failed !!!\n");
		return INVALID_HANDLE;
	}
	taskmsgFuncRegister(winInfor->msgDeal);
	winInfor->handle = hDialog;
SEND_MSG:
	msg.id 		= MSG_DIALOG_INIT;
	msg.para.p 	= (void*)argv;
	uiWinSendMsg(winInfor->handle,&msg);
	if(show)
		app_draw_Service(0);
	return winInfor->handle;
}

/*******************************************************************************
* Function Name  : windowIsOpen
* Description    : windowIsOpen
* Input          : WINDOWS_T* winInfor,u32 show,u32 argc,...
* Output         : none                                            
* Return         : none 
*******************************************************************************/
bool windowIsOpen(WINDOWS_T* winInfor)
{
	if(winInfor == NULL)
		return false;
	if(winInfor->handle)
		return true;
	return false;
}













