/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  GIF_API_H
#define  GIF_API_H

#include "gif_typedef.h"

/*******************************************************************************
* Function Name  : gif_get_frame
* Description    : gif_get_frame
* Input          : NONE
* Output         : NONE
* Return         : Return 1 if got a frame; 0 if got GIF trailer; -1 if error.		   
*******************************************************************************/
int gif_get_frame(lcdshow_frame_t *frame);
/*******************************************************************************
* Function Name  : gif_rewind
* Description    : gif_rewind
* Input          : NONE
* Output         : NONE
* Return         : 0 : success, < 0: err		   
*******************************************************************************/
int gif_rewind(void);
/*******************************************************************************
* Function Name  :gif_Dec_Start
* Description    :gif file decode start
* Input          :int type: MEDIA_SRC_FS / MEDIA_SRC_NVFS / MEDIA_SRC_RAM 
				  int fd: type == MEDIA_SRC_FS, fd means fs_open return value
				  		  type == MEDIA_SRC_NVFS, fd means  nv_open return value
						  type == MEDIA_SRC_RAM, fd means  ram addr
				  int size : type == MEDIA_SRC_FS, size means fs_size return value
				  			 type == MEDIA_SRC_NVFS, fd means  nv_size return value
							 type == MEDIA_SRC_RAM, fd means  ram size
* Output         : NONE
* Return         : 0: SUCCESS, -1: fail
*******************************************************************************/
int gif_Dec_Start(int type, int fd, int size);
/*******************************************************************************
* Function Name  : gif_loopcnt_get
* Description    : gif_loopcnt_get
* Input          : NONE
* Output         : NONE
* Return         : u16 loopcnt	   
*******************************************************************************/
u16 gif_loopcnt_get(void);
/*******************************************************************************
* Function Name  :gif_Dec_Stop
* Description    :gif file decode stop
* Input          : NONE 
* Output         : NONE
* Return         : NONE
*******************************************************************************/
void gif_Dec_Stop(void);

/*******************************************************************************
* Function Name  : gif_delay_get
* Description    : gif_delay_get
* Input          : NONE
* Output         : NONE
* Return         : u32 : ms		   
*******************************************************************************/
u32 gif_delay_get(void);
/*******************************************************************************
* Function Name  : gif_Dec_state
* Description    : gif_Dec_state
* Input          : NONE
* Output         : NONE
* Return         : Return 1 start; 0 if got GIF trailer; <0  if error.			   
*******************************************************************************/
int gif_Dec_state(void);
#endif
