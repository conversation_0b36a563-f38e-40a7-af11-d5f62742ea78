/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_DVP_720P_GC1064 > 0


SENSOR_INIT_SECTION const u8 GC1064InitTable[]=
{ 
	////mclk=24M pclk=48M htotal=2096 linetotal=916///////
	////row_time=43.66us,frame_rate=25fps
	////////////////////////////////
	//////////////////////   SYS   //////////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x80,
	0xf2 , 0x0f,
	0xf6 , 0x00,
	0xfc , 0xc4,
	0xf7 , 0xb9,
	0xf8 , 0x03,
	0xf9 , 0x0e,
	0xfa , 0x00,
	/////////////////////////////////////////////////////
	////////////////   ANALOG & CISCTL   ////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x00,
	0x03 , 0x00,//exp  line unit
	0x04 , 0xfa,
	0x05 , 0x01,//hb vLen:800 fps:25 --> hlen:1950  0x79e
	0x06 , 0x78,
	0x07 , 0x00,//vb  vlen:800 0x320
	0x08 , 0xac,
	0x0d , 0x02,//bit[1:0] window height[9:8]
	0x0e , 0xd8,
	0x0f , 0x05,//bit[2:0] window width[10:8]
	0x10 , 0x08,
	0x11 , 0x00,
	0x12 , 0x18,
	0x16 , 0xc0,
	//0x17 , 0x14,
	0x17 , 0x17,
	0x19 , 0x06,
	0x1b , 0x4f,
	0x1c , 0x11,
	0x1d , 0x10,//exp<1frame 图像闪烁
	0x1e , 0xf8,//fc 左侧发紫
	0x1f , 0x38,
	0x20 , 0x81,
	0x21 , 0x1f,//6f//2f
	0x22 , 0xc0,//c2 左侧发紫
	0x23 , 0x82,//f2 左侧发紫
	0x24 , 0x2f,
	0x25 , 0xd4,
	0x26 , 0xa8,
	0x29 , 0x3f,//54//3f
	0x2a , 0x00,
	0x2b , 0x00,//00--powernoise  03---强光拖尾
	0x2c , 0xe0,//左右range不一致
	0x2d , 0x0a,
	0x2e , 0x00,
	0x2f , 0x16,//1f--横闪线
	0x30 , 0x00,
	0x31 , 0x01,
	0x32 , 0x02,
	0x33 , 0x03,
	0x34 , 0x04,
	0x35 , 0x05,
	0x36 , 0x06,
	0x37 , 0x07,
	0x38 , 0x0f,
	0x39 , 0x17,
	0x3a , 0x1f,
	0x3f , 0x18,//关掉Vclamp 电压
	/////////////////////////////////////////////////////
	//////////////////////   ISP   //////////////////////
	///////////////////////////////////////////////////// 
	0xfe , 0x00,
	0x8a , 0x00,
	0x8c , 0x02,
	0x8e , 0x02,
	0x8f , 0x15,
	0x90 , 0x01,
	0x94 , 0x02,
	0x95 , 0x02,
	0x96 , 0xd0,
	0x97 , 0x05,
	0x98 , 0x00,
	/////////////////////////////////////////////////////
	//////////////////////	 MIPI	/////////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x03,
	0x01 , 0x00,
	0x02 , 0x00,
	0x03 , 0x00,
	0x06 , 0x00,
	0x10 , 0x00,
	0x15 , 0x00,
	/////////////////////////////////////////////////////
	//////////////////////	 BLK	/////////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x00,
	0x18 , 0x02,
	0x1a , 0x11,
	0x40 , 0x2b,//2b   bit1 dark_current en,   bit0 offset en
	0x5e , 0x00,//offset ratio
	0x66 , 0x80,//dark current ratio
	/////////////////////////////////////////////////////
	////////////////////// Dark SUN /////////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x00,
	0xcc , 0x25,//bit5:dark sum en
	0xce , 0xf3,

	///////////////////////////////////////////////////
	///////////R G B 通道的敏感性/////////////////////
	//////////////////////////////////////////////////
	//0xfe , 0x00,
	//0xa7 , 0x85,
	//0xa8 , 0x85,


	/////////////////////////////////////////////////////
	//////////////////////	 Gain	/////////////////////
	/////////////////////////////////////////////////////
	0xfe , 0x00,
	0xb0 , 0x50,//global gain
	0xb1 , 0x01,//bit[3:0] auto pre_gain[9:6]
	0xb2 , 0x00,//bit[7:2] auto pre_gain[5:0]
	0xb3 , 0x40,//awb_r_gian
	0xb4 , 0x40,//awb_g_gain
	0xb5 , 0x40,//awb_b_gain
	0xb6 , 0x00,//bit[3:0] col code
	/////////////////////////////////////////////////////
	//////////////////////   pad enable   ///////////////
	/////////////////////////////////////////////////////
	0xf2 , 0x0f,
	0xfe , 0x00,
	
	SENSOR_TAB_END
};



SENSOR_LSC_TAB_SECTION const u16 gc1064_lsc_tab[572]={
	813,512,415,360,330,320,321,342,384,461,679,
	695,465,387,336,309,301,303,322,360,427,593,
	607,433,361,316,293,283,288,304,340,400,527,
	549,408,342,301,279,272,277,290,324,383,486,
	506,388,322,289,271,264,267,283,313,370,463,
	483,372,313,280,265,257,263,277,306,361,451,
	471,367,310,278,261,252,257,272,301,355,444,
	471,366,311,278,261,252,257,272,301,353,442,
	479,368,312,280,263,256,260,275,303,355,447,
	496,375,317,286,268,262,265,280,309,361,458,
	529,385,325,292,274,269,272,287,317,373,484,
	580,402,337,303,283,278,280,297,328,390,529,
	590,404,340,305,286,278,283,299,330,393,534,

	813,512,415,360,330,320,321,342,384,461,679,
	695,465,387,336,309,301,303,322,360,427,593,
	607,433,361,316,293,283,288,304,340,400,527,
	549,408,342,301,279,272,277,290,324,383,486,
	506,388,322,289,271,264,267,283,313,370,463,
	483,372,313,280,265,257,263,277,306,361,451,
	471,367,310,278,261,252,257,272,301,355,444,
	471,366,311,278,261,252,257,272,301,353,442,
	479,368,312,280,263,256,260,275,303,355,447,
	496,375,317,286,268,262,265,280,309,361,458,
	529,385,325,292,274,269,272,287,317,373,484,
	580,402,337,303,283,278,280,297,328,390,529,
	590,404,340,305,286,278,283,299,330,393,534,

	813,512,415,360,330,320,321,342,384,461,679,
	695,465,387,336,309,301,303,322,360,427,593,
	607,433,361,316,293,283,288,304,340,400,527,
	549,408,342,301,279,272,277,290,324,383,486,
	506,388,322,289,271,264,267,283,313,370,463,
	483,372,313,280,265,257,263,277,306,361,451,
	471,367,310,278,261,252,257,272,301,355,444,
	471,366,311,278,261,252,257,272,301,353,442,
	479,368,312,280,263,256,260,275,303,355,447,
	496,375,317,286,268,262,265,280,309,361,458,
	529,385,325,292,274,269,272,287,317,373,484,
	580,402,337,303,283,278,280,297,328,390,529,
	590,404,340,305,286,278,283,299,330,393,534,

	813,512,415,360,330,320,321,342,384,461,679,
	695,465,387,336,309,301,303,322,360,427,593,
	607,433,361,316,293,283,288,304,340,400,527,
	549,408,342,301,279,272,277,290,324,383,486,
	506,388,322,289,271,264,267,283,313,370,463,
	483,372,313,280,265,257,263,277,306,361,451,
	471,367,310,278,261,252,257,272,301,355,444,
	471,366,311,278,261,252,257,272,301,353,442,
	479,368,312,280,263,256,260,275,303,355,447,
	496,375,317,286,268,262,265,280,309,361,458,
	529,385,325,292,274,269,272,287,317,373,484,
	580,402,337,303,283,278,280,297,328,390,529,
	590,404,340,305,286,278,283,299,330,393,534

};

/*static void GC1064_hvblank(u32 h,u32 v)
{
	u32 i,hb,vb;
	hb = h-1284-116;
	vb = v - 724 - 12-1;
	const u8 t[][3] = {
		{0x05,(hb >> 8)<<4},
		{0x04,hb & 0xff},
		{0x03,vb >> 8},
		{0x02,vb & 0xff},
		{0x01,0x0},
		{0x00,0x1},
	};
	for(i=0;i<4;i++)
		sensor_iic_write((u8 *)&t[i][0]); 
	
}*/


static void GC1064_rotate(u32 r)
{
	
	u8 flip = r&0x1;
	u8 mirror = r&0x1;
	r = (mirror<<1)|flip;
	u8 i,iicbuf[][2] = {
		{0xfe,0x00},
		{0x17,0xc0^r},
		//{0x0c,0x00+flip}, //1
		//{0x0a,0x04+mirror}, 	
		{0xfe,0x01},
		{0x92,0x02+flip}, //1
		{0x94,0x03+mirror}, //3
		}; //init table value
		
	deg_Printf("flip:%x\n",sensor_iic_read((u8 *)&iicbuf[3][0]));  //00
	deg_Printf("mirror:%x\n",sensor_iic_read((u8 *)&iicbuf[4][0]));  //00
	for(i=0;i<sizeof(iicbuf)/2;i++)
		sensor_iic_write((u8 *)&iicbuf[i][0]);
}


static void GC1064_hvblank(u32 h,u32 v)
{
	u32 i,hb,vb;
//	hb = (h - 694) / 2;
	vb = v - 724 - 16;
	hb = (h - 668)/2 ;
//	vb = v - 16;
	//debg("-------------hv:%d VB:%d\n",hb,vb);
	//hb = 0x20a;
	//vb = 0xa0;
	const u8 t[][2] = {
		{0x05,(hb >> 8)&0xf},
		{0x06,hb & 0xff},
		{0x07,(vb >> 8)&0x1f},
		{0x08,vb & 0xff},
	};
	for(i=0;i<4;i++)
		sensor_iic_write((u8 *)&t[i][0]);
}
static void sensor_GC1064_exp_wr(u32 EV)
{
	u8 iicbuf[6] = {0xfe,0x00,0x03,0x00,0x04,0x00};
	iicbuf[3] = ((EV >> 8) & 0x1f);
	iicbuf[5] = (EV & 0xff);
	sensor_iic_write(iicbuf);
	sensor_iic_write(iicbuf+2);
	sensor_iic_write(iicbuf+4);	
}
static void sensor_GC1064_gain_wr(u32 EV)
{ 
	u32 i; 
	u8 iicbuf[8] = {0xfe,0x00,0xb6,0x00,0xb1,0x01,0xb2,0x00};
	u32 analog_gain[11] = {256,422,479,788,896,1490,1715,2739,4045,5478,7884} ;
	if(EV < 256){
		iicbuf[3] = 0x00;//anolog_gian 0表示1倍,最大0x0a
		iicbuf[5] = 0x01;//[3:0]:digital_gain 1表示1倍
//		iicbuf[7] = EV>>2;//[4:0]:digital_gain 小数点 1/64
//		iicbuf[7] = 0;//[4:0]:digital_gain 小数点 1/64
	}
	if(EV >= 256 && EV < 7884){
		for(i = 0;i <= 9;i ++){
			if(EV >= analog_gain[i] && EV < analog_gain[i+1]){
				iicbuf[3] = i;
				iicbuf[5] = 0x01;
				iicbuf[7] = (((EV * 100)/analog_gain[i])%100)*64/100;
			//	debg("***%d\n",iicbuf[7]);
				//iicbuf[7] = ((((EV-analog_gain[i])<<6)/(analog_gain[i+1]-analog_gain[i]))<<2)&0x3f;
//				iicbuf[7] = 0;
				break;
			}
		}
	}
	
	if(EV >= 7884){
		iicbuf[3] = 0x0a;
		iicbuf[5] = (EV/7884)&0xf;
		iicbuf[7] = ((((EV-iicbuf[5]*7884)<<6)/7884)<<2)&0xfc;
		
	}
	sensor_iic_write(iicbuf);
	sensor_iic_write(iicbuf+2);
	sensor_iic_write(iicbuf+4);//digital gain integer
	sensor_iic_write(iicbuf+6);//digital gain dot
}
/*
static void sensor_GC1064_gain_wr(u32 EV)
{
	u32 i;
	u8 iicbuf[6] = {0xfe,0x01,0xb6,0x00,0xb2,0x00};
	//u8 iicbuf[4] = {0xb6,0x00,0xb2,0x00};
	u32 rough_gain[10] = {256,363,511,729,1031,1477,2063,2951,4216,5964};
	//u32 rough_gain[9] = {256,432,511,862,1080,1814,2160,3628,4320};
//	sensor_iic_enable();
//	sensor_iic_info();		
	for(i=0;i<9;i++){
		if((EV >= rough_gain[i])&&(EV < rough_gain[i+1])){
			iicbuf[3] = i;
			iicbuf[5] = ((EV*256) / rough_gain[i]-256);
			//iicbuf[5] = ((EV*(1+iicbuf[3])*128 / rough_gain[i]-iicbuf[3]*256)/4)<<2;
			sensor_iic_write(iicbuf);
			sensor_iic_write(iicbuf+2);	
			sensor_iic_write(iicbuf+4);	
			break;
		}
	}
// sensor_iic_disable();		
}
*/
static void sensor_GC1064_exp_gain_wr(u32 exp,u32 gain){
	sensor_GC1064_exp_wr(exp);
	sensor_GC1064_gain_wr(gain);	
}

SENSOR_OP_SECTION const Sensor_Adpt_T  gc1064_adpt = 
{
	.typ 				= CSI_TYPE_RAW10| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 1,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = 1280, 			//sensor input width
	.senPixelh          = 720,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 1280,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 720,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 1280,				//csi input width
	.pixelh				= 720,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 0,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_RGGB,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB
	
	.sensorCore			= SYS_VOL_V1_8,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V3_1,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56


	
	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},

	.hvb_adapt = {
		.pclk			= 48000000,			//csi pclk input
		.v_len			= 800,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 20,				//sensor fps set
#else
		.fps			= 25,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_EN_  <<_BLC_POS_ | _ISP_EN_  <<_LSC_POS_  | _ISP_AUTO_<<_DDC_POS_   | _ISP_AUTO_<<_AWB_POS_  \
					|_ISP_EN_  <<_CCM_POS_ | _ISP_AUTO_<<_AE_POS_   | _ISP_AUTO_<<_DGAIN_POS_ | _ISP_AUTO_<<_YGAMA_POS_ \
					| _ISP_AUTO_<<_RGB_GAMA_POS_ | _ISP_AUTO_<<_CH_POS_\
					|_ISP_AUTO_<<_VDE_POS_ | _ISP_AUTO_<<_EE_POS_   | _ISP_AUTO_<<_CFD_POS_    |_ISP_AUTO_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= 0,					//BLC red adjust //signed 10bit
		.blkl_gr	= 0,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= 0,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= 0,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 92,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 143,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 411, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xd0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			173,172,171,169,164,162,159,153,147,142,137,131,128,120,117,113,112,112,107,101, 96, 91, 85, 80, 74, 68, 63, 57, 52, 46, 41, 35, //bgain_out_high
			173,171,168,160,156,152,148,142,136,131,126,121,116,109,105,103,102, 99, 93, 88, 82, 76, 73, 69, 64, 59, 56, 51, 47, 43, 39, 35, //bgain_in_high
			172,156,146,145,140,135,128,124,118,114,107, 98, 93, 89, 87, 87, 79, 70, 66, 67, 65, 64, 61, 53, 51, 49, 47, 45, 40, 38, 36, 35, //bgain_in_low  
			171,145,132,131,127,122,117,113,106,100, 88, 84, 81, 78, 77, 69, 61, 58, 57, 55, 55, 52, 48, 47, 45, 44, 42, 41, 39, 38, 36, 35 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000,  
			0x00,	0x00,	0x100  
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {55,60,65,70,85,85,85,85}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 11,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*5,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 3,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 6,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 160,	//
			.ae_win_x1			= 320,
			.ae_win_x2			= 960,
			.ae_win_x3			= 1120,
			.ae_win_y0			= 200,
			.ae_win_y1			= 400,
			.ae_win_y2			= 640,
			.ae_win_y3			= 680,
			.weight_0_7			= 0x11111112,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x11114111,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x88811888,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},		
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {1,4,7,7,7,7,7,7},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 16,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 16,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {0,0,0,2,0,0},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0,2,0,2,0,0},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0,0,0,0,0,0},//[0]~[5]:r,g,b,y,c,m
		.sat		= {16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {12,12,12,14,14,14,15,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 8,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {64,64,64,64,64,64,64,64,64}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {12,12,13,14,14,14,15,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 8,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {5,5,4,4,3,3,3,3},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {8,6,4,2,2,2,2,2}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x4,0x4,0x5,0x5,0x5,0x5,0x5,0x5}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {10,12,12,12,13,14,15,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 8,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (1280/4)*1,	
		.win_h_end		= (1280/4)*3,
		.win_v_start	= (720/4)*1,
		.win_v_end		= (720/4)*3,
	}, 

	.p_fun_adapt = {
		.fp_rotate		= GC1064_rotate,
		.fp_hvblank		= GC1064_hvblank,
		.fp_exp_gain_wr	= sensor_GC1064_exp_gain_wr
	},

};
SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T gc1064_init =
{
	.sensor_struct_addr   	= (u32 *)&gc1064_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)GC1064InitTable,     
	.sensor_init_tab_size 	= sizeof(GC1064InitTable),
	.lsc_tab_adr 			= (u32 *)gc1064_lsc_tab,     
	.lsc_tab_size 			= sizeof(gc1064_lsc_tab), 
	.sensor_name	  		= "GC1064_720p",
	.w_cmd            		= 0x78,                   
	.r_cmd            		= 0x79,                   
	.addr_num         		= 0x01,                   
	.data_num         		= 0x01,   
	.id               		= 0x24, 
	.id_reg           		= 0xf1,  
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_RESET_CTRL_EN = 1 有效
	.reset_en				= 0, //目前不使用
	.reset_valid			= 0, //0:低电平reset， 1: 高电平reset
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_PWDN_CTRL_EN = 1 有效
	.pwdn_en				= 0, //根据需要来配置
	.pwdn_valid				= 1, //pwdn_en = 1时有效，0:低电平 power down， 1: 高电平power down； pwdn_en = 1时 默认高电平POWER DOWN
};

#endif