/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../app_common/inc/app_api.h"

ALIGNED(4) static msgDealFunc taskMsgDeal[EVENT_MAX];
ALIGNED(4) static msgDealFunc sysMsgDeal[EVENT_MAX];


/*******************************************************************************
* Function Name  : taskmsgFuncRegister
* Description    : taskmsgFuncRegister
* Input          : msgDealInfor* info
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskmsgFuncRegister(msgDealInfor* infor)
{
	u32 i;
	memset(taskMsgDeal,0,sizeof(taskMsgDeal));
	i=0;
	for(;;)
	{
		if(taskComMsgDeal[i].msgId >= EVENT_MAX || taskComMsgDeal[i].func==NULL)
			break;
		taskMsgDeal[taskComMsgDeal[i].msgId] = taskComMsgDeal[i].func;
		i++;
	}
	if(infor==NULL)
		return;
	i=0;
	for(;;)
	{
		if(infor[i].msgId >= EVENT_MAX || infor[i].func == NULL)
			break;
		taskMsgDeal[infor[i].msgId] = infor[i].func;
		i++;
	}
}
/*******************************************************************************
* Function Name  : sysMsgFuncRegister
* Description    : sysMsgFuncRegister
* Input          : msgDealInfor* info
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sysMsgFuncRegister(msgDealInfor* infor)
{
	u32 i;
	memset(sysMsgDeal,0,sizeof(sysMsgDeal));
	if(infor==NULL)
		return;
	i=0;
	for(;;)
	{
		if(infor[i].msgId >= EVENT_MAX|| infor[i].func == NULL)
			break;
		sysMsgDeal[infor[i].msgId] = infor[i].func;
		i++;
	}
}
/*******************************************************************************
* Function Name  : app_msgDeal
* Description    : app_msgDeal
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDeal(void)
{
	MSG_T* msgid;
	u8 err;
	msgid = XMsgQPend(SysCtrl.sysQ,&err);  // check system event
	if(err == X_ERR_NONE)
	{
		u32 msgType = getType(msgid);
		u32 msgData = getValue(msgid);
		//debg("msgType=%d\n",msgType);
		if(msgType < EVENT_MAX)
		{
			if(msgType != KEY_EVENT_POWEROFF && msgType>=KEY_EVENT_START && msgType<KEY_EVENT_END && msgData==KEY_PRESSED)
			{
				if( menuWinIsOpen() ||(app_taskCurId() != TASK_PLAY_AUDIO && app_taskCurId() != TASK_PLAY_VIDEO ))
				{
				#if 0//FUN_MP3_PLAY_EN
					if(app_taskCurId() == TASK_PLAY_MP3)
					{
						if(mp3_dec_sta() < MP3_DEC_START)
						{
							task_com_keysound_play();	
							while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY){XOSTimeDly(1);}		
						}
					}else 
				#endif
					if(!(msgType == KEY_EVENT_OK && app_taskCurId() == TASK_RECORD_PHOTO))
						task_com_keysound_play();
				}
			}
			if(taskMsgDeal[msgType])
				taskMsgDeal[msgType](uiWinGetCurrent(),1,&msgData);
			if(sysMsgDeal[msgType])
				sysMsgDeal[msgType](uiWinGetCurrent(),1,&msgData);
		}
	}
}
/*******************************************************************************
* Function Name  : app_msgDealByType
* Description    : app_msgDealByType
* Input          : u32 msgType,void* handle,u32 parameNum,u32* parame
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDealByType(u32 msgType,void* handle,u32 parameNum,u32* parame)
{
	if(taskMsgDeal[msgType])
		taskMsgDeal[msgType](handle,parameNum,parame);
	if(sysMsgDeal[msgType])
		sysMsgDeal[msgType](handle,parameNum,parame);
}
/*******************************************************************************
* Function Name  : app_msgDealByInfor
* Description    : app_msgDealByInfor
* Input          : msgDealInfor* infor,u32 msgType,void* handle,u32 parameNum,u32* parame
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDealByInfor(msgDealInfor* infor,u32 msgType,void* handle,u32 parameNum,u32* parame)
{
	u32 i;
	if(infor == NULL)
		return;
	i=0;
	for(;;)
	{
		if(infor[i].msgId >= EVENT_MAX || infor[i].func == NULL)
			break;
		if(infor[i].msgId == msgType)
		{
			if(infor[i].func)
				infor[i].func(handle,parameNum,parame);
			break;
		}
		i++;
	}
}







