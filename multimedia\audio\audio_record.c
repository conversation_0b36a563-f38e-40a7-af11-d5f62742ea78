/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"

#if MEDIA_AUDIO_ENCODE_EN > 0



typedef struct Audio_RCtrl_S
{
	AUIDO_REC_ARG_T arg;
	XWork_T 		*audioSync;
	INT32U 			audioRecTcnt;
	INT16U  		stat; 	
	INT16U  		cmd;
#if AUDIO_DBG_REC_EN		
    INT32U  		dbg_sampleCnt;
#endif
}Audio_RCtrl_T;

ALIGNED(4) static Audio_RCtrl_T audioRCtrl;

static void audioRecordIsr(void)
{
	if(audioRCtrl.stat == MEDIA_STAT_START)
	{
		audioRCtrl.audioRecTcnt++;
	}
}
/*******************************************************************************
* Function Name  : audioRecordInit
* Description    : initial audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordInit(AUIDO_REC_ARG_T *arg)
{
    if(arg == NULL)
		return STATUS_FAIL;
	memset((void*)&audioRCtrl,0, sizeof(Audio_RCtrl_T));
	
	memcpy(&audioRCtrl.arg, arg, sizeof(AUIDO_REC_ARG_T));
	
	
	audioRCtrl.arg.wav_arg.cachebuf = (INT8U *)hal_sysMemMalloc(AUDIO_CACHE_SIZE);//shareMemMalloc(AUDIO_RING_SIZE);//aviIdx1Cache;
	if(audioRCtrl.arg.wav_arg.cachebuf == NULL)
	{
		AUDIOREC_DBG("[AUDIO REC]: mem init fail\n");
		return STATUS_FAIL;
	}
	audioRCtrl.arg.wav_arg.cachelen = AUDIO_CACHE_SIZE;
	
	audioRCtrl.audioSync = XWorkCreate(1000*X_TICK,audioRecordIsr);  //1sec isr
	if(audioRCtrl.audioSync == NULL)
	{
		AUDIOREC_DBG("[AUDIO REC]:time sync init fail\n");
		return STATUS_FAIL;
	}
		
	audioRCtrl.stat = MEDIA_STAT_STOP;
	
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioRecordUninit
* Description    : uninitial audio record 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void audioRecordUninit(void)
{
	if(audioRCtrl.arg.wav_arg.cachebuf)
		hal_sysMemFree(audioRCtrl.arg.wav_arg.cachebuf);
	XWorkDestory(audioRCtrl.audioSync);

	audioRCtrl.arg.wav_arg.cachebuf = NULL;
	audioRCtrl.arg.wav_arg.cachelen = 0;
	audioRCtrl.audioSync = NULL;
}
/*******************************************************************************
* Function Name  : audioRecordStart
* Description    : start audio record
* Input          : AUDIO_RECORD_ARG_T *arg : audio record argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordStart(void)
{
	int ret;
    if(audioRCtrl.arg.callback == NULL)
    {	
		return STATUS_FAIL;
    }

    sd_api_lock();

	if(audioRCtrl.arg.looprec)
	{
	    if(audioRCtrl.arg.callback(CMD_COM_CHECK,audioRCtrl.arg.looptime*50)<0) // check space, not enough will del file
		{
			sd_api_unlock();
			return STATUS_FAIL;   // start fail		
		}
	}
	else
	{
	    if(audioRCtrl.arg.callback(CMD_COM_CHECK,0)<0) // check space , not del file
		{
			sd_api_unlock();
			return STATUS_FAIL;   // start fail		
		}
	}
	ret = audioRCtrl.arg.callback(CMD_AUDIO_RECORD_START,(INT32U)&audioRCtrl.arg.wav_arg.fd);
	//deg_Printf("RECORD_START:%d\n", ret);
	if(ret<0)
	{
		sd_api_unlock();
		return STATUS_FAIL;   // start fail		
	}
	ret = api_multimedia_init(MEDIA_ENCODE,audioRCtrl.arg.wav_arg.type);
	if(ret < 0)
	{
		AUDIOREC_DBG("[AUDIO REC]:api init fail.\n");
		return audioRecordStop(1);
	}
	audioRCtrl.arg.wav_arg.media_ch = ret;	
	ret = api_multimedia_start(audioRCtrl.arg.wav_arg.media_ch,(void*)&audioRCtrl.arg.wav_arg);
	if(ret<0)
	{
		AUDIOREC_DBG("[AUDIO REC]:start fail.\n");
		return audioRecordStop(1);
	}
	
	if(hal_auadcStart(PCM_REC_TYPE_WAV,audioRCtrl.arg.wav_arg.samplerate,audioRCtrl.arg.wav_arg.volume) == false)
	{
		return audioRecordStop(1);	
	}
#if AUDIO_DBG_REC_EN
    audioRCtrl.dbg_sampleCnt = 0;
#endif
	audioRCtrl.audioRecTcnt = 0;
	audioRCtrl.stat = MEDIA_STAT_START;
    AUDIOREC_DBG("[AUDIO REC]:start\n");
	return STATUS_OK;
}

/*******************************************************************************
* Function Name  : audioRecordStop
* Description    : stop audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordStop(INT8U error)
{
    hx330x_auadcEnable(0);
	api_multimedia_end(audioRCtrl.arg.wav_arg.media_ch);
	api_multimedia_uninit(audioRCtrl.arg.wav_arg.media_ch);
    if(audioRCtrl.arg.callback)
	{
		if(error)
			audioRCtrl.arg.callback(CMD_COM_ERROR,(INT32U)&audioRCtrl.arg.wav_arg.fd); // tell appliction,cmd start fail
		else
			audioRCtrl.arg.callback(CMD_AUDIO_RECORD_STOP,(INT32U)&audioRCtrl.arg.wav_arg.fd); 
	}
	
	sd_api_Stop();
	
    sd_api_unlock();
	
	audioRCtrl.arg.wav_arg.media_ch = -1;
	audioRCtrl.arg.wav_arg.fd		= -1;
	audioRCtrl.stat 				= MEDIA_STAT_STOP;
	audioRCtrl.audioRecTcnt			= 0;
	AUDIOREC_DBG("[AUDIO REC]: stop\n");
	if(error)
		return STATUS_FAIL;
	else 
		return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioRecordPuase
* Description    : pause audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordPuase(void)
{
	audioRCtrl.stat = MEDIA_STAT_PAUSE;
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioRecordResume
* Description    : resume audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordResume(void)
{
	audioRCtrl.stat = MEDIA_STAT_START;
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioRecordGetStatus
* Description    : get audio record 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioRecordGetStatus(void)
{
	return audioRCtrl.stat;
}
/*******************************************************************************
* Function Name  : audioRecordSetStatus
* Description    : Set audio record 
* Input          : stat
* Output         : none
* Return         : none
                      
*******************************************************************************/
void audioRecordSetStatus(u16 stat)
{
	audioRCtrl.stat = stat;
}

/*******************************************************************************
* Function Name  : audioRecordGetTime
* Description    : get audio record time
* Input          : none
* Output         : none
* Return         : int : s
                      
*******************************************************************************/
int audioRecordGetTime(void)
{
	return audioRCtrl.audioRecTcnt;
}
/*******************************************************************************
* Function Name  : audioRecordService
* Description    : audio record service
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioRecordService(void)
{
	u32 len;
	void *buff;
	INT32S res;

	if(audioRCtrl.stat == MEDIA_STAT_PAUSE)
	{
	//---release useless raw data	
		buff = hal_auadcBufferGet((u32 *)&len,NULL,NULL);
		if(buff)
			hal_auadcBufferRelease();
	}
	
	if(audioRCtrl.stat != MEDIA_STAT_START)
		return audioRCtrl.stat;
	if(audioRCtrl.arg.callback(CMD_COM_CHECK,0) < 0)
	{
		if(audioRCtrl.arg.looprec)
		{
			audioRecordStop(0);
			audioRecordStart();
		}else
		{
			audioRecordStop(0);
		}
		return audioRCtrl.stat;
	}
	if(audioRCtrl.audioRecTcnt > audioRCtrl.arg.looptime)
	{
		audioRecordStop(0);
		audioRecordStart();
		return audioRCtrl.stat;
	}
    buff = hal_auadcBufferGet(&len,NULL,NULL);
	if(buff)
	{
		res = api_multimedia_encodeframe(audioRCtrl.arg.wav_arg.media_ch,(void*)buff, len, 0);
		hal_auadcBufferRelease();
		if((res<0))// write error
		{
			goto AUDIO_RECORD_ERROR;
		}
	}



	
#if AUDIO_DBG_REC_EN	
	audioRCtrl.dbg_sampleCnt+=len;
	if(audioRCtrl.dbg_sampleCnt>=(audioRCtrl.arg.wav_arg.samplerate*audioRCtrl.arg.wav_arg.ch_out*2))
	{
		audioRCtrl.dbg_sampleCnt = 0;
		//AUDIOREC_DBG(".");
	}
#endif

	return audioRCtrl.stat;

AUDIO_RECORD_ERROR:
    audioRecordStop(1); // audio recorder can not handler this error.stop
	
	return audioRCtrl.stat;
}




#endif


