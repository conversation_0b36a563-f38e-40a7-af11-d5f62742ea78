/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_RGB_ILI9342C

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DAT(x)    LCD_CMD_RGB_DAT(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0xc8),DAT(0x1ff),DAT(0x193),DAT(0x142),
    <PERSON><PERSON>(0x11),
    <PERSON><PERSON>(100),
    <PERSON><PERSON>(0x36),DAT(0x1c8),
    CMD(0x3a),DAT(0x155),

    CMD(0xc0),DAT(0x114),DAT(0x118),
    CMD(0xc1),DAT(0x111),
    CMD(0xc5),DAT(0x1f4),

    CMD(0xb1),DAT(0x100),DAT(0x11b),
    CMD(0xb4),DAT(0x102),
    CMD(0xb5),DAT(0x115),DAT(0x115),DAT(0x115),DAT(0x115),
    CMD(0xb0),DAT(0x1e0),

    CMD(0xf6),DAT(0x101),DAT(0x100),DAT(0x107),

    CMD(0xe0),DAT(0x100),DAT(0x10a),DAT(0x111),
              DAT(0x108),DAT(0x116),DAT(0x10a),DAT(0x13c),
              DAT(0x19b),DAT(0x14a),DAT(0x109),DAT(0x10E),
              DAT(0x10a),DAT(0x11c),DAT(0x11d),DAT(0x10f),
    CMD(0xE1),DAT(0x100),DAT(0x123),DAT(0x125),DAT(0x104),
              DAT(0x110),DAT(0x107),DAT(0x139),DAT(0x146),
              DAT(0x14a),DAT(0x103),DAT(0x10c),DAT(0x10A),
              DAT(0x131),DAT(0x136),DAT(0x10f),
    CMD(0x11),
    DLY(50),
    CMD(0x29),
LCD_INIT_TAB_END()


//无白色边框的屏
LCD_DESC_BEGIN()
    .name 			= "RGB_ILI9342C",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_0,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 3,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 21,
    .vfp 			= 2,

    .hlw 			= 3,
    .hbp 			= 6,
    .hfp 			= 11,

    LCD_SPI_DEFAULT(9),

    .data_mode = LCD_DATA_MODE0_8BIT_RGB888,

    .screen_w 		= 320,
    .screen_h 		= 240,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif





























