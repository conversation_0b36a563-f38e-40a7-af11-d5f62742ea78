/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#if FUN_MP3_PLAY_EN
#include "taskPlayMp3MainWin.c"
#define  PLAYMP3MAIN_WAIT_KEYSOUND_END           while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY){XOSTimeDly(1);}
/*******************************************************************************
* Function Name  : getPlayAudioResInfor
* Description    : audioKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getPlayMp3MainResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= INVALID_RES_ID;
	if(str)
		*str	= (u32) filelist_GetFileNameByIndex(SysCtrl.mp3_list, (int)item);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainOpenWin
* Description    : playMp3MainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	taskPlayMp3RootDirOpen();
	uiItemManageSetHeightAvgGap(winItem(handle,PLAYMP3MAIN_SELECT_ID),	Rh(33));
	uiItemManageCreateItem(		winItem(handle,PLAYMP3MAIN_SELECT_ID),	uiItemCreateMenuOption,	getPlayMp3MainResInfor, task_playMp3_op.openListTotal);
	uiItemManageSetCharInfor(	winItem(handle,PLAYMP3MAIN_SELECT_ID),	DEFAULT_FONT,	ALIGNMENT_LEFT,		R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,PLAYMP3MAIN_SELECT_ID),	R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,PLAYMP3MAIN_SELECT_ID),	R_ID_PALETTE_Gray);
	uiItemManageSetCurItem(		winItem(handle,PLAYMP3MAIN_SELECT_ID),	SysCtrl.file_index);
	if(task_playMp3_op.openListTotal == 0)
		uiOpenWindow(&noFileWindow,0, 1, "no mp3 file");
	else
	{
		playMp3MainSDShow(handle);
		playMp3MainPlayTimeShow(handle);
		playMp3MainBaterryShow(handle);
		playMp3MainPlayModeShow(handle);
	}
			
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainCloseWin
* Description    : playMp3MainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainWinChildClose
* Description    : playMp3MainWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	taskPlayMp3RootDirOpen();
	if(task_playMp3_op.openListTotal == 0)
	{
		uiOpenWindow(&noFileWindow, 0, 1, "no mp3 file");
		return 0;
	}
	if(SysCtrl.file_index >= task_playMp3_op.openListTotal)
	{
		SysCtrl.file_index = 0;
		SysCtrl.play_total_time = 0;
	}
	uiItemManageUpdateRes(winItem(handle,PLAYMP3MAIN_SELECT_ID),task_playMp3_op.openListTotal,SysCtrl.file_index);
	playMp3MainSDShow(handle);
	playMp3MainPlayTimeShow(handle);
	playMp3MainBaterryShow(handle);
	playMp3MainPlayModeShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgOk
* Description    : playMp3MainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	u32 file_type;
	char *name;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYMP3MAIN_SELECT_ID));
		name = filelist_GetFileFullNameByIndex(SysCtrl.mp3_list,(int)item, (int *)&file_type);
		if(name != NULL)
		{
			if(SysCtrl.file_index != item)
			{
				mp3_api_stop();
				task_com_keysound_play();	
				PLAYMP3MAIN_WAIT_KEYSOUND_END;
				//SysCtrl.file_index = item;
			}
			if(file_type == FILELIST_TYPE_DIR)
			{
				//re scan filelist, ui show
				if(taskPlayMp3ChildDirOpen(item, name) < 0)
					return 0;
				else
				{
					uiItemManageUpdateRes(winItem(handle,PLAYMP3MAIN_SELECT_ID),(u32)task_playMp3_op.openListTotal,SysCtrl.file_index);
					if(mp3_dec_sta() >= MP3_DEC_START)
						task_playMp3_op.playfirstIndex = task_playMp3_op.openListTotal;	//当前音乐播放完继续播放打开的列表
					else
						task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
					//
					
				}		
			}else{
				task_playMp3_op.playfirstIndex 	= item;
				if(SysCtrl.file_index == item && mp3_dec_sta() >= MP3_DEC_START ) //当前正在播放的MP3文件，可能是PAUSE或者RESUME或者START
				{
					switch(mp3_dac_sta())
					{
						case MP3_DAC_START:
						case MP3_DAC_RESUME: 	mp3_dac_pause(); break;
						//case MP3_DAC_STOP:
						case MP3_DAC_PAUSE:		mp3_dac_resume(); break;
						default: break;
						
					}
				}else
				{
					taskPlayMp3MainStart(item);
				}
			}
		}
	}
			
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgUp
* Description    : playMp3MainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}
		uiItemManagePreItem(winItem(handle,PLAYMP3MAIN_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgDown
* Description    : playMp3MainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}
		uiItemManageNextItem(winItem(handle,PLAYMP3MAIN_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgMode
* Description    : playMp3MainKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		if(mp3_dac_sta() != MP3_DAC_START)
		{
			//task_com_keysound_play();	
			//PLAYMP3MAIN_WAIT_KEYSOUND_END;				
			app_taskChange();
		}else
		{
			uiOpenWindow(&playMp3SubWindow, 0, 0);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainKeyMsgMenu
* Description    : playMp3MainKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	char *name;
	int file_type;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYMP3MAIN_SELECT_ID));
		name = filelist_GetFileFullNameByIndex(SysCtrl.mp3_list,(int)item, (int *)&file_type);
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;	
		//}
		if(name)
		{
			if(file_type == FILELIST_TYPE_DIR)
			{
				uiOpenWindow(&playMp3DirOpsWindow, 0, 2, item, name);
			}else
			{
				uiOpenWindow(&playMp3FileOpsWindow, 0, 2, item,name);
			}
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgSD
* Description    : playMp3MainSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) 
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			mp3_api_stop();
		}
		uiOpenWindow(&noFileWindow, 0, 1, "no mp3 file");
	}
	else
	{
		taskPlayMp3RootDirOpen();
		if(task_playMp3_op.openListTotal == 0)
		{
			uiOpenWindow(&noFileWindow, 0, 1, "no mp3 file");
			return 0;
		}
		uiItemManageUpdateRes(winItem(handle,PLAYMP3MAIN_SELECT_ID),(u32)task_playMp3_op.openListTotal,SysCtrl.file_index);
		playMp3MainSDShow(handle);
		playMp3MainPlayTimeShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgUSB
* Description    : playMp3MainSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{	
	playMp3MainBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsgBattery
* Description    : playMp3MainSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		playMp3MainBaterryShow(handle);	
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3MainSysMsg1S
* Description    : playMp3MainSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if(uiWinIsVisible(winItem(handle,PLAYMP3MAIN_BATERRY_ID)))
			uiWinSetVisible(winItem(handle,PLAYMP3MAIN_BATERRY_ID),0);
		else
		{
			uiWinSetResid(winItem(handle,PLAYMP3MAIN_BATERRY_ID),R_ID_ICON_MTBATTERY5);
			uiWinSetVisible(winItem(handle,PLAYMP3MAIN_BATERRY_ID),1);
		}	
	}
	return 0;
}

/*******************************************************************************
* Function Name  : playAudioSysMsgTimeUpdate
* Description    : playAudioSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3MainSysMsgPlay(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 msg_event = UPDATE_NONE;
	if(parameNum == 1)
		msg_event = parame[0];
	if(msg_event & UPDATE_PLAYTIME)
	{
		playMp3MainPlayTimeShow(handle);
	} 
	if(msg_event & UPDATE_FILEITEM_SHOW)
	{
		uiItemManageSetCurItem(winItem(handle,PLAYMP3MAIN_SELECT_ID),	SysCtrl.file_index);
	}
	
	return 0;
}


ALIGNED(4) msgDealInfor playMp3MainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playMp3MainOpenWin},
	{SYS_CLOSE_WINDOW,		playMp3MainCloseWin},
	{SYS_CHILE_COLSE,		playMp3MainWinChildClose},
	{KEY_EVENT_OK,			playMp3MainKeyMsgOk},
	{KEY_EVENT_UP,			playMp3MainKeyMsgUp},
	{KEY_EVENT_DOWN,		playMp3MainKeyMsgDown},
	{KEY_EVENT_MENU,		playMp3MainKeyMsgMenu},
	{KEY_EVENT_MODE,		playMp3MainKeyMsgMode},
	{SYS_EVENT_SDC,			playMp3MainSysMsgSD},
	{SYS_EVENT_USBDEV,		playMp3MainSysMsgUSB},
	{SYS_EVENT_BAT,			playMp3MainSysMsgBattery},
	{SYS_EVENT_1S,			playMp3MainSysMsg1S},	
	{SYS_EVENT_PLAY,		playMp3MainSysMsgPlay},
	{EVENT_MAX,NULL},
};

WINDOW(playMp3MainWindow,playMp3MainMsgDeal,playMp3MainWin)

#endif
