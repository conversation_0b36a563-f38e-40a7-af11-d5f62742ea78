/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiItemMenuExProc
* Description    : uiItemMenuExProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemMenuExProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiWinObj *pWin;
	uiResInfo *infor;
	uiItemMenuExObj *pItemMenuEx;
	if(uiWidgetProc(msg))
		return;	
	hWin		= msg->curWin;
	pItemMenuEx	= (uiItemMenuExObj*)uiHandleToPtr(hWin);
	pWin		= &(pItemMenuEx->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("menuItemEx win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pItemMenuEx->select)
			{
				if(pItemMenuEx->selectColor != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuEx->selectColor);			
				if(pItemMenuEx->imageSelect.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenuEx->imageSelect);
			}else
			{
				if(pItemMenuEx->color != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuEx->color);				
				if(pItemMenuEx->image.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenuEx->image);				
			}
			return;
		case MSG_WIN_INVALID_RESID:
			uiWinSetResid(pItemMenuEx->hImage, 		INVALID_RES_ID);
			uiWinSetResid(pItemMenuEx->hStr,		INVALID_RES_ID);
			uiWinSetResid(pItemMenuEx->hImageSel,	INVALID_RES_ID);
			uiWinSetResid(pItemMenuEx->hStrSel,		INVALID_RES_ID);
			return;
		case MSG_WIN_CHANGE_RESID:
			if(RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenuEx->hImage,msg->para.v);
			else
				uiWinSetResid(pItemMenuEx->hStr,msg->para.v);
			return;
		case MSG_WIN_CHG_ITEM_SEL_RESID:
			if(RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenuEx->hImageSel,msg->para.v);
			else
				uiWinSetResid(pItemMenuEx->hStrSel,msg->para.v);
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx->imageSelect.id	   		= infor->image;
			pItemMenuEx->imageSelect.bgColor 	= infor->color;	
			pItemMenuEx->selectColor 		    = infor->color;	
			uiWinSendMsg(pItemMenuEx->hStr,msg);
			uiWinSendMsg(pItemMenuEx->hImage,msg);
			uiWinSendMsg(pItemMenuEx->hImageSel,msg);
			{
				uiResInfo tempInfor;
				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.strAlign = ALIGNMENT_RIGHT;
				uiWinSetSelectInfor(pItemMenuEx->hStrSel,&tempInfor);
			}
			
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx->image.id	   	= infor->image;
			pItemMenuEx->image.bgColor 	= infor->color;
			pItemMenuEx->color 		 	= infor->color;
			uiWinSendMsg(pItemMenuEx->hStr,msg);
			{
				uiResInfo tempInfor;
				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.strAlign = ALIGNMENT_RIGHT;
				uiWinSetUnselectInfor(pItemMenuEx->hStrSel,&tempInfor);

				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.fontColor = INVALID_COLOR;
				uiWinSetUnselectInfor(pItemMenuEx->hImage,&tempInfor);
				uiWinSetUnselectInfor(pItemMenuEx->hImageSel,&tempInfor);
			}
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			uiWinSendMsg(pItemMenuEx->hStr,msg);
			((uiStrInfo*)(msg->para.p))->strAlign = ALIGNMENT_RIGHT;
			uiWinSendMsg(pItemMenuEx->hStrSel,msg);
			return;
		case MSG_WIN_SELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx->imageSelect.id	   		= infor->image;
			pItemMenuEx->imageSelect.bgColor 	= infor->color;	
			pItemMenuEx->selectColor 		    = infor->color;
			return;
		case MSG_WIN_UNSELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuEx->image.id	   	= infor->image;
			pItemMenuEx->image.bgColor 	= infor->color;
			pItemMenuEx->color 		 	= infor->color;
			return;
		case MSG_WIN_UNSELECT:
			if(pItemMenuEx->select == 0)
				return;
			pItemMenuEx->select = 0;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuEx->hStr,msg);
			uiWinSendMsg(pItemMenuEx->hStrSel,msg);
			uiWinSendMsg(pItemMenuEx->hImage,msg);
			uiWinSendMsg(pItemMenuEx->hImageSel,msg);
			return;
		case MSG_WIN_SELECT:
			if(pItemMenuEx->select)
				return;
			pItemMenuEx->select = 1;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuEx->hStr,msg);
			uiWinSendMsg(pItemMenuEx->hStrSel,msg);
			uiWinSendMsg(pItemMenuEx->hImage,msg);
			uiWinSendMsg(pItemMenuEx->hImageSel,msg);
			return;
		case MSG_WIN_VISIBLE_SET:
			if(msg->para.v)
			{
				uiWinSetVisible(pItemMenuEx->hImage,1);
				uiWinSetVisible(pItemMenuEx->hStr,1);
				uiWinSetVisible(pItemMenuEx->hImageSel,1);
				uiWinSetVisible(pItemMenuEx->hStrSel,1);
			}
			else
			{
				uiWinSetVisible(pItemMenuEx->hImage,0);
				uiWinSetVisible(pItemMenuEx->hStr,0);
				uiWinSetVisible(pItemMenuEx->hImageSel,0);
				uiWinSetVisible(pItemMenuEx->hStrSel,0);
			}
			break;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}

/*******************************************************************************
* Function Name  : uiItemMenuExProc
* Description    : uiItemMenuExProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateMenuItemEx(s16 x0,s16 y0,u16 width,u16 height,u16 style)
{
	winHandle 		 hItemMenuEx;
	uiItemMenuExObj *pItemMenuEx;

	hItemMenuEx		= uiWinCreate(x0,y0,width,height,INVALID_HANDLE,uiItemMenuExProc,sizeof(uiItemMenuExObj),WIN_WIDGET|WIN_NOT_ZOOM);
	if(hItemMenuEx != INVALID_HANDLE)
	{
		pItemMenuEx = (uiItemMenuExObj*)uiHandleToPtr(hItemMenuEx);
		
		pItemMenuEx->image.id 				= INVALID_RES_ID;
		pItemMenuEx->image.iconAlign		= ALIGNMENT_CENTER;
		pItemMenuEx->image.bgColor			= INVALID_COLOR;
		pItemMenuEx->image.iconColor		= INVALID_COLOR;
		pItemMenuEx->image.visible			= 1;
		pItemMenuEx->imageSelect.id 		= INVALID_RES_ID;
		pItemMenuEx->imageSelect.iconAlign 	= ALIGNMENT_CENTER;
		pItemMenuEx->imageSelect.bgColor	= INVALID_COLOR;
		pItemMenuEx->imageSelect.iconColor	= INVALID_COLOR;
		pItemMenuEx->imageSelect.visible	= 1;
		pItemMenuEx->select					= 0;
		pItemMenuEx->color					= INVALID_COLOR;
		pItemMenuEx->selectColor			= INVALID_COLOR;	
		uiWidgetSetId(hItemMenuEx,INVALID_WIDGET_ID);
		uiWinSetbgColor(hItemMenuEx, INVALID_COLOR);
		pItemMenuEx->hImage		= uiImageIconCreateDirect( x0,        		y0, height,           height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuEx->hStr		= uiStringIconCreateDirect(x0+height, 		y0 - 4, width/2 - height, height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuEx->hStrSel	= uiStringIconCreateDirect(x0+width/2,		y0 - 4, width/2 - height, height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuEx->hImageSel	= uiImageIconCreateDirect(x0+width-height, 	y0, height,           height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		
	}	
	return hItemMenuEx;
}