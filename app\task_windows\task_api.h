/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  __TASK_API_H_
#define  __TASK_API_H_


#define  TIPS_KEEP_SHOWING     		0xffffffff

#define  TIPS_SPI_FULL    			R_ID_STR_SDC_FULL//"SPI FULL"
#define  TIPS_SD_NOT_INSERT    		R_ID_STR_SDC_NULL
#define  TIPS_SD_FULL          		R_ID_STR_SDC_FULL
#define  TIPS_SD_ERROR         		R_ID_STR_SDC_ERROR
#define  TIPS_USENSOR_POWER_LOW  	R_ID_STR_PWR_BACKLOW
#define  TIPS_POWER_LOW   			R_ID_STR_PWR_LOW
#define  TIPS_NO_POWER   			R_ID_STR_PWR_NO
#define  TIPS_NO_DC_POWEROFF		R_ID_STR_DC_OUT
typedef enum
{
	TIPS_TYPE_SD,
	TIPS_TYPE_SPI,
	TIPS_TYPE_POWER,
	TIPS_COM_WAITING,
	TIPS_COM_SUCCESS,
	TIPS_COM_FAIL,
	TIPS_NO_FILE,
	TIPS_SET_LOCKED,
	TIPS_FMT_SUCCESS,
	TIPS_FMT_FAIL,
	TIPS_ERROR,
	TIPS_VERSION,

}tipsType;
typedef enum
{
	TASK_POWER_ON = 0,
	TASK_POWER_OFF,

	TASK_RECORD_VIDEO,
	TASK_RECORD_AUDIO,
	TASK_RECORD_PHOTO,

	TASK_PLAY_AUDIO,
	TASK_PLAY_VIDEO,
	TASK_PLAY_MP3,
	TASK_USB_DEVICE,
	TASK_USB_UPDATE,
	TASK_SD_UPDATE,

	TASK_NES_GAME,
#if  FUN_BATTERY_CHARGE_SHOW
	TASK_BAT_CHARGE ,
#endif	

	TASK_MAX
}taskID;

typedef struct sysTask_S
{
	char* name;
	u32   arg;
	void  (*taskOpen)(u32 arg);
	void  (*taskClose)(u32 arg);
	void  (*taskService)(u32 arg);
}sysTask_T;
typedef struct SYS_TASK_OP_S{
	sysTask_T* 	taskArray[TASK_MAX];
	sysTask_T* 	curTask;
	u32   		curTaskId;
	u32   		nextTaskId;
	u32   		preTaskId;
}SYS_TASK_OP_T;


#include "msg_api.h"
#include "windows_api.h"

#include "menu_windows/inc/menu_api.h"
#include "task_common/inc/task_common.h"
#include "task_play_audio/inc/taskPlayAudio.h"
#include "task_play_mp3/inc/taskPlayMp3.h"
#include "task_play_video/inc/taskPlayVideo.h"
#include "task_record_audio/inc/taskRecordAudio.h"
#include "task_record_photo/inc/taskRecordPhoto.h"
#include "task_record_video/inc/taskRecordVideo.h"
#include "task_poweroff/inc/taskPoweroff.h"
#include "task_usb_device/inc/taskUsbDevice.h"
#include "task_sd_update/inc/taskSdUpdate.h"
#if  FUN_BATTERY_CHARGE_SHOW
#include "task_bat_charge/inc/taskBatCharge.h"
#endif

/*******************************************************************************
* Function Name  : app_taskInit
* Description    : app_taskInit
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskInit(void);
/*******************************************************************************
* Function Name  : app_taskCurId
* Description    : app_taskCurId
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
taskID app_taskCurId(void);
/*******************************************************************************
* Function Name  : app_taskStart
* Description    : app_taskStart
* Input          : taskID id,uint32 arg
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskStart(u32 id,u32 arg);
/*******************************************************************************
* Function Name  : app_taskChange
* Description    : app_taskChange
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskChange(void);
/*******************************************************************************
* Function Name  : app_taskService
* Description    : app_taskService
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void app_taskService(void);


#endif
