Archive member included to satisfy reference by file (symbol)

..\lib\libboot.a(boot.o)      obj\Debug\dev\battery\src\battery_api.o (boot_vddrtcCalculate)
..\lib\libboot.a(boot_loader.o)
                              obj\Debug\mcu\boot\spi_boot_cfg.o (.bootsect)
..\lib\libboot.a(reset.o)     obj\Debug\mcu\boot\spi_boot_cfg.o (_start)
..\lib\libboot.a(boot_lib.o)  ..\lib\libboot.a(boot_loader.o) (boot_sdram_init)
..\lib\libmcu.a(hx330x_adc.o)
                              obj\Debug\hal\src\hal_adc.o (hx330x_adcEnable)
..\lib\libmcu.a(hx330x_auadc.o)
                              obj\Debug\hal\src\hal_auadc.o (hx330x_auadcHalfIRQRegister)
..\lib\libmcu.a(hx330x_csi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_csiInit)
..\lib\libmcu.a(hx330x_dac.o)
                              obj\Debug\hal\src\hal_dac.o (hx330x_dacSampleRateSet)
..\lib\libmcu.a(hx330x_dma.o)
                              obj\Debug\app\app_common\src\app_lcdshow.o (hx330x_dmaNocDefault)
..\lib\libmcu.a(hx330x_dmauart.o)
                              obj\Debug\hal\src\hal_dmauart.o (hx330x_DmaUart_con_cfg)
..\lib\libmcu.a(hx330x_gpio.o)
                              ..\lib\libmcu.a(hx330x_csi.o) (hx330x_gpioSFRSet)
..\lib\libmcu.a(hx330x_iic.o)
                              obj\Debug\hal\src\hal_iic.o (hx330x_iic0Init)
..\lib\libmcu.a(hx330x_int.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_intCriticalEnterFunc)
..\lib\libmcu.a(hx330x_isp.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_isp_mask_tab_cfg)
..\lib\libmcu.a(hx330x_isp_tab.o)
                              ..\lib\libmcu.a(hx330x_isp.o) (GAOS3X3_TAB)
..\lib\libmcu.a(hx330x_jpg.o)
                              obj\Debug\hal\src\hal_mjpAEncode.o (hx330x_mjpA_EncodeISRRegister)
..\lib\libmcu.a(hx330x_jpg_tab.o)
                              ..\lib\libmcu.a(hx330x_jpg.o) (hx330x_mjpA_table_init)
..\lib\libmcu.a(hx330x_lcd.o)
                              obj\Debug\dev\lcd\src\lcd_api.o (hx330x_lcdReset)
..\lib\libmcu.a(hx330x_lcdrotate.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_rotateIRQHandler)
..\lib\libmcu.a(hx330x_lcdui.o)
                              obj\Debug\app\app_common\src\app_init.o (hx330x_lcdShowWaitDone)
..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uiLzoIRQHandler)
..\lib\libmcu.a(hx330x_lcdwin.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_lcdWinABConfig)
..\lib\libmcu.a(hx330x_md.o)  obj\Debug\hal\src\hal_md.o (hx330x_mdEnable)
..\lib\libmcu.a(hx330x_mipi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_MipiCSIUinit)
..\lib\libmcu.a(hx330x_misc.o)
                              obj\Debug\dev\touchpanel\src\touchpanel_api.o (hx330x_abs)
..\lib\libmcu.a(hx330x_rtc.o)
                              obj\Debug\hal\src\hal_rtc.o (hx330x_rtcRamRead)
..\lib\libmcu.a(hx330x_sd.o)  obj\Debug\dev\sd\src\sd_api.o (hx330x_emmc_set_bus)
..\lib\libmcu.a(hx330x_spi0.o)
                              obj\Debug\hal\src\hal_spi.o (hx330x_spi0ManualInit)
..\lib\libmcu.a(hx330x_spi1.o)
                              obj\Debug\hal\src\hal_spi1.o (hx330x_spi1_CS_Config)
..\lib\libmcu.a(hx330x_sys.o)
                              obj\Debug\dev\gsensor\src\gsensor_da380.o (hx330x_sysCpuMsDelay)
..\lib\libmcu.a(hx330x_timer.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_timer0IRQHandler)
..\lib\libmcu.a(hx330x_tminf.o)
                              obj\Debug\hal\src\hal_watermark.o (hx330x_mjpA_TimeinfoEnable)
..\lib\libmcu.a(hx330x_uart.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uart0IRQHandler)
..\lib\libmcu.a(hx330x_usb.o)
                              obj\Debug\dev\usb\dusb\src\dusb_api.o (hx330x_usb20_CallbackRegister)
..\lib\libmcu.a(hx330x_wdt.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_wdtEnable)
..\lib\libmcu.a(hx330x_emi.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_emiIRQHandler)
..\lib\libisp.a(hal_isp.o)    obj\Debug\dev\sensor\src\sensor_api.o (hal_sensor_fps_adpt)
..\lib\libhusb.a(husb_enum.o)
                              obj\Debug\dev\usb\husb\src\husb_api.o (husb20_ep0_cfg)
..\lib\libjpg.a(hal_jpg.o)    obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_enle_init)
..\lib\libjpg.a(hal_step_jpg.o)
                              obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_step_init)
..\lib\liblcd.a(hal_lcd.o)    obj\Debug\hal\src\hal_lcdshow.o (lcd_show_ctrl)
..\lib\liblcd.a(hal_lcdMem.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_lcdAddrCalculate)
..\lib\liblcd.a(hal_lcdrotate.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_rotateInit)
..\lib\liblcd.a(hal_lcdUi.o)  obj\Debug\app\app_common\src\app_lcdshow.o (hal_uiDrawBufMalloc)
..\lib\liblcd.a(hal_lcdUiLzo.o)
                              ..\lib\liblcd.a(hal_lcdUi.o) (hal_uiLzokick)
..\lib\liblcd.a(lcd_tab.o)    obj\Debug\dev\lcd\src\lcd_api.o (hal_lcdParaLoad)
..\lib\libmultimedia.a(api_multimedia.o)
                              obj\Debug\multimedia\audio\audio_playback.o (api_multimedia_init)
..\lib\libmultimedia.a(avi_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_dec_func)
..\lib\libmultimedia.a(avi_odml_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_odml_enc_func)
..\lib\libmultimedia.a(avi_std_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_std_enc_func)
..\lib\libmultimedia.a(gif_dec.o)
                              obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o (gif_get_frame)
..\lib\libmultimedia.a(wav_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_dec_func)
..\lib\libmultimedia.a(wav_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_enc_func)
..\lib\libmultimedia.a(wav_pcm.o)
                              ..\lib\libmultimedia.a(wav_enc.o) (pcm_encode)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                              obj\Debug\app\user_config\src\mbedtls_md5.o (memcmp)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                              obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o (memcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                              obj\Debug\hal\src\hal_uart.o (memset)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                              obj\Debug\dev\gsensor\src\gsensor_api.o (strcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
                              obj\Debug\app\task_windows\task_common\src\task_common.o (__divdi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                              obj\Debug\dev\fs\src\ff.o (__udivdi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                              obj\Debug\dev\fs\src\ff.o (__umoddi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o) (__udivsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o) (__umodsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o) (__clz_tab)

Allocating common symbols
Common symbol       size              file

autoPowerOff_config
                    0x4               obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
flash_success       0x1               obj\Debug\app\app_common\src\main.o
WAV_TYPE_E          0x4               obj\Debug\dev\battery\src\battery_api.o
SysCtrl             0x148             obj\Debug\app\app_common\src\app_init.o
gsensor_ctl         0x8               obj\Debug\dev\gsensor\src\gsensor_api.o
SENSOR_FILTER_TYPE  0x4               obj\Debug\dev\battery\src\battery_api.o
batChargeOp         0x30              obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
RGB_GMMA_Tab        0x300             ..\lib\libmcu.a(hx330x_isp.o)
ui_draw_ctrl        0x10              obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
usb_dev_ctl         0x29c             obj\Debug\dev\usb\dusb\src\dusb_api.o
rc128k_div          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
gif_dec             0x4               ..\lib\libmultimedia.a(gif_dec.o)
husb_ctl            0x1434            obj\Debug\dev\usb\husb\src\husb_api.o
rtcSecondISR        0x4               ..\lib\libmcu.a(hx330x_rtc.o)
task_play_audio_stat
                    0x4               obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
sd_update_op        0x64              obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
mediaVideoCtl       0xf4              obj\Debug\multimedia\video\video_record.o
Y_GMA_Tab           0x200             ..\lib\libmcu.a(hx330x_isp.o)
rtcAlamISR          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
usensor_handle      0x4               obj\Debug\dev\usb\husb\src\husb_usensor.o
USB_CH              0x4               obj\Debug\dev\battery\src\battery_api.o
UVC_CACHE_STA       0x4               obj\Debug\dev\battery\src\battery_api.o
fs_exfunc           0x14              obj\Debug\dev\fs\src\fs_api.o
SDCON0_T            0x4               obj\Debug\dev\battery\src\battery_api.o
dw9714p             0x2               obj\Debug\dev\sensor\src\sensor_api.o
usbDeviceOp         0xc               obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
XOSNesting          0x4               obj\Debug\mcu\xos\xos.o
smph_dmacopy        0x4               ..\lib\libmcu.a(hx330x_sys.o)
videoResolution_config
                    0x4               obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
SDCON1_T            0x4               obj\Debug\dev\battery\src\battery_api.o
tp_api_t            0x20              obj\Debug\dev\touchpanel\src\touchpanel_api.o
hx330x_lcdISR       0x14              ..\lib\libmcu.a(hx330x_lcd.o)
dfm                 0x3ac             ..\lib\liblcd.a(hal_lcdMem.o)
dev_key_tab         0x78              obj\Debug\dev\key\src\key_api.o
hx330x_timerISR     0x10              ..\lib\libmcu.a(hx330x_timer.o)
UVC_FSTACK_STA      0x4               obj\Debug\dev\battery\src\battery_api.o
rc128k_rtc_cnt      0x4               ..\lib\libmcu.a(hx330x_rtc.o)
lcd_show_ctrl       0x68              ..\lib\liblcd.a(hal_lcd.o)
recordPhotoOp       0x20              obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
rc128k_timer_cnt    0x4               ..\lib\libmcu.a(hx330x_rtc.o)
playVideoOp         0x44              obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
CHANNEL_EXCHANGE_E  0x4               obj\Debug\dev\battery\src\battery_api.o

Memory Configuration

Name             Origin             Length             Attributes
boot             0x01fffc00         0x00000200
ram_boot         0x00000000         0x00006c00
ram_user         0x00000000         0x00007000
usb_ram          0x00008000         0x00008000
line_ram         0x00200000         0x00010000
mp3_text         0x00008000         0x00008000
mp3_ram          0x00200000         0x00010000
nes_text         0x00008000         0x00008000
nes_ram          0x00200000         0x00010000
sdram            0x02000000         0x00800000
flash            0x06000000         0x00800000
exsdram          0x00000000         0x00800000
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj\Debug\dev\battery\src\battery_api.o
LOAD obj\Debug\dev\dev_api.o
LOAD obj\Debug\dev\fs\src\diskio.o
LOAD obj\Debug\dev\fs\src\ff.o
LOAD obj\Debug\dev\fs\src\ffunicode.o
LOAD obj\Debug\dev\fs\src\fs_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_da380.o
LOAD obj\Debug\dev\gsensor\src\gsensor_gma301.o
LOAD obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
LOAD obj\Debug\dev\ir\src\ir_api.o
LOAD obj\Debug\dev\key\src\key_api.o
LOAD obj\Debug\dev\lcd\src\lcd_api.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_GC9307.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_JD9853.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
LOAD obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
LOAD obj\Debug\dev\led\src\led_api.o
LOAD obj\Debug\dev\led_pwm\src\led_pwm_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_jpg.o
LOAD obj\Debug\dev\sd\src\sd_api.o
LOAD obj\Debug\dev\sensor\src\sensor_api.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H63P.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
LOAD obj\Debug\dev\sensor\src\sensor_tab.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_api.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_iic.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_enum.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_msc.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uac.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uvc.o
LOAD obj\Debug\dev\usb\husb\src\husb_api.o
LOAD obj\Debug\dev\usb\husb\src\husb_tpbulk.o
LOAD obj\Debug\dev\usb\husb\src\husb_usensor.o
LOAD obj\Debug\dev\usb\husb\src\husb_uvc.o
LOAD obj\Debug\hal\src\hal_adc.o
LOAD obj\Debug\hal\src\hal_auadc.o
LOAD obj\Debug\hal\src\hal_csi.o
LOAD obj\Debug\hal\src\hal_dac.o
LOAD obj\Debug\hal\src\hal_dmauart.o
LOAD obj\Debug\hal\src\hal_eeprom.o
LOAD obj\Debug\hal\src\hal_gpio.o
LOAD obj\Debug\hal\src\hal_iic.o
LOAD obj\Debug\hal\src\hal_int.o
LOAD obj\Debug\hal\src\hal_lcdshow.o
LOAD obj\Debug\hal\src\hal_md.o
LOAD obj\Debug\hal\src\hal_mjpAEncode.o
LOAD obj\Debug\hal\src\hal_mjpBEncode.o
LOAD obj\Debug\hal\src\hal_mjpDecode.o
LOAD obj\Debug\hal\src\hal_rtc.o
LOAD obj\Debug\hal\src\hal_spi.o
LOAD obj\Debug\hal\src\hal_spi1.o
LOAD obj\Debug\hal\src\hal_stream.o
LOAD obj\Debug\hal\src\hal_sys.o
LOAD obj\Debug\hal\src\hal_timer.o
LOAD obj\Debug\hal\src\hal_uart.o
LOAD obj\Debug\hal\src\hal_watermark.o
LOAD obj\Debug\hal\src\hal_wdt.o
LOAD obj\Debug\mcu\boot\spi_boot_cfg.o
LOAD obj\Debug\mcu\xos\xmbox.o
LOAD obj\Debug\mcu\xos\xmsgq.o
LOAD obj\Debug\mcu\xos\xos.o
LOAD obj\Debug\mcu\xos\xwork.o
LOAD obj\Debug\multimedia\audio\audio_playback.o
LOAD obj\Debug\multimedia\audio\audio_record.o
LOAD obj\Debug\multimedia\image\image_decode.o
LOAD obj\Debug\multimedia\image\image_encode.o
LOAD obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
LOAD obj\Debug\multimedia\video\video_playback.o
LOAD obj\Debug\multimedia\video\video_record.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
LOAD obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
LOAD obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
LOAD obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
LOAD obj\Debug\sys_manage\res_manage\res_manage_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
LOAD obj\Debug\app\app_common\src\app_init.o
LOAD obj\Debug\app\app_common\src\app_lcdshow.o
LOAD obj\Debug\app\app_common\src\main.o
LOAD obj\Debug\app\resource\user_res.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
LOAD obj\Debug\app\task_windows\msg_api.o
LOAD obj\Debug\app\task_windows\task_api.o
LOAD obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
LOAD obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
LOAD obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common_msg.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
LOAD obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3.o
LOAD obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3DirOpsMsg.o
LOAD obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3FileOpsMsg.o
LOAD obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3MainMsg.o
LOAD obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3SubMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
LOAD obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
LOAD obj\Debug\app\task_windows\windows_api.o
LOAD obj\Debug\app\user_config\src\mbedtls_md5.o
LOAD obj\Debug\app\user_config\src\user_config_api.o
LOAD obj\Debug\app\user_config\src\user_config_tab.o
                0x00200000                __sdram_size = (boot_sdram_size == 0x1)?0x800000:0x200000

.bootsec        0x01fffc00      0x200 load address 0x00000000
 *(.bootsec)
 .bootsec       0x01fffc00      0x200 ..\lib\libboot.a(boot_loader.o)
                0x01fffc10                hex
                0x01fffc10                .hex
                0x01fffd1c                .bootsect

.boot_code      0x00000000     0x2a80 load address 0x00000200
                0x00000000                _boot_vma = .
 *(.vector)
 .vector        0x00000000      0x380 ..\lib\libboot.a(reset.o)
                0x000002a0                _start
                0x000002ec                _step_in
                0x00000330                _step_out
 *(.vector.kepttext)
 .vector.kepttext
                0x00000380       0x6c obj\Debug\dev\dev_api.o
                0x00000380                exception_lowpower_io_cfg
 .vector.kepttext
                0x000003ec      0x460 ..\lib\libboot.a(boot.o)
                0x000004d8                boot_vddrtcCalculate
                0x00000504                boot_putchar
                0x000005b0                boot_uart_puts
                0x00000604                exception
                0x00000830                exception_trigger
 .vector.kepttext
                0x0000084c       0x20 ..\lib\libboot.a(boot_lib.o)
                0x0000084c                boot_getChipSN
 .vector.kepttext
                0x0000086c      0x210 ..\lib\libmcu.a(hx330x_gpio.o)
                0x0000086c                exception_gpioDataSet
                0x000008f8                hx330x_gpioDataGet
                0x0000097c                hx330x_gpioCommonDataGet
                0x000009c4                exception_io1d1_softstart_clr
 .vector.kepttext
                0x00000a7c       0xb8 ..\lib\libmcu.a(hx330x_int.o)
                0x00000a7c                hx330x_intCriticalEnterFunc
                0x00000ab4                hx330x_intCriticalExitFunc
                0x00000ad8                hx330x_intHeCriticalEnterFunc
                0x00000b10                hx330x_intHeCriticalExitFunc
 .vector.kepttext
                0x00000b34      0x304 ..\lib\libmcu.a(hx330x_rtc.o)
                0x00000b34                hx330x_rtcWriteByte
                0x00000b84                hx330x_rtcReadByte
                0x00000be8                hx330x_rtcDataRead
                0x00000c70                hx330x_rtcDataWrite
                0x00000cf8                hx330x_WKI1WakeupEnable
 .vector.kepttext
                0x00000e38      0x1f0 ..\lib\libmcu.a(hx330x_sys.o)
                0x00000e38                hx330x_sysCpuMsDelay
                0x00000e8c                hx330x_sysCpuNopDelay
                0x00000ec8                hx330x_bytes_memcpy
                0x00000f38                hx330x_bytes_memset
                0x00000f90                hx330x_bytes_cmp
 .vector.kepttext
                0x00001028       0x5c ..\lib\libmcu.a(hx330x_uart.o)
                0x00001028                hx330x_uart0SendByte
 .vector.kepttext
                0x00001084       0x84 ..\lib\libmcu.a(hx330x_wdt.o)
                0x00001084                hx330x_wdtEnable
                0x000010b8                hx330x_wdtClear
                0x000010ec                hx330x_wdtReset
 *(.vector.keptdata)
 .vector.keptdata
                0x00001108      0x16c obj\Debug\app\user_config\src\user_config_tab.o
                0x00001108                hardware_setup
 .vector.keptdata
                0x00001274       0x3c ..\lib\libboot.a(boot.o)
                0x00001274                vbg_param
 .vector.keptdata
                0x000012b0      0x100 ..\lib\libboot.a(reset.o)
                0x000012b0                _step_data
 .vector.keptdata
                0x000013b0        0x4 ..\lib\libboot.a(boot_lib.o)
 .vector.keptdata
                0x000013b4        0x4 ..\lib\libmcu.a(hx330x_gpio.o)
 .vector.keptdata
                0x000013b8        0x4 ..\lib\libmcu.a(hx330x_spi0.o)
                0x000013b8                spi_auto_mode
                0x000013bc                . = ALIGN (0x4)
                0x000013bc                _boot_kept_vma = .
 *(.vector.text)
 .vector.text   0x000013bc      0x8f4 ..\lib\libboot.a(boot.o)
                0x0000162c                boot_vbg_pre_init
                0x000016c4                boot_vbg_back_init
                0x0000170c                exception_init
                0x00001744                bool_pll_init
                0x00001bbc                boot_clktun_check
                0x00001c48                boot_clktun_save
 .vector.text   0x00001cb0      0xbbc ..\lib\libboot.a(boot_lib.o)
                0x00001e98                boot_sdram_init
 *(.vector.data)
 .vector.data   0x0000286c        0x4 ..\lib\libboot.a(boot.o)
                0x0000286c                vbg_adc
 .vector.data   0x00002870      0x210 ..\lib\libboot.a(boot_lib.o)
                0x00002870                tune_values
                0x000029f0                tune_by
                0x000029fc                tune_tab_2_clk
                0x00002a1c                tune_tab_1_clk
                0x00002a3c                tuning_test_addr
                0x00002a5c                tuning_test_data
                0x00002a7c                SDRTUN2_CON

.ram            0x00000000     0x4508
                0x000013bc                . = _boot_kept_vma
 *fill*         0x00000000     0x13bc 
                0x000013bc                __sram_start = .
 *(.sram_usb11fifo)
 .sram_usb11fifo
                0x000013bc      0x96c obj\Debug\hal\src\hal_sys.o
                0x000013bc                usb11_fifo
 *(.sram_comm)
 .sram_comm     0x00001d28     0x1ac0 obj\Debug\dev\fs\src\fs_api.o
 .sram_comm     0x000037e8      0x200 obj\Debug\dev\sd\src\sd_api.o
                0x000037e8                sd0RamBuffer
 .sram_comm     0x000039e8       0x40 obj\Debug\hal\src\hal_dmauart.o
                0x000039e8                dmauart_fifo
 .sram_comm     0x00003a28      0x400 obj\Debug\hal\src\hal_mjpDecode.o
 .sram_comm     0x00003e28       0x60 obj\Debug\mcu\xos\xmsgq.o
 .sram_comm     0x00003e88      0x400 ..\lib\libmcu.a(hx330x_jpg.o)
                0x00003e88                jpg_dri_tab
 .sram_comm     0x00004288      0x280 ..\lib\libisp.a(hal_isp.o)
                0x00004508                __sram_end = .

.usb_ram        0x00008000     0x3630
                0x00008000                __ufifo_start = .
 *(.uram_usb20fifo)
 .uram_usb20fifo
                0x00008000     0x1d30 obj\Debug\hal\src\hal_sys.o
                0x00008000                usb20_fifo
 *(.uram_comm)
 .uram_comm     0x00009d30     0x1900 obj\Debug\hal\src\hal_watermark.o
                0x00009d30                tminf_font
                0x0000b630                __ufifo_end = .

.line_ram       0x00200000        0x0
                0x00200000                __line_start = .
 *(.lram_comm)
                0x00200000                __line_end = .

.on_sdram       0x02000000     0x7860 load address 0x00002e00
                0x02000000                _onsdram_start = .
 *(.sdram_text)
 .sdram_text    0x02000000       0x3c obj\Debug\dev\fs\src\ff.o
                0x02000000                clst2sect
 .sdram_text    0x0200003c       0x58 obj\Debug\dev\fs\src\fs_api.o
                0x0200003c                fs_getClustStartSector
 .sdram_text    0x02000094      0x18c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x02000094                nv_jpg_write_by_linkmap
 .sdram_text    0x02000220      0x4fc obj\Debug\dev\sd\src\sd_api.o
                0x020002c4                sd_api_Stop
                0x02000554                sd_api_Exist
                0x0200057c                sd_api_Write
                0x0200065c                sd_api_Read
 .sdram_text    0x0200071c       0x3c obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .sdram_text    0x02000758       0x3c obj\Debug\hal\src\hal_adc.o
                0x02000758                hal_adcGetChannel
 .sdram_text    0x02000794       0x48 obj\Debug\hal\src\hal_dac.o
 .sdram_text    0x020007dc      0x104 obj\Debug\hal\src\hal_gpio.o
                0x020007dc                hal_gpioInit
                0x02000884                hal_gpioEPullSet
 .sdram_text    0x020008e0      0xa18 obj\Debug\hal\src\hal_spi.o
                0x02000910                hal_spiUpdata_led_show
                0x020009c8                hal_spiUpdata_led_show_init
                0x02000a48                hal_spiUpdata_led_show_uinit
                0x02000aa0                hal_spiManualInit
                0x02000acc                hal_spiAutoModeInit
                0x02000afc                hal_spiModeSwitch
                0x02000b78                hal_spiFlashReadID
                0x02000be8                hal_spiFlashWriteEnable
                0x02000c1c                hal_spiFlashWait
                0x02000cac                hal_spiFlashReadPage
                0x02000d04                hal_spiFlashRead
                0x02000d90                hal_spiFlashWritePage
                0x02000e24                hal_spiFlashWrite
                0x02000f04                hal_spiFlashWriteInManual
                0x02000f98                hal_spiFlashEraseSector
                0x0200103c                hal_spiFlashEraseBlock
                0x020010dc                hal_spiFlashEraseChip
                0x02001118                hal_spiFlashReadUniqueID
                0x020011a0                hal_spiFlashReadOTP
                0x0200124c                hal_spiFlashReadManual
 .sdram_text    0x020012f8      0x498 obj\Debug\hal\src\hal_stream.o
                0x020012f8                hal_streamMalloc
                0x0200142c                hal_streamIn
                0x02001518                hal_streamOut
                0x02001620                hal_streamOutNext
                0x020016e8                hal_streamfree
 .sdram_text    0x02001790       0x24 obj\Debug\hal\src\hal_timer.o
                0x02001790                hal_timerPWMStop
 .sdram_text    0x020017b4      0x158 obj\Debug\hal\src\hal_uart.o
                0x020017b4                uart_Printf
 .sdram_text    0x0200190c      0xd60 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x020019a0                taskSdGetClst
                0x02001a14                taskSdReadBuf
                0x02001b50                taskSdUpdateProcess
 .sdram_text    0x0200266c      0x330 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x0200266c                taskSdUpdateDrawStartAddrCal
                0x02002744                taskSdUpdateDrawAddrReCal
                0x020028b0                taskSdUpdate_uiProgress
 .sdram_text    0x0200299c      0x11c ..\lib\libmcu.a(hx330x_adc.o)
                0x0200299c                hx330x_adcEnable
                0x020029f8                hx330x_adcSetBaudrate
                0x02002a2c                hx330x_adcConverStart
                0x02002a50                hx330x_adcRead
 .sdram_text    0x02002ab8      0x1e8 ..\lib\libmcu.a(hx330x_dac.o)
                0x02002ab8                hx330x_dacIRQHandler
                0x02002b8c                hx330x_dacBufferSet
                0x02002bac                hx330x_dacBufferFlush
                0x02002bcc                hx330x_dacStart
                0x02002c48                hx330x_check_dacstop
 .sdram_text    0x02002ca0       0xa4 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x02002ca0                hx330x_uart1IRQHandler
 .sdram_text    0x02002d44      0x4ec ..\lib\libmcu.a(hx330x_gpio.o)
                0x02002d44                hx330x_gpioDirSet
                0x02002de8                hx330x_gpioPullSet
                0x02002ea8                hx330x_gpioPinPollSet
                0x02002ef4                hx330x_gpioDrvSet
                0x02002f80                hx330x_gpioDataSet
                0x0200300c                hx330x_gpioPinDataSet
                0x02003058                hx330x_gpioMapSet
                0x020030e4                hx330x_gpioDigitalSet
                0x02003170                hx330x_gpioLedPull
 .sdram_text    0x02003230      0x258 ..\lib\libmcu.a(hx330x_int.o)
                0x02003230                hx330x_intHandler
                0x02003244                fast_isr
                0x02003344                slow_isr
                0x02003440                hx330x_intEnable
 .sdram_text    0x02003488       0x54 ..\lib\libmcu.a(hx330x_jpg.o)
                0x02003488                hx330x_mjpA_EncodeLoadAddrGet
                0x020034a4                hx330x_mjpA_EncodeStartAddrGet
                0x020034c0                hx330x_mjpA_EncodeDriTabGet
 .sdram_text    0x020034dc       0x24 ..\lib\libmcu.a(hx330x_lcd.o)
                0x020034dc                hx330x_lcdKick
 .sdram_text    0x02003500      0x35c ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02003500                hx330x_lcdShowWaitDone
                0x0200354c                hx330x_lcdShowKick
                0x02003570                hx330x_lcdVideoSetScanMode
                0x02003604                hx330x_lcdVideoSetAddr
                0x02003620                hx330x_lcdVideoSetStride
                0x02003644                hx330x_lcdvideoSetPosition
                0x02003668                hx330x_lcdUiEnable
                0x020036bc                hx330x_lcdVideoUpScaler_cfg
                0x020037b4                hx330x_lcdVideoUpScalerSoftRotate_cfg
 .sdram_text    0x0200385c       0x60 ..\lib\libmcu.a(hx330x_misc.o)
                0x0200385c                hx330x_min
                0x02003880                hx330x_data_check
 .sdram_text    0x020038bc      0x138 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020038bc                hx330x_WKOEnable
                0x02003918                hx330x_WKI1WakeupTriger
                0x02003968                hx330x_WKI0WakeupTriger
                0x020039b8                hx330x_WakeUpCleanPending
 .sdram_text    0x020039f4      0x674 ..\lib\libmcu.a(hx330x_sd.o)
                0x020039f4                hx330x_sd0SendCmd
                0x02003af4                hx330x_sd0Recv
                0x02003b50                hx330x_sd0Send
                0x02003b88                hx330x_sd0WaitDAT0
                0x02003c28                hx330x_sd0WaitPend
                0x02003cb0                hx330x_sd0GetRsp
                0x02003ccc                hx330x_sd0CRCCheck
                0x02003d30                hx330x_sd1SendCmd
                0x02003e2c                hx330x_sd1Recv
                0x02003e88                hx330x_sd1Send
                0x02003ec0                hx330x_sd1WaitDAT0
                0x02003f60                hx330x_sd1WaitPend
                0x02003fe8                hx330x_sd1GetRsp
                0x02004004                hx330x_sd1CRCCheck
 .sdram_text    0x02004068      0x678 ..\lib\libmcu.a(hx330x_spi0.o)
                0x02004068                hx330x_spi0PinConfig
                0x020040e8                hx330x_spi0ManualInit
                0x02004214                hx330x_spi0SendByte
                0x02004268                hx330x_spi0RecvByte
                0x020042bc                hx330x_spi0Send
                0x02004374                hx330x_spi0Recv
                0x02004438                hx330x_spi0CS0Config
                0x0200447c                hx330x_spi0AutoModeInit
                0x02004668                hx330x_spi0ExitAutoMode
 .sdram_text    0x020046e0      0x5c0 ..\lib\libmcu.a(hx330x_sys.o)
                0x020046e0                table_init_sfr
                0x02004760                hx330x_sysDcacheWback
                0x020047fc                hx330x_sysDcacheFlush
                0x02004898                hx330x_sysClkSet
                0x020048f4                hx330x_mcpy0_sdram2gram
                0x020049b4                hx330x_mcpy0_sdram2gram_nocache
                0x02004a5c                hx330x_mcpy1_sdram2gram_nocache_waitdone
                0x02004ad0                hx330x_mcpy1_sdram2gram_nocache_kick
                0x02004b60                hx330x_mcpy1_sdram2gram
                0x02004c0c                hx330x_mcpy1_sdram2gram_nocache
 .sdram_text    0x02004ca0      0x45c ..\lib\libmcu.a(hx330x_timer.o)
                0x02004ca0                hx330x_timer0IRQHandler
                0x02004ce4                hx330x_timer1IRQHandler
                0x02004d28                hx330x_timer2IRQHandler
                0x02004d6c                hx330x_timer3IRQHandler
                0x02004db0                hx330x_timerISRRegister
                0x02004df4                hx330x_timerStart
                0x02004f40                hx330x_timerStop
                0x02005010                hx330x_timerTickStart
                0x02005044                hx330x_timerTickStop
                0x02005060                hx330x_timerTickCount
                0x0200507c                hx330x_timerPWMStop
 .sdram_text    0x020050fc       0x64 ..\lib\libmcu.a(hx330x_uart.o)
                0x020050fc                hx330x_uart0IRQHandler
 .sdram_text    0x02005160      0x324 ..\lib\libmcu.a(hx330x_usb.o)
                0x02005160                hx330x_bulk20_tx
                0x02005304                hx330x_bulk20_rx
 .sdram_text    0x02005484       0x68 ..\lib\libjpg.a(hal_jpg.o)
                0x02005484                hal_mjp_enle_tab_get
 .sdram_text    0x020054ec       0x30 ..\lib\libjpg.a(hal_step_jpg.o)
                0x020054ec                hal_mjp_step_enle_tab_get
 .sdram_text    0x0200551c       0x58 ..\lib\liblcd.a(hal_lcdUi.o)
                0x0200551c                hal_lcdUiEnable
 *(.sdram_code)
 .sdram_code    0x02005574      0x110 ..\lib\libmcu.a(hx330x_spi0.o)
                0x02005574                spi0PinCfg_tab
                0x02005584                SPI0_4_LINE_tab
                0x020055c4                SPI0_2_LINE1_tab
                0x02005604                SPI0_2_LINE0_tab
                0x02005644                SPI0_1_LINE_tab
 *(.sdram_data)
 *(.data*)
 .data          0x02005684        0x0 obj\Debug\dev\battery\src\battery_api.o
 .data          0x02005684      0x120 obj\Debug\dev\dev_api.o
                0x02005684                dev_node
 .data          0x020057a4        0x0 obj\Debug\dev\fs\src\diskio.o
 .data          0x020057a4        0x0 obj\Debug\dev\fs\src\ff.o
 .data          0x020057a4        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .data          0x020057a4        0x0 obj\Debug\dev\fs\src\fs_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\gsensor\src\gsensor_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .data          0x020057a4        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .data          0x020057a4        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .data          0x020057a4        0x0 obj\Debug\dev\ir\src\ir_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\key\src\key_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_GC9307.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_JD9853.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .data          0x020057a4        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .data          0x020057a4        0x0 obj\Debug\dev\led\src\led_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\nvfs\src\nvfs_api.o
 .data          0x020057a4        0x0 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .data          0x020057a4      0x160 obj\Debug\dev\sd\src\sd_api.o
                0x020057a4                sd_ident_tab
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_api.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H63P.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .data          0x02005904        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .data          0x02005904        0x4 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .data          0x02005908        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .data          0x02005908        0x0 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .data          0x02005908        0x0 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .data          0x02005908        0x0 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .data          0x02005908      0x208 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x02005908                dusb_com_cfgdsc
                0x02005af0                dusb_msc_cfgdsc
 .data          0x02005b10        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\husb\src\husb_api.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .data          0x02005b10        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_adc.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_auadc.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_csi.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_dac.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_dmauart.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_eeprom.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_gpio.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_iic.o
 .data          0x02005b10        0x0 obj\Debug\hal\src\hal_int.o
 .data          0x02005b10        0x1 obj\Debug\hal\src\hal_lcdshow.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_md.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_mjpAEncode.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_mjpBEncode.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_mjpDecode.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_rtc.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_spi.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_spi1.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_stream.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_sys.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_timer.o
 .data          0x02005b11        0x0 obj\Debug\hal\src\hal_uart.o
 *fill*         0x02005b11        0x3 
 .data          0x02005b14       0x48 obj\Debug\hal\src\hal_watermark.o
 .data          0x02005b5c        0x0 obj\Debug\hal\src\hal_wdt.o
 .data          0x02005b5c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .data          0x02005b5c        0x0 obj\Debug\mcu\xos\xmbox.o
 .data          0x02005b5c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .data          0x02005b5c        0x0 obj\Debug\mcu\xos\xos.o
 .data          0x02005b5c        0x0 obj\Debug\mcu\xos\xwork.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\audio\audio_playback.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\audio\audio_record.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\image\image_decode.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\image\image_encode.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\video\video_playback.o
 .data          0x02005b5c        0x0 obj\Debug\multimedia\video\video_record.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .data          0x02005b5c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .data          0x02005b5c        0x0 obj\Debug\app\app_common\src\app_init.o
 .data          0x02005b5c        0x2 obj\Debug\app\app_common\src\app_lcdshow.o
 .data          0x02005b5e        0x0 obj\Debug\app\app_common\src\main.o
 *fill*         0x02005b5e        0x2 
 .data          0x02005b60      0xb88 obj\Debug\app\resource\user_res.o
                0x02005b60                User_Icon_Table
                0x02005fb0                User_String_Table
 .data          0x020066e8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .data          0x020066e8       0x84 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
                0x020066e8                menuplayBack
                0x020066f4                menuPageplayBack
                0x02006708                menuItemplayBack
 .data          0x0200676c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .data          0x0200676c      0x2fc obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x0200676c                menurecord
                0x02006778                menuPagerecord
                0x0200678c                menuItemrecord
                0x02006868                menuOptionversion
                0x02006870                menuOptionscreenSave
                0x02006890                menuOptionirLed
                0x020068a8                menuOptionfrequency
                0x020068b8                menuOptionlanguage
                0x02006928                menuOptionautoPowerOff
                0x02006940                menuOptionkeySound
                0x02006950                menuOptiongsensor
                0x02006970                menuOptiontimeStamp
                0x02006980                menuOptionparking
                0x02006990                menuOptionaudio
                0x020069a0                menuOptionmd
                0x020069b0                menuOptionev
                0x020069d8                menuOptionawb
                0x02006a00                menuOptionloopRecord
                0x02006a20                menuOptionphotoResolution
                0x02006a58                menuOptionvideoResolution
 .data          0x02006a68       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                0x02006a68                autoPowerOffWindow
                0x02006a78                autoPowerOffMsgDeal
 .data          0x02006ad0       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x02006ad0                dateTimeWindow
                0x02006ae0                dateTimeMsgDeal
 .data          0x02006b40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x02006b40                defaultWindow
                0x02006b50                defaultMsgDeal
 .data          0x02006ba8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x02006ba8                delAllWindow
                0x02006bb8                delAllMsgDeal
 .data          0x02006c10       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x02006c10                delCurWindow
                0x02006c20                delCurMsgDeal
 .data          0x02006c78       0x60 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x02006c78                formatWindow
                0x02006c88                formatMsgDeal
 .data          0x02006cd8       0x90 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x02006ce0                menuItemWindow
                0x02006cf0                menuItemMsgDeal
 .data          0x02006d68       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x02006d68                lockCurWindow
                0x02006d78                lockCurMsgDeal
 .data          0x02006dd0       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x02006dd0                menuOptionWindow
                0x02006de0                menuOptionMsgDeal
 .data          0x02006e40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x02006e40                unlockAllWindow
                0x02006e50                unlockAllMsgDeal
 .data          0x02006ea8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x02006ea8                unlockCurWindow
                0x02006eb8                unlockCurMsgDeal
 .data          0x02006f10       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
                0x02006f10                UsbDeviceSelectWindow
                0x02006f20                UsbDeviceSelectMsgDeal
 .data          0x02006f80        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .data          0x02006f80       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
                0x02006f80                videoResolutionWindow
                0x02006f90                videoResolutionMsgDeal
 .data          0x02006fe8       0x38 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x02006fe8                asternWindow
                0x02006ff8                asternMsgDeal
 .data          0x02007020       0x60 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x02007020                noFileWindow
                0x02007030                noFileMsgDeal
 .data          0x02007080       0x98 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x02007080                selfTestWindow
                0x02007090                selfTestMsgDeal
 .data          0x02007118       0x7c obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x02007118                tips1Window
                0x02007128                tips1MsgDeal
 .data          0x02007194       0x7c obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x02007194                tipsWindow
                0x020071a4                tipsMsgDeal
 .data          0x02007210        0x0 obj\Debug\app\task_windows\msg_api.o
 .data          0x02007210        0x0 obj\Debug\app\task_windows\task_api.o
 .data          0x02007210       0x14 obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
                0x02007210                taskBatCharge
 .data          0x02007224       0x64 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
                0x02007228                batChargeWindow
                0x02007238                batChargeMsgDeal
 .data          0x02007288        0x1 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x02007289        0x3 
 .data          0x0200728c       0x3c obj\Debug\app\task_windows\task_common\src\task_common.o
                0x020072c4                pwm_value
 .data          0x020072c8        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .data          0x020072c8       0x14 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x020072c8                taskPlayAudio
 .data          0x020072dc       0x80 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x020072dc                playAudioWindow
                0x020072ec                playAudioMsgDeal
 .data          0x0200735c        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3.o
 .data          0x0200735c        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3DirOpsMsg.o
 .data          0x0200735c        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3FileOpsMsg.o
 .data          0x0200735c        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3MainMsg.o
 .data          0x0200735c        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3SubMsg.o
 .data          0x0200735c       0x14 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0200735c                taskPlayVideo
 .data          0x02007370       0xa4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x0200737c                playVideoMainWindow
                0x0200738c                playVideoMainMsgDeal
 .data          0x02007414       0x9c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x02007420                playVideoSlideWindow
                0x02007430                playVideoSlideMsgDeal
 .data          0x020074b0       0x88 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x020074b0                playVideoThumbnallWindow
                0x020074c0                playVideoThumbnallMsgDeal
 .data          0x02007538       0x14 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                0x02007538                taskPowerOff
 .data          0x0200754c       0x14 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                0x0200754c                taskRecordAudio
 .data          0x02007560       0x50 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x02007560                RecordAudioWindow
                0x02007570                recordAudioMsgDeal
 .data          0x020075b0       0x14 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x020075b0                taskRecordPhoto
 .data          0x020075c4       0xc0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x020075c4                recordPhotoWindow
                0x020075d4                photoEncodeMsgDeal
 .data          0x02007684       0x14 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x02007684                taskRecordVideo
 .data          0x02007698       0xb8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x02007698                recordVideoWindow
                0x020076a8                recordVideoMsgDeal
 .data          0x02007750       0x14 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02007750                taskSDUpdate
 .data          0x02007764        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .data          0x02007764       0x14 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02007764                taskUSBDevice
 .data          0x02007778       0x78 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x02007778                usbDeviceWindow
                0x02007788                usbDeviceMsgDeal
 .data          0x020077f0        0x0 obj\Debug\app\task_windows\windows_api.o
 .data          0x020077f0        0x8 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x020077f0                MY_KEY
 .data          0x020077f8        0x0 obj\Debug\app\user_config\src\user_config_api.o
 .data          0x020077f8        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .data          0x020077f8        0x0 ..\lib\libboot.a(boot.o)
 .data          0x020077f8        0x0 ..\lib\libboot.a(boot_loader.o)
 .data          0x020077f8        0x0 ..\lib\libboot.a(reset.o)
 .data          0x020077f8        0x0 ..\lib\libboot.a(boot_lib.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_auadc.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_csi.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_dac.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_dmauart.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_gpio.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_isp.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .data          0x020077f8        0x0 ..\lib\libmcu.a(hx330x_jpg.o)
 .data          0x020077f8        0x1 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 *fill*         0x020077f9        0x3 
 .data          0x020077fc        0x4 ..\lib\libmcu.a(hx330x_lcd.o)
 .data          0x02007800        0x0 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .data          0x02007800        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02007800                video_scanmode_tab
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_rtc.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_sd.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_spi1.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_sys.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_tminf.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_uart.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_usb.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .data          0x02007808        0x0 ..\lib\libmcu.a(hx330x_emi.o)
 .data          0x02007808       0x18 ..\lib\libisp.a(hal_isp.o)
                0x02007808                ccm_filter_nocolor_ccm
 .data          0x02007820        0x0 ..\lib\libhusb.a(husb_enum.o)
 .data          0x02007820        0x0 ..\lib\libjpg.a(hal_jpg.o)
 .data          0x02007820        0x0 ..\lib\libjpg.a(hal_step_jpg.o)
 .data          0x02007820       0x40 ..\lib\liblcd.a(hal_lcd.o)
 .data          0x02007860        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 .data          0x02007860        0x0 ..\lib\liblcd.a(hal_lcdrotate.o)
 .data          0x02007860        0x0 ..\lib\liblcd.a(hal_lcdUi.o)
 .data          0x02007860        0x0 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .data          0x02007860        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(api_multimedia.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(gif_dec.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .data          0x02007860        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .data          0x02007860        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.bss            0x02007860    0x13334 load address 0x0000a660
                0x02007860                __bss_start = .
 *(.bss*)
 .bss           0x02007860        0x8 obj\Debug\dev\battery\src\battery_api.o
 .bss           0x02007868        0x8 obj\Debug\dev\dev_api.o
 .bss           0x02007870        0x0 obj\Debug\dev\fs\src\diskio.o
 .bss           0x02007870      0x468 obj\Debug\dev\fs\src\ff.o
 .bss           0x02007cd8        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .bss           0x02007cd8        0x0 obj\Debug\dev\fs\src\fs_api.o
 .bss           0x02007cd8        0x4 obj\Debug\dev\gsensor\src\gsensor_api.o
 .bss           0x02007cdc        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .bss           0x02007cdc        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .bss           0x02007cdc        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .bss           0x02007cdc        0x4 obj\Debug\dev\ir\src\ir_api.o
 .bss           0x02007ce0       0x18 obj\Debug\dev\key\src\key_api.o
 .bss           0x02007cf8       0xb0 obj\Debug\dev\lcd\src\lcd_api.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_GC9307.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_JD9853.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .bss           0x02007da8        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .bss           0x02007da8        0x4 obj\Debug\dev\led\src\led_api.o
 .bss           0x02007dac        0x4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .bss           0x02007db0       0x18 obj\Debug\dev\nvfs\src\nvfs_api.o
 .bss           0x02007dc8       0x10 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .bss           0x02007dd8       0x28 obj\Debug\dev\sd\src\sd_api.o
                0x02007dd8                hal_sdc_speed
 .bss           0x02007e00     0x134c obj\Debug\dev\sensor\src\sensor_api.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H63P.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .bss           0x0200914c        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .bss           0x0200914c        0x8 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .bss           0x02009154        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .bss           0x02009154        0x4 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .bss           0x02009158       0x1c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .bss           0x02009174        0x4 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .bss           0x02009178        0x0 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .bss           0x02009178        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .bss           0x02009178      0x110 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .bss           0x02009288        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .bss           0x02009288        0x2 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 *fill*         0x0200928a        0x2 
 .bss           0x0200928c        0xc obj\Debug\dev\usb\husb\src\husb_api.o
 .bss           0x02009298        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .bss           0x02009298        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .bss           0x0200929c        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .bss           0x0200929c        0x0 obj\Debug\hal\src\hal_adc.o
 .bss           0x0200929c      0x178 obj\Debug\hal\src\hal_auadc.o
                0x0200929c                auadccnt
 .bss           0x02009414       0x18 obj\Debug\hal\src\hal_csi.o
 .bss           0x0200942c        0x8 obj\Debug\hal\src\hal_dac.o
 .bss           0x02009434       0x50 obj\Debug\hal\src\hal_dmauart.o
 .bss           0x02009484        0x0 obj\Debug\hal\src\hal_eeprom.o
 .bss           0x02009484        0x0 obj\Debug\hal\src\hal_gpio.o
 .bss           0x02009484        0x2 obj\Debug\hal\src\hal_iic.o
 .bss           0x02009486        0x0 obj\Debug\hal\src\hal_int.o
 .bss           0x02009486        0x0 obj\Debug\hal\src\hal_lcdshow.o
 *fill*         0x02009486        0x2 
 .bss           0x02009488        0x4 obj\Debug\hal\src\hal_md.o
 .bss           0x0200948c      0x3c4 obj\Debug\hal\src\hal_mjpAEncode.o
 .bss           0x02009850      0x3a0 obj\Debug\hal\src\hal_mjpBEncode.o
 .bss           0x02009bf0       0x74 obj\Debug\hal\src\hal_mjpDecode.o
 .bss           0x02009c64       0x34 obj\Debug\hal\src\hal_rtc.o
 .bss           0x02009c98        0x8 obj\Debug\hal\src\hal_spi.o
                0x02009c98                spi_updata_led
 .bss           0x02009ca0       0x10 obj\Debug\hal\src\hal_spi1.o
 .bss           0x02009cb0        0x0 obj\Debug\hal\src\hal_stream.o
 .bss           0x02009cb0      0x50c obj\Debug\hal\src\hal_sys.o
 .bss           0x0200a1bc        0x0 obj\Debug\hal\src\hal_timer.o
 .bss           0x0200a1bc        0x8 obj\Debug\hal\src\hal_uart.o
 .bss           0x0200a1c4        0x8 obj\Debug\hal\src\hal_watermark.o
 .bss           0x0200a1cc        0x0 obj\Debug\hal\src\hal_wdt.o
 .bss           0x0200a1cc        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .bss           0x0200a1cc        0x0 obj\Debug\mcu\xos\xmbox.o
 .bss           0x0200a1cc        0x0 obj\Debug\mcu\xos\xmsgq.o
 .bss           0x0200a1cc        0x8 obj\Debug\mcu\xos\xos.o
 .bss           0x0200a1d4       0x80 obj\Debug\mcu\xos\xwork.o
 .bss           0x0200a254       0x60 obj\Debug\multimedia\audio\audio_playback.o
 .bss           0x0200a2b4       0x48 obj\Debug\multimedia\audio\audio_record.o
 .bss           0x0200a2fc        0x0 obj\Debug\multimedia\image\image_decode.o
 .bss           0x0200a2fc        0x0 obj\Debug\multimedia\image\image_encode.o
 .bss           0x0200a2fc        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .bss           0x0200a2fc      0x1c4 obj\Debug\multimedia\video\video_playback.o
 .bss           0x0200a4c0        0x0 obj\Debug\multimedia\video\video_record.o
 .bss           0x0200a4c0        0xc obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .bss           0x0200a4cc        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .bss           0x0200a4cc      0x530 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .bss           0x0200a9fc       0x18 obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
 .bss           0x0200aa14       0x94 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .bss           0x0200aaa8        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .bss           0x0200aaa8        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .bss           0x0200aaa8       0x60 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .bss           0x0200ab08        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .bss           0x0200ab08     0x5060 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .bss           0x0200fb68        0x4 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .bss           0x0200fb6c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .bss           0x0200fb6c       0x50 obj\Debug\app\app_common\src\app_init.o
 .bss           0x0200fbbc        0xc obj\Debug\app\app_common\src\app_lcdshow.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\app_common\src\main.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\resource\user_res.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .bss           0x0200fbc8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .bss           0x0200fbc8        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                0x0200fbc8                autoPowerOff_enable
 .bss           0x0200fbcc       0x48 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .bss           0x0200fc14        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .bss           0x0200fc14        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .bss           0x0200fc14        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .bss           0x0200fc14        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .bss           0x0200fc14        0x8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .bss           0x0200fc1c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .bss           0x0200fc1c        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .bss           0x0200fc20        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .bss           0x0200fc20        0xc obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .bss           0x0200fc2c        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .bss           0x0200fc30        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .bss           0x0200fc34      0x110 obj\Debug\app\task_windows\msg_api.o
 .bss           0x0200fd44       0x44 obj\Debug\app\task_windows\task_api.o
 .bss           0x0200fd88        0x0 obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
 .bss           0x0200fd88        0x0 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
 .bss           0x0200fd88        0x0 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .bss           0x0200fd88      0x208 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x0200fd88                af_adapt
 .bss           0x0200ff90        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .bss           0x0200ff90        0x0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .bss           0x0200ff90        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3DirOpsMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3FileOpsMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3MainMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3SubMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .bss           0x0200ff94        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .bss           0x0200ff94        0xc obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .bss           0x0200ffa0        0x0 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .bss           0x0200ffa0        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .bss           0x0200ffa0        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .bss           0x0200ffa0        0x0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .bss           0x0200ffa0        0x3 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 *fill*         0x0200ffa3        0x1 
 .bss           0x0200ffa4        0x8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .bss           0x0200ffac        0x8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .bss           0x0200ffb4        0x1 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .bss           0x0200ffb5        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .bss           0x0200ffb5        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .bss           0x0200ffb5        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .bss           0x0200ffb5        0x0 obj\Debug\app\task_windows\windows_api.o
 .bss           0x0200ffb5        0x0 obj\Debug\app\user_config\src\mbedtls_md5.o
 *fill*         0x0200ffb5        0x3 
 .bss           0x0200ffb8      0x20c obj\Debug\app\user_config\src\user_config_api.o
 .bss           0x020101c4        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .bss           0x020101c4        0x0 ..\lib\libboot.a(boot.o)
 .bss           0x020101c4        0x0 ..\lib\libboot.a(boot_loader.o)
 .bss           0x020101c4        0x0 ..\lib\libboot.a(reset.o)
 .bss           0x020101c4        0x0 ..\lib\libboot.a(boot_lib.o)
 .bss           0x020101c4        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .bss           0x020101c4        0x8 ..\lib\libmcu.a(hx330x_auadc.o)
 .bss           0x020101cc       0x40 ..\lib\libmcu.a(hx330x_csi.o)
 .bss           0x0201020c        0x4 ..\lib\libmcu.a(hx330x_dac.o)
 .bss           0x02010210        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .bss           0x02010210        0x4 ..\lib\libmcu.a(hx330x_dmauart.o)
 .bss           0x02010214       0x6c ..\lib\libmcu.a(hx330x_gpio.o)
 .bss           0x02010280        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .bss           0x02010280        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .bss           0x02010280        0xc ..\lib\libmcu.a(hx330x_isp.o)
                0x02010280                isp_ccf_dn_tab
                0x02010284                isp_ee_dn_tab
                0x02010288                isp_ee_sharp_tab
 .bss           0x0201028c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .bss           0x0201028c       0x50 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0201028c                mjpBEncAvgSize
                0x02010290                mjpAEncAvgSize
 .bss           0x020102dc        0x0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .bss           0x020102dc        0x0 ..\lib\libmcu.a(hx330x_lcd.o)
 .bss           0x020102dc        0x4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .bss           0x020102e0        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
 .bss           0x020102e8        0x4 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .bss           0x020102ec        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .bss           0x020102ec        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .bss           0x020102ec        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .bss           0x020102ec        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .bss           0x020102ec        0x4 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020102ec                rtcAlarmFlag
 .bss           0x020102f0       0x10 ..\lib\libmcu.a(hx330x_sd.o)
 .bss           0x02010300        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .bss           0x02010300        0x4 ..\lib\libmcu.a(hx330x_spi1.o)
 .bss           0x02010304        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02010304                mcp1_lock
 .bss           0x02010308        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .bss           0x02010308       0x28 ..\lib\libmcu.a(hx330x_tminf.o)
 .bss           0x02010330        0x4 ..\lib\libmcu.a(hx330x_uart.o)
 .bss           0x02010334       0x7c ..\lib\libmcu.a(hx330x_usb.o)
 .bss           0x020103b0        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .bss           0x020103b0        0x4 ..\lib\libmcu.a(hx330x_emi.o)
 .bss           0x020103b4      0x180 ..\lib\libisp.a(hal_isp.o)
 .bss           0x02010534        0x0 ..\lib\libhusb.a(husb_enum.o)
 .bss           0x02010534       0x4c ..\lib\libjpg.a(hal_jpg.o)
 .bss           0x02010580       0x98 ..\lib\libjpg.a(hal_step_jpg.o)
 .bss           0x02010618       0x1a ..\lib\liblcd.a(hal_lcd.o)
 .bss           0x02010632        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 *fill*         0x02010632        0x2 
 .bss           0x02010634       0x28 ..\lib\liblcd.a(hal_lcdrotate.o)
 .bss           0x0201065c       0x28 ..\lib\liblcd.a(hal_lcdUi.o)
 .bss           0x02010684        0x8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .bss           0x0201068c        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .bss           0x0201068c       0x60 ..\lib\libmultimedia.a(api_multimedia.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(gif_dec.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .bss           0x020106ec        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .bss           0x020106ec        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(COMMON)
 COMMON         0x020106ec       0x20 obj\Debug\dev\battery\src\battery_api.o
                0x020106ec                WAV_TYPE_E
                0x020106f0                SENSOR_FILTER_TYPE
                0x020106f4                USB_CH
                0x020106f8                UVC_CACHE_STA
                0x020106fc                SDCON0_T
                0x02010700                SDCON1_T
                0x02010704                UVC_FSTACK_STA
                0x02010708                CHANNEL_EXCHANGE_E
 COMMON         0x0201070c       0x14 obj\Debug\dev\fs\src\fs_api.o
                0x0201070c                fs_exfunc
 COMMON         0x02010720        0x8 obj\Debug\dev\gsensor\src\gsensor_api.o
                0x02010720                gsensor_ctl
 COMMON         0x02010728       0x78 obj\Debug\dev\key\src\key_api.o
                0x02010728                dev_key_tab
 COMMON         0x020107a0        0x2 obj\Debug\dev\sensor\src\sensor_api.o
                0x020107a0                dw9714p
 *fill*         0x020107a2        0x2 
 COMMON         0x020107a4       0x20 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x020107a4                tp_api_t
 COMMON         0x020107c4      0x29c obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x020107c4                usb_dev_ctl
 COMMON         0x02010a60     0x1434 obj\Debug\dev\usb\husb\src\husb_api.o
                0x02010a60                husb_ctl
 COMMON         0x02011e94        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x02011e94                usensor_handle
 COMMON         0x02011e98        0x4 obj\Debug\mcu\xos\xos.o
                0x02011e98                XOSNesting
 COMMON         0x02011e9c       0xf4 obj\Debug\multimedia\video\video_record.o
                0x02011e9c                mediaVideoCtl
 COMMON         0x02011f90       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x02011f90                ui_draw_ctrl
 COMMON         0x02011fa0      0x148 obj\Debug\app\app_common\src\app_init.o
                0x02011fa0                SysCtrl
 COMMON         0x020120e8        0x1 obj\Debug\app\app_common\src\main.o
                0x020120e8                flash_success
 *fill*         0x020120e9        0x3 
 COMMON         0x020120ec        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                0x020120ec                autoPowerOff_config
 COMMON         0x020120f0        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
                0x020120f0                videoResolution_config
 COMMON         0x020120f4       0x30 obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
                0x020120f4                batChargeOp
 COMMON         0x02012124        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02012124                task_play_audio_stat
 COMMON         0x02012128       0x44 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02012128                playVideoOp
 COMMON         0x0201216c       0x20 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0201216c                recordPhotoOp
 COMMON         0x0201218c       0x64 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x0201218c                sd_update_op
 COMMON         0x020121f0        0xc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x020121f0                usbDeviceOp
 COMMON         0x020121fc      0x500 ..\lib\libmcu.a(hx330x_isp.o)
                0x020121fc                RGB_GMMA_Tab
                0x020124fc                Y_GMA_Tab
 COMMON         0x020126fc       0x14 ..\lib\libmcu.a(hx330x_lcd.o)
                0x020126fc                hx330x_lcdISR
 COMMON         0x02012710       0x14 ..\lib\libmcu.a(hx330x_rtc.o)
                0x02012710                rc128k_div
                0x02012714                rtcSecondISR
                0x02012718                rtcAlamISR
                0x0201271c                rc128k_rtc_cnt
                0x02012720                rc128k_timer_cnt
 COMMON         0x02012724        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02012724                smph_dmacopy
 COMMON         0x02012728       0x10 ..\lib\libmcu.a(hx330x_timer.o)
                0x02012728                hx330x_timerISR
 COMMON         0x02012738       0x68 ..\lib\liblcd.a(hal_lcd.o)
                0x02012738                lcd_show_ctrl
 COMMON         0x020127a0      0x3ac ..\lib\liblcd.a(hal_lcdMem.o)
                0x020127a0                dfm
 COMMON         0x02012b4c        0x4 ..\lib\libmultimedia.a(gif_dec.o)
                0x02012b4c                gif_dec
 *(.big_buffer*)
 *(._sdram_buf_)
 ._sdram_buf_   0x02012b50     0x8044 obj\Debug\dev\fs\src\fs_api.o
                0x02012b50                work_fatfs
                0x0201abc0                _sdram_remian_addr = (ALIGN (0x40) + 0x0)
                0x0000a800                _text_lma = (((LOADADDR (.on_sdram) + SIZEOF (.on_sdram)) + 0x1ff) & 0xfffffe00)
                0x0600a800                _text_vma = (ORIGIN (flash) + _text_lma)

.text           0x0600a800    0x88028 load address 0x0000a800
 *(.text*)
 .text          0x0600a800      0x24c obj\Debug\dev\battery\src\battery_api.o
                0x0600a800                dev_battery_ioctrl
                0x0600a984                dev_battery_init
 .text          0x0600aa4c      0x254 obj\Debug\dev\dev_api.o
                0x0600aa4c                dev_api_node_init
                0x0600ab70                dev_open
                0x0600ac30                dev_ioctrl
 .text          0x0600aca0      0x244 obj\Debug\dev\fs\src\diskio.o
                0x0600aca0                get_fattime
                0x0600ad20                disk_status
                0x0600ad70                disk_initialize
                0x0600adc4                disk_read
                0x0600ae20                disk_write
                0x0600ae7c                disk_ioctl
 .text          0x0600aee4     0x8084 obj\Debug\dev\fs\src\ff.o
                0x0600eccc                f_mount
                0x0600ed7c                f_open
                0x0600f2cc                f_read
                0x0600f604                f_write
                0x0600f994                f_sync
                0x0600fbf8                f_close
                0x0600fc44                f_ftime
                0x0600fca0                f_lseek
                0x06010424                f_opendir
                0x0601057c                f_closedir
                0x060105b8                f_readdir
                0x0601065c                f_findnext
                0x06010700                f_findfirst
                0x06010754                f_stat
                0x060107e8                f_getfree
                0x06010b64                f_truncate
                0x06010cbc                f_unlink
                0x06010e70                f_mkdir
                0x06011160                f_rename
                0x06011444                f_chmod
                0x0601152c                f_utime
                0x06011610                f_expand
                0x060118f4                f_mkfs
                0x06012bec                FEX_getlink_clust
                0x06012c24                f_merge
                0x06012dd4                _f_bound
 .text.unlikely
                0x06012f68       0x30 obj\Debug\dev\fs\src\ff.o
 .text          0x06012f98      0x204 obj\Debug\dev\fs\src\ffunicode.o
                0x06012f98                ff_uni2oem
                0x06013010                ff_oem2uni
                0x06013088                ff_wtoupper
 .text          0x0601319c      0xa34 obj\Debug\dev\fs\src\fs_api.o
                0x0601319c                fs_exfunc_init
                0x060131f4                fs_nodeinit
                0x06013224                fs_mount
                0x06013320                fs_open
                0x060133f8                fs_close
                0x06013470                fs_read
                0x060134e8                fs_write
                0x060135d8                fs_seek
                0x060136a4                fs_getcltbl
                0x060136f4                fs_getclusize
                0x06013744                fs_mkdir
                0x06013770                fs_alloc
                0x060137e4                fs_sync
                0x0601384c                fs_merge
                0x060138d8                fs_bound
                0x06013964                fs_getclustersize
                0x06013988                fs_size
                0x060139d4                fs_pre_size
                0x06013a20                fs_tell
                0x06013a6c                fs_free_size
                0x06013af0                fs_check
                0x06013b18                fs_getStartSector
                0x06013b74                fs_ftime
 .text          0x06013bd0      0x36c obj\Debug\dev\gsensor\src\gsensor_api.o
                0x06013bd0                gsensor_iic_enable
                0x06013bf4                gsensor_iic_disable
                0x06013c14                gSensorGetName
                0x06013c48                dev_gSensor_Init
                0x06013d68                dev_gSensor_ioctrl
 .text          0x06013f3c      0x550 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .text          0x0601448c      0x80c obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .text          0x06014c98      0x4fc obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .text          0x06015194      0x12c obj\Debug\dev\ir\src\ir_api.o
                0x06015194                dev_ir_init
                0x060151dc                dev_ir_ioctrl
 .text          0x060152c0      0x57c obj\Debug\dev\key\src\key_api.o
                0x060152c0                dev_key_init
                0x06015414                dev_key_ioctrl
                0x060157fc                getKeyADCvalue
                0x0601581c                getKeyCurEvent
 .text          0x0601583c      0x2b0 obj\Debug\dev\lcd\src\lcd_api.o
                0x0601583c                lcd_initTab_config
                0x0601594c                LcdGetName
                0x06015968                dev_lcd_init
                0x06015a18                dev_lcd_ioctrl
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_GC9307.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_JD9853.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .text          0x06015aec        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .text          0x06015aec      0x104 obj\Debug\dev\led\src\led_api.o
                0x06015aec                dev_led_init
                0x06015b04                dev_led_ioctrl
 .text          0x06015bf0      0x1f4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                0x06015bf0                dev_led_pwm_init
                0x06015c78                dev_led_pwm_ioctrl
 .text          0x06015de4      0x30c obj\Debug\dev\nvfs\src\nvfs_api.o
                0x06015de4                nv_port_read
                0x06015e04                nv_init
                0x06015f34                nv_uninit
                0x06015f4c                nv_configAddr
                0x06015fc4                nv_open
                0x06016040                nv_size
                0x060160c0                nv_read
 .text          0x060160f0     0x1f0c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x06016978                nv_dir_read
                0x06016abc                nv_dir_readfirst
                0x06016b2c                nv_dir_readnext
                0x06016b80                nv_jpg_ex_force_init
                0x06016ba0                nv_jpg_ex_init
                0x06016c7c                nv_jpg_formart_init
                0x06016e4c                nv_jpg_init
                0x0601708c                nv_jpg_uinit
                0x060170f4                nv_jpg_format
                0x06017184                nv_jpg_open
                0x060174bc                nv_jpg_change_lock
                0x06017598                nv_jpg_close
                0x06017670                nv_jpgfile_read
                0x06017870                nv_jpgfile_seek
                0x06017a8c                nv_jpgfile_delete
                0x06017b84                nv_jpgfile_size
                0x06017bc4                nvjpg_free_size
                0x06017bf8                nv_jpgfile_write
                0x06017fcc                nvjpg_free_dir
 .text          0x06017ffc      0xcb4 obj\Debug\dev\sd\src\sd_api.o
                0x0601881c                sd_api_init
                0x06018a68                sd_api_getNextLBA
                0x06018a88                sd_api_Uninit
                0x06018aa8                sd_api_lock
                0x06018ae8                sd_api_unlock
                0x06018b14                sd_api_CardState_Set
                0x06018b34                sd_api_CardState_Get
                0x06018b54                sd_api_GetBusWidth
                0x06018b74                sd_api_Capacity
                0x06018b94                sd_api_speed_debg
                0x06018be0                dev_sdc_init
                0x06018c10                dev_sdc_ioctrl
 .text          0x06018cb0      0xcb8 obj\Debug\dev\sensor\src\sensor_api.o
                0x06018cb0                sensor_iic_write
                0x06018d1c                sensor_iic_read
                0x06018d9c                sensor_rgbgamma_tab_load
                0x06018e64                sensor_ygamma_tab_load
                0x06018f2c                sensor_lsc_tab_load
                0x06018f7c                SensorGetName
                0x06018f98                sensor_pwdn_cfg
                0x06019014                sensor_reset_cfg
                0x06019090                sensor_on_io_cfg
                0x06019114                sensor_off_io_cfg
                0x06019168                dev_sensor_init
                0x060192bc                dev_sensor_ioctrl
                0x0601994c                get_sensor_api
 .text          0x06019968        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .text          0x06019968       0xb4 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .text          0x06019a1c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .text          0x06019a1c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .text          0x06019a1c        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .text          0x06019a1c      0x1b8 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H63P.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .text          0x06019bd4        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .text          0x06019bd4      0x504 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x06019c34                dev_touchpanel_Init
                0x06019dd0                dev_touchpanel_ioctrl
 .text          0x0601a0d8      0x1ec obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0601a0d8                tp_icnt81_getPoint
 .text          0x0601a2c4      0x74c obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                0x0601a708                tp_iic_init
                0x0601a77c                tp_iic_config
                0x0601a7a0                tp_iic_write
                0x0601a8b0                tp_iic_read
 .text          0x0601aa10      0x374 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0601aa10                tp_ns2009_Match
                0x0601aab4                tp_ns2009_getPoint
 .text          0x0601ad84      0x3e8 obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x0601addc                dusb_api_online
                0x0601adfc                dusb_api_Init
                0x0601aeec                dusb_api_Uninit
                0x0601af2c                dusb_api_offline
                0x0601af64                dusb_api_Process
                0x0601afd4                dev_dusb_init
                0x0601b010                dev_dusb_ioctrl
 .text          0x0601b16c      0xc5c obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0601b204                dusb_ep0_class_ram_wr
                0x0601b23c                dusb_ep0_class_addr_wr
                0x0601b388                dusb_stall_ep
                0x0601b414                dusb_ep0_tx
                0x0601b490                dusb_ep0_recieve_set
                0x0601b4c8                dusb_ep0_process
                0x0601bcb0                dusb_ep0_cfg
                0x0601bd04                dusb_cfg_reg
 .text          0x0601bdc8     0x1148 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0601bf40                msc_epx_cfg
                0x0601bfdc                dusb_WriteToMem
                0x0601c01c                dusb_ReadFromMem
                0x0601c040                rbc_mem_read
                0x0601c0b0                rbc_mem_write
                0x0601c128                rbc_mem_rxfunc
                0x0601c164                sdk_returnmask
                0x0601c29c                cbw_updatartc
                0x0601c2e4                mscCmd_ufmod
                0x0601c330                sent_csw
                0x0601c40c                mscCmd_Read
                0x0601c50c                mscCmd_Write
                0x0601c61c                scsi_cmd_analysis
                0x0601cb78                get_cbw
                0x0601cdc4                rbc_rec_pkg
                0x0601ce50                rbc_process
 .text          0x0601cf10      0x424 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
                0x0601d238                pqtool_get_sdk_info
 .text          0x0601d334      0x57c obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0601d334                uac_set_volume
                0x0601d380                uac_set_mute
                0x0601d3c8                uac_isr_process
                0x0601d4cc                uac_epx_cfg
                0x0601d540                uac_get_volume
                0x0601d5d0                uac_unit_ctl_hal
                0x0601d6a4                UacHandleToStreaming
                0x0601d740                uac_start
                0x0601d804                UacReceiveSetSamplingFreqCallback
                0x0601d854                uac_stop
 .text          0x0601d8b0      0x8e4 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0601d8b0                uvc_pic_callback
                0x0601d8d8                unitsel_set
                0x0601d914                uvc_video_probe_control_callback
                0x0601d948                uvc_still_probe_control_callback
                0x0601d97c                uvc_epx_cfg
                0x0601dae0                uvc_unit_ctl_hal
                0x0601db88                uvc_video_probe_control
                0x0601dbd0                uvc_still_probe_control
                0x0601dc18                uvc_still_trigger_control
                0x0601dc4c                uvc_probe_ctl_hal
                0x0601dcc8                uvc_start
                0x0601dd6c                uvc_stop
                0x0601ddd0                uvc_is_start
                0x0601ddf0                uvc_pic_sanp
                0x0601de10                uvc_header_fill
                0x0601def8                uvc_isr_process
                0x0601e088                uvc_process
 .text          0x0601e194      0x810 obj\Debug\dev\usb\husb\src\husb_api.o
                0x0601e194                husb_api_u20_remove
                0x0601e1f0                husb_api_u11_remove
                0x0601e464                husb_api_handle_get
                0x0601e494                husb_api_init
                0x0601e5e0                husb_api_devicesta
                0x0601e614                husb_api_msc_try_tran
                0x0601e650                dev_husb_io_power_set
                0x0601e738                dev_husb_init
                0x0601e7d8                dev_husb_ioctrl
 .text          0x0601e9a4     0x10cc obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                0x0601e9a4                udisk_cap
                0x0601e9c8                udisk_online
                0x0601e9f8                husb_msc_init
                0x0601eb74                epbulk20_send_dat
                0x0601ed20                epbulk20_recieve_dat
                0x0601ee90                epbulk11_send_dat
                0x0601f00c                epbulk11_recieve_dat
                0x0601f170                epbulk_send_dat
                0x0601f1c0                epbulk_receive_dat
                0x0601f210                cbw_init
                0x0601f25c                rbc_read_lba
                0x0601f360                rbc_write_lba
                0x0601f454                spc_inquiry
                0x0601f508                spc_test_unit_rdy
                0x0601f5a8                spc_request_sense
                0x0601f65c                rbc_read_capacity
                0x0601f7ec                spc_StartStopUnit
                0x0601f874                enum_mass_dev
 .text          0x0601fa70      0x4cc obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x0601fa70                husb_api_handle_reg
                0x0601fa90                husb_api_usensor_tran_sta
                0x0601fac8                husb_api_usensor_atech_sta
                0x0601fb00                husb_api_usensor_res_get
                0x0601fb48                husb_api_usensor_res_type_is_mjp
                0x0601fb84                husb_api_usensor_res_type_is_yuv
                0x0601fbc0                husb_api_astern_set
                0x0601fc14                husb_api_astern_get
                0x0601fc4c                husb_api_detech_check
                0x0601fc84                husb_api_usensor_linkingLcd
                0x0601fcb0                husb_api_usensor_relinkLcd_reg
                0x0601fcdc                husb_api_usensor_dcdown
                0x0601fd14                husb_api_usensor_detech
                0x0601fd80                husb_api_usensor_asterncheck
                0x0601fdb4                husb_api_usensor_asternset
                0x0601fe04                husb_api_usensor_frame_read
                0x0601fe70                husb_api_usensor_notran_check
 .text          0x0601ff3c     0x1108 obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x060209ac                huvc_cache_dcd_down
                0x06020a4c                husb_uvc_frame_read
                0x06020b9c                husb_uvc_atech_codec
                0x06020c58                husb_uvc_dcd_cache_get
                0x06020c7c                husb_uvc_init
                0x06020f2c                husb_uvc_linking
                0x06020fc0                husb_uvc_relink_register
                0x06020ff0                husb_uvc_detech
 .text          0x06021044       0xcc obj\Debug\hal\src\hal_adc.o
                0x06021044                hal_adcInit
                0x06021084                hal_adcRead
                0x060210b8                hal_adcSetChannel
 .text          0x06021110      0xaa8 obj\Debug\hal\src\hal_auadc.o
                0x06021440                hal_auadc_stamp_out
                0x06021460                hal_auadc_stamp_next
                0x06021480                hal_auadcInit
                0x060214d4                hal_auadcMemInit
                0x06021598                hal_auadcMemInitB
                0x06021620                hal_auadc_pcmsize_get
                0x06021640                hal_auadcMemUninit
                0x0602169c                hal_auadcMemUninitB
                0x060216f0                hal_auadc_cnt
                0x0602171c                hal_auadcmutebuf_get
                0x0602173c                hal_auadcStart
                0x060218dc                hal_auadcStartB
                0x06021984                hal_auadcBufferGet
                0x060219f0                hal_auadcBufferRelease
                0x06021a18                hal_auadcBufferGetB
                0x06021a8c                hal_auadcBufferReleaseB
                0x06021ab4                hal_auadcStop
                0x06021af8                hal_adcBuffer_prefull
                0x06021b38                hal_adcBufferB_prefull
                0x06021b78                hal_adc_volume_set
                0x06021b98                hal_adc_volume_setB
 .text          0x06021bb8      0x594 obj\Debug\hal\src\hal_csi.o
                0x06021bb8                hal_csi_save_raw_isr
                0x06021c94                hal_SensorRatioResolutionGet
                0x06021d7c                hal_csi_init
                0x06021f28                hal_csi_save_raw_start
                0x0602212c                hal_csi_save_raw_state
 .text          0x0602214c      0x1e8 obj\Debug\hal\src\hal_dac.o
                0x0602214c                hal_dacInit
                0x060221a8                hal_dacPlayInit
                0x06022230                hal_dacPlayStart
                0x0602228c                hal_dacPlayStop
                0x060222b4                hal_dacSetVolume
                0x06022314                hal_dacCallBackRegister
 .text          0x06022334      0x42c obj\Debug\hal\src\hal_dmauart.o
                0x06022334                hal_dmaUartRxOverWait
                0x060223c8                hal_dmaUartRxDataOut
                0x06022464                hal_dmaUartIRQHandler
                0x060224ec                hal_dmauartInit
                0x06022594                hal_dmauartTxDma
                0x060225f4                hal_dmauartTxDmaKick
                0x0602264c                hal_dmauartTest
 .text          0x06022760        0x0 obj\Debug\hal\src\hal_eeprom.o
 .text          0x06022760       0xa8 obj\Debug\hal\src\hal_gpio.o
                0x06022760                hal_gpioInit_io1d1
 .text          0x06022808      0xa94 obj\Debug\hal\src\hal_iic.o
                0x06022808                hal_iic0Init
                0x06022838                hal_iic0Uninit
                0x06022858                hal_iic08bitAddrWriteData
                0x06022908                hal_iic08bitAddrReadData
                0x06022990                hal_iic08bitAddrWrite
                0x06022a20                hal_iic08bitAddrRead
                0x06022ac0                hal_iic016bitAddrWriteData
                0x06022b38                hal_iic016bitAddrReadData
                0x06022bcc                hal_iic016bitAddrWrite
                0x06022c68                hal_iic016bitAddrRead
                0x06022d14                hal_iic1IOShare
                0x06022d44                hal_iic1IOShareCheck
                0x06022d8c                hal_iic1Init
                0x06022dcc                hal_iic1Uninit
                0x06022e04                hal_iic18bitAddrWriteData
                0x06022e74                hal_iic18bitAddrReadData
                0x06022f00                hal_iic18bitAddrWrite
                0x06022f94                hal_iic18bitAddrRead
                0x06023038                hal_iic116bitAddrWriteData
                0x060230b4                hal_iic116bitAddrReadData
                0x0602314c                hal_iic116bitAddrWrite
                0x060231ec                hal_iic116bitAddrRead
 .text          0x0602329c       0x34 obj\Debug\hal\src\hal_int.o
                0x0602329c                hal_intInit
 .text          0x060232d0     0x13b4 obj\Debug\hal\src\hal_lcdshow.o
                0x060232d0                hal_lcdSetCsiCrop
                0x06023380                hal_lcdGetCsiCrop
                0x06023408                hal_lcdSetPlayScalerCheckDone
                0x06023450                hal_lcdSetPlayScaler
                0x06023534                hal_lcdVideoSetRotate
                0x06023674                hal_lcdVideoSetRotate180
                0x06023758                hal_lcdUiSetRotate
                0x06023818                hal_lcdSetRatio
                0x06023990                hal_lcdVideoNeedRotateGet
                0x060239b8                hal_lcdUiNeedRotateGet
                0x060239e0                hal_lcdSetBufYUV
                0x06023a74                hal_lcdSetBufYUV_2
                0x06023af8                hal_lcdSetWINAB
                0x06023bcc                hal_lcdWinEnablePreSet
                0x06023bec                hal_lcdSetWinEnable
                0x06023c24                lcd_struct_get
                0x06023c44                hal_lcdLCMPowerOff
                0x06023cc8                hal_lcd_decwin_done
                0x06023f88                hal_lcd_encwin_done
                0x06024038                hal_CSI_lcdFrameEndCallback
                0x060244b4                hal_lcd_pause_set
                0x060244f4                hal_lcd_pause_sta_get
                0x06024534                hal_lcdSetGamma
 .text          0x06024684      0x154 obj\Debug\hal\src\hal_md.o
                0x060246ac                hal_mdInit
                0x06024758                hal_mdEnable
                0x060247a0                hal_mdCheck
 .text          0x060247d8     0x2a98 obj\Debug\hal\src\hal_mjpAEncode.o
                0x060257d0                hal_mjpA_Sizecalculate
                0x060257fc                hal_jA_fcnt_mnt
                0x06025850                hal_mjpA_EncodeInit
                0x060258dc                hal_mjpA_LineBuf_MenInit
                0x06025a28                hal_mjpA_buf_MenInit
                0x06025b1c                hal_mjpA_linebufUninit
                0x06025b6c                hal_mjpA_MemUninit
                0x06025bbc                hal_mjpA_EncodeUninit
                0x06025c60                hal_mjpAEncodeManualResume
                0x06025d10                hal_mjpAEncodePhotoResumeLLPKG
                0x06025da4                hal_mjpAEncodePhotoResumeRam
                0x06025edc                hal_mjpAEncodeStepSubManualResume
                0x06025f8c                hal_mjpAEncodeStepManualResume
                0x06026174                hal_mjpA_Enc_Video_Start
                0x06026488                hal_mjpA_Enc_Photo_Start
                0x06026a0c                hal_mjpA_LineBuf_lcd_MenInit
                0x06026b00                hal_mjpA_EncLcd_Video_Start
                0x06026d50                hal_mjpA_EncLcd_Photo_Start
                0x06027010                hal_mjpA_photo_encode_type
                0x06027030                hal_mjpA_RawBufferfree
                0x06027058                hal_mjpA_RawBufferGet
                0x06027154                hal_mjpA_RkgBufferGet
                0x060271f8                hal_mjpA_Buffer_prefull
                0x06027234                hal_mjpA_Buffer_halffull
 .text          0x06027270      0x880 obj\Debug\hal\src\hal_mjpBEncode.o
                0x06027270                hal_mjpBEncodeKickManual
                0x06027368                hal_mjpB_Sizecalculate
                0x06027390                hal_jB_fcnt_mnt
                0x060273e4                hal_mjpBEnc_state
                0x06027410                hal_mjpBEncodeDoneCfg
                0x06027434                hal_mjpBEncodeDoneManual
                0x06027540                hal_mjpB_LineBuf_MenInit
                0x060275dc                hal_mjpB_LineBuf_cfg
                0x06027600                hal_mjpB_LineBuf_get
                0x0602762c                hal_mjpB_buf_MenInit
                0x060276f0                hal_mjpB_MemUninit
                0x06027750                hal_mjpB_usb_resolution_set
                0x06027774                hal_mjpB_DecodeODMA1En
                0x060277a8                hal_mjpB_Enc_Start
                0x06027920                hal_mjpB_Enc_Stop
                0x06027954                hal_mjpB_RawBufferfree
                0x0602797c                hal_mjpB_RawBufferGet
                0x06027a78                hal_mjpB_Buffer_prefull
                0x06027ab4                hal_mjpB_Buffer_halffull
 .text          0x06027af0      0xedc obj\Debug\hal\src\hal_mjpDecode.o
                0x06027af0                hal_mjpHeaderParse
                0x060281a4                hal_mjpDecodeIsYUV422
                0x060281c4                hal_mjpDecodeGetResolution
                0x06028208                hal_mjpDecodeSetResolution
                0x0602822c                hal_mjpegDecodePicture_packetResume
                0x060282ac                hal_mjpDecodeBusyCheck
                0x060282cc                hal_mjpDecodeErrorCheck
                0x060282ec                hal_mjpDecodeStop
                0x0602830c                hal_mjpDecodeReset
                0x06028338                hal_mjpDecodePicture
                0x060283c8                hal_mjpegDecodePicture_noisr
                0x06028454                hal_mjpegDecodePicture_packet
                0x0602850c                hal_mjpDecodeParse
                0x0602853c                hal_mjpDecodeOneFrame
                0x060285b4                hal_mjpDecodeOneFrame_Ext
                0x0602864c                hal_mjpDecodeRestart_Ext
                0x060286b8                hal_mjpDecodeOneFrame_Fast
                0x060287b4                hal_mjpDecodeMiniSize
                0x06028938                hal_mjpDecodeODma1Cfg
                0x0602895c                hal_BackRecDecodeStatusCheck
 .text          0x060289cc      0xc48 obj\Debug\hal\src\hal_rtc.o
                0x060289f8                hal_rtcCallBackRegister
                0x06028a58                hal_rtcCallBackRelease
                0x06028a94                hal_rtcUninit
                0x06028ad0                hal_rtcTimeGet
                0x06028aec                hal_rtcTimeGetExt
                0x06028b20                hal_rtcSecondGet
                0x06028b40                hal_rtcTime2String
                0x06028df4                hal_rtcTime2StringExt
                0x06028e58                hal_rtcLeapYear
                0x0602907c                hal_rtcTime
                0x06029190                hal_rtcInit
                0x060292bc                hal_rtcSecondSet
                0x060292f8                hal_rtcValue
                0x06029408                hal_rtcTimeSet
                0x060294ac                hal_rtcAlarmSet
                0x06029514                hal_rtcAlarmSetExt
                0x060295b8                hal_rtcAlarmStatusGet
                0x060295f4                hal_rtcTrimCallBack
 .text          0x06029614        0x0 obj\Debug\hal\src\hal_spi.o
 .text          0x06029614      0x504 obj\Debug\hal\src\hal_spi1.o
                0x06029614                hal_spi1DmaCallback
                0x060296a8                hal_spi1DmaDoneCheck
                0x06029728                hal_spi1Init
                0x06029780                hal_spi1SendByte
                0x060297a4                hal_spi1RecvByte
                0x060297c4                hal_spi1SendDmaKick
                0x0602985c                hal_spi1SendDma
                0x060298b4                hal_spi1RecvDmaKick
                0x06029950                hal_spi1RecvDma
                0x060299a8                hal_spi1_test
 .text          0x06029b18      0x214 obj\Debug\hal\src\hal_stream.o
                0x06029b18                hal_streamInit
                0x06029c74                hal_streamMallocDrop
                0x06029d14                hal_stream_size
 .text          0x06029d2c      0x9ac obj\Debug\hal\src\hal_sys.o
                0x0602a028                hal_sysMemPrint
                0x0602a134                hal_sysMemMalloc
                0x0602a24c                hal_sysMemMallocLast
                0x0602a378                hal_sysMemFree
                0x0602a554                hal_sysMemRemain
                0x0602a5ac                hal_sysInit
 .text          0x0602a6d8       0xe0 obj\Debug\hal\src\hal_timer.o
                0x0602a6d8                hal_timerEnable
                0x0602a710                hal_timerTickEnable
                0x0602a744                hal_timerPWMStart
 .text          0x0602a7b8      0x540 obj\Debug\hal\src\hal_uart.o
                0x0602a81c                hal_uartIOShare
                0x0602a858                hal_uartIOShareCheck
                0x0602a8a4                hal_uartInit
                0x0602a8fc                hal_uartSendData
                0x0602a920                hal_uartRXIsrRegister
                0x0602a940                uart_PutChar_n
                0x0602a974                uart_PutStr
                0x0602a9b4                uart_Put_hex
                0x0602aae0                uart_Put_udec
                0x0602ab74                uart_Put_dec
                0x0602ac1c                uart_PrintfBuf
                0x0602acb4                hal_uartPrintString
 .text          0x0602acf8      0xa28 obj\Debug\hal\src\hal_watermark.o
                0x0602ad60                hal_watermarkInit
                0x0602ae10                hal_watermarkClose
                0x0602aea4                hal_watermarkClear
                0x0602af24                hal_watermarkOpen
                0x0602afc4                hal_watermarkColor
                0x0602b024                hal_watermarkAddr
                0x0602b03c                hal_watermarkSize
                0x0602b088                hal_watermarkPosition
                0x0602b0d4                hal_watermarkCallbackRegister
                0x0602b11c                hal_watermarkRam
                0x0602b398                hal_watermarkEnable
                0x0602b4e8                hal_jpg_watermark_init
                0x0602b578                hal_jpg_watermark_uinit
                0x0602b5bc                hal_jpg_watermarkStart
                0x0602b6b0                hal_jpgB_watermarkPos_Adjust
 .text          0x0602b720        0x0 obj\Debug\hal\src\hal_wdt.o
 .text          0x0602b720        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .text          0x0602b720        0x0 obj\Debug\mcu\xos\xmbox.o
 .text          0x0602b720      0x404 obj\Debug\mcu\xos\xmsgq.o
                0x0602b720                XMsgQInit
                0x0602b760                XMsgQCreate
                0x0602b820                XMsgQDestory
                0x0602b870                XMsgQFlush
                0x0602b8c8                XMsgQPost
                0x0602b978                XMsgQPostFront
                0x0602ba28                XMsgQPend
                0x0602bae8                XMsgQCheck
 .text          0x0602bb24      0x14c obj\Debug\mcu\xos\xos.o
                0x0602bb24                XOSInit
                0x0602bb60                XOSTickService
                0x0602bbd0                XOSTimeGet
                0x0602bbf0                XOSTimeDly
                0x0602bc50                XOSRandom
 .text          0x0602bc70      0x1b4 obj\Debug\mcu\xos\xwork.o
                0x0602bc70                XWorkInit
                0x0602bcb0                XWorkCreate
                0x0602bd70                XWorkDestory
                0x0602bdb8                XWorkService
 .text          0x0602be24      0x994 obj\Debug\multimedia\audio\audio_playback.o
                0x0602be24                audioPlaybackInit
                0x0602be6c                audioPlaybackParse
                0x0602bf5c                audioPlaybackStop
                0x0602c064                audioPlaybackUninit
                0x0602c09c                audioPlaybackStart
                0x0602c46c                audioPlaybackPause
                0x0602c4a8                audioPlaybackFirstPause
                0x0602c504                audioPlaybackResume
                0x0602c56c                audioPlaybackGetStatus
                0x0602c58c                audioPlaybackGetTime
                0x0602c608                audioPlaybackSetVolume
                0x0602c65c                audioPlaybackGetVolume
                0x0602c67c                audioPlaybackService
 .text          0x0602c7b8      0x528 obj\Debug\multimedia\audio\audio_record.o
                0x0602c7ec                audioRecordInit
                0x0602c8b4                audioRecordUninit
                0x0602c908                audioRecordStop
                0x0602c9b4                audioRecordStart
                0x0602caf0                audioRecordPuase
                0x0602cb20                audioRecordResume
                0x0602cb50                audioRecordGetStatus
                0x0602cb70                audioRecordSetStatus
                0x0602cb90                audioRecordGetTime
                0x0602cbb0                audioRecordService
 .text          0x0602cce0      0xbf4 obj\Debug\multimedia\image\image_decode.o
                0x0602cce0                imageDecodeSubCheck
                0x0602ce5c                imageDecodeStart
                0x0602d424                imageDecodeSpiStart
                0x0602d634                imageDecodeGetResolution
                0x0602d658                imageDecodeDirect
 .text          0x0602d8d4      0xc8c obj\Debug\multimedia\image\image_encode.o
                0x0602d8d4                imageEncodeInit
                0x0602d900                imageEncodeUninit
                0x0602d924                imageEncodeStart
                0x0602e1f8                imageEncodeStartB
                0x0602e344                imageEncodeToSpi
 .text          0x0602e560      0x158 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x0602e560                jpg_encode
 .text          0x0602e6b8     0x192c obj\Debug\multimedia\video\video_playback.o
                0x0602e7ec                videoPlaybackClear
                0x0602e850                videoPlaybackInit
                0x0602e930                videoPlaybackDecodeWait
                0x0602e97c                videoPlaybackStop
                0x0602ea88                videoPlaybackUninit
                0x0602ecfc                videoPlaybackPause
                0x0602ed78                videoPlaybackResume
                0x0602eddc                videoPlaybackGetStatus
                0x0602edfc                videoPlaybackSetVolume
                0x0602ee4c                videoPlaybackGetVolume
                0x0602ee6c                videoPlaybackGetTime
                0x0602eea4                videoPlaybabkGetArg
                0x0602eec0                videoGetFirstFrame
                0x0602f09c                videoDecodeFirstFrame
                0x0602f1b0                videoPlaybackService
                0x0602fa4c                videoPlaybackStart
                0x0602fe58                videoPlaybackFastForward
                0x0602ff20                videoPlaybackFastBackward
 .text          0x0602ffe4     0x166c obj\Debug\multimedia\video\video_record.o
                0x0602ffe4                videoRecordInit
                0x06030154                videoRecordUninit
                0x060301a0                videoRecordFileStart
                0x06030358                videoRecordFileStop
                0x06030434                videoRecordFileError
                0x060304a0                videoRecordError
                0x06030518                videoRecordStart
                0x06030730                videoRecordGetTimeSec
                0x06030774                videoRecordGetStatus
                0x06030794                videoRecordJunkSync
                0x060307d0                videoRecordStop
                0x06030890                videoRecordRestart
                0x06030a38                videoRecordFrameProcess
                0x06030f84                videoRecordService
                0x060313d4                videoRecordCmdSet
                0x060314ac                videoRecordSizePreSec
                0x060315cc                videoRecordTakePhotoCfg
                0x060315f0                videoRecordTakePhotoStatus
                0x06031610                videoRecordSetPhotoStatus
                0x06031630                videoRecordTakePhotoFd
 .text          0x06031650     0x1a1c obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                0x06031744                filelist_listFlush
                0x06031848                filelist_api_Init
                0x06031874                filelist_api_nodecreate
                0x06031bdc                filelist_api_nodedestory
                0x06031cf8                filelist_api_scan
                0x06031ef4                filenode_api_CountGet
                0x06031f24                filelist_api_CountGet
                0x06031f64                filelist_api_MaxCountGet
                0x06031f94                filelist_GetFileNameByIndex
                0x06032010                filelist_GetFileFullNameByIndex
                0x06032174                filelist_GetFileShortNameByIndex
                0x06032294                filelist_GetFileIndexByIndex
                0x06032340                filelist_findFirstFileName
                0x060323c0                filelist_findFileNameByFname
                0x06032494                filelist_delFileByIndex
                0x060325ac                filelist_delFileByFname
                0x06032664                filelist_listDelAll
                0x060327d8                filelist_createNewFileFullName
                0x06032840                filelist_createNewFileFullNameByFname
                0x06032880                filenode_addFileByFname
                0x060328c8                filenode_filefullnameLock
                0x0603299c                filenode_filefullnameUnlock
                0x06032a70                filenode_fnameLockByIndex
                0x06032ae8                filenode_fnameUnlockByIndex
                0x06032b64                filelist_fnameChecklockByIndex
                0x06032bf0                filenode_parentdir_get
                0x06032c98                filelist_GetLrcFileFullNameByIndex
                0x06032d40                filelist_SpaceCheck
 .text          0x0603306c      0xedc obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                0x0603306c                filelist_NameChangeSufType
                0x060330cc                filenode_fname_check
                0x06033108                filenode_fname_createNew
                0x0603326c                filenode_filename_CreateByFname
                0x060335bc                filenode_filefullname_CreateByFname
                0x06033638                filenode_AddFileByFname
                0x060337f4                filenode_Scan
                0x06033da8                filenode_api_findfirst
                0x06033e88                filenode_api_findByFname
 .text          0x06033f48      0x21c obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                0x06033f48                res_ascii_get
                0x0603404c                res_getAsciiCharSize
                0x06034084                res_getAsciiStringSize
 .text          0x06034164        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
 .text          0x06034164        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .text          0x06034164        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .text          0x06034164        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .text          0x06034164        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .text          0x06034164      0x65c obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                0x06034164                res_font_Init
                0x06034294                res_font_SetLanguage
                0x06034364                res_font_GetString
                0x0603447c                res_font_GetChar
                0x06034644                res_font_StringTableInit
                0x060346d0                res_font_GetAddrAndSize
 .text          0x060347c0      0x344 obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
                0x0603493c                res_gif_show_start
                0x06034a88                res_gif_show_process
                0x06034acc                res_gif_show_stop
 .text          0x06034b04      0x738 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                0x06034b9c                res_iconInit
                0x06034c9c                res_iconBuffInit
                0x06034d08                res_iconGetSizeByIndex
                0x06034dbc                res_iconGetAddrByIndex
                0x06034e3c                res_icon_GetAddrAndSize
                0x06034f08                res_icon_GetTColor
                0x06034f3c                res_iconBuffTimeUpdate
                0x06034f80                res_iconGetData
                0x060351b8                res_iconGetPalette
 .text          0x0603523c      0x234 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                0x0603523c                res_image_show
                0x06035394                res_image_decode
 .text          0x06035470      0x118 obj\Debug\sys_manage\res_manage\res_manage_api.o
                0x06035470                res_GetStringInfor
                0x060354d8                res_GetCharInfor
 .text          0x06035588      0x380 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                0x06035588                res_music_end
                0x060355e0                res_music_start
                0x06035728                res_keysound_init
                0x0603585c                res_keysound_play
                0x06035890                res_keysound_stop
                0x060358f0                res_mp3_start
 .text          0x06035908        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .text          0x06035908      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                0x060359d4                uiButtonCreate
 .text          0x06035ad0      0x2f8 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                0x06035bcc                uiCycleCreateDirect
                0x06035c58                uiCycleCreate
 .text          0x06035dc8      0x1ec obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                0x06035dc8                uiDialogCreate
                0x06035efc                uiDialogItem
 .text          0x06035fb4     0x1a90 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x06036364                uiWinDrawInit
                0x060363e0                uiWinDrawUpdate
                0x0603643c                uiWinDrawLine
                0x060368a8                uiWinDrawRect
                0x06036b78                uiWinDrawPoint
                0x06036c6c                uiWinDrawCircle
                0x06036e00                uiWinDrawIcon
                0x060371c0                uiWinDrawString
 .text          0x06037a44      0x1f0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                0x06037b9c                uiFrameWinCreate
 .text          0x06037c34      0x3a0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                0x06037e18                uiImageIconCreateDirect
                0x06037ef8                uiImageIconCreate
 .text          0x06037fd4     0x1838 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                0x06037fd4                uiItemManageCreate
                0x0603807c                uiItemManageSetItemHeight
                0x0603821c                uiItemManageSetHeightAvgGap
                0x0603839c                uiItemManageSetHeightNotGap
                0x060384d8                uiItemManageSetRowSum
                0x06038640                uiItemManageSetColumnSumWithGap
                0x060387c0                uiItemManageCreateItem
                0x06038960                uiItemManageSetResInforFuncEx
                0x060389c0                uiItemManageSetCurItem
                0x06038ca0                uiItemManageUpdateRes
                0x06038d58                uiItemManageUpdateAllItem
                0x06038dbc                uiItemManageUpdateCurItem
                0x06038f60                uiItemManageNextItem
                0x06038fd0                uiItemManagePreItem
                0x06039040                uiItemManageNextPage
                0x060390c4                uiItemManagePrePage
                0x0603914c                uiItemManageGetCurrentItem
                0x060391b0                uiItemManageSetCharInfor
                0x06039268                uiItemManageSetSelectColor
                0x06039300                uiItemManageSetSelectImage
                0x06039398                uiItemManageSetUnselectColor
                0x06039430                uiItemManageSetUnselectImage
                0x060394c8                uiItemManageGetTouchInfor
                0x0603968c                uiItemManageSetSelectColorEx
                0x0603974c                uiItemManageSetUnselectColorEx
 .text          0x0603980c      0x3fc obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                0x06039a98                uiItemCreateItemMenu
 .text          0x06039c08      0x5a0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                0x06039fac                uiItemCreateMenuItemEx
 .text          0x0603a1a8      0x34c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                0x0603a3b4                uiItemCreateMenuOption
 .text          0x0603a4f4      0x250 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                0x0603a58c                uiLineCreate
 .text          0x0603a744     0x1b38 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                0x0603a744                uiWinSendMsg
                0x0603a784                uiWinSendMsgId
                0x0603a7ac                uiWinSendMsgToParent
                0x0603a7dc                uiWinSendMsgIdToParent
                0x0603a804                uiWinOverlapCmp
                0x0603a868                uiWinInsideCmp
                0x0603a8cc                uiWinStringExRowCal
                0x0603a934                uiWinStringExGetByRow
                0x0603a998                uiWinStringExGetNext
                0x0603a9fc                uiWinInterSection
                0x0603aa7c                uiWinHasInvalidRect
                0x0603ab18                uiWinFreeInvalidRect
                0x0603adc4                uiWinSetbgColor
                0x0603adf8                uiWinSetCycleRadius
                0x0603ae30                uiWinSetfgColor
                0x0603ae64                uiWinSetVisible
                0x0603aee8                uiWinIsVisible
                0x0603af24                uiWinSetResid
                0x0603af54                uiWinSetItemSelResid
                0x0603af84                uiWinUpdateResId
                0x0603afa8                uiWinUpdateAllResId
                0x0603b00c                uiWinSetStrInfor
                0x0603b058                uiResInforInit
                0x0603b0a4                uiWinSetSelectInfor
                0x0603b0d4                uiWinSetUnselectInfor
                0x0603b104                uiWinGetResSum
                0x0603b144                uiWinSetResSum
                0x0603b174                uiWinSetResidByNum
                0x0603b1c4                uiWinSetPorgressRate
                0x0603b1f4                uiWinParentRedraw
                0x0603b25c                uiWinGetRelativePos
                0x0603b2ec                uiWinGetPos
                0x0603b330                uiWinUpdateInvalid
                0x0603b3a0                uiWinSetProgressRate
                0x0603b3d0                uiWinSetName
                0x0603b3f0                uiWinGetCurrent
                0x0603b410                uiWinDefaultProc
                0x0603b7a0                uiWinCreate
                0x0603bb48                uiWinDestroy
                0x0603bcf4                uiWinGetTouchInfor
                0x0603bd24                uiWinSetTouchInfor
                0x0603bd54                uiWinTouchProcess
                0x0603bf5c                uiWinDrawProcess
                0x0603c070                uiWinDestroyDeskTopChildWin
                0x0603c0e4                uiWinInit
                0x0603c200                uiWinUninit
 .text          0x0603c27c      0x380 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                0x0603c27c                uiWinHeapInit
                0x0603c2bc                uiWinHeapMemInfo
                0x0603c338                uiWinHeapMalloc
                0x0603c408                uiWinHeapFree
                0x0603c4b8                uiMemPoolCreate
                0x0603c538                uiMemPoolGet
                0x0603c580                uiMemPoolPut
                0x0603c5bc                uiMemPoolInfo
 .text          0x0603c5fc      0x2d8 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                0x0603c790                uiProgressBarCreateDirect
                0x0603c82c                uiProgressBarCreate
 .text          0x0603c8d4      0x29c obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                0x0603ca40                uiRectCreateDirect
                0x0603cacc                uiRectCreate
 .text          0x0603cb70      0x674 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                0x0603cef8                uiStringExCreateDirect
                0x0603cff4                uiStringExCreate
 .text          0x0603d1e4      0x3f0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                0x0603d3cc                uiStringIconCreateDirect
                0x0603d4d0                uiStringIconCreate
 .text          0x0603d5d4      0x24c obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                0x0603d754                uiTipsCreate
 .text          0x0603d820      0x120 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                0x0603d820                uiWidgetProc
                0x0603d86c                uiWidgetSetType
                0x0603d8a0                uiWidgetGetType
                0x0603d8d4                uiWidgetGetId
                0x0603d90c                uiWidgetSetId
 .text          0x0603d940      0x2e0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                0x0603db80                uiWidgetManageCreate
 .text          0x0603dc20      0x91c obj\Debug\app\app_common\src\app_init.o
                0x0603dc20                app_logo_show
                0x0603dcc8                app_version_get
                0x0603dcfc                app_draw_init
                0x0603dd2c                app_uninit
                0x0603df24                app_init
                0x0603e3a4                app_sendDrawUIMsg
                0x0603e3d8                app_draw_Service
                0x0603e420                app_systemService
                0x0603e454                app_sensor_filter_ctrl
 .text          0x0603e53c      0xb4c obj\Debug\app\app_common\src\app_lcdshow.o
                0x0603e53c                app_lcdVideoShowScaler_cfg
                0x0603e778                app_lcdPlayShowScaler_cfg
                0x0603ea38                app_lcdCsiVideoShowStart
                0x0603eab0                app_lcdCsiVideoShowStop
                0x0603eafc                app_lcdVideoIdleFrameGet
                0x0603eb1c                app_lcdUiShowInit
                0x0603ec48                app_lcdUiShowUinit
                0x0603ec98                app_lcdUiDrawIdleFrameGet
                0x0603ecbc                app_lcdShowWinModeCfg
                0x0603ef30                app_lcdVideoShowRatio_cfg
                0x0603ef78                app_Cmos_Sensor_Switch
                0x0603f048                app_lcdVideoShowRotate180
 .text          0x0603f088        0x0 obj\Debug\app\app_common\src\main.o
 .text.startup  0x0603f088       0x84 obj\Debug\app\app_common\src\main.o
                0x0603f088                main
 .text          0x0603f10c        0x0 obj\Debug\app\resource\user_res.o
 .text          0x0603f10c       0xf0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                0x0603f10c                menuProcDelCur
                0x0603f13c                menuProcDelAll
                0x0603f16c                menuProcLockCur
                0x0603f19c                menuProcUnlockCur
                0x0603f1cc                menuProcUnlockAll
 .text          0x0603f1fc        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .text          0x0603f1fc       0x90 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                0x0603f1fc                menuProcDateTime
                0x0603f22c                menuProcFormat
                0x0603f25c                menuProcDefault
 .text          0x0603f28c       0x60 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x0603f28c                menuProcVideoResolution
                0x0603f2bc                menuProcAutoPowerOff
 .text          0x0603f2ec      0x510 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
 .text          0x0603f7fc      0x958 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .text          0x06040154      0x3e8 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .text          0x0604053c      0x544 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .text          0x06040a80      0x5a8 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .text          0x06041028      0x4ec obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .text          0x06041514      0xe24 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06042318                menuWinIsOpen
 .text          0x06042338      0x548 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .text          0x06042880      0x4fc obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .text          0x06042d7c      0x550 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .text          0x060432cc      0x530 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .text          0x060437fc      0x48c obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
 .text          0x06043c88        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .text          0x06043c88      0x4a8 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
 .text          0x06044130      0x124 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .text          0x06044254      0x1dc obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .text          0x06044430      0xad0 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .text          0x06044f00      0x3e0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .text          0x060452e0      0x380 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .text          0x06045660      0x358 obj\Debug\app\task_windows\msg_api.o
                0x06045660                taskmsgFuncRegister
                0x06045718                sysMsgFuncRegister
                0x06045794                app_msgDeal
                0x060458c0                app_msgDealByType
                0x0604595c                app_msgDealByInfor
 .text          0x060459b8      0x444 obj\Debug\app\task_windows\task_api.o
                0x06045a60                app_taskInit
                0x06045b34                app_taskCurId
                0x06045b54                app_taskStart
                0x06045c20                app_taskChange
                0x06045c9c                app_taskService
 .text          0x06045dfc      0x65c obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
                0x06045fb4                taskBatChargeBarShowInit
                0x060461ac                taskBatChargeBarShow
 .text          0x06046458      0x344 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
 .text          0x0604679c      0x414 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .text          0x06046bb0     0x27f4 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x06046bb0                task_com_para_init
                0x06046c20                task_com_usbhost_set
                0x06046fb8                task_com_usb_dev_out
                0x06046ffc                task_com_spijpg_Init
                0x06047010                task_com_sdlist_scan
                0x06047024                task_com_sdc_stat_set
                0x0604711c                task_com_lcdbk_set
                0x06047190                task_com_sreen_check
                0x0604724c                task_com_auto_poweroff
                0x06047300                task_com_keysound_play
                0x0604734c                task_com_sdc_freesize_check
                0x06047404                task_com_fs_scan
                0x06047548                task_com_sdc_freesize_modify
                0x060475dc                task_com_ir_set
                0x06047640                task_com_powerOnTime_str
                0x060476d4                task_com_rec_show_time_str
                0x06047770                task_com_rec_remain_time_str
                0x06047834                task_com_play_time_str
                0x06047974                task_com_tips_show
                0x06047b14                task_com_playscaler_rate
                0x06047b50                task_com_LedPwm_ctrl_Add
                0x06047c08                task_com_LedPwm_ctrl_Dec
                0x06047c9c                task_com_LedOnOffChange
                0x06047d0c                task_com_led_on
                0x06047d78                task_com_led_off
                0x06047de0                task_com_tips_insert_sd_start
                0x06047e04                task_com_tips_insert_sd_clear
                0x06047e30                clarity_statistics
                0x060481b8                task_common_get_af_handler_buffer
                0x060481d8                task_common_get_af_state
                0x060481f8                task_common_set_af_state
                0x06048218                task_common_get_af_clarity
                0x06048230                task_common_find_best_clarity
                0x0604825c                task_common_af_adjust_simple
                0x06048980                task_com_service
                0x0604932c                task_com_AF_ctrl
 .text          0x060493a4      0x168 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .text          0x0604950c      0x2c0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x06049670                app_taskPlayAudio_start
 .text          0x060497cc      0xa84 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .text          0x0604a250        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3.o
 .text          0x0604a250        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3DirOpsMsg.o
 .text          0x0604a250        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3FileOpsMsg.o
 .text          0x0604a250        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3MainMsg.o
 .text          0x0604a250        0x0 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3SubMsg.o
 .text          0x0604a250     0x13ec obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0604a434                taskPlayVideoThumbnallDrawImage
                0x0604a63c                taskPlayVideoSlideOpen
                0x0604a798                taskPlayVideoSlideClose
                0x0604a7f0                taskPlayVideoSlidePause
                0x0604a834                taskPlayVideoSlideStart
                0x0604a984                taskPlayVideoMainStart
 .text          0x0604b63c     0x1204 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .text          0x0604c840      0x3c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .text          0x0604cc08      0xbd4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .text          0x0604d7dc       0xdc obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .text          0x0604d8b8      0x364 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .text          0x0604dc1c      0x274 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .text          0x0604de90      0x7a8 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0604df6c                taskRecordPhotoRemainCal
                0x0604e0c4                app_taskRecordPhoto_callback
                0x0604e410                taskRecordPhotoProcess
 .text          0x0604e638      0xfe8 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .text          0x0604f620     0x10f4 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x0604f620                app_taskRecordVideo_setTimerInterval
                0x06050138                taskRecordvideoTimeCount1S
                0x060501f4                app_taskRecordVideo_caltime
                0x060502a0                app_taskRecordVideo_Capture
                0x0605053c                app_taskRecordVideo_start
                0x06050600                app_taskRecordVideo_stop
 .text          0x06050714     0x109c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .text          0x060517b0       0x20 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .text          0x060517d0       0xd8 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x060517d0                taskSdUpdate_uiInit
 .text          0x060518a8      0x140 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .text          0x060519e8      0x90c obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .text          0x060522f4      0x3a0 obj\Debug\app\task_windows\windows_api.o
                0x060524ec                uiParentDealMsg
                0x0605251c                uiOpenWindow
                0x06052664                windowIsOpen
 .text          0x06052694     0x1488 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x06052694                mbedtls_md5_init
                0x060526bc                mbedtls_md5_free
                0x060526fc                mbedtls_md5_clone
                0x06052720                mbedtls_md5_starts
                0x06052770                mbedtls_md5_process
                0x06053750                mbedtls_md5_update
                0x0605377c                mbedtls_md5_finish
                0x060538b8                mbedtls_md5
                0x0605392c                check_uid_entryption
                0x06053afc                spi_flash_check_md5
 .text          0x06053b1c      0x4c8 obj\Debug\app\user_config\src\user_config_api.o
                0x06053b1c                user_config_set
                0x06053b54                user_config_get
                0x06053b90                user_config_save
                0x06053c38                userConfig_Reset
                0x06053ca0                userConfig_Init
                0x06053d88                userConfigInitial
                0x06053da8                user_configValue2Int
                0x06053e10                user_config_Language
                0x06053e78                user_config_cfgSys
                0x06053fa8                user_config_cfgSysAll
 .text          0x06053fe4        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .text          0x06053fe4        0x0 ..\lib\libboot.a(boot.o)
 .text          0x06053fe4        0x0 ..\lib\libboot.a(boot_loader.o)
 .text          0x06053fe4        0x0 ..\lib\libboot.a(reset.o)
 .text          0x06053fe4        0x0 ..\lib\libboot.a(boot_lib.o)
 .text          0x06053fe4        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .text          0x06053fe4      0x4b0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06053fe4                hx330x_auadcIRQHandler
                0x06054074                hx330x_auadcHalfIRQRegister
                0x06054094                hx330x_auadcEndIRQRegister
                0x060540b4                hx330x_auadcEnable
                0x0605416c                hx330x_auadcAGCEnable
                0x060541fc                hx330x_auadcGainSet
                0x06054294                hx330x_auadcBufferSet
                0x060542f4                hx330x_auadcInit
                0x060543e4                hx330x_auadcSetSampleSet
                0x06054474                hx330x_agc_pwr_get
 .text          0x06054494     0x13bc ..\lib\libmcu.a(hx330x_csi.o)
                0x06054494                hx330x_CSI_LBFDMAERR_callback
                0x06054518                hx330x_CSI_SenSizeErr_callback
                0x06054580                hx330x_csiIOConfig
                0x0605461c                hx330x_csi_reset
                0x06054660                hx330x_csiInit
                0x060546ac                hx330x_csi_fcnt_mnt
                0x060546d8                hx330x_csiISRRegiser
                0x06054714                hx330x_csiOutputSet
                0x06054750                hx330x_csiMclkSet
                0x0605482c                hx330x_csiSyncSet
                0x06054890                hx330x_csiPrioritySet
                0x060548cc                hx330x_csiTypeSet
                0x06054950                hx330x_csiModeSet
                0x0605498c                hx330x_csiModeGet
                0x060549a8                hx330x_pclk_digital_fir_Set
                0x06054a0c                hx330x_pclk_analog_Set
                0x06054a5c                hx330x_pclk_inv_Set
                0x06054aa0                hx330x_csi_clk_tun_Set
                0x06054af8                hx330x_csiSizeSet
                0x06054b30                hx330x_sen_Image_Size_Set
                0x06054b68                hx330x_csi_in_CropSet
                0x06054bec                hx330x_csiInputAddrSet
                0x06054c04                hx330x_csiTestModeSet
                0x06054c64                hx330x_csiDvpClkDivSet
                0x06054ca4                hx330x_csiINTSet
                0x06054ce0                hx330x_csiEnable
                0x06054d70                hx330x_csiLCDScalerDoneCheck
                0x06054d98                hx330x_csiToMjpAddrCfg
                0x06054db4                hx330x_csiMJPEGFrameSet
                0x06054e38                hx330x_csiWifiFrameSet
                0x06054e90                hx330x_csiLCDFrameSet
                0x06054ebc                hx330x_csi_YUVFrameSet
                0x06054f1c                hx330x_csiMJPEGScaler
                0x060550a0                hx330x_csiMJPEGCrop
                0x060551a0                hx330x_csiWifiScaler
                0x0605532c                hx330x_csiSetLsawbtooth
                0x06055360                hx330x_csiLCDScaler
                0x06055474                hx330x_csiMJPEGDmaEnable
                0x06055514                hx330x_csiWifiDmaEnable
                0x060555b4                hx330x_csiLCDDmaEnable
                0x06055668                hx330x_csiLCDDmaKick
                0x060556ac                hx330x_csi_common_int_set
                0x060556f0                hx330x_csiIRQHandler
 .text          0x06055850      0x4b0 ..\lib\libmcu.a(hx330x_dac.o)
                0x06055890                hx330x_dacTypeCfg
                0x060558d4                hx330x_dacSampleRateSet
                0x06055944                hx330x_dacEnable
                0x060559c8                hx330x_dacVolumeSet
                0x06055a40                hx330x_dacReset
                0x06055a64                hx330x_dacStop
                0x06055a94                hx330x_dacISRRegister
                0x06055ab4                hx330x_dacHPSet
                0x06055b48                eq_coeff_init
                0x06055bb8                eq_gain_init
                0x06055c28                hx330x_dacInit
 .text          0x06055d00      0x164 ..\lib\libmcu.a(hx330x_dma.o)
                0x06055d00                hx330x_dmaNocCfg
                0x06055d74                hx330x_dmaNocDefault
                0x06055d9c                hx330x_dmaNocWinA
                0x06055dc4                hx330x_dmaNocWinB
                0x06055dec                hx330x_dmaNocPlayBack
                0x06055e14                hx330x_dmaChannelEnable
 .text          0x06055e64      0x41c ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06055e64                hx330x_DmaUart_sta_cfg
                0x06055ea4                hx330x_DmaUart_con_cfg
                0x06055ee4                hx330x_DmaUartIOCfg
                0x0605603c                hx330x_DmaUartInit
                0x060560ac                hx330x_DmaUart_CallbackRegister
                0x060560cc                hx330x_dmauart_sendbyte
                0x060560fc                hx330x_dmauart_sendDmakick
                0x06056130                hx330x_dmauart_sendDma
                0x060561ac                hx330x_dmauart_recvAutoDmakick
                0x060561f8                hx330x_dmauart_recvBytekick
                0x0605622c                hx330x_dmauart_rxOutAdrGet
                0x06056248                hx330x_dmauart_rxCntGet
                0x06056264                hx330x_dmauart_rxFifoOut
 .text          0x06056280      0x980 ..\lib\libmcu.a(hx330x_gpio.o)
                0x06056280                hx330x_gpioSFRSet
                0x060564c4                hx330x_gpioSFRGet
                0x06056680                hx330x_gpioHystersisSet
                0x0605670c                hx330x_GPIO_FUNC
                0x06056778                hx330x_gpioCommonConfig
                0x0605680c                hx330x_gpioLedInit
                0x0605689c                hx330x_gpioINTCheck
                0x060568d4                hx330x_gpioINTClear
                0x060568f8                hx330x_gpioINTInit
                0x06056a14                hx330x_gpioIRQHandler
                0x06056aac                hx330x_io1d1_softstart
                0x06056b68                hx330x_io1d1_pd_enable
 .text          0x06056c00      0xb14 ..\lib\libmcu.a(hx330x_iic.o)
                0x06056cd0                soft_iic0_sdaout
                0x06056d1c                soft_iic0_sdain
                0x06056d68                soft_iic0_sda_set
                0x06056d9c                soft_iic0_scl_set
                0x06056e30                soft_iic0_sda_get
                0x06056e60                soft_iic0_init
                0x06056e90                soft_iic0_start
                0x06056ef0                soft_iic0_stop
                0x06056f50                soft_iic0_wait_ack
                0x06056ff4                hx330x_iic0Init
                0x0605709c                hx330x_iic0Uninit
                0x060570e0                hx330x_iic0Start
                0x06057120                hx330x_iic0Stop
                0x060571a4                hx330x_iic0RecvACK
                0x060571f8                hx330x_iic0SendACK
                0x06057240                hx330x_iic0SendByte
                0x06057344                hx330x_iic0RecvByte
                0x06057458                hx330x_iic1Init
                0x060574f0                hx330x_iic1Uninit
                0x06057568                hx330x_iic1Start
                0x06057584                hx330x_iic1Stop
                0x060575e4                hx330x_iic1RecvACK
                0x0605760c                hx330x_iic1SendACK
                0x06057630                hx330x_iic1SendByte
                0x060576a0                hx330x_iic1RecvByte
 .text          0x06057714      0x18c ..\lib\libmcu.a(hx330x_int.o)
                0x06057714                hx330x_int_priority
                0x0605775c                hx330x_intInit
 .text          0x060578a0     0x16c0 ..\lib\libmcu.a(hx330x_isp.o)
                0x060578a0                hx330x_isp_mask_tab_cfg
                0x060578d8                hx330x_ispModeSet
                0x06057914                hx330x_isp_BLC_cfg
                0x06057960                hx330x_isp_LSC_cfg
                0x06057998                hx330x_isp_DDC_cfg
                0x06057b10                hx330x_isp_AWB_GAIN_adj
                0x06057b44                hx330x_isp_whtpnt_stat_cfg
                0x06057db0                hx330x_isp_CCM_cfg
                0x06057e74                hx330x_isp_RGB_DGAIN_adj
                0x06057f04                hx330x_isp_hist_stat_cfg
                0x06057f8c                hx330x_isp_YGAMMA_cfg
                0x060580bc                hx330x_isp_ylog_ygamma_cal
                0x06058100                hx330x_isp_RGBGAMMA_cfg
                0x06058290                hx330x_isp_RGBGAMMA_cfg_filter
                0x06058340                hx330x_isp_CH_cfg
                0x06058698                hx330x_isp_VDE_cfg
                0x0605876c                hx330x_isp_CR_cfg
                0x060587a8                hx330x_isp_EE_cfg
                0x06058b74                hx330x_isp_CCF_cfg
                0x06058cc0                hx330x_isp_SAJ_cfg
                0x06058dd8                hx330x_isp_kick_stat
                0x06058e08                hx330x_isp_stat_en
                0x06058e4c                hx330x_isp_stat_cp_kick_st
                0x06058e70                hx330x_isp_stat_cp_done
                0x06058ea0                hx330x_isp_model_cfg
 .text          0x06058f60        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .text          0x06058f60     0x1b64 ..\lib\libmcu.a(hx330x_jpg.o)
                0x06058f60                hx330x_mjpA_EncodeISRRegister
                0x06058f80                hx330x_MJPA_EncodeLcdPreRegister
                0x06058fa0                hx330x_MJPA_EncodeLcdPre_Func_call
                0x06058fd4                hx330x_MJPA_EncodeLcdPre_Func_Check
                0x06059000                hx330x_MJPA_EncodeLcdBackRegister
                0x06059020                hx330x_MJPA_EncodeLcdBack_Func_call
                0x06059054                hx330x_MJPA_EncodeLcdBack_Func_Check
                0x06059080                hx330x_mjpB_EncodeISRRegister
                0x060590a0                hx330x_mjpA_isr_check
                0x060590cc                hx330x_mjpB_DecodeISRRegister
                0x060590ec                hx330x_mjpB_Encode_StartFunc_Check
                0x06059118                hx330x_mjpB_Encode_StartFunc_Reg
                0x06059138                hx330x_mjpB_Encode_StartFunc_call
                0x0605916c                hx330x_mjpA_reset
                0x060591b0                hx330x_mjpB_reset
                0x06059200                hx330x_mjpA_EncodeSizeSet
                0x060592a8                hx330x_mjpA_EncodeSizeSet2
                0x06059430                hx330x_mjpA_EncodeSizeSet3
                0x060594f0                hx330x_mjpA_EncodeQuilitySet
                0x06059530                hx330x_mjpA_EncodeInfoSet
                0x06059570                hx330x_mjpA_EncodeBufferSet
                0x060595bc                hx330x_mjpA_Encode_inlinebuf_init
                0x06059614                hx330x_mjpA_Encode_manual_on
                0x06059674                hx330x_mjpA_Encode_manual_stop
                0x060596a0                hx330x_mjpA_EncodeEnable
                0x0605971c                hx330x_mjpA_EncodeQadj
                0x060597e4                hx330x_mjpA_EncodeInit
                0x0605988c                hx330x_mjpA_EncodeDriModeSet
                0x06059910                hx330x_cal_jASize
                0x06059940                hx330x_mjpA_Encode_check
                0x06059adc                hx330x_mjpA_IRQHandler
                0x06059b28                hx330x_mjpA_Flag_Clr
                0x06059b40                hx330x_mjpB_EncodeQuilitySet
                0x06059b80                hx330x_mjpB_EncodeQadj
                0x06059c48                hx330x_mjpB_Encodeinit
                0x06059dcc                hx330x_cal_jBSize
                0x06059dfc                hx330x_mjpB_Encode_inlinebuf_init
                0x06059e28                hx330x_mjpB_Encode_output_init
                0x06059e54                hx330x_mjpB_Encode_manual_stop
                0x06059ebc                hx330x_mjpB_Encode_manual_start
                0x06059f08                hx330x_mjpB_EncodeLoadAddrGet
                0x06059f24                hx330x_mjpB_as_Encode
                0x06059f50                hx330x_mjpB_DecodeScalerCal
                0x0605a020                hx330x_mjpB_DecodeSetSize
                0x0605a254                hx330x_mjpB_DecodeOutputSet
                0x0605a270                hx330x_mjpB_DecodeInputSet
                0x0605a2b4                hx330x_mjpB_DecodeInputResume
                0x0605a2ec                hx330x_mjpB_DecodeDriSet
                0x0605a304                hx330x_mjpB_DecodeCompressSet
                0x0605a31c                hx330x_mjpB_DecodeInitTable
                0x0605a34c                hx330x_mjpB_yuvfmt_set
                0x0605a398                hx330x_mjpB_DecodeInit
                0x0605a4f0                hx330x_mjpB_DecodeDCTimeSet
                0x0605a5a0                hx330x_mjpB_DecodeEnable
                0x0605a610                hx330x_mjpB_DecodeKick
                0x0605a658                hx330x_mjpB_DecodeStop
                0x0605a694                hx330x_mjpB_DecodeQDTCfg
                0x0605a6e8                hx330x_mjpB_DecodeBusyCheck
                0x0605a71c                hx330x_mjpB_DecodeCheck
                0x0605a74c                hx330x_mjpB_DecodeODma1Cfg
                0x0605a7fc                hx330x_mjpB_DecodePacket_check
                0x0605a8c0                hx330x_mjpB_Decode_InResume
                0x0605a8e4                hx330x_mjpB_Decode_check
                0x0605a9c0                hx330x_mjpB_IRQHandler
 .text          0x0605aac4      0x1c8 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0605aac4                hx330x_mjpA_table_init
                0x0605ab9c                hx330x_mjpB_table_init
 .text          0x0605ac8c     0x11f8 ..\lib\libmcu.a(hx330x_lcd.o)
                0x0605ac8c                hx330x_lcdReset
                0x0605aca0                hx330x_lcdSPIMode
                0x0605accc                hx330x_lcdSPIInit
                0x0605adec                hx330x_lcdSPIUninit
                0x0605ae48                hx330x_lcdSPISendData
                0x0605afbc                hx330x_lcdMcuSendCmd
                0x0605b030                hx330x_lcdMcuSendData
                0x0605b08c                hx330x_lcdMcuSendCmd16
                0x0605b120                hx330x_lcdMcuSendData16
                0x0605b1b4                hx330x_lcdInit
                0x0605b200                hx330x_lcdIRQEnable
                0x0605b248                hx330x_lcdPreLineSet
                0x0605b264                hx330x_lcdSignalSet
                0x0605b2fc                hx330x_lcdBusWidth
                0x0605b350                hx330x_lcdBusEnable
                0x0605b3a0                hx330x_lcdClkSet
                0x0605b3bc                hx330x_lcdSyncSet
                0x0605b3e0                hx330x_lcdDESignalSet
                0x0605b404                hx330x_lcdPositionSet
                0x0605b420                hx330x_lcdResolutionSet
                0x0605b43c                hx330x_lcdWindowSizeSet
                0x0605b458                hx330x_lcdDataModeSet
                0x0605b49c                hx330x_lcdClkNumberSet
                0x0605b4b4                hx330x_lcdEndLineSet
                0x0605b4d0                hx330x_lcdPanelMode
                0x0605b518                hx330x_lcdEnable
                0x0605b57c                hx330x_lcdTeMode
                0x0605b5c4                hx330x_lcdTeCheck
                0x0605b640                hx330x_lcdIRQHandler
                0x0605b798                hx330x_lcdISRRegister
                0x0605b7dc                hx330x_lcdRGBTimimgInit
                0x0605ba2c                hx330x_lcdMCUTimimgInit
                0x0605bc00                hx330x_lcdRGBIOConfig
                0x0605bd60                hx330x_lcdMCUIOConfig
 .text          0x0605be84      0x288 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x0605be84                hx330x_rotateISRRegiser
                0x0605bea4                hx330x_rotateCheckBusy
                0x0605bec4                hx330x_rotateReset
                0x0605bf08                hx330x_rotateStart
                0x0605c028                hx330x_rotateWaitFrameDone
                0x0605c08c                hx330x_rotateIRQHandler
                0x0605c0dc                hx330x_rotateGetSrcYAddr
 .text          0x0605c10c      0x9cc ..\lib\libmcu.a(hx330x_lcdui.o)
                0x0605c10c                hx330x_checkLcdShowStatus
                0x0605c130                hx330x_lcdShowIRQHandler
                0x0605c190                hx330x_lcdShowISRRegister
                0x0605c1b0                hx330x_lcdshowInit
                0x0605c22c                hx330x_lcdshowSetCritical
                0x0605c270                hx330x_lcdSetVideoBgColor
                0x0605c2a4                hx330x_lcdSetVideoBSC
                0x0605c2f0                hx330x_lcdSetVideoBrightness
                0x0605c348                hx330x_lcdVideoSetRgbWidth
                0x0605c3b4                hx330x_lcdVideoSetScalePara
                0x0605c400                hx330x_lcdVideoSetScaleLine
                0x0605c468                hx330x_lcdVideoGetYAddr
                0x0605c498                hx330x_lcdVideoGetUVAddr
                0x0605c4c8                hx330x_lcdVideoSetSize
                0x0605c500                hx330x_lcdvideoMemcpy
                0x0605c560                hx330x_lcdvideoEnable
                0x0605c5a4                hx330x_lcdVideoSetGAMA
                0x0605c650                hx330x_lcdVideo_CCM_cfg
                0x0605c738                hx330x_lcdVideo_SAJ_cfg
                0x0605c7a4                hx330x_lcdvideoGammaEnable
                0x0605c7e8                hx330x_lcdUiSetSize
                0x0605c834                hx330x_lcdUiSetPosition
                0x0605c878                hx330x_lcdUiSetPalette
                0x0605c8dc                hx330x_lcdUiSetPaletteForNes
                0x0605c920                hx330x_lcdUiSetAddr
                0x0605c9d4                hx330x_UiGetAddr
                0x0605ca24                hx330x_lcdUiSetAlpha
                0x0605ca88                hx330x_lcdUiLzoSoftCreate
 .text          0x0605cad8      0x378 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x0605cad8                hx330x_uiLzoISRRegiser
                0x0605caf8                hx330x_uiLzoCheckBusy
                0x0605cb1c                hx330x_uiLzoReset
                0x0605cb60                hx330x_uiLzoGetOutSize
                0x0605cb80                hx330x_uiLzoWaiDone
                0x0605cc28                hx330x_uiLzoStart
                0x0605cd28                hx330x_uiLzoKick
                0x0605ce04                hx330x_uiLzoIRQHandler
 .text          0x0605ce50      0x14c ..\lib\libmcu.a(hx330x_lcdwin.o)
                0x0605ce50                hx330x_lcdWinABConfig
                0x0605cecc                hx330x_lcdWinABEnable
                0x0605cf10                hx330x_lcdWinReset
                0x0605cf50                hx330x_lcdWinGetTopLayer
                0x0605cf74                hx330x_lcdWinGetBotLayer
 .text          0x0605cf9c      0x118 ..\lib\libmcu.a(hx330x_md.o)
                0x0605cf9c                hx330x_mdEnable
                0x0605d004                hx330x_mdEnable_check
                0x0605d024                hx330x_mdInit
                0x0605d06c                hx330x_mdXPos
                0x0605d090                hx330x_mdYPos
 .text          0x0605d0b4      0x304 ..\lib\libmcu.a(hx330x_mipi.o)
                0x0605d0b4                hx330x_mipiClkCfg
                0x0605d108                hx330x_MipiCSIUinit
                0x0605d168                hx330x_MipiCSIInit
 .text          0x0605d3b8      0x860 ..\lib\libmcu.a(hx330x_misc.o)
                0x0605d3b8                hx330x_sin
                0x0605d400                hx330x_cos
                0x0605d424                hx330x_abs
                0x0605d444                hx330x_dif_abs
                0x0605d468                hx330x_max
                0x0605d48c                hx330x_clip
                0x0605d4bc                hx330x_str_cpy
                0x0605d520                hx330x_str_ncpy
                0x0605d598                hx330x_str_char
                0x0605d5e0                hx330x_str_cmp
                0x0605d664                hx330x_str_ncmp
                0x0605d70c                hx330x_str_len
                0x0605d748                hx330x_str_cat
                0x0605d7b8                hx330x_str_seek
                0x0605d824                hx330x_strTransform
                0x0605d878                hx330x_dec_num2str
                0x0605d8b8                hx330x_char2hex
                0x0605d90c                hx330x_str2num
                0x0605d964                hx330x_hex2str
                0x0605d9dc                hx330x_num2str
                0x0605da40                hx330x_num2str_cnt
                0x0605dacc                hx330x_CountToString
                0x0605dbb8                hx330x_str_noftcpy
                0x0605dbe0                hx330x_greatest_divisor
 .text          0x0605dc18      0xe90 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0605dc18                hx330x_rtcRamRead
                0x0605dcb4                hx330x_rtcRamWrite
                0x0605dd50                hx330x_rtcSecondTrim
                0x0605dedc                hx330x_rtcInit
                0x0605e1c8                hx330x_rtc128K_div_cfg
                0x0605e284                hx330x_rtc128K_trim
                0x0605e334                hx330x_rtcIRQHandler
                0x0605e400                hx330x_rtcSencodEnable
                0x0605e47c                hx330x_rtcAlamEnable
                0x0605e564                hx330x_rtcAlamSet
                0x0605e5ac                hx330x_rtcGet
                0x0605e600                hx330x_rtc_alarm_weakup_reset
                0x0605e688                hx330x_rtcSet
                0x0605e6d0                hx330x_VDDGSENEnable
                0x0605e740                hx330x_WKI0InputEnable
                0x0605e7b0                hx330x_WKI1InputEnable
                0x0605e820                hx330x_WKI1Read
                0x0605e844                hx330x_WKI0Read
                0x0605e868                hx330x_WKI0WakeupEnable
                0x0605e9a8                hx330x_rtcBatDecEnable
                0x0605ea18                hx330x_rtcSenHVEnable
                0x0605ea88                hx330x_rtcAlarmWakeUpFlag
 .text          0x0605eaa8      0x6dc ..\lib\libmcu.a(hx330x_sd.o)
                0x0605eaa8                hx330x_emmc_set_bus
                0x0605eaec                hx330x_emmc_excap
                0x0605eb04                hx330x_sd0Init
                0x0605ec58                hx330x_sd0Uninit
                0x0605ed2c                hx330x_sd0BusSet
                0x0605ed70                hx330x_sd0Buffer
                0x0605ed90                hx330x_sd0ClkSet
                0x0605ee28                hx330x_sd1_pin_config
                0x0605eebc                hx330x_sd1Init
                0x0605ef84                hx330x_sd1Uninit
                0x0605f088                hx330x_sd1BusSet
                0x0605f0cc                hx330x_sd1Buffer
                0x0605f0ec                hx330x_sd1ClkSet
 .text          0x0605f184        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .text          0x0605f184      0x510 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0605f184                hx330x_spi1_pin_config
                0x0605f204                hx330x_spi1_CS_Config
                0x0605f260                hx330x_spi1Init
                0x0605f310                hx330x_spi1DMAIRQ_CallbackRegister
                0x0605f330                hx330x_spi1DMAIRQHandler
                0x0605f394                hx330x_spi1SendByte
                0x0605f414                hx330x_spi1RecvByte
                0x0605f45c                hx330x_sp1RecvDmaKick
                0x0605f4b4                hx330x_spi1DmaDoneCheck
                0x0605f4d8                hx330x_sp1SendDmaKick
                0x0605f534                hx330x_sp1RecvDma
                0x0605f5d0                hx330x_sp1SendDma
                0x0605f650                hx330x_sp1Enable
 .text          0x0605f694      0x8c4 ..\lib\libmcu.a(hx330x_sys.o)
                0x0605f694                hx330x_word_memcpy
                0x0605f704                hx330x_halfword_memcpy
                0x0605f774                hx330x_word_memset
                0x0605f7d0                hx330x_mtsfr_memcpy
                0x0605f834                hx330x_mfsfr_memcpy
                0x0605f898                table_init_data
                0x0605f918                hx330x_sysDcacheInit
                0x0605f95c                hx330x_sysIcacheInit
                0x0605f9a0                hx330x_sysDcacheInvalid
                0x0605fa38                hx330x_sysSRAMClear
                0x0605fa8c                hx330x_sysBSSClear
                0x0605faf0                hx330x_sysLDOSet
                0x0605fca0                hx330x_sysReset
                0x0605fce0                hx330x_mcpy0_llp
                0x0605fd70                hx330x_sysInit
                0x0605fea4                hx330x_sysUninit
 .text          0x0605ff58      0x3f8 ..\lib\libmcu.a(hx330x_timer.o)
                0x0605ff58                hx330x_timerEnable
                0x0605ffe4                hx330x_timerDisable
                0x06060088                hx330x_timerPWMStart
 .text          0x06060350      0x444 ..\lib\libmcu.a(hx330x_tminf.o)
                0x06060350                hx330x_mjpA_TimeinfoEnable
                0x060603a8                hx330x_mjpB_TimeinfoEnable
                0x06060414                hx330x_mjpA_TimeinfoColor
                0x06060458                hx330x_mjpB_TimeinfoColor
                0x060604ac                hx330x_mjpA_TimeinfoSize
                0x06060508                hx330x_mjpB_TimeinfoSize
                0x06060590                hx330x_mjpA_TimeinfoPos
                0x060605ec                hx330x_mjpB_TimeinfoPos
                0x06060674                hx330x_mjpA_TimeinfoAddr
                0x060606b8                hx330x_mjpB_TimeinfoAddr
                0x06060728                hx330x_recfg_mjpb_tminf
 .text          0x06060794      0x224 ..\lib\libmcu.a(hx330x_uart.o)
                0x06060794                hx330x_uart0IOCfg
                0x06060918                hx330x_uart0Init
 .text          0x060609b8     0x1210 ..\lib\libmcu.a(hx330x_usb.o)
                0x06060a40                hx330x_usb20_CallbackInit
                0x06060a70                hx330x_usb20_CallbackRegister
                0x06060aac                hx330x_usb20_Func_Call
                0x06060ad8                hx330x_usb20_eptx_register
                0x06060b70                hx330x_usb20_eprx_register
                0x06060c00                hx330x_USB20_EPTX_Flush
                0x06060c2c                hx330x_usb20_HighSpeed
                0x06060c58                hx330x_iso20_tx
                0x06060d90                hx330x_iso20_tx_kick
                0x06060e4c                hx330x_usb20_dev_reset
                0x06060ec0                hx330x_usb20_host_speed_connect
                0x06060f60                hx330x_usb20_host_reset
                0x06060fc8                hx330x_usb20_dev_init
                0x06061128                hx330x_usb20_host_init
                0x06061300                hx330x_usb20_uinit
                0x06061378                get_u16softcnt
                0x06061398                hx330x_usb20DevIRQHanlder
                0x060614d0                hx330x_usb20_hostIRQHanlder
                0x060615d8                hx330x_usb11_CallbackInit
                0x06061608                hx330x_usb11_CallbackRegister
                0x06061644                hx330x_usb11_Func_Call
                0x06061670                hx330x_usb11_host_init
                0x06061764                hx330x_usb11_host_eprx_register
                0x06061810                hx330x_usb11_host_reset
                0x06061868                hx330x_usb11_host_speed_connect
                0x060618b0                hx330x_usb11_uinit
                0x06061978                hx330x_usb11_hostIRQHanlder
                0x06061a80                hx330x_usb20_dev_check_init
                0x06061a9c                hx330x_usb20_host_check_init
                0x06061ab8                hx330x_usb11_host_check_init
                0x06061ad4                hx330x_usb20_device_check
                0x06061b08                hx330x_usb20_host_check
                0x06061b9c                hx330x_usb11_host_check
 .text          0x06061bc8       0x14 ..\lib\libmcu.a(hx330x_wdt.o)
                0x06061bc8                hx330x_wdtTimeSet
 .text          0x06061bdc      0x2c4 ..\lib\libmcu.a(hx330x_emi.o)
                0x06061bdc                hx330x_emiInit
                0x06061ce4                hx330x_emiISRRegister
                0x06061d18                hx330x_emiIRQHandler
                0x06061d74                hx330x_emiKick
                0x06061dd0                hx330x_emiCheckBusy
                0x06061df0                hx330x_emiSend
                0x06061e7c                hx330x_emiCheckRXError
 .text          0x06061ea0     0x22b8 ..\lib\libisp.a(hal_isp.o)
                0x060621c8                hal_sensor_fps_adpt
                0x060622a8                hal_isp_process
                0x06063238                hal_sensor_awb_scene_set
                0x06063328                hal_sensor_EV_set
                0x06063438                hal_isp_br_get
                0x06063458                hal_ispService
                0x06063538                hal_isp_filter_cfg
                0x06063650                hal_isp_color_filter_init
                0x060636d4                hal_isp_init
                0x06063ad8                hal_SensorRegister
                0x06063b74                hal_SensorApiGet
                0x06063b94                hal_SensorResolutionGet
                0x06063bf0                hal_SensorRotate
                0x06063c5c                hal_isplog_cnt
                0x06063c9c                hal_isp_cur_yloga
                0x06063cdc                hal_sensor_ISO_set
                0x06063da0                hal_sensor_sharp_set
                0x06063ea8                hal_sensor_beauty_set
                0x06063fbc                hal_sensor_wdr_set
                0x06064090                hal_sensor_scene_mode_set
 .text          0x06064158     0x1b54 ..\lib\libhusb.a(husb_enum.o)
                0x0606457c                husb_get_pcommit_cs100_ack
                0x06064d3c                husb_get_max_lun_ack
                0x06064d70                husb_set_intfs_uvc_1_ack
                0x06064ee8                husb20_ep0_cfg
                0x06064f08                husb11_ep0_cfg
                0x06064f28                usensor_resolution_select
                0x06065218                husb_all_get_cfg_desc_ack
                0x060659dc                husb_astern_check
                0x06065a2c                husb_astern_ack
                0x06065a88                husb_api_ep0_kick
                0x06065b54                husb_api_ep0_process
                0x06065c60                husb_api_ep0_asterncheck_kick
 .text          0x06065cac      0xc5c ..\lib\libjpg.a(hal_jpg.o)
                0x06065ddc                hal_mjp_enle_init
                0x06065e0c                hal_mjp_enle_unit
                0x06065eb0                hal_mjp_enle_check
                0x06066360                hal_mjp_enle_manual_kickstart
                0x06066508                hal_mjp_enle_buf_mdf
                0x0606667c                hal_mjp_enle_manual_done
 .text          0x06066908     0x1ba4 ..\lib\libjpg.a(hal_step_jpg.o)
                0x06066d14                hal_mjp_step_init
                0x06066d44                hal_mjp_step_unit
                0x06066de4                hal_mjp_Step0_EncodeCallback
                0x06066f50                hal_mjp_step_get
                0x06066f90                hal_mjpstep_manual_kick
                0x06066fd0                hal_mjp_step_done
                0x06067200                hal_mjp_stepsub_resume
                0x06067428                hal_mjp_stepsub_done
                0x06067628                hal_mjp_step_src_buf_get
                0x0606766c                hal_mjp_step_resume
                0x06067a74                hal_mjp_step_elne_buf_mdf
                0x06067ba0                hal_mjp_step_elne_done
                0x06067db4                hal_mjp_step_start
                0x0606848c                hal_step_is_elne_mode
 .text          0x060684ac     0x1454 ..\lib\liblcd.a(hal_lcd.o)
                0x0606898c                hal_lcdWinUpdata
                0x06068a88                hal_lcdWinUpdataCheckDone
                0x06068ad4                hal_lcdCsiShowStop
                0x06068b54                hal_lcdVideoShowFrameGet
                0x06068bc4                hal_lcdVideoIdleFrameMalloc
                0x06068c04                hal_lcdCsiShowStart
                0x06068d5c                hal_lcdVideoSetFrameWait
                0x06068da4                hal_lcdVideoSetFrame
                0x06068e58                hal_lcdGetSreenResolution
                0x06068edc                hal_lcdGetUiResolution
                0x06068f38                hal_lcdGetUiPosition
                0x06068f94                hal_lcdVideoScanModeGet
                0x06068fc4                hal_lcdUiScanModeGet
                0x06068ff4                hal_lcdGetVideoRatioResolution
                0x06069050                hal_lcdGetVideoRatioDestResolution
                0x060690ac                hal_lcdGetVideoRatioPos
                0x06069108                hal_lcdGetVideoResolution
                0x06069164                hal_lcdGetVideoPos
                0x060691c0                hal_lcdBrightnessGet
                0x060691f0                hal_lcdRegister
                0x060697fc                hal_lcdFrameEndCallBackRegister
                0x06069844                hal_lcd_send_cmd
 .text          0x06069900      0xde0 ..\lib\liblcd.a(hal_lcdMem.o)
                0x0606998c                hal_lcdAddrCalculate
                0x06069c1c                hal_dispframeInit
                0x0606a370                hal_lcdVideoFrameFlush
                0x0606a514                hal_dispframeMalloc
                0x0606a594                hal_dispframeFree
                0x0606a650                hal_dispframeSetCallback
                0x0606a668                hal_dispframeGetFrameNums
                0x0606a69c                hal_dispframeUinit
 .text          0x0606a6e0      0x370 ..\lib\liblcd.a(hal_lcdrotate.o)
                0x0606a868                hal_rotateInit
                0x0606a8c8                hal_rotateAdd
 .text          0x0606aa50      0x820 ..\lib\liblcd.a(hal_lcdUi.o)
                0x0606aa50                hal_uiRotateBufMalloc
                0x0606aa74                hal_uiLzoBufMalloc
                0x0606aa98                hal_uiDrawBufMalloc
                0x0606ab1c                hal_uiBuffFree
                0x0606ab44                hal_lcdUiKickWait
                0x0606abb4                hal_lcdUiKick
                0x0606ac40                hal_lcdUiSetAddr
                0x0606acb0                hal_lcdUiSetBuffer
                0x0606ad44                hal_lcdUiSetBufferWaitDone
                0x0606ad98                hal_lcdUiBuffFlush
                0x0606ae50                hal_lcdUiInit
                0x0606afec                hal_lcdUiSetPosition
                0x0606b068                hal_lcdUiSetPalette
                0x0606b0bc                hal_lcdUiSetSize
                0x0606b138                hal_lcdUiSetAlpha
                0x0606b1b4                hal_lcdUiResolutionGet
                0x0606b220                hal_lcdVideo_CCM_cfg
                0x0606b248                hal_lcdVideo_SAJ_cfg
 .text          0x0606b270      0x1a8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                0x0606b30c                hal_uiLzokick
                0x0606b3f0                hal_uiLzoInit
 .text          0x0606b418      0x1ec ..\lib\liblcd.a(lcd_tab.o)
                0x0606b418                hal_lcdParaLoad
                0x0606b4dc                hal_lcdSetLsawbtooth
                0x0606b590                hal_lcdPQToolGetInfo
 .text          0x0606b604      0xa98 ..\lib\libmultimedia.a(api_multimedia.o)
                0x0606b7cc                api_multimedia_cache_init
                0x0606b8e0                api_multimedia_cachePreRead
                0x0606b970                api_multimedia_cacheRead
                0x0606b9ec                api_multimedia_init
                0x0606bb3c                api_multimedia_uninit
                0x0606bbc0                api_multimedia_start
                0x0606bc3c                api_multimedia_end
                0x0606bcb8                api_multimedia_service
                0x0606bd34                api_multimedia_addjunk
                0x0606bdb0                api_multimedia_encodeframe
                0x0606be30                api_multimedia_decodeframe
                0x0606beac                api_multimedia_gettime
                0x0606bf28                api_multimedia_getsta
                0x0606bfa4                api_multimedia_decodefast
                0x0606c020                api_multimedia_getArg
 .text          0x0606c09c     0x1468 ..\lib\libmultimedia.a(avi_dec.o)
 .text          0x0606d504      0xc3c ..\lib\libmultimedia.a(avi_odml_enc.o)
 .text          0x0606e140      0xaa4 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x0606e798                avi_stdEncidxEnd
                0x0606e88c                avi_stdEncEnd
 .text          0x0606ebe4     0x18a4 ..\lib\libmultimedia.a(gif_dec.o)
                0x0606f2f4                gif_to_yuv_frame_process
                0x0606f6d8                gif_get_frame
                0x0606ff10                gif_rewind
                0x0606ff90                gif_delay_get
                0x0606ffdc                gif_loopcnt_get
                0x06070030                gif_Dec_Start
                0x060703ec                gif_Dec_Stop
                0x06070444                gif_Dec_state
 .text          0x06070488      0x958 ..\lib\libmultimedia.a(wav_dec.o)
 .text          0x06070de0      0x400 ..\lib\libmultimedia.a(wav_enc.o)
                0x06070de0                wav_EncEnd
 .text          0x060711e0      0x1c8 ..\lib\libmultimedia.a(wav_pcm.o)
                0x060711e0                pcm_encode
                0x06071290                pcm_decode
 .text          0x060713a8       0xb0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                0x060713a8                memcmp
 .text          0x06071458      0x158 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                0x06071458                memcpy
 .text          0x060715b0      0x15c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                0x060715b0                memset
 .text          0x0607170c       0xc0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                0x0607170c                strcpy
 .text          0x060717cc      0x890 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
                0x060717cc                __divdi3
 .text          0x0607205c      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                0x0607205c                __udivdi3
 .text          0x06072824      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                0x06072824                __umoddi3
 .text          0x06072fec       0xf4 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                0x06072fec                __udivsi3
                0x06072fec                __udivsi3_internal
 .text          0x060730e0       0x1c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                0x060730e0                __umodsi3
 .text          0x060730fc        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(.rodata*)
 .rodata.str1.1
                0x060730fc       0x15 obj\Debug\dev\battery\src\battery_api.o
 *fill*         0x06073111        0x3 
 .rodata        0x06073114        0xc obj\Debug\dev\battery\src\battery_api.o
 .rodata.str1.1
                0x06073120       0x49 obj\Debug\dev\dev_api.o
 .rodata.str1.1
                0x06073169       0x65 obj\Debug\dev\fs\src\ff.o
                                 0x69 (size before relaxing)
 *fill*         0x060731ce        0x2 
 .rodata.cst4   0x060731d0        0x4 obj\Debug\dev\fs\src\ff.o
 .rodata        0x060731d4       0xb0 obj\Debug\dev\fs\src\ff.o
 .rodata        0x06073284      0x3d4 obj\Debug\dev\fs\src\ffunicode.o
 .rodata.str1.1
                0x06073658       0xb7 obj\Debug\dev\fs\src\fs_api.o
                                 0xb8 (size before relaxing)
 .rodata.str1.1
                0x0607370f       0x7a obj\Debug\dev\gsensor\src\gsensor_api.o
 *fill*         0x06073789        0x3 
 .rodata        0x0607378c        0xc obj\Debug\dev\gsensor\src\gsensor_api.o
 .rodata        0x06073798       0x38 obj\Debug\dev\gsensor\src\gsensor_da380.o
                0x060737b0                da380
 .rodata        0x060737d0       0x38 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                0x060737e8                gma301
 .rodata        0x06073808       0x38 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                0x06073820                sc7a30e
 .rodata.str1.1
                0x06073840       0x16 obj\Debug\dev\lcd\src\lcd_api.o
 .rodata.str1.1
                0x06073856       0x70 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .rodata.str1.1
                0x060738c6       0x6a obj\Debug\dev\nvfs\src\nvfs_api.o
 .rodata.str1.1
                0x06073930       0x8b obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .rodata.str1.1
                0x060739bb      0x153 obj\Debug\dev\sd\src\sd_api.o
 .rodata.str1.1
                0x06073b0e      0x104 obj\Debug\dev\sensor\src\sensor_api.o
 *fill*         0x06073c12        0x2 
 .rodata        0x06073c14       0x44 obj\Debug\dev\sensor\src\sensor_api.o
                0x06073c14                user_ccf_dn_tab
                0x06073c20                user_ee_dn_tab
                0x06073c3c                user_ee_sharp_tab
 .rodata        0x06073c58        0xc obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .rodata        0x06073c64       0x3c obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .rodata        0x06073ca0      0x88c obj\Debug\dev\sensor\src\sensor_tab.o
                0x06073ca0                test_img_init
                0x06073ce4                test_img_adpt
 .rodata.str1.1
                0x0607452c       0x57 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .rodata.str1.1
                0x06074583       0x47 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 *fill*         0x060745ca        0x2 
 .rodata        0x060745cc       0x1c obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x060745cc                tp_icnt81
 .rodata.str1.1
                0x060745e8       0xa8 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .rodata.str1.1
                0x06074690       0x13 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x17 (size before relaxing)
 *fill*         0x060746a3        0x1 
 .rodata        0x060746a4       0x2c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x060746b4                tp_ns2009
 .rodata.str1.1
                0x060746d0       0x50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .rodata        0x06074720      0x100 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0607476c                StringDescTbl
                0x06074784                DEV_QAULIFIER_DESC_DATA
                0x06074790                UsbStrDescSerialNumber
                0x060747b0                UsbStrDescProduct_1
                0x060747c8                UsbStrDescProduct_0
                0x060747e0                UsbStrDescManufacturer
                0x060747f0                UsbLanguageID
                0x060747f4                dusb_com_devdsc
                0x06074808                dusb_msc_devdsc
                0x0607481c                SDK_CHIP_INF
 .rodata.str1.1
                0x06074820       0x4d obj\Debug\dev\usb\dusb\src\dusb_msc.o
 *fill*         0x0607486d        0x3 
 .rodata        0x06074870       0x40 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x06074870                MscSenseCode
                0x0607487c                device_inquiry_data
 .rodata.str1.1
                0x060748b0        0xc obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .rodata        0x060748bc        0x4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x060748bc                uac_vol_tbl
 .rodata        0x060748c0      0x1e0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x060748d4                uvc_ctl_tab
                0x06074a14                unit_callback
                0x06074a64                vc_still_probe_commit_desc
                0x06074a70                vc_probe_commit_desc
                0x06074a8c                uvc_res_tab
 .rodata.str1.1
                0x06074aa0       0x69 obj\Debug\dev\usb\husb\src\husb_api.o
 .rodata.str1.1
                0x06074b09      0x271 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .rodata.str1.1
                0x06074d7a       0x17 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .rodata.str1.1
                0x06074d91      0x153 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .rodata.str1.1
                0x06074ee4       0xec obj\Debug\hal\src\hal_auadc.o
 .rodata.str1.1
                0x06074fd0       0x74 obj\Debug\hal\src\hal_csi.o
 .rodata        0x06075044       0x2c obj\Debug\hal\src\hal_dac.o
 .rodata.str1.1
                0x06075070       0x6b obj\Debug\hal\src\hal_dmauart.o
 .rodata.str1.1
                0x060750db       0x66 obj\Debug\hal\src\hal_iic.o
 .rodata.str1.1
                0x06075141       0xd2 obj\Debug\hal\src\hal_lcdshow.o
                                 0xd8 (size before relaxing)
 .rodata.str1.1
                0x06075213      0x31b obj\Debug\hal\src\hal_mjpAEncode.o
                                0x31d (size before relaxing)
 *fill*         0x0607552e        0x2 
 .rodata        0x06075530        0xc obj\Debug\hal\src\hal_mjpAEncode.o
                0x06075530                mjpegQualityTable
 .rodata.str1.1
                0x0607553c       0xbb obj\Debug\hal\src\hal_mjpBEncode.o
 .rodata.str1.1
                0x060755f7       0x47 obj\Debug\hal\src\hal_mjpDecode.o
 *fill*         0x0607563e        0x2 
 .rodata        0x06075640        0x8 obj\Debug\hal\src\hal_mjpDecode.o
 .rodata.str1.1
                0x06075648        0xa obj\Debug\hal\src\hal_rtc.o
 *fill*         0x06075652        0x2 
 .rodata        0x06075654        0xc obj\Debug\hal\src\hal_rtc.o
                0x06075654                monDaysTable
 .rodata.str1.1
                0x06075660       0xdf obj\Debug\hal\src\hal_spi1.o
 .rodata.str1.1
                0x0607573f       0xc7 obj\Debug\hal\src\hal_sys.o
 .rodata.str1.1
                0x06075806       0x14 obj\Debug\hal\src\hal_timer.o
 .rodata.str1.1
                0x0607581a       0x2a obj\Debug\hal\src\hal_watermark.o
 .rodata.str1.1
                0x06075844      0x116 obj\Debug\multimedia\audio\audio_playback.o
 .rodata.str1.1
                0x0607595a       0x98 obj\Debug\multimedia\audio\audio_record.o
 .rodata.str1.1
                0x060759f2       0xc5 obj\Debug\multimedia\image\image_decode.o
 .rodata.str1.1
                0x06075ab7      0x1d8 obj\Debug\multimedia\image\image_encode.o
 *fill*         0x06075c8f        0x1 
 .rodata        0x06075c90      0x2dc obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06075c90                exif_head
 .rodata.str1.1
                0x06075f6c      0x297 obj\Debug\multimedia\video\video_playback.o
                                0x29d (size before relaxing)
 *fill*         0x06076203        0x1 
 .rodata        0x06076204        0x8 obj\Debug\multimedia\video\video_playback.o
 .rodata.str1.1
                0x0607620c      0x491 obj\Debug\multimedia\video\video_record.o
 *fill*         0x0607669d        0x3 
 .rodata        0x060766a0       0x10 obj\Debug\multimedia\video\video_record.o
 .rodata.str1.1
                0x060766b0      0x1a6 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .rodata.str1.1
                0x06076856       0x95 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                 0xb1 (size before relaxing)
 *fill*         0x060768eb        0x1 
 .rodata        0x060768ec      0xd10 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
                0x060768ec                ascii_num0_table
                0x06076a64                ascii_num0_126
                0x06076a78                ascii_num0_125
                0x06076a8c                ascii_num0_124
                0x06076aa0                ascii_num0_123
                0x06076ab4                ascii_num0_122
                0x06076ad8                ascii_num0_121
                0x06076afc                ascii_num0_120
                0x06076b20                ascii_num0_119
                0x06076b44                ascii_num0_118
                0x06076b68                ascii_num0_117
                0x06076b8c                ascii_num0_116
                0x06076bb0                ascii_num0_115
                0x06076bd4                ascii_num0_114
                0x06076bf8                ascii_num0_113
                0x06076c1c                ascii_num0_112
                0x06076c40                ascii_num0_111
                0x06076c64                ascii_num0_110
                0x06076c88                ascii_num0_109
                0x06076cac                ascii_num0_108
                0x06076cd0                ascii_num0_107
                0x06076cf4                ascii_num0_106
                0x06076d18                ascii_num0_105
                0x06076d3c                ascii_num0_104
                0x06076d60                ascii_num0_103
                0x06076d84                ascii_num0_102
                0x06076da8                ascii_num0_101
                0x06076dcc                ascii_num0_100
                0x06076df0                ascii_num0_99
                0x06076e14                ascii_num0_98
                0x06076e38                ascii_num0_97
                0x06076e5c                ascii_num0_96
                0x06076e70                ascii_num0_95
                0x06076e94                ascii_num0_94
                0x06076ea8                ascii_num0_93
                0x06076ebc                ascii_num0_92
                0x06076ed0                ascii_num0_91
                0x06076ee4                ascii_num0_90
                0x06076f08                ascii_num0_89
                0x06076f2c                ascii_num0_88
                0x06076f50                ascii_num0_87
                0x06076f74                ascii_num0_86
                0x06076f98                ascii_num0_85
                0x06076fbc                ascii_num0_84
                0x06076fe0                ascii_num0_83
                0x06077004                ascii_num0_82
                0x06077028                ascii_num0_81
                0x0607704c                ascii_num0_80
                0x06077070                ascii_num0_79
                0x06077094                ascii_num0_78
                0x060770b8                ascii_num0_77
                0x060770dc                ascii_num0_76
                0x06077100                ascii_num0_75
                0x06077124                ascii_num0_74
                0x06077148                ascii_num0_73
                0x0607715c                ascii_num0_72
                0x06077180                ascii_num0_71
                0x060771a4                ascii_num0_70
                0x060771c8                ascii_num0_69
                0x060771ec                ascii_num0_68
                0x06077210                ascii_num0_67
                0x06077234                ascii_num0_66
                0x06077258                ascii_num0_65
                0x0607727c                ascii_num0_64
                0x060772a0                ascii_num0_63
                0x060772b4                ascii_num0_62
                0x060772c8                ascii_num0_61
                0x060772dc                ascii_num0_60
                0x060772f0                ascii_num0_59
                0x06077314                ascii_num0_58
                0x06077338                ascii_num0_57
                0x0607735c                ascii_num0_56
                0x06077380                ascii_num0_55
                0x060773a4                ascii_num0_54
                0x060773c8                ascii_num0_53
                0x060773ec                ascii_num0_52
                0x06077410                ascii_num0_51
                0x06077434                ascii_num0_50
                0x06077458                ascii_num0_49
                0x0607747c                ascii_num0_48
                0x060774a0                ascii_num0_47
                0x060774c4                ascii_num0_46
                0x060774d8                ascii_num0_45
                0x060774fc                ascii_num0_44
                0x06077510                ascii_num0_43
                0x06077524                ascii_num0_42
                0x06077538                ascii_num0_41
                0x0607754c                ascii_num0_40
                0x06077560                ascii_num0_39
                0x06077574                ascii_num0_38
                0x06077588                ascii_num0_37
                0x060775ac                ascii_num0_36
                0x060775c0                ascii_num0_35
                0x060775d4                ascii_num0_33
                0x060775e8                ascii_num0_32
 .rodata        0x060775fc      0xeb0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
                0x060775fc                ascii_num1_table
                0x06077774                ascii_num1_126
                0x06077798                ascii_num1_125
                0x060777bc                ascii_num1_124
                0x060777e0                ascii_num1_123
                0x06077804                ascii_num1_122
                0x06077828                ascii_num1_121
                0x0607784c                ascii_num1_120
                0x06077870                ascii_num1_119
                0x06077894                ascii_num1_118
                0x060778b8                ascii_num1_117
                0x060778dc                ascii_num1_116
                0x06077900                ascii_num1_115
                0x06077924                ascii_num1_114
                0x06077948                ascii_num1_113
                0x0607796c                ascii_num1_112
                0x06077990                ascii_num1_111
                0x060779b4                ascii_num1_110
                0x060779d8                ascii_num1_109
                0x060779fc                ascii_num1_108
                0x06077a20                ascii_num1_107
                0x06077a44                ascii_num1_106
                0x06077a68                ascii_num1_105
                0x06077a8c                ascii_num1_104
                0x06077ab0                ascii_num1_103
                0x06077ad4                ascii_num1_102
                0x06077af8                ascii_num1_101
                0x06077b1c                ascii_num1_100
                0x06077b40                ascii_num1_99
                0x06077b64                ascii_num1_98
                0x06077b88                ascii_num1_97
                0x06077bac                ascii_num1_96
                0x06077bd0                ascii_num1_95
                0x06077bf4                ascii_num1_94
                0x06077c18                ascii_num1_93
                0x06077c3c                ascii_num1_92
                0x06077c60                ascii_num1_91
                0x06077c84                ascii_num1_90
                0x06077ca8                ascii_num1_89
                0x06077ccc                ascii_num1_88
                0x06077cf0                ascii_num1_87
                0x06077d14                ascii_num1_86
                0x06077d38                ascii_num1_85
                0x06077d5c                ascii_num1_84
                0x06077d80                ascii_num1_83
                0x06077da4                ascii_num1_82
                0x06077dc8                ascii_num1_81
                0x06077dec                ascii_num1_80
                0x06077e10                ascii_num1_79
                0x06077e34                ascii_num1_78
                0x06077e58                ascii_num1_77
                0x06077e7c                ascii_num1_76
                0x06077ea0                ascii_num1_75
                0x06077ec4                ascii_num1_74
                0x06077ee8                ascii_num1_73
                0x06077f0c                ascii_num1_72
                0x06077f30                ascii_num1_71
                0x06077f54                ascii_num1_70
                0x06077f78                ascii_num1_69
                0x06077f9c                ascii_num1_68
                0x06077fc0                ascii_num1_67
                0x06077fe4                ascii_num1_66
                0x06078008                ascii_num1_65
                0x0607802c                ascii_num1_64
                0x06078050                ascii_num1_63
                0x06078074                ascii_num1_62
                0x06078098                ascii_num1_61
                0x060780bc                ascii_num1_60
                0x060780e0                ascii_num1_59
                0x06078104                ascii_num1_58
                0x06078128                ascii_num1_57
                0x0607814c                ascii_num1_56
                0x06078170                ascii_num1_55
                0x06078194                ascii_num1_54
                0x060781b8                ascii_num1_53
                0x060781dc                ascii_num1_52
                0x06078200                ascii_num1_51
                0x06078224                ascii_num1_50
                0x06078248                ascii_num1_49
                0x0607826c                ascii_num1_48
                0x06078290                ascii_num1_47
                0x060782b4                ascii_num1_46
                0x060782d8                ascii_num1_45
                0x060782fc                ascii_num1_44
                0x06078320                ascii_num1_43
                0x06078344                ascii_num1_42
                0x06078368                ascii_num1_41
                0x0607838c                ascii_num1_40
                0x060783b0                ascii_num1_39
                0x060783d4                ascii_num1_38
                0x060783f8                ascii_num1_37
                0x0607841c                ascii_num1_36
                0x06078440                ascii_num1_35
                0x06078464                ascii_num1_33
                0x06078488                ascii_num1_32
 .rodata        0x060784ac     0x1a70 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
                0x060784ac                ascii_num2_table
                0x06078624                ascii_num2_126
                0x06078668                ascii_num2_125
                0x060786ac                ascii_num2_124
                0x060786f0                ascii_num2_123
                0x06078734                ascii_num2_122
                0x06078778                ascii_num2_121
                0x060787bc                ascii_num2_120
                0x06078800                ascii_num2_119
                0x06078844                ascii_num2_118
                0x06078888                ascii_num2_117
                0x060788cc                ascii_num2_116
                0x06078910                ascii_num2_115
                0x06078954                ascii_num2_114
                0x06078998                ascii_num2_113
                0x060789dc                ascii_num2_112
                0x06078a20                ascii_num2_111
                0x06078a64                ascii_num2_110
                0x06078aa8                ascii_num2_109
                0x06078aec                ascii_num2_108
                0x06078b30                ascii_num2_107
                0x06078b74                ascii_num2_106
                0x06078bb8                ascii_num2_105
                0x06078bfc                ascii_num2_104
                0x06078c40                ascii_num2_103
                0x06078c84                ascii_num2_102
                0x06078cc8                ascii_num2_101
                0x06078d0c                ascii_num2_100
                0x06078d50                ascii_num2_99
                0x06078d94                ascii_num2_98
                0x06078dd8                ascii_num2_97
                0x06078e1c                ascii_num2_96
                0x06078e60                ascii_num2_95
                0x06078ea4                ascii_num2_94
                0x06078ee8                ascii_num2_93
                0x06078f2c                ascii_num2_92
                0x06078f70                ascii_num2_91
                0x06078fb4                ascii_num2_90
                0x06078ff8                ascii_num2_89
                0x0607903c                ascii_num2_88
                0x06079080                ascii_num2_87
                0x060790c4                ascii_num2_86
                0x06079108                ascii_num2_85
                0x0607914c                ascii_num2_84
                0x06079190                ascii_num2_83
                0x060791d4                ascii_num2_82
                0x06079218                ascii_num2_81
                0x0607925c                ascii_num2_80
                0x060792a0                ascii_num2_79
                0x060792e4                ascii_num2_78
                0x06079328                ascii_num2_77
                0x0607936c                ascii_num2_76
                0x060793b0                ascii_num2_75
                0x060793f4                ascii_num2_74
                0x06079438                ascii_num2_73
                0x0607947c                ascii_num2_72
                0x060794c0                ascii_num2_71
                0x06079504                ascii_num2_70
                0x06079548                ascii_num2_69
                0x0607958c                ascii_num2_68
                0x060795d0                ascii_num2_67
                0x06079614                ascii_num2_66
                0x06079658                ascii_num2_65
                0x0607969c                ascii_num2_64
                0x060796e0                ascii_num2_63
                0x06079724                ascii_num2_62
                0x06079768                ascii_num2_61
                0x060797ac                ascii_num2_60
                0x060797f0                ascii_num2_59
                0x06079834                ascii_num2_58
                0x06079878                ascii_num2_57
                0x060798bc                ascii_num2_56
                0x06079900                ascii_num2_55
                0x06079944                ascii_num2_54
                0x06079988                ascii_num2_53
                0x060799cc                ascii_num2_52
                0x06079a10                ascii_num2_51
                0x06079a54                ascii_num2_50
                0x06079a98                ascii_num2_49
                0x06079adc                ascii_num2_48
                0x06079b20                ascii_num2_47
                0x06079b64                ascii_num2_46
                0x06079ba8                ascii_num2_45
                0x06079bec                ascii_num2_44
                0x06079c30                ascii_num2_43
                0x06079c74                ascii_num2_42
                0x06079cb8                ascii_num2_41
                0x06079cfc                ascii_num2_40
                0x06079d40                ascii_num2_39
                0x06079d84                ascii_num2_38
                0x06079dc8                ascii_num2_37
                0x06079e0c                ascii_num2_36
                0x06079e50                ascii_num2_35
                0x06079e94                ascii_num2_33
                0x06079ed8                ascii_num2_32
 .rodata        0x06079f1c     0x5430 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
                0x06079f1c                ascii_num3_table
                0x0607a094                ascii_num3_126
                0x0607a198                ascii_num3_125
                0x0607a21c                ascii_num3_124
                0x0607a2a0                ascii_num3_123
                0x0607a324                ascii_num3_122
                0x0607a3e8                ascii_num3_121
                0x0607a4ac                ascii_num3_120
                0x0607a570                ascii_num3_119
                0x0607a674                ascii_num3_118
                0x0607a738                ascii_num3_117
                0x0607a83c                ascii_num3_116
                0x0607a8c0                ascii_num3_115
                0x0607a984                ascii_num3_114
                0x0607aa08                ascii_num3_113
                0x0607ab0c                ascii_num3_112
                0x0607ac10                ascii_num3_111
                0x0607ad14                ascii_num3_110
                0x0607ae18                ascii_num3_109
                0x0607af5c                ascii_num3_108
                0x0607afe0                ascii_num3_107
                0x0607b0a4                ascii_num3_106
                0x0607b128                ascii_num3_105
                0x0607b1ac                ascii_num3_104
                0x0607b2b0                ascii_num3_103
                0x0607b3b4                ascii_num3_102
                0x0607b438                ascii_num3_101
                0x0607b53c                ascii_num3_100
                0x0607b640                ascii_num3_99
                0x0607b704                ascii_num3_98
                0x0607b808                ascii_num3_97
                0x0607b90c                ascii_num3_96
                0x0607b990                ascii_num3_95
                0x0607ba54                ascii_num3_94
                0x0607bb18                ascii_num3_93
                0x0607bb9c                ascii_num3_92
                0x0607bc20                ascii_num3_91
                0x0607bca4                ascii_num3_90
                0x0607bda8                ascii_num3_89
                0x0607beac                ascii_num3_88
                0x0607bfb0                ascii_num3_87
                0x0607c134                ascii_num3_86
                0x0607c238                ascii_num3_85
                0x0607c33c                ascii_num3_84
                0x0607c440                ascii_num3_83
                0x0607c544                ascii_num3_82
                0x0607c648                ascii_num3_81
                0x0607c78c                ascii_num3_80
                0x0607c890                ascii_num3_79
                0x0607c9d4                ascii_num3_78
                0x0607cad8                ascii_num3_77
                0x0607cc1c                ascii_num3_76
                0x0607cd20                ascii_num3_75
                0x0607ce24                ascii_num3_74
                0x0607cee8                ascii_num3_73
                0x0607cf6c                ascii_num3_72
                0x0607d070                ascii_num3_71
                0x0607d1b4                ascii_num3_70
                0x0607d2b8                ascii_num3_69
                0x0607d3bc                ascii_num3_68
                0x0607d4c0                ascii_num3_67
                0x0607d5c4                ascii_num3_66
                0x0607d6c8                ascii_num3_65
                0x0607d7cc                ascii_num3_64
                0x0607d950                ascii_num3_63
                0x0607da54                ascii_num3_62
                0x0607db58                ascii_num3_61
                0x0607dc5c                ascii_num3_60
                0x0607dd60                ascii_num3_59
                0x0607dde4                ascii_num3_58
                0x0607de68                ascii_num3_57
                0x0607df6c                ascii_num3_56
                0x0607e070                ascii_num3_55
                0x0607e174                ascii_num3_54
                0x0607e278                ascii_num3_53
                0x0607e37c                ascii_num3_52
                0x0607e480                ascii_num3_51
                0x0607e584                ascii_num3_50
                0x0607e688                ascii_num3_49
                0x0607e78c                ascii_num3_48
                0x0607e890                ascii_num3_47
                0x0607e914                ascii_num3_46
                0x0607e998                ascii_num3_45
                0x0607ea1c                ascii_num3_44
                0x0607eaa0                ascii_num3_43
                0x0607eba4                ascii_num3_42
                0x0607ec68                ascii_num3_41
                0x0607ecec                ascii_num3_40
                0x0607ed70                ascii_num3_39
                0x0607edf4                ascii_num3_38
                0x0607eef8                ascii_num3_37
                0x0607f03c                ascii_num3_36
                0x0607f140                ascii_num3_35
                0x0607f244                ascii_num3_33
                0x0607f2c8                ascii_num3_32
 .rodata        0x0607f34c     0x7870 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
                0x0607f34c                ascii_num4_table
                0x0607f4c4                ascii_num4_126
                0x0607f608                ascii_num4_125
                0x0607f74c                ascii_num4_124
                0x0607f890                ascii_num4_123
                0x0607f9d4                ascii_num4_122
                0x0607fb18                ascii_num4_121
                0x0607fc5c                ascii_num4_120
                0x0607fda0                ascii_num4_119
                0x0607fee4                ascii_num4_118
                0x06080028                ascii_num4_117
                0x0608016c                ascii_num4_116
                0x060802b0                ascii_num4_115
                0x060803f4                ascii_num4_114
                0x06080538                ascii_num4_113
                0x0608067c                ascii_num4_112
                0x060807c0                ascii_num4_111
                0x06080904                ascii_num4_110
                0x06080a48                ascii_num4_109
                0x06080b8c                ascii_num4_108
                0x06080cd0                ascii_num4_107
                0x06080e14                ascii_num4_106
                0x06080f58                ascii_num4_105
                0x0608109c                ascii_num4_104
                0x060811e0                ascii_num4_103
                0x06081324                ascii_num4_102
                0x06081468                ascii_num4_101
                0x060815ac                ascii_num4_100
                0x060816f0                ascii_num4_99
                0x06081834                ascii_num4_98
                0x06081978                ascii_num4_97
                0x06081abc                ascii_num4_96
                0x06081c00                ascii_num4_95
                0x06081d44                ascii_num4_94
                0x06081e88                ascii_num4_93
                0x06081fcc                ascii_num4_92
                0x06082110                ascii_num4_91
                0x06082254                ascii_num4_90
                0x06082398                ascii_num4_89
                0x060824dc                ascii_num4_88
                0x06082620                ascii_num4_87
                0x06082764                ascii_num4_86
                0x060828a8                ascii_num4_85
                0x060829ec                ascii_num4_84
                0x06082b30                ascii_num4_83
                0x06082c74                ascii_num4_82
                0x06082db8                ascii_num4_81
                0x06082efc                ascii_num4_80
                0x06083040                ascii_num4_79
                0x06083184                ascii_num4_78
                0x060832c8                ascii_num4_77
                0x0608340c                ascii_num4_76
                0x06083550                ascii_num4_75
                0x06083694                ascii_num4_74
                0x060837d8                ascii_num4_73
                0x0608391c                ascii_num4_72
                0x06083a60                ascii_num4_71
                0x06083ba4                ascii_num4_70
                0x06083ce8                ascii_num4_69
                0x06083e2c                ascii_num4_68
                0x06083f70                ascii_num4_67
                0x060840b4                ascii_num4_66
                0x060841f8                ascii_num4_65
                0x0608433c                ascii_num4_64
                0x06084480                ascii_num4_63
                0x060845c4                ascii_num4_62
                0x06084708                ascii_num4_61
                0x0608484c                ascii_num4_60
                0x06084990                ascii_num4_59
                0x06084ad4                ascii_num4_58
                0x06084c18                ascii_num4_57
                0x06084d5c                ascii_num4_56
                0x06084ea0                ascii_num4_55
                0x06084fe4                ascii_num4_54
                0x06085128                ascii_num4_53
                0x0608526c                ascii_num4_52
                0x060853b0                ascii_num4_51
                0x060854f4                ascii_num4_50
                0x06085638                ascii_num4_49
                0x0608577c                ascii_num4_48
                0x060858c0                ascii_num4_47
                0x06085a04                ascii_num4_46
                0x06085b48                ascii_num4_45
                0x06085c8c                ascii_num4_44
                0x06085dd0                ascii_num4_43
                0x06085f14                ascii_num4_42
                0x06086058                ascii_num4_41
                0x0608619c                ascii_num4_40
                0x060862e0                ascii_num4_39
                0x06086424                ascii_num4_38
                0x06086568                ascii_num4_37
                0x060866ac                ascii_num4_36
                0x060867f0                ascii_num4_35
                0x06086934                ascii_num4_33
                0x06086a78                ascii_num4_32
 .rodata.str1.1
                0x06086bbc       0xae obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .rodata.str1.1
                0x06086c6a       0x1e obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
 .rodata.str1.1
                0x06086c88       0x5c obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .rodata.str1.1
                0x06086ce4       0x3a obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .rodata.str1.1
                0x06086d1e       0x27 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 *fill*         0x06086d45        0x3 
 .rodata        0x06086d48      0x71c obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
                0x06086d48                res_key_music
 .rodata.str1.1
                0x06087464       0x13 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .rodata.str1.1
                0x06087477       0x2e obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 *fill*         0x060874a5        0x3 
 .rodata        0x060874a8       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .rodata.str1.1
                0x060874b8       0x2f obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 *fill*         0x060874e7        0x1 
 .rodata        0x060874e8       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .rodata        0x060874f8       0x68 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .rodata.str1.1
                0x06087560       0x17 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 *fill*         0x06087577        0x1 
 .rodata        0x06087578       0x68 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .rodata        0x060875e0       0x68 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .rodata        0x06087648       0x68 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .rodata.str1.1
                0x060876b0      0x198 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata        0x06087848       0x1c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata.str1.1
                0x06087864       0x5b obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 *fill*         0x060878bf        0x1 
 .rodata        0x060878c0       0x68 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .rodata        0x06087928       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .rodata        0x06087988       0x64 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .rodata.str1.1
                0x060879ec      0x126 obj\Debug\app\app_common\src\app_init.o
                                0x12d (size before relaxing)
 .rodata.str1.1
                0x06087b12       0x10 obj\Debug\app\app_common\src\app_lcdshow.o
 *fill*         0x06087b22        0x2 
 .rodata        0x06087b24       0x30 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata.str1.1
                0x06087b54       0x2e obj\Debug\app\app_common\src\main.o
 .rodata.str1.1
                0x06087b82       0x55 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                                 0x57 (size before relaxing)
 *fill*         0x06087bd7        0x1 
 .rodata        0x06087bd8      0x108 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                0x06087bd8                autoPowerOffTab
                0x06087bf0                autoPowerOffWin
 .rodata.str1.1
                0x06087ce0       0x8a obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 *fill*         0x06087d6a        0x2 
 .rodata        0x06087d6c       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x06087d94                dateTimeWin
 .rodata.str1.1
                0x06087e0c       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 *fill*         0x06087e52        0x2 
 .rodata        0x06087e54       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x06087e54                defaultWin
 .rodata.str1.1
                0x06087ef4       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 *fill*         0x06087f43        0x1 
 .rodata        0x06087f44       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x06087f44                delAllWin
 .rodata.str1.1
                0x06087fe4       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 *fill*         0x06088042        0x2 
 .rodata        0x06088044       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x06088044                delCurWin
 .rodata.str1.1
                0x060880e4       0x76 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                 0x77 (size before relaxing)
 *fill*         0x0608815a        0x2 
 .rodata        0x0608815c       0xc8 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x0608815c                formatWin
 .rodata.str1.1
                0x06088224       0x51 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                 0x56 (size before relaxing)
 *fill*         0x06088275        0x3 
 .rodata        0x06088278      0x1c8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06088288                menuItemWin
 .rodata.str1.1
                0x06088440       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x06088486        0x2 
 .rodata        0x06088488       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x06088488                lockCurWin
 .rodata.str1.1
                0x06088528       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                 0x51 (size before relaxing)
 *fill*         0x06088577        0x1 
 .rodata        0x06088578       0xf0 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x06088578                menuOptionWin
 .rodata.str1.1
                0x06088668       0x5f obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                 0x6d (size before relaxing)
 *fill*         0x060886c7        0x1 
 .rodata        0x060886c8       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x060886c8                unlockAllWin
 .rodata.str1.1
                0x06088768       0x4c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x060887b4       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x060887b4                unlockCurWin
 .rodata.str1.1
                0x06088854       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
 *fill*         0x060888b2        0x2 
 .rodata        0x060888b4       0x84 obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
                0x060888b4                usb_id_tab
                0x060888c0                UsbDeviceSelectWin
 .rodata.str1.1
                0x06088938       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
                                 0x60 (size before relaxing)
 *fill*         0x06088996        0x2 
 .rodata        0x06088998      0x100 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
                0x06088998                videoResolutionTab
                0x060889a8                videoResolutionWin
 .rodata.str1.1
                0x06088a98       0x5d obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 *fill*         0x06088af5        0x3 
 .rodata        0x06088af8      0x348 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x06088af8                asternWin
 .rodata.str1.1
                0x06088e40       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 *fill*         0x06088e83        0x1 
 .rodata        0x06088e84       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x06088e84                noFileWin
 .rodata.str1.1
                0x06088efc      0x11f obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x17b (size before relaxing)
 *fill*         0x0608901b        0x1 
 .rodata        0x0608901c      0x4b8 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x06089074                selfTestWin
 .rodata.str1.1
                0x060894d4       0x40 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                 0x42 (size before relaxing)
 .rodata        0x06089514       0xa0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x06089514                tips1Win
 .rodata.str1.1
                0x060895b4       0x3d obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                 0x3f (size before relaxing)
 *fill*         0x060895f1        0x3 
 .rodata        0x060895f4       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x060895f4                tipsWin
 .rodata.str1.1
                0x0608966c      0x148 obj\Debug\app\task_windows\task_api.o
 .rodata        0x060897b4       0x28 obj\Debug\app\task_windows\task_api.o
 .rodata.str1.1
                0x060897dc       0x2d obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
 .rodata.str1.1
                0x06089809       0x18 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
                                 0x19 (size before relaxing)
 *fill*         0x06089821        0x3 
 .rodata        0x06089824       0xa0 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
                0x06089824                batChargeWin
 .rodata.str1.1
                0x060898c4       0x78 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .rodata        0x0608993c       0x58 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                0x0608993c                sysComMsgDeal
 .rodata.str1.1
                0x06089994      0x3b5 obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x3c5 (size before relaxing)
 *fill*         0x06089d49        0x3 
 .rodata        0x06089d4c       0x64 obj\Debug\app\task_windows\task_common\src\task_common.o
 .rodata.str1.1
                0x06089db0       0x1c obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .rodata        0x06089dcc       0x10 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                0x06089dcc                taskComMsgDeal
 .rodata.str1.1
                0x06089ddc       0x72 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .rodata.str1.1
                0x06089e4e       0x63 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x06089eb1        0x3 
 .rodata        0x06089eb4      0x128 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x06089ec4                playAudioWin
 .rodata.str1.1
                0x06089fdc      0x211 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x21a (size before relaxing)
 .rodata.str1.1
                0x0608a1ed       0x8f obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                 0xa9 (size before relaxing)
 .rodata        0x0608a27c      0x1c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x0608a28c                playVideoMainWin
 .rodata.str1.1
                0x0608a444       0x5b obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x0608a49f        0x1 
 .rodata        0x0608a4a0       0xc8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x0608a4a0                playVideoSlideWin
 .rodata.str1.1
                0x0608a568       0x8d obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                 0x97 (size before relaxing)
 *fill*         0x0608a5f5        0x3 
 .rodata        0x0608a5f8      0x4d4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x0608a5f8                playVideoThumbnallWin
 .rodata.str1.1
                0x0608aacc        0xa obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .rodata.str1.1
                0x0608aad6       0xb1 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .rodata.str1.1
                0x0608ab87        0x9 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0x16 (size before relaxing)
 .rodata        0x0608ab90       0xa0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x0608ab90                RecordAudioWin
 .rodata.str1.1
                0x0608ac30      0x19e obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .rodata.str1.1
                0x0608adce       0x92 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                 0xc9 (size before relaxing)
 .rodata        0x0608ae60      0x194 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x0608ae8c                recordPhotoWin
 .rodata.str1.1
                0x0608aff4      0x3cb obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x3d7 (size before relaxing)
 .rodata.str1.1
                0x0608b3bf       0xb9 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                 0xf4 (size before relaxing)
 .rodata        0x0608b478      0x1a0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x0608b488                recordVideoWin
 .rodata.str1.1
                0x0608b618      0x13f obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                0x151 (size before relaxing)
 .rodata.str1.1
                0x0608b757        0xb obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x17 (size before relaxing)
 .rodata.str1.1
                0x0608b762      0x117 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 *fill*         0x0608b879        0x3 
 .rodata        0x0608b87c      0x260 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x0608b88c                usbDeviceWin
 .rodata.str1.1
                0x0608badc      0x13b obj\Debug\app\task_windows\windows_api.o
                                0x155 (size before relaxing)
 *fill*         0x0608bc17        0x1 
 .rodata        0x0608bc18       0x1c obj\Debug\app\task_windows\windows_api.o
 .rodata.str1.1
                0x0608bc34       0x72 obj\Debug\app\user_config\src\mbedtls_md5.o
                                 0x75 (size before relaxing)
 *fill*         0x0608bca6        0x2 
 .rodata        0x0608bca8       0x40 obj\Debug\app\user_config\src\mbedtls_md5.o
 .rodata.str1.1
                0x0608bce8       0x81 obj\Debug\app\user_config\src\user_config_api.o
 *fill*         0x0608bd69        0x3 
 .rodata        0x0608bd6c      0x280 obj\Debug\app\user_config\src\user_config_api.o
 .rodata        0x0608bfec      0x108 obj\Debug\app\user_config\src\user_config_tab.o
                0x0608bfec                user_cfg_tab
 .rodata        0x0608c0f4       0xd0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x0608c0f4                tbl_micvol
                0x0608c134                auadc_samplerate_tab
 .rodata        0x0608c1c4      0x154 ..\lib\libmcu.a(hx330x_csi.o)
                0x0608c1c4                csi_dvp_map_tab
                0x0608c1d8                csi_dvp_map4
                0x0608c218                csi_dvp_map3
                0x0608c258                csi_dvp_map2
                0x0608c298                csi_dvp_map1
                0x0608c2d8                csi_dvp_map0
 .rodata        0x0608c318       0xac ..\lib\libmcu.a(hx330x_dac.o)
                0x0608c318                gain
                0x0608c32c                eq_coeff
                0x0608c364                hx330x_dacInit_table
 .rodata        0x0608c3c4      0x2c0 ..\lib\libmcu.a(hx330x_dma.o)
                0x0608c3c4                playback_cfg
                0x0608c474                winB_cfg
                0x0608c524                winA_cfg
                0x0608c5d4                default_cfg
 .rodata        0x0608c684       0xd0 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x0608c684                uart1_IO_MAP_tab
 .rodata        0x0608c754       0xb8 ..\lib\libmcu.a(hx330x_gpio.o)
 .rodata        0x0608c80c      0x4a4 ..\lib\libmcu.a(hx330x_iic.o)
                0x0608c80c                iic1_uinit_map_tab
                0x0608c828                iic1_init_map_tab
                0x0608c844                iic1_uinit_map6
                0x0608c86c                iic1_init_map6
                0x0608c8b8                iic1_uinit_map5
                0x0608c8e0                iic1_init_map5
                0x0608c92c                iic1_uinit_map4
                0x0608c954                iic1_init_map4
                0x0608c9a0                iic1_uinit_map3
                0x0608c9c8                iic1_init_map3
                0x0608ca14                iic1_uinit_map2
                0x0608ca3c                iic1_init_map2
                0x0608ca88                iic1_uinit_map1
                0x0608cab0                iic1_init_map1
                0x0608cafc                iic1_uinit_map0
                0x0608cb24                iic1_init_map0
                0x0608cb70                iic0_init_map_tab
                0x0608cb80                iic0_init_map3
                0x0608cbcc                iic0_init_map2
                0x0608cc18                iic0_init_map1
                0x0608cc64                iic0_init_map0
 .rodata        0x0608ccb0       0xf0 ..\lib\libmcu.a(hx330x_int.o)
 .rodata        0x0608cda0      0x14c ..\lib\libmcu.a(hx330x_isp_tab.o)
                0x0608cda0                Ratio_of_Evstep
                0x0608cea8                GAOS3X3_TAB
                0x0608ceb4                GAOS5X5_TAB
                0x0608ced0                LOG_TAB
 .rodata        0x0608ceec       0x14 ..\lib\libmcu.a(hx330x_jpg.o)
 .rodata.str1.1
                0x0608cf00       0x16 ..\lib\libmcu.a(hx330x_jpg.o)
 *fill*         0x0608cf16        0x2 
 .rodata        0x0608cf18      0xda0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0608d958                c_table_chroma
                0x0608d998                belta_table_chroma
                0x0608d9d8                alpha_table_chroma
                0x0608da18                c_table_luma
                0x0608da58                belta_table_luma
                0x0608da98                alpha_table_luma
                0x0608dad8                bic_coef_tabR
                0x0608db78                bic_coef_tabL
                0x0608dc18                bic_coef_tab
 .rodata        0x0608dcb8      0x11c ..\lib\libmcu.a(hx330x_lcd.o)
 .rodata        0x0608ddd4       0xa0 ..\lib\libmcu.a(hx330x_lcdui.o)
 .rodata        0x0608de74      0x168 ..\lib\libmcu.a(hx330x_misc.o)
 .rodata        0x0608dfdc      0x328 ..\lib\libmcu.a(hx330x_sd.o)
                0x0608dfdc                sd1PinCfg_1line_tab
                0x0608dff4                sd1PinCfg_4line_tab
                0x0608e00c                sd1_1LINE_Pos5_tab
                0x0608e04c                sd1_4LINE_Pos5_tab
                0x0608e08c                sd1_1LINE_Pos4_tab
                0x0608e108                sd1_1LINE_Pos3_tab
                0x0608e148                sd1_1LINE_Pos2_tab
                0x0608e188                sd1_4LINE_Pos2_tab
                0x0608e1c8                sd1_1LINE_Pos1_tab
                0x0608e208                sd1_4LINE_Pos1_tab
                0x0608e248                sd1_1LINE_Pos0_tab
                0x0608e288                sd1_4LINE_Pos0_tab
 .rodata        0x0608e304      0x248 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0608e304                SPI1_CS_MAP_tab
                0x0608e32c                spi1PinCfg_tab
                0x0608e34c                SPI1_2LINE_Pos3_tab
                0x0608e38c                SPI1_3LINE_Pos3_tab
                0x0608e3cc                SPI1_2LINE_Pos2_tab
                0x0608e40c                SPI1_3LINE_Pos2_tab
                0x0608e44c                SPI1_2LINE_Pos1_tab
                0x0608e48c                SPI1_3LINE_Pos1_tab
                0x0608e4cc                SPI1_2LINE_Pos0_tab
                0x0608e50c                SPI1_3LINE_Pos0_tab
 .rodata        0x0608e54c       0x18 ..\lib\libmcu.a(hx330x_sys.o)
 .rodata        0x0608e564      0x100 ..\lib\libmcu.a(hx330x_timer.o)
                0x0608e564                timer3_PWM_IO_MAP_tab
                0x0608e594                timer2_PWM_IO_MAP_tab
                0x0608e5cc                timer1_PWM_IO_MAP_tab
                0x0608e60c                timer0_PWM_IO_MAP_tab
 .rodata        0x0608e664       0xd8 ..\lib\libmcu.a(hx330x_uart.o)
                0x0608e664                uart0_IO_MAP_tab
 .rodata.cst4   0x0608e73c        0x8 ..\lib\libmcu.a(hx330x_usb.o)
 .rodata        0x0608e744      0x380 ..\lib\libmcu.a(hx330x_emi.o)
                0x0608e744                emi_map_tab
                0x0608e760                hx330x_emiPinConfigSlave_table6
                0x0608e7b8                hx330x_emiPinConfigSlave_table5
                0x0608e810                hx330x_emiPinConfigSlave_table4
                0x0608e868                hx330x_emiPinConfigMaster_table3
                0x0608e8c0                hx330x_emiPinConfigMaster_table2
                0x0608e96c                hx330x_emiPinConfigMaster_table1
                0x0608ea18                hx330x_emiPinConfigMaster_table0
 .rodata        0x0608eac4       0x44 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x0608eb08       0x29 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x0608eb31      0x4da ..\lib\libhusb.a(husb_enum.o)
                                0x4dc (size before relaxing)
 *fill*         0x0608f00b        0x1 
 .rodata        0x0608f00c      0x1d0 ..\lib\libhusb.a(husb_enum.o)
                0x0608f03c                husb_astern_hcdtrb
                0x0608f05c                husb_uvc_switch_hcdtrb
                0x0608f0cc                husb_enum_hcdtrb
 .rodata.str1.1
                0x0608f1dc       0x94 ..\lib\libjpg.a(hal_jpg.o)
 .rodata.str1.1
                0x0608f270      0x285 ..\lib\libjpg.a(hal_step_jpg.o)
 .rodata.str1.1
                0x0608f4f5       0x36 ..\lib\liblcd.a(hal_lcd.o)
 .rodata.str1.1
                0x0608f52b       0x14 ..\lib\liblcd.a(hal_lcdMem.o)
 .rodata.str1.1
                0x0608f53f       0x11 ..\lib\liblcd.a(hal_lcdrotate.o)
 .rodata.str1.1
                0x0608f550       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 *fill*         0x0608f562        0x2 
 .rodata        0x0608f564       0x20 ..\lib\libmultimedia.a(api_multimedia.o)
 .rodata.str1.1
                0x0608f584       0x81 ..\lib\libmultimedia.a(avi_dec.o)
 *fill*         0x0608f605        0x3 
 .rodata        0x0608f608       0x30 ..\lib\libmultimedia.a(avi_dec.o)
                0x0608f608                avi_dec_func
 .rodata.str1.1
                0x0608f638       0x78 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .rodata        0x0608f6b0     0x2c30 ..\lib\libmultimedia.a(avi_odml_enc.o)
                0x0608f6b0                avi_odml_enc_func
                0x0608f6e0                avi_odml_header
 .rodata.str1.1
                0x060922e0       0x5f ..\lib\libmultimedia.a(avi_std_enc.o)
 *fill*         0x0609233f        0x1 
 .rodata        0x06092340      0x218 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06092340                avi_std_enc_func
 .rodata.str1.1
                0x06092558       0xe3 ..\lib\libmultimedia.a(gif_dec.o)
 .rodata.str1.1
                0x0609263b       0x3c ..\lib\libmultimedia.a(wav_dec.o)
 *fill*         0x06092677        0x1 
 .rodata        0x06092678       0x30 ..\lib\libmultimedia.a(wav_dec.o)
                0x06092678                wav_dec_func
 .rodata.str1.1
                0x060926a8       0x24 ..\lib\libmultimedia.a(wav_enc.o)
 .rodata        0x060926cc       0x30 ..\lib\libmultimedia.a(wav_enc.o)
                0x060926cc                wav_enc_func
 .rodata        0x060926fc       0x2c ..\lib\libmultimedia.a(wav_pcm.o)
                0x060926fc                wav_pcm_head
 .rodata        0x06092728      0x100 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                0x06092728                __clz_tab

.lcd_resource   0x00000000     0x2ae0 load address 0x00092a00
 *(.lcd_res.struct)
 .lcd_res.struct
                0x00000000       0xac obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
                0x00000000                __lcd_desc
 *(.lcd_res*)
 .lcd_res       0x000000ac      0x234 obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
 .lcd_res       0x000002e0     0x2800 ..\lib\liblcd.a(lcd_tab.o)
                0x000005e0                lcd_contra_tab
                0x000012e0                lcd_gamma

.sensor_resource
                0x00000000     0x7404 load address 0x000954e0
 *(.sensor_res.header)
 .sensor_res.header
                0x00000000       0x40 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000000                RES_SensorHeader
                0x00000040                _res_sensor_header_item_start = .
 *(.sensor_res.header.items)
 .sensor_res.header.items
                0x00000040       0x44 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
                0x00000040                BF314A_init
 .sensor_res.header.items
                0x00000084       0x44 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
                0x00000084                gc1054_init
 .sensor_res.header.items
                0x000000c8       0x44 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
                0x000000c8                gc0308_init
 .sensor_res.header.items
                0x0000010c       0x44 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
                0x0000010c                gc0329_init
 .sensor_res.header.items
                0x00000150       0x44 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
                0x00000150                h62_init
                0x00000194                _res_sensor_header_item_end = .
 *(.sensor_res.isp_tab)
 .sensor_res.isp_tab
                0x00000194      0x478 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .sensor_res.isp_tab
                0x0000060c      0x478 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .sensor_res.isp_tab
                0x00000a84      0x478 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
 .sensor_res.isp_tab
                0x00000efc     0x3600 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000efc                sensor_rgb_gamma
                0x000020fc                sensor_ygamma_tab
 *(.sensor_res.struct)
 .sensor_res.struct
                0x000044fc      0x848 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
                0x000044fc                BF314A_adpt
 .sensor_res.struct
                0x00004d44      0x848 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
                0x00004d44                gc1054_adpt
 .sensor_res.struct
                0x0000558c      0x848 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
                0x0000558c                gc0308_adpt
 .sensor_res.struct
                0x00005dd4      0x848 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
                0x00005dd4                gc0329_adpt
 .sensor_res.struct
                0x0000661c      0x848 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
                0x0000661c                h62_adpt
 *(.sensor_res.init_tab)
 .sensor_res.init_tab
                0x00006e64       0x44 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .sensor_res.init_tab
                0x00006ea8       0xb4 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .sensor_res.init_tab
                0x00006f5c      0x238 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .sensor_res.init_tab
                0x00007194      0x1a4 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
 .sensor_res.init_tab
                0x00007338       0xcc obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o

.eh_frame       0x00007404    0x10f54 load address 0x0009c8e4
 *(.eh_frame)
 .eh_frame      0x00007404       0x50 obj\Debug\dev\battery\src\battery_api.o
 .eh_frame      0x00007454       0x80 obj\Debug\dev\dev_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x000074d4       0xa8 obj\Debug\dev\fs\src\diskio.o
                                 0xbc (size before relaxing)
 .eh_frame      0x0000757c      0x914 obj\Debug\dev\fs\src\ff.o
                                0x928 (size before relaxing)
 .eh_frame      0x00007e90       0x54 obj\Debug\dev\fs\src\ffunicode.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00007ee4      0x2a4 obj\Debug\dev\fs\src\fs_api.o
                                0x2b8 (size before relaxing)
 .eh_frame      0x00008188       0x94 obj\Debug\dev\gsensor\src\gsensor_api.o
                                 0xa8 (size before relaxing)
 .eh_frame      0x0000821c       0x80 obj\Debug\dev\gsensor\src\gsensor_da380.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000829c       0x80 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000831c       0x7c obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                                 0x90 (size before relaxing)
 .eh_frame      0x00008398       0x3c obj\Debug\dev\ir\src\ir_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x000083d4       0x74 obj\Debug\dev\key\src\key_api.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00008448       0x74 obj\Debug\dev\lcd\src\lcd_api.o
                                 0x88 (size before relaxing)
 .eh_frame      0x000084bc       0x38 obj\Debug\dev\led\src\led_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x000084f4       0x3c obj\Debug\dev\led_pwm\src\led_pwm_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00008530       0xc8 obj\Debug\dev\nvfs\src\nvfs_api.o
                                 0xdc (size before relaxing)
 .eh_frame      0x000085f8      0x430 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                                0x444 (size before relaxing)
 .eh_frame      0x00008a28      0x3f0 obj\Debug\dev\sd\src\sd_api.o
                                0x404 (size before relaxing)
 .eh_frame      0x00008e18      0x178 obj\Debug\dev\sensor\src\sensor_api.o
                                0x18c (size before relaxing)
 .eh_frame      0x00008f90       0x20 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00008fb0       0x3c obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00008fec       0x64 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                                 0x78 (size before relaxing)
 .eh_frame      0x00009050       0x58 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                                 0x6c (size before relaxing)
 .eh_frame      0x000090a8      0x138 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                                0x14c (size before relaxing)
 .eh_frame      0x000091e0       0x78 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00009258      0x110 obj\Debug\dev\usb\dusb\src\dusb_api.o
                                0x124 (size before relaxing)
 .eh_frame      0x00009368      0x160 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                                0x174 (size before relaxing)
 .eh_frame      0x000094c8      0x27c obj\Debug\dev\usb\dusb\src\dusb_msc.o
                                0x290 (size before relaxing)
 .eh_frame      0x00009744      0x1ac obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
                                0x1c0 (size before relaxing)
 .eh_frame      0x000098f0      0x128 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                                0x13c (size before relaxing)
 .eh_frame      0x00009a18      0x1ec obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00009c04      0x1a4 obj\Debug\dev\usb\husb\src\husb_api.o
                                0x1b8 (size before relaxing)
 .eh_frame      0x00009da8      0x264 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                                0x278 (size before relaxing)
 .eh_frame      0x0000a00c      0x1c0 obj\Debug\dev\usb\husb\src\husb_usensor.o
                                0x1d4 (size before relaxing)
 .eh_frame      0x0000a1cc      0x1b8 obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x1cc (size before relaxing)
 .eh_frame      0x0000a384       0x74 obj\Debug\hal\src\hal_adc.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000a3f8      0x2b8 obj\Debug\hal\src\hal_auadc.o
                                0x2cc (size before relaxing)
 .eh_frame      0x0000a6b0       0x98 obj\Debug\hal\src\hal_csi.o
                                 0xac (size before relaxing)
 .eh_frame      0x0000a748       0xc4 obj\Debug\hal\src\hal_dac.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x0000a80c       0xd8 obj\Debug\hal\src\hal_dmauart.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000a8e4       0x6c obj\Debug\hal\src\hal_gpio.o
                                 0x80 (size before relaxing)
 .eh_frame      0x0000a950      0x2c8 obj\Debug\hal\src\hal_iic.o
                                0x2dc (size before relaxing)
 .eh_frame      0x0000ac18       0x1c obj\Debug\hal\src\hal_int.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000ac34      0x2d0 obj\Debug\hal\src\hal_lcdshow.o
                                0x2e4 (size before relaxing)
 .eh_frame      0x0000af04       0x68 obj\Debug\hal\src\hal_md.o
                                 0x7c (size before relaxing)
 .eh_frame      0x0000af6c      0x4a0 obj\Debug\hal\src\hal_mjpAEncode.o
                                0x4b4 (size before relaxing)
 .eh_frame      0x0000b40c      0x21c obj\Debug\hal\src\hal_mjpBEncode.o
                                0x230 (size before relaxing)
 .eh_frame      0x0000b628      0x298 obj\Debug\hal\src\hal_mjpDecode.o
                                0x2ac (size before relaxing)
 .eh_frame      0x0000b8c0      0x248 obj\Debug\hal\src\hal_rtc.o
                                0x25c (size before relaxing)
 .eh_frame      0x0000bb08      0x280 obj\Debug\hal\src\hal_spi.o
                                0x294 (size before relaxing)
 .eh_frame      0x0000bd88      0x13c obj\Debug\hal\src\hal_spi1.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000bec4      0x104 obj\Debug\hal\src\hal_stream.o
                                0x118 (size before relaxing)
 .eh_frame      0x0000bfc8      0x178 obj\Debug\hal\src\hal_sys.o
                                0x18c (size before relaxing)
 .eh_frame      0x0000c140       0x78 obj\Debug\hal\src\hal_timer.o
                                 0x8c (size before relaxing)
 .eh_frame      0x0000c1b8      0x1b0 obj\Debug\hal\src\hal_uart.o
                                0x1c4 (size before relaxing)
 .eh_frame      0x0000c368      0x1fc obj\Debug\hal\src\hal_watermark.o
                                0x210 (size before relaxing)
 .eh_frame      0x0000c564       0xf0 obj\Debug\mcu\xos\xmsgq.o
                                0x104 (size before relaxing)
 .eh_frame      0x0000c654       0x88 obj\Debug\mcu\xos\xos.o
                                 0x9c (size before relaxing)
 .eh_frame      0x0000c6dc       0x74 obj\Debug\mcu\xos\xwork.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000c750      0x19c obj\Debug\multimedia\audio\audio_playback.o
                                0x1b0 (size before relaxing)
 .eh_frame      0x0000c8ec      0x134 obj\Debug\multimedia\audio\audio_record.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000ca20       0xb4 obj\Debug\multimedia\image\image_decode.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x0000cad4       0xa8 obj\Debug\multimedia\image\image_encode.o
                                 0xbc (size before relaxing)
 .eh_frame      0x0000cb7c       0x20 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                                 0x34 (size before relaxing)
 .eh_frame      0x0000cb9c      0x2a0 obj\Debug\multimedia\video\video_playback.o
                                0x2b4 (size before relaxing)
 .eh_frame      0x0000ce3c      0x23c obj\Debug\multimedia\video\video_record.o
                                0x250 (size before relaxing)
 .eh_frame      0x0000d078      0x3c4 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                                0x3d8 (size before relaxing)
 .eh_frame      0x0000d43c      0x134 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000d570       0x60 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000d5d0       0xc4 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x0000d694       0x80 obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000d714      0x144 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                                0x158 (size before relaxing)
 .eh_frame      0x0000d858       0x40 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000d898       0x38 obj\Debug\sys_manage\res_manage\res_manage_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000d8d0       0xb4 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x0000d984       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000d9c4       0x5c obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                                 0x70 (size before relaxing)
 .eh_frame      0x0000da20       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000da64      0x178 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                                0x18c (size before relaxing)
 .eh_frame      0x0000dbdc       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000dc1c       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000dc7c      0x358 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                                0x36c (size before relaxing)
 .eh_frame      0x0000dfd4       0x48 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                                 0x5c (size before relaxing)
 .eh_frame      0x0000e01c       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000e068       0x48 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                                 0x5c (size before relaxing)
 .eh_frame      0x0000e0b0       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000e0f0      0x56c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                                0x580 (size before relaxing)
 .eh_frame      0x0000e65c       0xd8 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000e734       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000e794       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000e7f4       0x70 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000e864       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000e8c4       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000e904       0x80 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000e984       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000e9c8      0x114 obj\Debug\app\app_common\src\app_init.o
                                0x128 (size before relaxing)
 .eh_frame      0x0000eadc      0x18c obj\Debug\app\app_common\src\app_lcdshow.o
                                0x1a0 (size before relaxing)
 .eh_frame      0x0000ec68       0x1c obj\Debug\app\app_common\src\main.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000ec84       0x8c obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x0000ed10       0x54 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000ed64       0x38 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000ed9c      0x13c obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000eed8      0x17c obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                0x190 (size before relaxing)
 .eh_frame      0x0000f054      0x130 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                                0x144 (size before relaxing)
 .eh_frame      0x0000f184      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000f2b8      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000f3f0      0x11c obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                0x130 (size before relaxing)
 .eh_frame      0x0000f50c      0x2c0 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                0x2d4 (size before relaxing)
 .eh_frame      0x0000f7cc      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000f904      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000fa54      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000fb8c      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000fcc4      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000fe14      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000ff4c       0x70 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000ffbc       0xac obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                                 0xc0 (size before relaxing)
 .eh_frame      0x00010068      0x194 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x000101fc      0x118 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                0x12c (size before relaxing)
 .eh_frame      0x00010314      0x114 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                0x128 (size before relaxing)
 .eh_frame      0x00010428       0x98 obj\Debug\app\task_windows\msg_api.o
                                 0xac (size before relaxing)
 .eh_frame      0x000104c0       0xb0 obj\Debug\app\task_windows\task_api.o
                                 0xc4 (size before relaxing)
 .eh_frame      0x00010570       0xb8 obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
                                 0xcc (size before relaxing)
 .eh_frame      0x00010628       0xf8 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
                                0x10c (size before relaxing)
 .eh_frame      0x00010720       0xbc obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                                 0xd0 (size before relaxing)
 .eh_frame      0x000107dc      0x458 obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x46c (size before relaxing)
 .eh_frame      0x00010c34       0x20 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00010c54       0x74 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00010cc8      0x200 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                0x214 (size before relaxing)
 .eh_frame      0x00010ec8      0x160 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x174 (size before relaxing)
 .eh_frame      0x00011028      0x308 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x31c (size before relaxing)
 .eh_frame      0x00011330      0x194 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x000114c4      0x210 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                0x224 (size before relaxing)
 .eh_frame      0x000116d4       0x20 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                                 0x34 (size before relaxing)
 .eh_frame      0x000116f4       0x7c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                                 0x90 (size before relaxing)
 .eh_frame      0x00011770       0xe0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x00011850       0xa0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                                 0xb4 (size before relaxing)
 .eh_frame      0x000118f0      0x340 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x354 (size before relaxing)
 .eh_frame      0x00011c30      0x15c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x170 (size before relaxing)
 .eh_frame      0x00011d8c      0x3f4 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                0x408 (size before relaxing)
 .eh_frame      0x00012180       0xa4 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00012224       0x9c obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x000122c0       0x54 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00012314      0x1a4 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                                0x1b8 (size before relaxing)
 .eh_frame      0x000124b8       0x78 obj\Debug\app\task_windows\windows_api.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00012530      0x154 obj\Debug\app\user_config\src\mbedtls_md5.o
                                0x168 (size before relaxing)
 .eh_frame      0x00012684      0x120 obj\Debug\app\user_config\src\user_config_api.o
                                0x134 (size before relaxing)
 .eh_frame      0x000127a4      0x1d0 ..\lib\libboot.a(boot.o)
                                0x1e4 (size before relaxing)
 .eh_frame      0x00012974       0x78 ..\lib\libboot.a(boot_lib.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x000129ec       0x68 ..\lib\libmcu.a(hx330x_adc.o)
                                 0x7c (size before relaxing)
 .eh_frame      0x00012a54      0x110 ..\lib\libmcu.a(hx330x_auadc.o)
                                0x124 (size before relaxing)
 .eh_frame      0x00012b64      0x4e4 ..\lib\libmcu.a(hx330x_csi.o)
                                0x4f8 (size before relaxing)
 .eh_frame      0x00013048      0x1d4 ..\lib\libmcu.a(hx330x_dac.o)
                                0x1e8 (size before relaxing)
 .eh_frame      0x0001321c       0xa4 ..\lib\libmcu.a(hx330x_dma.o)
                                 0xb8 (size before relaxing)
 .eh_frame      0x000132c0      0x170 ..\lib\libmcu.a(hx330x_dmauart.o)
                                0x184 (size before relaxing)
 .eh_frame      0x00013430      0x2c8 ..\lib\libmcu.a(hx330x_gpio.o)
                                0x2dc (size before relaxing)
 .eh_frame      0x000136f8      0x310 ..\lib\libmcu.a(hx330x_iic.o)
                                0x324 (size before relaxing)
 .eh_frame      0x00013a08      0x104 ..\lib\libmcu.a(hx330x_int.o)
                                0x118 (size before relaxing)
 .eh_frame      0x00013b0c      0x2a0 ..\lib\libmcu.a(hx330x_isp.o)
                                0x2b4 (size before relaxing)
 .eh_frame      0x00013dac      0x744 ..\lib\libmcu.a(hx330x_jpg.o)
                                0x758 (size before relaxing)
 .eh_frame      0x000144f0       0x38 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00014528      0x3d4 ..\lib\libmcu.a(hx330x_lcd.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x000148fc       0xc4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x000149c0      0x3e4 ..\lib\libmcu.a(hx330x_lcdui.o)
                                0x3f8 (size before relaxing)
 .eh_frame      0x00014da4       0xe8 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                                 0xfc (size before relaxing)
 .eh_frame      0x00014e8c       0x80 ..\lib\libmcu.a(hx330x_lcdwin.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00014f0c       0x80 ..\lib\libmcu.a(hx330x_md.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00014f8c       0x5c ..\lib\libmcu.a(hx330x_mipi.o)
                                 0x70 (size before relaxing)
 .eh_frame      0x00014fe8      0x29c ..\lib\libmcu.a(hx330x_misc.o)
                                0x2b0 (size before relaxing)
 .eh_frame      0x00015284      0x378 ..\lib\libmcu.a(hx330x_rtc.o)
                                0x38c (size before relaxing)
 .eh_frame      0x000155fc      0x30c ..\lib\libmcu.a(hx330x_sd.o)
                                0x320 (size before relaxing)
 .eh_frame      0x00015908      0x108 ..\lib\libmcu.a(hx330x_spi0.o)
                                0x11c (size before relaxing)
 .eh_frame      0x00015a10      0x184 ..\lib\libmcu.a(hx330x_spi1.o)
                                0x198 (size before relaxing)
 .eh_frame      0x00015b94      0x3d8 ..\lib\libmcu.a(hx330x_sys.o)
                                0x3ec (size before relaxing)
 .eh_frame      0x00015f6c      0x188 ..\lib\libmcu.a(hx330x_timer.o)
                                0x19c (size before relaxing)
 .eh_frame      0x000160f4      0x110 ..\lib\libmcu.a(hx330x_tminf.o)
                                0x124 (size before relaxing)
 .eh_frame      0x00016204       0x78 ..\lib\libmcu.a(hx330x_uart.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x0001627c      0x444 ..\lib\libmcu.a(hx330x_usb.o)
                                0x458 (size before relaxing)
 .eh_frame      0x000166c0       0x60 ..\lib\libmcu.a(hx330x_wdt.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00016720       0xc4 ..\lib\libmcu.a(hx330x_emi.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x000167e4      0x300 ..\lib\libisp.a(hal_isp.o)
                                0x314 (size before relaxing)
 .eh_frame      0x00016ae4      0x3e4 ..\lib\libhusb.a(husb_enum.o)
                                0x3f8 (size before relaxing)
 .eh_frame      0x00016ec8      0x118 ..\lib\libjpg.a(hal_jpg.o)
                                0x12c (size before relaxing)
 .eh_frame      0x00016fe0      0x230 ..\lib\libjpg.a(hal_step_jpg.o)
                                0x244 (size before relaxing)
 .eh_frame      0x00017210      0x2f0 ..\lib\liblcd.a(hal_lcd.o)
                                0x304 (size before relaxing)
 .eh_frame      0x00017500      0x138 ..\lib\liblcd.a(hal_lcdMem.o)
                                0x14c (size before relaxing)
 .eh_frame      0x00017638       0x84 ..\lib\liblcd.a(hal_lcdrotate.o)
                                 0x98 (size before relaxing)
 .eh_frame      0x000176bc      0x238 ..\lib\liblcd.a(hal_lcdUi.o)
                                0x24c (size before relaxing)
 .eh_frame      0x000178f4       0x60 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00017954       0x58 ..\lib\liblcd.a(lcd_tab.o)
                                 0x6c (size before relaxing)
 .eh_frame      0x000179ac      0x208 ..\lib\libmultimedia.a(api_multimedia.o)
                                0x21c (size before relaxing)
 .eh_frame      0x00017bb4      0x148 ..\lib\libmultimedia.a(avi_dec.o)
                                0x15c (size before relaxing)
 .eh_frame      0x00017cfc      0x120 ..\lib\libmultimedia.a(avi_odml_enc.o)
                                0x134 (size before relaxing)
 .eh_frame      0x00017e1c      0x144 ..\lib\libmultimedia.a(avi_std_enc.o)
                                0x158 (size before relaxing)
 .eh_frame      0x00017f60      0x1e4 ..\lib\libmultimedia.a(gif_dec.o)
                                0x1f8 (size before relaxing)
 .eh_frame      0x00018144       0xb8 ..\lib\libmultimedia.a(wav_dec.o)
                                 0xcc (size before relaxing)
 .eh_frame      0x000181fc       0xa0 ..\lib\libmultimedia.a(wav_enc.o)
                                 0xb4 (size before relaxing)
 .eh_frame      0x0001829c       0x38 ..\lib\libmultimedia.a(wav_pcm.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x000182d4       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x00018300       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x0001832c       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                 0x40 (size before relaxing)

.rela.dyn       0x00018358        0x0 load address 0x000ad838
 .rela.text     0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.data     0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.kepttext
                0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.rodata   0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sdram_text
                0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sensor_res.header
                0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.text.startup
                0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.text
                0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.bootsec  0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector   0x00018358        0x0 obj\Debug\dev\battery\src\battery_api.o

.mp3_text       0x00008000        0x0 load address 0x0009ca00
                0x00008000                __mp3_text_start = .
 *(.mp3_text)
                0x00008000                . = ALIGN (0x4)

.mp3_code       0x00200000        0x0 load address 0x0009ca00
                0x00200000                __mp3_code_start = .
 *(.mp3_code)
                0x00200000                . = ALIGN (0x4)

.mp3_data
 *(.mp3_data)

.nes_code       0x00200000        0x0 load address 0x0009ca00
                0x00200000                __nes_code_start = .
 *(.nes_code)
                0x00200000                . = ALIGN (0x4)

.nes_data
 *(.nes_data)

.nes_com_text   0x00008000        0x0 load address 0x0009ca00
                0x00008000                __nes_text_start = .
 *(.nes_com_text)
                0x00008000                . = ALIGN (0x4)

.mapper000      0x00008000        0x0 load address 0x0009ca00
 *(.mapper000_text)
 *(.mapper000_data)
                0x0009ca00                PROVIDE (__load_start_mapper000, LOADADDR (.mapper000))
                0x0009ca00                PROVIDE (__load_stop_mapper000, (LOADADDR (.mapper000) + SIZEOF (.mapper000)))

.mapper001      0x00008000        0x0
 *(.mapper001_text)
 *(.mapper001_data)
                0x00008000                PROVIDE (__load_start_mapper001, LOADADDR (.mapper001))
                0x00008000                PROVIDE (__load_stop_mapper001, (LOADADDR (.mapper001) + SIZEOF (.mapper001)))

.mapper002      0x00008000        0x0
 *(.mapper002_text)
 *(.mapper002_data)
                0x00008000                PROVIDE (__load_start_mapper002, LOADADDR (.mapper002))
                0x00008000                PROVIDE (__load_stop_mapper002, (LOADADDR (.mapper002) + SIZEOF (.mapper002)))

.mapper003      0x00008000        0x0
 *(.mapper003_text)
 *(.mapper003_data)
                0x00008000                PROVIDE (__load_start_mapper003, LOADADDR (.mapper003))
                0x00008000                PROVIDE (__load_stop_mapper003, (LOADADDR (.mapper003) + SIZEOF (.mapper003)))

.mapper004      0x00008000        0x0
 *(.mapper004_text)
 *(.mapper004_data)
                0x00008000                PROVIDE (__load_start_mapper004, LOADADDR (.mapper004))
                0x00008000                PROVIDE (__load_stop_mapper004, (LOADADDR (.mapper004) + SIZEOF (.mapper004)))

.mapper005      0x00008000        0x0
 *(.mapper005_text)
 *(.mapper005_data)
                0x00008000                PROVIDE (__load_start_mapper005, LOADADDR (.mapper005))
                0x00008000                PROVIDE (__load_stop_mapper005, (LOADADDR (.mapper005) + SIZEOF (.mapper005)))

.mapper006      0x00008000        0x0
 *(.mapper006_text)
 *(.mapper006_data)
                0x00008000                PROVIDE (__load_start_mapper006, LOADADDR (.mapper006))
                0x00008000                PROVIDE (__load_stop_mapper006, (LOADADDR (.mapper006) + SIZEOF (.mapper006)))

.mapper007      0x00008000        0x0
 *(.mapper007_text)
 *(.mapper007_data)
                0x00008000                PROVIDE (__load_start_mapper007, LOADADDR (.mapper007))
                0x00008000                PROVIDE (__load_stop_mapper007, (LOADADDR (.mapper007) + SIZEOF (.mapper007)))

.mapper008      0x00008000        0x0
 *(.mapper008_text)
 *(.mapper008_data)
                0x00008000                PROVIDE (__load_start_mapper008, LOADADDR (.mapper008))
                0x00008000                PROVIDE (__load_stop_mapper008, (LOADADDR (.mapper008) + SIZEOF (.mapper008)))

.mapper009      0x00008000        0x0
 *(.mapper009_text)
 *(.mapper009_data)
                0x00008000                PROVIDE (__load_start_mapper009, LOADADDR (.mapper009))
                0x00008000                PROVIDE (__load_stop_mapper009, (LOADADDR (.mapper009) + SIZEOF (.mapper009)))

.mapper010      0x00008000        0x0
 *(.mapper010_text)
 *(.mapper010_data)
                0x00008000                PROVIDE (__load_start_mapper010, LOADADDR (.mapper010))
                0x00008000                PROVIDE (__load_stop_mapper010, (LOADADDR (.mapper010) + SIZEOF (.mapper010)))

.mapper011      0x00008000        0x0
 *(.mapper011_text)
 *(.mapper011_data)
                0x00008000                PROVIDE (__load_start_mapper011, LOADADDR (.mapper011))
                0x00008000                PROVIDE (__load_stop_mapper011, (LOADADDR (.mapper011) + SIZEOF (.mapper011)))

.mapper012      0x00008000        0x0
 *(.mapper012_text)
 *(.mapper012_data)
                0x00008000                PROVIDE (__load_start_mapper012, LOADADDR (.mapper012))
                0x00008000                PROVIDE (__load_stop_mapper012, (LOADADDR (.mapper012) + SIZEOF (.mapper012)))

.mapper013      0x00008000        0x0
 *(.mapper013_text)
 *(.mapper013_data)
                0x00008000                PROVIDE (__load_start_mapper013, LOADADDR (.mapper013))
                0x00008000                PROVIDE (__load_stop_mapper013, (LOADADDR (.mapper013) + SIZEOF (.mapper013)))

.mapper014      0x00008000        0x0
 *(.mapper014_text)
 *(.mapper014_data)
                0x00008000                PROVIDE (__load_start_mapper014, LOADADDR (.mapper014))
                0x00008000                PROVIDE (__load_stop_mapper014, (LOADADDR (.mapper014) + SIZEOF (.mapper014)))

.mapper015      0x00008000        0x0
 *(.mapper015_text)
 *(.mapper015_data)
                0x00008000                PROVIDE (__load_start_mapper015, LOADADDR (.mapper015))
                0x00008000                PROVIDE (__load_stop_mapper015, (LOADADDR (.mapper015) + SIZEOF (.mapper015)))

.mapper016      0x00008000        0x0
 *(.mapper016_text)
 *(.mapper016_data)
                0x00008000                PROVIDE (__load_start_mapper016, LOADADDR (.mapper016))
                0x00008000                PROVIDE (__load_stop_mapper016, (LOADADDR (.mapper016) + SIZEOF (.mapper016)))

.mapper017      0x00008000        0x0
 *(.mapper017_text)
 *(.mapper017_data)
                0x00008000                PROVIDE (__load_start_mapper017, LOADADDR (.mapper017))
                0x00008000                PROVIDE (__load_stop_mapper017, (LOADADDR (.mapper017) + SIZEOF (.mapper017)))

.mapper018      0x00008000        0x0
 *(.mapper018_text)
 *(.mapper018_data)
                0x00008000                PROVIDE (__load_start_mapper018, LOADADDR (.mapper018))
                0x00008000                PROVIDE (__load_stop_mapper018, (LOADADDR (.mapper018) + SIZEOF (.mapper018)))

.mapper019      0x00008000        0x0
 *(.mapper019_text)
 *(.mapper019_data)
                0x00008000                PROVIDE (__load_start_mapper019, LOADADDR (.mapper019))
                0x00008000                PROVIDE (__load_stop_mapper019, (LOADADDR (.mapper019) + SIZEOF (.mapper019)))

.mapper020      0x00008000        0x0
 *(.mapper020_text)
 *(.mapper020_data)
                0x00008000                PROVIDE (__load_start_mapper020, LOADADDR (.mapper020))
                0x00008000                PROVIDE (__load_stop_mapper020, (LOADADDR (.mapper020) + SIZEOF (.mapper020)))

.mapper021      0x00008000        0x0
 *(.mapper021_text)
 *(.mapper021_data)
                0x00008000                PROVIDE (__load_start_mapper021, LOADADDR (.mapper021))
                0x00008000                PROVIDE (__load_stop_mapper021, (LOADADDR (.mapper021) + SIZEOF (.mapper021)))

.mapper022      0x00008000        0x0
 *(.mapper022_text)
 *(.mapper022_data)
                0x00008000                PROVIDE (__load_start_mapper022, LOADADDR (.mapper022))
                0x00008000                PROVIDE (__load_stop_mapper022, (LOADADDR (.mapper022) + SIZEOF (.mapper022)))

.mapper023      0x00008000        0x0
 *(.mapper023_text)
 *(.mapper023_data)
                0x00008000                PROVIDE (__load_start_mapper023, LOADADDR (.mapper023))
                0x00008000                PROVIDE (__load_stop_mapper023, (LOADADDR (.mapper023) + SIZEOF (.mapper023)))

.mapper024      0x00008000        0x0
 *(.mapper024_text)
 *(.mapper024_data)
                0x00008000                PROVIDE (__load_start_mapper024, LOADADDR (.mapper024))
                0x00008000                PROVIDE (__load_stop_mapper024, (LOADADDR (.mapper024) + SIZEOF (.mapper024)))

.mapper025      0x00008000        0x0
 *(.mapper025_text)
 *(.mapper025_data)
                0x00008000                PROVIDE (__load_start_mapper025, LOADADDR (.mapper025))
                0x00008000                PROVIDE (__load_stop_mapper025, (LOADADDR (.mapper025) + SIZEOF (.mapper025)))

.mapper026      0x00008000        0x0
 *(.mapper026_text)
 *(.mapper026_data)
                0x00008000                PROVIDE (__load_start_mapper026, LOADADDR (.mapper026))
                0x00008000                PROVIDE (__load_stop_mapper026, (LOADADDR (.mapper026) + SIZEOF (.mapper026)))

.mapper027      0x00008000        0x0
 *(.mapper027_text)
 *(.mapper027_data)
                0x00008000                PROVIDE (__load_start_mapper027, LOADADDR (.mapper027))
                0x00008000                PROVIDE (__load_stop_mapper027, (LOADADDR (.mapper027) + SIZEOF (.mapper027)))

.mapper028      0x00008000        0x0
 *(.mapper028_text)
 *(.mapper028_data)
                0x00008000                PROVIDE (__load_start_mapper028, LOADADDR (.mapper028))
                0x00008000                PROVIDE (__load_stop_mapper028, (LOADADDR (.mapper028) + SIZEOF (.mapper028)))

.mapper029      0x00008000        0x0
 *(.mapper029_text)
 *(.mapper029_data)
                0x00008000                PROVIDE (__load_start_mapper029, LOADADDR (.mapper029))
                0x00008000                PROVIDE (__load_stop_mapper029, (LOADADDR (.mapper029) + SIZEOF (.mapper029)))

.mapper030      0x00008000        0x0
 *(.mapper030_text)
 *(.mapper030_data)
                0x00008000                PROVIDE (__load_start_mapper030, LOADADDR (.mapper030))
                0x00008000                PROVIDE (__load_stop_mapper030, (LOADADDR (.mapper030) + SIZEOF (.mapper030)))

.mapper031      0x00008000        0x0
 *(.mapper031_text)
 *(.mapper031_data)
                0x00008000                PROVIDE (__load_start_mapper031, LOADADDR (.mapper031))
                0x00008000                PROVIDE (__load_stop_mapper031, (LOADADDR (.mapper031) + SIZEOF (.mapper031)))

.mapper032      0x00008000        0x0
 *(.mapper032_text)
 *(.mapper032_data)
                0x00008000                PROVIDE (__load_start_mapper032, LOADADDR (.mapper032))
                0x00008000                PROVIDE (__load_stop_mapper032, (LOADADDR (.mapper032) + SIZEOF (.mapper032)))

.mapper033      0x00008000        0x0
 *(.mapper033_text)
 *(.mapper033_data)
                0x00008000                PROVIDE (__load_start_mapper033, LOADADDR (.mapper033))
                0x00008000                PROVIDE (__load_stop_mapper033, (LOADADDR (.mapper033) + SIZEOF (.mapper033)))

.mapper034      0x00008000        0x0
 *(.mapper034_text)
 *(.mapper034_data)
                0x00008000                PROVIDE (__load_start_mapper034, LOADADDR (.mapper034))
                0x00008000                PROVIDE (__load_stop_mapper034, (LOADADDR (.mapper034) + SIZEOF (.mapper034)))

.mapper035      0x00008000        0x0
 *(.mapper035_text)
 *(.mapper035_data)
                0x00008000                PROVIDE (__load_start_mapper035, LOADADDR (.mapper035))
                0x00008000                PROVIDE (__load_stop_mapper035, (LOADADDR (.mapper035) + SIZEOF (.mapper035)))

.mapper036      0x00008000        0x0
 *(.mapper036_text)
 *(.mapper036_data)
                0x00008000                PROVIDE (__load_start_mapper036, LOADADDR (.mapper036))
                0x00008000                PROVIDE (__load_stop_mapper036, (LOADADDR (.mapper036) + SIZEOF (.mapper036)))

.mapper037      0x00008000        0x0
 *(.mapper037_text)
 *(.mapper037_data)
                0x00008000                PROVIDE (__load_start_mapper037, LOADADDR (.mapper037))
                0x00008000                PROVIDE (__load_stop_mapper037, (LOADADDR (.mapper037) + SIZEOF (.mapper037)))

.mapper038      0x00008000        0x0
 *(.mapper038_text)
 *(.mapper038_data)
                0x00008000                PROVIDE (__load_start_mapper038, LOADADDR (.mapper038))
                0x00008000                PROVIDE (__load_stop_mapper038, (LOADADDR (.mapper038) + SIZEOF (.mapper038)))

.mapper039      0x00008000        0x0
 *(.mapper039_text)
 *(.mapper039_data)
                0x00008000                PROVIDE (__load_start_mapper039, LOADADDR (.mapper039))
                0x00008000                PROVIDE (__load_stop_mapper039, (LOADADDR (.mapper039) + SIZEOF (.mapper039)))

.mapper040      0x00008000        0x0
 *(.mapper040_text)
 *(.mapper040_data)
                0x00008000                PROVIDE (__load_start_mapper040, LOADADDR (.mapper040))
                0x00008000                PROVIDE (__load_stop_mapper040, (LOADADDR (.mapper040) + SIZEOF (.mapper040)))

.mapper041      0x00008000        0x0
 *(.mapper041_text)
 *(.mapper041_data)
                0x00008000                PROVIDE (__load_start_mapper041, LOADADDR (.mapper041))
                0x00008000                PROVIDE (__load_stop_mapper041, (LOADADDR (.mapper041) + SIZEOF (.mapper041)))

.mapper042      0x00008000        0x0
 *(.mapper042_text)
 *(.mapper042_data)
                0x00008000                PROVIDE (__load_start_mapper042, LOADADDR (.mapper042))
                0x00008000                PROVIDE (__load_stop_mapper042, (LOADADDR (.mapper042) + SIZEOF (.mapper042)))

.mapper043      0x00008000        0x0
 *(.mapper043_text)
 *(.mapper043_data)
                0x00008000                PROVIDE (__load_start_mapper043, LOADADDR (.mapper043))
                0x00008000                PROVIDE (__load_stop_mapper043, (LOADADDR (.mapper043) + SIZEOF (.mapper043)))

.mapper044      0x00008000        0x0
 *(.mapper044_text)
 *(.mapper044_data)
                0x00008000                PROVIDE (__load_start_mapper044, LOADADDR (.mapper044))
                0x00008000                PROVIDE (__load_stop_mapper044, (LOADADDR (.mapper044) + SIZEOF (.mapper044)))

.mapper045      0x00008000        0x0
 *(.mapper045_text)
 *(.mapper045_data)
                0x00008000                PROVIDE (__load_start_mapper045, LOADADDR (.mapper045))
                0x00008000                PROVIDE (__load_stop_mapper045, (LOADADDR (.mapper045) + SIZEOF (.mapper045)))

.mapper046      0x00008000        0x0
 *(.mapper046_text)
 *(.mapper046_data)
                0x00008000                PROVIDE (__load_start_mapper046, LOADADDR (.mapper046))
                0x00008000                PROVIDE (__load_stop_mapper046, (LOADADDR (.mapper046) + SIZEOF (.mapper046)))

.mapper047      0x00008000        0x0
 *(.mapper047_text)
 *(.mapper047_data)
                0x00008000                PROVIDE (__load_start_mapper047, LOADADDR (.mapper047))
                0x00008000                PROVIDE (__load_stop_mapper047, (LOADADDR (.mapper047) + SIZEOF (.mapper047)))

.mapper048      0x00008000        0x0
 *(.mapper048_text)
 *(.mapper048_data)
                0x00008000                PROVIDE (__load_start_mapper048, LOADADDR (.mapper048))
                0x00008000                PROVIDE (__load_stop_mapper048, (LOADADDR (.mapper048) + SIZEOF (.mapper048)))

.mapper049      0x00008000        0x0
 *(.mapper049_text)
 *(.mapper049_data)
                0x00008000                PROVIDE (__load_start_mapper049, LOADADDR (.mapper049))
                0x00008000                PROVIDE (__load_stop_mapper049, (LOADADDR (.mapper049) + SIZEOF (.mapper049)))

.mapper050      0x00008000        0x0
 *(.mapper050_text)
 *(.mapper050_data)
                0x00008000                PROVIDE (__load_start_mapper050, LOADADDR (.mapper050))
                0x00008000                PROVIDE (__load_stop_mapper050, (LOADADDR (.mapper050) + SIZEOF (.mapper050)))

.mapper051      0x00008000        0x0
 *(.mapper051_text)
 *(.mapper051_data)
                0x00008000                PROVIDE (__load_start_mapper051, LOADADDR (.mapper051))
                0x00008000                PROVIDE (__load_stop_mapper051, (LOADADDR (.mapper051) + SIZEOF (.mapper051)))

.mapper052      0x00008000        0x0
 *(.mapper052_text)
 *(.mapper052_data)
                0x00008000                PROVIDE (__load_start_mapper052, LOADADDR (.mapper052))
                0x00008000                PROVIDE (__load_stop_mapper052, (LOADADDR (.mapper052) + SIZEOF (.mapper052)))

.mapper053      0x00008000        0x0
 *(.mapper053_text)
 *(.mapper053_data)
                0x00008000                PROVIDE (__load_start_mapper053, LOADADDR (.mapper053))
                0x00008000                PROVIDE (__load_stop_mapper053, (LOADADDR (.mapper053) + SIZEOF (.mapper053)))

.mapper054      0x00008000        0x0
 *(.mapper054_text)
 *(.mapper054_data)
                0x00008000                PROVIDE (__load_start_mapper054, LOADADDR (.mapper054))
                0x00008000                PROVIDE (__load_stop_mapper054, (LOADADDR (.mapper054) + SIZEOF (.mapper054)))

.mapper055      0x00008000        0x0
 *(.mapper055_text)
 *(.mapper055_data)
                0x00008000                PROVIDE (__load_start_mapper055, LOADADDR (.mapper055))
                0x00008000                PROVIDE (__load_stop_mapper055, (LOADADDR (.mapper055) + SIZEOF (.mapper055)))

.mapper056      0x00008000        0x0
 *(.mapper056_text)
 *(.mapper056_data)
                0x00008000                PROVIDE (__load_start_mapper056, LOADADDR (.mapper056))
                0x00008000                PROVIDE (__load_stop_mapper056, (LOADADDR (.mapper056) + SIZEOF (.mapper056)))

.mapper057      0x00008000        0x0
 *(.mapper057_text)
 *(.mapper057_data)
                0x00008000                PROVIDE (__load_start_mapper057, LOADADDR (.mapper057))
                0x00008000                PROVIDE (__load_stop_mapper057, (LOADADDR (.mapper057) + SIZEOF (.mapper057)))

.mapper058      0x00008000        0x0
 *(.mapper058_text)
 *(.mapper058_data)
                0x00008000                PROVIDE (__load_start_mapper058, LOADADDR (.mapper058))
                0x00008000                PROVIDE (__load_stop_mapper058, (LOADADDR (.mapper058) + SIZEOF (.mapper058)))

.mapper059      0x00008000        0x0
 *(.mapper059_text)
 *(.mapper059_data)
                0x00008000                PROVIDE (__load_start_mapper059, LOADADDR (.mapper059))
                0x00008000                PROVIDE (__load_stop_mapper059, (LOADADDR (.mapper059) + SIZEOF (.mapper059)))

.mapper060      0x00008000        0x0
 *(.mapper060_text)
 *(.mapper060_data)
                0x00008000                PROVIDE (__load_start_mapper060, LOADADDR (.mapper060))
                0x00008000                PROVIDE (__load_stop_mapper060, (LOADADDR (.mapper060) + SIZEOF (.mapper060)))

.mapper061      0x00008000        0x0
 *(.mapper061_text)
 *(.mapper061_data)
                0x00008000                PROVIDE (__load_start_mapper061, LOADADDR (.mapper061))
                0x00008000                PROVIDE (__load_stop_mapper061, (LOADADDR (.mapper061) + SIZEOF (.mapper061)))

.mapper062      0x00008000        0x0
 *(.mapper062_text)
 *(.mapper062_data)
                0x00008000                PROVIDE (__load_start_mapper062, LOADADDR (.mapper062))
                0x00008000                PROVIDE (__load_stop_mapper062, (LOADADDR (.mapper062) + SIZEOF (.mapper062)))

.mapper063      0x00008000        0x0
 *(.mapper063_text)
 *(.mapper063_data)
                0x00008000                PROVIDE (__load_start_mapper063, LOADADDR (.mapper063))
                0x00008000                PROVIDE (__load_stop_mapper063, (LOADADDR (.mapper063) + SIZEOF (.mapper063)))

.mapper064      0x00008000        0x0
 *(.mapper064_text)
 *(.mapper064_data)
                0x00008000                PROVIDE (__load_start_mapper064, LOADADDR (.mapper064))
                0x00008000                PROVIDE (__load_stop_mapper064, (LOADADDR (.mapper064) + SIZEOF (.mapper064)))

.mapper065      0x00008000        0x0
 *(.mapper065_text)
 *(.mapper065_data)
                0x00008000                PROVIDE (__load_start_mapper065, LOADADDR (.mapper065))
                0x00008000                PROVIDE (__load_stop_mapper065, (LOADADDR (.mapper065) + SIZEOF (.mapper065)))

.mapper066      0x00008000        0x0
 *(.mapper066_text)
 *(.mapper066_data)
                0x00008000                PROVIDE (__load_start_mapper066, LOADADDR (.mapper066))
                0x00008000                PROVIDE (__load_stop_mapper066, (LOADADDR (.mapper066) + SIZEOF (.mapper066)))

.mapper067      0x00008000        0x0
 *(.mapper067_text)
 *(.mapper067_data)
                0x00008000                PROVIDE (__load_start_mapper067, LOADADDR (.mapper067))
                0x00008000                PROVIDE (__load_stop_mapper067, (LOADADDR (.mapper067) + SIZEOF (.mapper067)))

.mapper068      0x00008000        0x0
 *(.mapper068_text)
 *(.mapper068_data)
                0x00008000                PROVIDE (__load_start_mapper068, LOADADDR (.mapper068))
                0x00008000                PROVIDE (__load_stop_mapper068, (LOADADDR (.mapper068) + SIZEOF (.mapper068)))

.mapper069      0x00008000        0x0
 *(.mapper069_text)
 *(.mapper069_data)
                0x00008000                PROVIDE (__load_start_mapper069, LOADADDR (.mapper069))
                0x00008000                PROVIDE (__load_stop_mapper069, (LOADADDR (.mapper069) + SIZEOF (.mapper069)))

.mapper070      0x00008000        0x0
 *(.mapper070_text)
 *(.mapper070_data)
                0x00008000                PROVIDE (__load_start_mapper070, LOADADDR (.mapper070))
                0x00008000                PROVIDE (__load_stop_mapper070, (LOADADDR (.mapper070) + SIZEOF (.mapper070)))

.mapper071      0x00008000        0x0
 *(.mapper071_text)
 *(.mapper071_data)
                0x00008000                PROVIDE (__load_start_mapper071, LOADADDR (.mapper071))
                0x00008000                PROVIDE (__load_stop_mapper071, (LOADADDR (.mapper071) + SIZEOF (.mapper071)))

.mapper072      0x00008000        0x0
 *(.mapper072_text)
 *(.mapper072_data)
                0x00008000                PROVIDE (__load_start_mapper072, LOADADDR (.mapper072))
                0x00008000                PROVIDE (__load_stop_mapper072, (LOADADDR (.mapper072) + SIZEOF (.mapper072)))

.mapper073      0x00008000        0x0
 *(.mapper073_text)
 *(.mapper073_data)
                0x00008000                PROVIDE (__load_start_mapper073, LOADADDR (.mapper073))
                0x00008000                PROVIDE (__load_stop_mapper073, (LOADADDR (.mapper073) + SIZEOF (.mapper073)))

.mapper074      0x00008000        0x0
 *(.mapper074_text)
 *(.mapper074_data)
                0x00008000                PROVIDE (__load_start_mapper074, LOADADDR (.mapper074))
                0x00008000                PROVIDE (__load_stop_mapper074, (LOADADDR (.mapper074) + SIZEOF (.mapper074)))

.mapper075      0x00008000        0x0
 *(.mapper075_text)
 *(.mapper075_data)
                0x00008000                PROVIDE (__load_start_mapper075, LOADADDR (.mapper075))
                0x00008000                PROVIDE (__load_stop_mapper075, (LOADADDR (.mapper075) + SIZEOF (.mapper075)))

.mapper076      0x00008000        0x0
 *(.mapper076_text)
 *(.mapper076_data)
                0x00008000                PROVIDE (__load_start_mapper076, LOADADDR (.mapper076))
                0x00008000                PROVIDE (__load_stop_mapper076, (LOADADDR (.mapper076) + SIZEOF (.mapper076)))

.mapper077      0x00008000        0x0
 *(.mapper077_text)
 *(.mapper077_data)
                0x00008000                PROVIDE (__load_start_mapper077, LOADADDR (.mapper077))
                0x00008000                PROVIDE (__load_stop_mapper077, (LOADADDR (.mapper077) + SIZEOF (.mapper077)))

.mapper078      0x00008000        0x0
 *(.mapper078_text)
 *(.mapper078_data)
                0x00008000                PROVIDE (__load_start_mapper078, LOADADDR (.mapper078))
                0x00008000                PROVIDE (__load_stop_mapper078, (LOADADDR (.mapper078) + SIZEOF (.mapper078)))

.mapper079      0x00008000        0x0
 *(.mapper079_text)
 *(.mapper079_data)
                0x00008000                PROVIDE (__load_start_mapper079, LOADADDR (.mapper079))
                0x00008000                PROVIDE (__load_stop_mapper079, (LOADADDR (.mapper079) + SIZEOF (.mapper079)))

.mapper080      0x00008000        0x0
 *(.mapper080_text)
 *(.mapper080_data)
                0x00008000                PROVIDE (__load_start_mapper080, LOADADDR (.mapper080))
                0x00008000                PROVIDE (__load_stop_mapper080, (LOADADDR (.mapper080) + SIZEOF (.mapper080)))

.mapper081      0x00008000        0x0
 *(.mapper081_text)
 *(.mapper081_data)
                0x00008000                PROVIDE (__load_start_mapper081, LOADADDR (.mapper081))
                0x00008000                PROVIDE (__load_stop_mapper081, (LOADADDR (.mapper081) + SIZEOF (.mapper081)))

.mapper082      0x00008000        0x0
 *(.mapper082_text)
 *(.mapper082_data)
                0x00008000                PROVIDE (__load_start_mapper082, LOADADDR (.mapper082))
                0x00008000                PROVIDE (__load_stop_mapper082, (LOADADDR (.mapper082) + SIZEOF (.mapper082)))

.mapper083      0x00008000        0x0
 *(.mapper083_text)
 *(.mapper083_data)
                0x00008000                PROVIDE (__load_start_mapper083, LOADADDR (.mapper083))
                0x00008000                PROVIDE (__load_stop_mapper083, (LOADADDR (.mapper083) + SIZEOF (.mapper083)))

.mapper084      0x00008000        0x0
 *(.mapper084_text)
 *(.mapper084_data)
                0x00008000                PROVIDE (__load_start_mapper084, LOADADDR (.mapper084))
                0x00008000                PROVIDE (__load_stop_mapper084, (LOADADDR (.mapper084) + SIZEOF (.mapper084)))

.mapper085      0x00008000        0x0
 *(.mapper085_text)
 *(.mapper085_data)
                0x00008000                PROVIDE (__load_start_mapper085, LOADADDR (.mapper085))
                0x00008000                PROVIDE (__load_stop_mapper085, (LOADADDR (.mapper085) + SIZEOF (.mapper085)))

.mapper086      0x00008000        0x0
 *(.mapper086_text)
 *(.mapper086_data)
                0x00008000                PROVIDE (__load_start_mapper086, LOADADDR (.mapper086))
                0x00008000                PROVIDE (__load_stop_mapper086, (LOADADDR (.mapper086) + SIZEOF (.mapper086)))

.mapper087      0x00008000        0x0
 *(.mapper087_text)
 *(.mapper087_data)
                0x00008000                PROVIDE (__load_start_mapper087, LOADADDR (.mapper087))
                0x00008000                PROVIDE (__load_stop_mapper087, (LOADADDR (.mapper087) + SIZEOF (.mapper087)))

.mapper088      0x00008000        0x0
 *(.mapper088_text)
 *(.mapper088_data)
                0x00008000                PROVIDE (__load_start_mapper088, LOADADDR (.mapper088))
                0x00008000                PROVIDE (__load_stop_mapper088, (LOADADDR (.mapper088) + SIZEOF (.mapper088)))

.mapper089      0x00008000        0x0
 *(.mapper089_text)
 *(.mapper089_data)
                0x00008000                PROVIDE (__load_start_mapper089, LOADADDR (.mapper089))
                0x00008000                PROVIDE (__load_stop_mapper089, (LOADADDR (.mapper089) + SIZEOF (.mapper089)))

.mapper090      0x00008000        0x0
 *(.mapper090_text)
 *(.mapper090_data)
                0x00008000                PROVIDE (__load_start_mapper090, LOADADDR (.mapper090))
                0x00008000                PROVIDE (__load_stop_mapper090, (LOADADDR (.mapper090) + SIZEOF (.mapper090)))

.mapper091      0x00008000        0x0
 *(.mapper091_text)
 *(.mapper091_data)
                0x00008000                PROVIDE (__load_start_mapper091, LOADADDR (.mapper091))
                0x00008000                PROVIDE (__load_stop_mapper091, (LOADADDR (.mapper091) + SIZEOF (.mapper091)))

.mapper092      0x00008000        0x0
 *(.mapper092_text)
 *(.mapper092_data)
                0x00008000                PROVIDE (__load_start_mapper092, LOADADDR (.mapper092))
                0x00008000                PROVIDE (__load_stop_mapper092, (LOADADDR (.mapper092) + SIZEOF (.mapper092)))

.mapper093      0x00008000        0x0
 *(.mapper093_text)
 *(.mapper093_data)
                0x00008000                PROVIDE (__load_start_mapper093, LOADADDR (.mapper093))
                0x00008000                PROVIDE (__load_stop_mapper093, (LOADADDR (.mapper093) + SIZEOF (.mapper093)))

.mapper094      0x00008000        0x0
 *(.mapper094_text)
 *(.mapper094_data)
                0x00008000                PROVIDE (__load_start_mapper094, LOADADDR (.mapper094))
                0x00008000                PROVIDE (__load_stop_mapper094, (LOADADDR (.mapper094) + SIZEOF (.mapper094)))

.mapper095      0x00008000        0x0
 *(.mapper095_text)
 *(.mapper095_data)
                0x00008000                PROVIDE (__load_start_mapper095, LOADADDR (.mapper095))
                0x00008000                PROVIDE (__load_stop_mapper095, (LOADADDR (.mapper095) + SIZEOF (.mapper095)))

.mapper096      0x00008000        0x0
 *(.mapper096_text)
 *(.mapper096_data)
                0x00008000                PROVIDE (__load_start_mapper096, LOADADDR (.mapper096))
                0x00008000                PROVIDE (__load_stop_mapper096, (LOADADDR (.mapper096) + SIZEOF (.mapper096)))

.mapper097      0x00008000        0x0
 *(.mapper097_text)
 *(.mapper097_data)
                0x00008000                PROVIDE (__load_start_mapper097, LOADADDR (.mapper097))
                0x00008000                PROVIDE (__load_stop_mapper097, (LOADADDR (.mapper097) + SIZEOF (.mapper097)))

.mapper098      0x00008000        0x0
 *(.mapper098_text)
 *(.mapper098_data)
                0x00008000                PROVIDE (__load_start_mapper098, LOADADDR (.mapper098))
                0x00008000                PROVIDE (__load_stop_mapper098, (LOADADDR (.mapper098) + SIZEOF (.mapper098)))

.mapper099      0x00008000        0x0
 *(.mapper099_text)
 *(.mapper099_data)
                0x00008000                PROVIDE (__load_start_mapper099, LOADADDR (.mapper099))
                0x00008000                PROVIDE (__load_stop_mapper099, (LOADADDR (.mapper099) + SIZEOF (.mapper099)))

.mapper100      0x00008000        0x0
 *(.mapper100_text)
 *(.mapper100_data)
                0x00008000                PROVIDE (__load_start_mapper100, LOADADDR (.mapper100))
                0x00008000                PROVIDE (__load_stop_mapper100, (LOADADDR (.mapper100) + SIZEOF (.mapper100)))

.mapper101      0x00008000        0x0
 *(.mapper101_text)
 *(.mapper101_data)
                0x00008000                PROVIDE (__load_start_mapper101, LOADADDR (.mapper101))
                0x00008000                PROVIDE (__load_stop_mapper101, (LOADADDR (.mapper101) + SIZEOF (.mapper101)))

.mapper102      0x00008000        0x0
 *(.mapper102_text)
 *(.mapper102_data)
                0x00008000                PROVIDE (__load_start_mapper102, LOADADDR (.mapper102))
                0x00008000                PROVIDE (__load_stop_mapper102, (LOADADDR (.mapper102) + SIZEOF (.mapper102)))

.mapper103      0x00008000        0x0
 *(.mapper103_text)
 *(.mapper103_data)
                0x00008000                PROVIDE (__load_start_mapper103, LOADADDR (.mapper103))
                0x00008000                PROVIDE (__load_stop_mapper103, (LOADADDR (.mapper103) + SIZEOF (.mapper103)))

.mapper104      0x00008000        0x0
 *(.mapper104_text)
 *(.mapper104_data)
                0x00008000                PROVIDE (__load_start_mapper104, LOADADDR (.mapper104))
                0x00008000                PROVIDE (__load_stop_mapper104, (LOADADDR (.mapper104) + SIZEOF (.mapper104)))

.mapper105      0x00008000        0x0
 *(.mapper105_text)
 *(.mapper105_data)
                0x00008000                PROVIDE (__load_start_mapper105, LOADADDR (.mapper105))
                0x00008000                PROVIDE (__load_stop_mapper105, (LOADADDR (.mapper105) + SIZEOF (.mapper105)))

.mapper106      0x00008000        0x0
 *(.mapper106_text)
 *(.mapper106_data)
                0x00008000                PROVIDE (__load_start_mapper106, LOADADDR (.mapper106))
                0x00008000                PROVIDE (__load_stop_mapper106, (LOADADDR (.mapper106) + SIZEOF (.mapper106)))

.mapper107      0x00008000        0x0
 *(.mapper107_text)
 *(.mapper107_data)
                0x00008000                PROVIDE (__load_start_mapper107, LOADADDR (.mapper107))
                0x00008000                PROVIDE (__load_stop_mapper107, (LOADADDR (.mapper107) + SIZEOF (.mapper107)))

.mapper108      0x00008000        0x0
 *(.mapper108_text)
 *(.mapper108_data)
                0x00008000                PROVIDE (__load_start_mapper108, LOADADDR (.mapper108))
                0x00008000                PROVIDE (__load_stop_mapper108, (LOADADDR (.mapper108) + SIZEOF (.mapper108)))

.mapper109      0x00008000        0x0
 *(.mapper109_text)
 *(.mapper109_data)
                0x00008000                PROVIDE (__load_start_mapper109, LOADADDR (.mapper109))
                0x00008000                PROVIDE (__load_stop_mapper109, (LOADADDR (.mapper109) + SIZEOF (.mapper109)))

.mapper110      0x00008000        0x0
 *(.mapper110_text)
 *(.mapper110_data)
                0x00008000                PROVIDE (__load_start_mapper110, LOADADDR (.mapper110))
                0x00008000                PROVIDE (__load_stop_mapper110, (LOADADDR (.mapper110) + SIZEOF (.mapper110)))

.mapper111      0x00008000        0x0
 *(.mapper111_text)
 *(.mapper111_data)
                0x00008000                PROVIDE (__load_start_mapper111, LOADADDR (.mapper111))
                0x00008000                PROVIDE (__load_stop_mapper111, (LOADADDR (.mapper111) + SIZEOF (.mapper111)))

.mapper112      0x00008000        0x0
 *(.mapper112_text)
 *(.mapper112_data)
                0x00008000                PROVIDE (__load_start_mapper112, LOADADDR (.mapper112))
                0x00008000                PROVIDE (__load_stop_mapper112, (LOADADDR (.mapper112) + SIZEOF (.mapper112)))

.mapper113      0x00008000        0x0
 *(.mapper113_text)
 *(.mapper113_data)
                0x00008000                PROVIDE (__load_start_mapper113, LOADADDR (.mapper113))
                0x00008000                PROVIDE (__load_stop_mapper113, (LOADADDR (.mapper113) + SIZEOF (.mapper113)))

.mapper114      0x00008000        0x0
 *(.mapper114_text)
 *(.mapper114_data)
                0x00008000                PROVIDE (__load_start_mapper114, LOADADDR (.mapper114))
                0x00008000                PROVIDE (__load_stop_mapper114, (LOADADDR (.mapper114) + SIZEOF (.mapper114)))

.mapper115      0x00008000        0x0
 *(.mapper115_text)
 *(.mapper115_data)
                0x00008000                PROVIDE (__load_start_mapper115, LOADADDR (.mapper115))
                0x00008000                PROVIDE (__load_stop_mapper115, (LOADADDR (.mapper115) + SIZEOF (.mapper115)))

.mapper116      0x00008000        0x0
 *(.mapper116_text)
 *(.mapper116_data)
                0x00008000                PROVIDE (__load_start_mapper116, LOADADDR (.mapper116))
                0x00008000                PROVIDE (__load_stop_mapper116, (LOADADDR (.mapper116) + SIZEOF (.mapper116)))

.mapper117      0x00008000        0x0
 *(.mapper117_text)
 *(.mapper117_data)
                0x00008000                PROVIDE (__load_start_mapper117, LOADADDR (.mapper117))
                0x00008000                PROVIDE (__load_stop_mapper117, (LOADADDR (.mapper117) + SIZEOF (.mapper117)))

.mapper118      0x00008000        0x0
 *(.mapper118_text)
 *(.mapper118_data)
                0x00008000                PROVIDE (__load_start_mapper118, LOADADDR (.mapper118))
                0x00008000                PROVIDE (__load_stop_mapper118, (LOADADDR (.mapper118) + SIZEOF (.mapper118)))

.mapper119      0x00008000        0x0
 *(.mapper119_text)
 *(.mapper119_data)
                0x00008000                PROVIDE (__load_start_mapper119, LOADADDR (.mapper119))
                0x00008000                PROVIDE (__load_stop_mapper119, (LOADADDR (.mapper119) + SIZEOF (.mapper119)))

.mapper120      0x00008000        0x0
 *(.mapper120_text)
 *(.mapper120_data)
                0x00008000                PROVIDE (__load_start_mapper120, LOADADDR (.mapper120))
                0x00008000                PROVIDE (__load_stop_mapper120, (LOADADDR (.mapper120) + SIZEOF (.mapper120)))

.mapper121      0x00008000        0x0
 *(.mapper121_text)
 *(.mapper121_data)
                0x00008000                PROVIDE (__load_start_mapper121, LOADADDR (.mapper121))
                0x00008000                PROVIDE (__load_stop_mapper121, (LOADADDR (.mapper121) + SIZEOF (.mapper121)))

.mapper122      0x00008000        0x0
 *(.mapper122_text)
 *(.mapper122_data)
                0x00008000                PROVIDE (__load_start_mapper122, LOADADDR (.mapper122))
                0x00008000                PROVIDE (__load_stop_mapper122, (LOADADDR (.mapper122) + SIZEOF (.mapper122)))

.mapper123      0x00008000        0x0
 *(.mapper123_text)
 *(.mapper123_data)
                0x00008000                PROVIDE (__load_start_mapper123, LOADADDR (.mapper123))
                0x00008000                PROVIDE (__load_stop_mapper123, (LOADADDR (.mapper123) + SIZEOF (.mapper123)))

.mapper124      0x00008000        0x0
 *(.mapper124_text)
 *(.mapper124_data)
                0x00008000                PROVIDE (__load_start_mapper124, LOADADDR (.mapper124))
                0x00008000                PROVIDE (__load_stop_mapper124, (LOADADDR (.mapper124) + SIZEOF (.mapper124)))

.mapper125      0x00008000        0x0
 *(.mapper125_text)
 *(.mapper125_data)
                0x00008000                PROVIDE (__load_start_mapper125, LOADADDR (.mapper125))
                0x00008000                PROVIDE (__load_stop_mapper125, (LOADADDR (.mapper125) + SIZEOF (.mapper125)))

.mapper126      0x00008000        0x0
 *(.mapper126_text)
 *(.mapper126_data)
                0x00008000                PROVIDE (__load_start_mapper126, LOADADDR (.mapper126))
                0x00008000                PROVIDE (__load_stop_mapper126, (LOADADDR (.mapper126) + SIZEOF (.mapper126)))

.mapper127      0x00008000        0x0
 *(.mapper127_text)
 *(.mapper127_data)
                0x00008000                PROVIDE (__load_start_mapper127, LOADADDR (.mapper127))
                0x00008000                PROVIDE (__load_stop_mapper127, (LOADADDR (.mapper127) + SIZEOF (.mapper127)))

.mapper128      0x00008000        0x0
 *(.mapper128_text)
 *(.mapper128_data)
                0x00008000                PROVIDE (__load_start_mapper128, LOADADDR (.mapper128))
                0x00008000                PROVIDE (__load_stop_mapper128, (LOADADDR (.mapper128) + SIZEOF (.mapper128)))

.mapper129      0x00008000        0x0
 *(.mapper129_text)
 *(.mapper129_data)
                0x00008000                PROVIDE (__load_start_mapper129, LOADADDR (.mapper129))
                0x00008000                PROVIDE (__load_stop_mapper129, (LOADADDR (.mapper129) + SIZEOF (.mapper129)))

.mapper130      0x00008000        0x0
 *(.mapper130_text)
 *(.mapper130_data)
                0x00008000                PROVIDE (__load_start_mapper130, LOADADDR (.mapper130))
                0x00008000                PROVIDE (__load_stop_mapper130, (LOADADDR (.mapper130) + SIZEOF (.mapper130)))

.mapper131      0x00008000        0x0
 *(.mapper131_text)
 *(.mapper131_data)
                0x00008000                PROVIDE (__load_start_mapper131, LOADADDR (.mapper131))
                0x00008000                PROVIDE (__load_stop_mapper131, (LOADADDR (.mapper131) + SIZEOF (.mapper131)))

.mapper132      0x00008000        0x0
 *(.mapper132_text)
 *(.mapper132_data)
                0x00008000                PROVIDE (__load_start_mapper132, LOADADDR (.mapper132))
                0x00008000                PROVIDE (__load_stop_mapper132, (LOADADDR (.mapper132) + SIZEOF (.mapper132)))

.mapper133      0x00008000        0x0
 *(.mapper133_text)
 *(.mapper133_data)
                0x00008000                PROVIDE (__load_start_mapper133, LOADADDR (.mapper133))
                0x00008000                PROVIDE (__load_stop_mapper133, (LOADADDR (.mapper133) + SIZEOF (.mapper133)))

.mapper134      0x00008000        0x0
 *(.mapper134_text)
 *(.mapper134_data)
                0x00008000                PROVIDE (__load_start_mapper134, LOADADDR (.mapper134))
                0x00008000                PROVIDE (__load_stop_mapper134, (LOADADDR (.mapper134) + SIZEOF (.mapper134)))

.mapper135      0x00008000        0x0
 *(.mapper135_text)
 *(.mapper135_data)
                0x00008000                PROVIDE (__load_start_mapper135, LOADADDR (.mapper135))
                0x00008000                PROVIDE (__load_stop_mapper135, (LOADADDR (.mapper135) + SIZEOF (.mapper135)))

.mapper136      0x00008000        0x0
 *(.mapper136_text)
 *(.mapper136_data)
                0x00008000                PROVIDE (__load_start_mapper136, LOADADDR (.mapper136))
                0x00008000                PROVIDE (__load_stop_mapper136, (LOADADDR (.mapper136) + SIZEOF (.mapper136)))

.mapper137      0x00008000        0x0
 *(.mapper137_text)
 *(.mapper137_data)
                0x00008000                PROVIDE (__load_start_mapper137, LOADADDR (.mapper137))
                0x00008000                PROVIDE (__load_stop_mapper137, (LOADADDR (.mapper137) + SIZEOF (.mapper137)))

.mapper138      0x00008000        0x0
 *(.mapper138_text)
 *(.mapper138_data)
                0x00008000                PROVIDE (__load_start_mapper138, LOADADDR (.mapper138))
                0x00008000                PROVIDE (__load_stop_mapper138, (LOADADDR (.mapper138) + SIZEOF (.mapper138)))

.mapper139      0x00008000        0x0
 *(.mapper139_text)
 *(.mapper139_data)
                0x00008000                PROVIDE (__load_start_mapper139, LOADADDR (.mapper139))
                0x00008000                PROVIDE (__load_stop_mapper139, (LOADADDR (.mapper139) + SIZEOF (.mapper139)))

.mapper140      0x00008000        0x0
 *(.mapper140_text)
 *(.mapper140_data)
                0x00008000                PROVIDE (__load_start_mapper140, LOADADDR (.mapper140))
                0x00008000                PROVIDE (__load_stop_mapper140, (LOADADDR (.mapper140) + SIZEOF (.mapper140)))

.mapper141      0x00008000        0x0
 *(.mapper141_text)
 *(.mapper141_data)
                0x00008000                PROVIDE (__load_start_mapper141, LOADADDR (.mapper141))
                0x00008000                PROVIDE (__load_stop_mapper141, (LOADADDR (.mapper141) + SIZEOF (.mapper141)))

.mapper142      0x00008000        0x0
 *(.mapper142_text)
 *(.mapper142_data)
                0x00008000                PROVIDE (__load_start_mapper142, LOADADDR (.mapper142))
                0x00008000                PROVIDE (__load_stop_mapper142, (LOADADDR (.mapper142) + SIZEOF (.mapper142)))

.mapper143      0x00008000        0x0
 *(.mapper143_text)
 *(.mapper143_data)
                0x00008000                PROVIDE (__load_start_mapper143, LOADADDR (.mapper143))
                0x00008000                PROVIDE (__load_stop_mapper143, (LOADADDR (.mapper143) + SIZEOF (.mapper143)))

.mapper144      0x00008000        0x0
 *(.mapper144_text)
 *(.mapper144_data)
                0x00008000                PROVIDE (__load_start_mapper144, LOADADDR (.mapper144))
                0x00008000                PROVIDE (__load_stop_mapper144, (LOADADDR (.mapper144) + SIZEOF (.mapper144)))

.mapper145      0x00008000        0x0
 *(.mapper145_text)
 *(.mapper145_data)
                0x00008000                PROVIDE (__load_start_mapper145, LOADADDR (.mapper145))
                0x00008000                PROVIDE (__load_stop_mapper145, (LOADADDR (.mapper145) + SIZEOF (.mapper145)))

.mapper146      0x00008000        0x0
 *(.mapper146_text)
 *(.mapper146_data)
                0x00008000                PROVIDE (__load_start_mapper146, LOADADDR (.mapper146))
                0x00008000                PROVIDE (__load_stop_mapper146, (LOADADDR (.mapper146) + SIZEOF (.mapper146)))

.mapper147      0x00008000        0x0
 *(.mapper147_text)
 *(.mapper147_data)
                0x00008000                PROVIDE (__load_start_mapper147, LOADADDR (.mapper147))
                0x00008000                PROVIDE (__load_stop_mapper147, (LOADADDR (.mapper147) + SIZEOF (.mapper147)))

.mapper148      0x00008000        0x0
 *(.mapper148_text)
 *(.mapper148_data)
                0x00008000                PROVIDE (__load_start_mapper148, LOADADDR (.mapper148))
                0x00008000                PROVIDE (__load_stop_mapper148, (LOADADDR (.mapper148) + SIZEOF (.mapper148)))

.mapper149      0x00008000        0x0
 *(.mapper149_text)
 *(.mapper149_data)
                0x00008000                PROVIDE (__load_start_mapper149, LOADADDR (.mapper149))
                0x00008000                PROVIDE (__load_stop_mapper149, (LOADADDR (.mapper149) + SIZEOF (.mapper149)))

.mapper150      0x00008000        0x0
 *(.mapper150_text)
 *(.mapper150_data)
                0x00008000                PROVIDE (__load_start_mapper150, LOADADDR (.mapper150))
                0x00008000                PROVIDE (__load_stop_mapper150, (LOADADDR (.mapper150) + SIZEOF (.mapper150)))

.mapper151      0x00008000        0x0
 *(.mapper151_text)
 *(.mapper151_data)
                0x00008000                PROVIDE (__load_start_mapper151, LOADADDR (.mapper151))
                0x00008000                PROVIDE (__load_stop_mapper151, (LOADADDR (.mapper151) + SIZEOF (.mapper151)))

.mapper152      0x00008000        0x0
 *(.mapper152_text)
 *(.mapper152_data)
                0x00008000                PROVIDE (__load_start_mapper152, LOADADDR (.mapper152))
                0x00008000                PROVIDE (__load_stop_mapper152, (LOADADDR (.mapper152) + SIZEOF (.mapper152)))

.mapper153      0x00008000        0x0
 *(.mapper153_text)
 *(.mapper153_data)
                0x00008000                PROVIDE (__load_start_mapper153, LOADADDR (.mapper153))
                0x00008000                PROVIDE (__load_stop_mapper153, (LOADADDR (.mapper153) + SIZEOF (.mapper153)))

.mapper154      0x00008000        0x0
 *(.mapper154_text)
 *(.mapper154_data)
                0x00008000                PROVIDE (__load_start_mapper154, LOADADDR (.mapper154))
                0x00008000                PROVIDE (__load_stop_mapper154, (LOADADDR (.mapper154) + SIZEOF (.mapper154)))

.mapper155      0x00008000        0x0
 *(.mapper155_text)
 *(.mapper155_data)
                0x00008000                PROVIDE (__load_start_mapper155, LOADADDR (.mapper155))
                0x00008000                PROVIDE (__load_stop_mapper155, (LOADADDR (.mapper155) + SIZEOF (.mapper155)))

.mapper156      0x00008000        0x0
 *(.mapper156_text)
 *(.mapper156_data)
                0x00008000                PROVIDE (__load_start_mapper156, LOADADDR (.mapper156))
                0x00008000                PROVIDE (__load_stop_mapper156, (LOADADDR (.mapper156) + SIZEOF (.mapper156)))

.mapper157      0x00008000        0x0
 *(.mapper157_text)
 *(.mapper157_data)
                0x00008000                PROVIDE (__load_start_mapper157, LOADADDR (.mapper157))
                0x00008000                PROVIDE (__load_stop_mapper157, (LOADADDR (.mapper157) + SIZEOF (.mapper157)))

.mapper158      0x00008000        0x0
 *(.mapper158_text)
 *(.mapper158_data)
                0x00008000                PROVIDE (__load_start_mapper158, LOADADDR (.mapper158))
                0x00008000                PROVIDE (__load_stop_mapper158, (LOADADDR (.mapper158) + SIZEOF (.mapper158)))

.mapper159      0x00008000        0x0
 *(.mapper159_text)
 *(.mapper159_data)
                0x00008000                PROVIDE (__load_start_mapper159, LOADADDR (.mapper159))
                0x00008000                PROVIDE (__load_stop_mapper159, (LOADADDR (.mapper159) + SIZEOF (.mapper159)))

.mapper160      0x00008000        0x0
 *(.mapper160_text)
 *(.mapper160_data)
                0x00008000                PROVIDE (__load_start_mapper160, LOADADDR (.mapper160))
                0x00008000                PROVIDE (__load_stop_mapper160, (LOADADDR (.mapper160) + SIZEOF (.mapper160)))

.mapper161      0x00008000        0x0
 *(.mapper161_text)
 *(.mapper161_data)
                0x00008000                PROVIDE (__load_start_mapper161, LOADADDR (.mapper161))
                0x00008000                PROVIDE (__load_stop_mapper161, (LOADADDR (.mapper161) + SIZEOF (.mapper161)))

.mapper162      0x00008000        0x0
 *(.mapper162_text)
 *(.mapper162_data)
                0x00008000                PROVIDE (__load_start_mapper162, LOADADDR (.mapper162))
                0x00008000                PROVIDE (__load_stop_mapper162, (LOADADDR (.mapper162) + SIZEOF (.mapper162)))

.mapper163      0x00008000        0x0
 *(.mapper163_text)
 *(.mapper163_data)
                0x00008000                PROVIDE (__load_start_mapper163, LOADADDR (.mapper163))
                0x00008000                PROVIDE (__load_stop_mapper163, (LOADADDR (.mapper163) + SIZEOF (.mapper163)))

.mapper164      0x00008000        0x0
 *(.mapper164_text)
 *(.mapper164_data)
                0x00008000                PROVIDE (__load_start_mapper164, LOADADDR (.mapper164))
                0x00008000                PROVIDE (__load_stop_mapper164, (LOADADDR (.mapper164) + SIZEOF (.mapper164)))

.mapper165      0x00008000        0x0
 *(.mapper165_text)
 *(.mapper165_data)
                0x00008000                PROVIDE (__load_start_mapper165, LOADADDR (.mapper165))
                0x00008000                PROVIDE (__load_stop_mapper165, (LOADADDR (.mapper165) + SIZEOF (.mapper165)))

.mapper166      0x00008000        0x0
 *(.mapper166_text)
 *(.mapper166_data)
                0x00008000                PROVIDE (__load_start_mapper166, LOADADDR (.mapper166))
                0x00008000                PROVIDE (__load_stop_mapper166, (LOADADDR (.mapper166) + SIZEOF (.mapper166)))

.mapper167      0x00008000        0x0
 *(.mapper167_text)
 *(.mapper167_data)
                0x00008000                PROVIDE (__load_start_mapper167, LOADADDR (.mapper167))
                0x00008000                PROVIDE (__load_stop_mapper167, (LOADADDR (.mapper167) + SIZEOF (.mapper167)))

.mapper168      0x00008000        0x0
 *(.mapper168_text)
 *(.mapper168_data)
                0x00008000                PROVIDE (__load_start_mapper168, LOADADDR (.mapper168))
                0x00008000                PROVIDE (__load_stop_mapper168, (LOADADDR (.mapper168) + SIZEOF (.mapper168)))

.mapper169      0x00008000        0x0
 *(.mapper169_text)
 *(.mapper169_data)
                0x00008000                PROVIDE (__load_start_mapper169, LOADADDR (.mapper169))
                0x00008000                PROVIDE (__load_stop_mapper169, (LOADADDR (.mapper169) + SIZEOF (.mapper169)))

.mapper170      0x00008000        0x0
 *(.mapper170_text)
 *(.mapper170_data)
                0x00008000                PROVIDE (__load_start_mapper170, LOADADDR (.mapper170))
                0x00008000                PROVIDE (__load_stop_mapper170, (LOADADDR (.mapper170) + SIZEOF (.mapper170)))

.mapper171      0x00008000        0x0
 *(.mapper171_text)
 *(.mapper171_data)
                0x00008000                PROVIDE (__load_start_mapper171, LOADADDR (.mapper171))
                0x00008000                PROVIDE (__load_stop_mapper171, (LOADADDR (.mapper171) + SIZEOF (.mapper171)))

.mapper172      0x00008000        0x0
 *(.mapper172_text)
 *(.mapper172_data)
                0x00008000                PROVIDE (__load_start_mapper172, LOADADDR (.mapper172))
                0x00008000                PROVIDE (__load_stop_mapper172, (LOADADDR (.mapper172) + SIZEOF (.mapper172)))

.mapper173      0x00008000        0x0
 *(.mapper173_text)
 *(.mapper173_data)
                0x00008000                PROVIDE (__load_start_mapper173, LOADADDR (.mapper173))
                0x00008000                PROVIDE (__load_stop_mapper173, (LOADADDR (.mapper173) + SIZEOF (.mapper173)))

.mapper174      0x00008000        0x0
 *(.mapper174_text)
 *(.mapper174_data)
                0x00008000                PROVIDE (__load_start_mapper174, LOADADDR (.mapper174))
                0x00008000                PROVIDE (__load_stop_mapper174, (LOADADDR (.mapper174) + SIZEOF (.mapper174)))

.mapper175      0x00008000        0x0
 *(.mapper175_text)
 *(.mapper175_data)
                0x00008000                PROVIDE (__load_start_mapper175, LOADADDR (.mapper175))
                0x00008000                PROVIDE (__load_stop_mapper175, (LOADADDR (.mapper175) + SIZEOF (.mapper175)))

.mapper176      0x00008000        0x0
 *(.mapper176_text)
 *(.mapper176_data)
                0x00008000                PROVIDE (__load_start_mapper176, LOADADDR (.mapper176))
                0x00008000                PROVIDE (__load_stop_mapper176, (LOADADDR (.mapper176) + SIZEOF (.mapper176)))

.mapper177      0x00008000        0x0
 *(.mapper177_text)
 *(.mapper177_data)
                0x00008000                PROVIDE (__load_start_mapper177, LOADADDR (.mapper177))
                0x00008000                PROVIDE (__load_stop_mapper177, (LOADADDR (.mapper177) + SIZEOF (.mapper177)))

.mapper178      0x00008000        0x0
 *(.mapper178_text)
 *(.mapper178_data)
                0x00008000                PROVIDE (__load_start_mapper178, LOADADDR (.mapper178))
                0x00008000                PROVIDE (__load_stop_mapper178, (LOADADDR (.mapper178) + SIZEOF (.mapper178)))

.mapper179      0x00008000        0x0
 *(.mapper179_text)
 *(.mapper179_data)
                0x00008000                PROVIDE (__load_start_mapper179, LOADADDR (.mapper179))
                0x00008000                PROVIDE (__load_stop_mapper179, (LOADADDR (.mapper179) + SIZEOF (.mapper179)))

.mapper180      0x00008000        0x0
 *(.mapper180_text)
 *(.mapper180_data)
                0x00008000                PROVIDE (__load_start_mapper180, LOADADDR (.mapper180))
                0x00008000                PROVIDE (__load_stop_mapper180, (LOADADDR (.mapper180) + SIZEOF (.mapper180)))

.mapper181      0x00008000        0x0
 *(.mapper181_text)
 *(.mapper181_data)
                0x00008000                PROVIDE (__load_start_mapper181, LOADADDR (.mapper181))
                0x00008000                PROVIDE (__load_stop_mapper181, (LOADADDR (.mapper181) + SIZEOF (.mapper181)))

.mapper182      0x00008000        0x0
 *(.mapper182_text)
 *(.mapper182_data)
                0x00008000                PROVIDE (__load_start_mapper182, LOADADDR (.mapper182))
                0x00008000                PROVIDE (__load_stop_mapper182, (LOADADDR (.mapper182) + SIZEOF (.mapper182)))

.mapper183      0x00008000        0x0
 *(.mapper183_text)
 *(.mapper183_data)
                0x00008000                PROVIDE (__load_start_mapper183, LOADADDR (.mapper183))
                0x00008000                PROVIDE (__load_stop_mapper183, (LOADADDR (.mapper183) + SIZEOF (.mapper183)))

.mapper184      0x00008000        0x0
 *(.mapper184_text)
 *(.mapper184_data)
                0x00008000                PROVIDE (__load_start_mapper184, LOADADDR (.mapper184))
                0x00008000                PROVIDE (__load_stop_mapper184, (LOADADDR (.mapper184) + SIZEOF (.mapper184)))

.mapper185      0x00008000        0x0
 *(.mapper185_text)
 *(.mapper185_data)
                0x00008000                PROVIDE (__load_start_mapper185, LOADADDR (.mapper185))
                0x00008000                PROVIDE (__load_stop_mapper185, (LOADADDR (.mapper185) + SIZEOF (.mapper185)))

.mapper186      0x00008000        0x0
 *(.mapper186_text)
 *(.mapper186_data)
                0x00008000                PROVIDE (__load_start_mapper186, LOADADDR (.mapper186))
                0x00008000                PROVIDE (__load_stop_mapper186, (LOADADDR (.mapper186) + SIZEOF (.mapper186)))

.mapper187      0x00008000        0x0
 *(.mapper187_text)
 *(.mapper187_data)
                0x00008000                PROVIDE (__load_start_mapper187, LOADADDR (.mapper187))
                0x00008000                PROVIDE (__load_stop_mapper187, (LOADADDR (.mapper187) + SIZEOF (.mapper187)))

.mapper188      0x00008000        0x0
 *(.mapper188_text)
 *(.mapper188_data)
                0x00008000                PROVIDE (__load_start_mapper188, LOADADDR (.mapper188))
                0x00008000                PROVIDE (__load_stop_mapper188, (LOADADDR (.mapper188) + SIZEOF (.mapper188)))

.mapper189      0x00008000        0x0
 *(.mapper189_text)
 *(.mapper189_data)
                0x00008000                PROVIDE (__load_start_mapper189, LOADADDR (.mapper189))
                0x00008000                PROVIDE (__load_stop_mapper189, (LOADADDR (.mapper189) + SIZEOF (.mapper189)))

.mapper190      0x00008000        0x0
 *(.mapper190_text)
 *(.mapper190_data)
                0x00008000                PROVIDE (__load_start_mapper190, LOADADDR (.mapper190))
                0x00008000                PROVIDE (__load_stop_mapper190, (LOADADDR (.mapper190) + SIZEOF (.mapper190)))

.mapper191      0x00008000        0x0
 *(.mapper191_text)
 *(.mapper191_data)
                0x00008000                PROVIDE (__load_start_mapper191, LOADADDR (.mapper191))
                0x00008000                PROVIDE (__load_stop_mapper191, (LOADADDR (.mapper191) + SIZEOF (.mapper191)))

.mapper192      0x00008000        0x0
 *(.mapper192_text)
 *(.mapper192_data)
                0x00008000                PROVIDE (__load_start_mapper192, LOADADDR (.mapper192))
                0x00008000                PROVIDE (__load_stop_mapper192, (LOADADDR (.mapper192) + SIZEOF (.mapper192)))

.mapper193      0x00008000        0x0
 *(.mapper193_text)
 *(.mapper193_data)
                0x00008000                PROVIDE (__load_start_mapper193, LOADADDR (.mapper193))
                0x00008000                PROVIDE (__load_stop_mapper193, (LOADADDR (.mapper193) + SIZEOF (.mapper193)))

.mapper194      0x00008000        0x0
 *(.mapper194_text)
 *(.mapper194_data)
                0x00008000                PROVIDE (__load_start_mapper194, LOADADDR (.mapper194))
                0x00008000                PROVIDE (__load_stop_mapper194, (LOADADDR (.mapper194) + SIZEOF (.mapper194)))

.mapper195      0x00008000        0x0
 *(.mapper195_text)
 *(.mapper195_data)
                0x00008000                PROVIDE (__load_start_mapper195, LOADADDR (.mapper195))
                0x00008000                PROVIDE (__load_stop_mapper195, (LOADADDR (.mapper195) + SIZEOF (.mapper195)))

.mapper196      0x00008000        0x0
 *(.mapper196_text)
 *(.mapper196_data)
                0x00008000                PROVIDE (__load_start_mapper196, LOADADDR (.mapper196))
                0x00008000                PROVIDE (__load_stop_mapper196, (LOADADDR (.mapper196) + SIZEOF (.mapper196)))

.mapper197      0x00008000        0x0
 *(.mapper197_text)
 *(.mapper197_data)
                0x00008000                PROVIDE (__load_start_mapper197, LOADADDR (.mapper197))
                0x00008000                PROVIDE (__load_stop_mapper197, (LOADADDR (.mapper197) + SIZEOF (.mapper197)))

.mapper198      0x00008000        0x0
 *(.mapper198_text)
 *(.mapper198_data)
                0x00008000                PROVIDE (__load_start_mapper198, LOADADDR (.mapper198))
                0x00008000                PROVIDE (__load_stop_mapper198, (LOADADDR (.mapper198) + SIZEOF (.mapper198)))

.mapper199      0x00008000        0x0
 *(.mapper199_text)
 *(.mapper199_data)
                0x00008000                PROVIDE (__load_start_mapper199, LOADADDR (.mapper199))
                0x00008000                PROVIDE (__load_stop_mapper199, (LOADADDR (.mapper199) + SIZEOF (.mapper199)))

.mapper200      0x00008000        0x0
 *(.mapper200_text)
 *(.mapper200_data)
                0x00008000                PROVIDE (__load_start_mapper200, LOADADDR (.mapper200))
                0x00008000                PROVIDE (__load_stop_mapper200, (LOADADDR (.mapper200) + SIZEOF (.mapper200)))

.mapper201      0x00008000        0x0
 *(.mapper201_text)
 *(.mapper201_data)
                0x00008000                PROVIDE (__load_start_mapper201, LOADADDR (.mapper201))
                0x00008000                PROVIDE (__load_stop_mapper201, (LOADADDR (.mapper201) + SIZEOF (.mapper201)))

.mapper202      0x00008000        0x0
 *(.mapper202_text)
 *(.mapper202_data)
                0x00008000                PROVIDE (__load_start_mapper202, LOADADDR (.mapper202))
                0x00008000                PROVIDE (__load_stop_mapper202, (LOADADDR (.mapper202) + SIZEOF (.mapper202)))

.mapper203      0x00008000        0x0
 *(.mapper203_text)
 *(.mapper203_data)
                0x00008000                PROVIDE (__load_start_mapper203, LOADADDR (.mapper203))
                0x00008000                PROVIDE (__load_stop_mapper203, (LOADADDR (.mapper203) + SIZEOF (.mapper203)))

.mapper204      0x00008000        0x0
 *(.mapper204_text)
 *(.mapper204_data)
                0x00008000                PROVIDE (__load_start_mapper204, LOADADDR (.mapper204))
                0x00008000                PROVIDE (__load_stop_mapper204, (LOADADDR (.mapper204) + SIZEOF (.mapper204)))

.mapper205      0x00008000        0x0
 *(.mapper205_text)
 *(.mapper205_data)
                0x00008000                PROVIDE (__load_start_mapper205, LOADADDR (.mapper205))
                0x00008000                PROVIDE (__load_stop_mapper205, (LOADADDR (.mapper205) + SIZEOF (.mapper205)))

.mapper206      0x00008000        0x0
 *(.mapper206_text)
 *(.mapper206_data)
                0x00008000                PROVIDE (__load_start_mapper206, LOADADDR (.mapper206))
                0x00008000                PROVIDE (__load_stop_mapper206, (LOADADDR (.mapper206) + SIZEOF (.mapper206)))

.mapper207      0x00008000        0x0
 *(.mapper207_text)
 *(.mapper207_data)
                0x00008000                PROVIDE (__load_start_mapper207, LOADADDR (.mapper207))
                0x00008000                PROVIDE (__load_stop_mapper207, (LOADADDR (.mapper207) + SIZEOF (.mapper207)))

.mapper208      0x00008000        0x0
 *(.mapper208_text)
 *(.mapper208_data)
                0x00008000                PROVIDE (__load_start_mapper208, LOADADDR (.mapper208))
                0x00008000                PROVIDE (__load_stop_mapper208, (LOADADDR (.mapper208) + SIZEOF (.mapper208)))

.mapper209      0x00008000        0x0
 *(.mapper209_text)
 *(.mapper209_data)
                0x00008000                PROVIDE (__load_start_mapper209, LOADADDR (.mapper209))
                0x00008000                PROVIDE (__load_stop_mapper209, (LOADADDR (.mapper209) + SIZEOF (.mapper209)))

.mapper210      0x00008000        0x0
 *(.mapper210_text)
 *(.mapper210_data)
                0x00008000                PROVIDE (__load_start_mapper210, LOADADDR (.mapper210))
                0x00008000                PROVIDE (__load_stop_mapper210, (LOADADDR (.mapper210) + SIZEOF (.mapper210)))

.mapper211      0x00008000        0x0
 *(.mapper211_text)
 *(.mapper211_data)
                0x00008000                PROVIDE (__load_start_mapper211, LOADADDR (.mapper211))
                0x00008000                PROVIDE (__load_stop_mapper211, (LOADADDR (.mapper211) + SIZEOF (.mapper211)))

.mapper212      0x00008000        0x0
 *(.mapper212_text)
 *(.mapper212_data)
                0x00008000                PROVIDE (__load_start_mapper212, LOADADDR (.mapper212))
                0x00008000                PROVIDE (__load_stop_mapper212, (LOADADDR (.mapper212) + SIZEOF (.mapper212)))

.mapper213      0x00008000        0x0
 *(.mapper213_text)
 *(.mapper213_data)
                0x00008000                PROVIDE (__load_start_mapper213, LOADADDR (.mapper213))
                0x00008000                PROVIDE (__load_stop_mapper213, (LOADADDR (.mapper213) + SIZEOF (.mapper213)))

.mapper214      0x00008000        0x0
 *(.mapper214_text)
 *(.mapper214_data)
                0x00008000                PROVIDE (__load_start_mapper214, LOADADDR (.mapper214))
                0x00008000                PROVIDE (__load_stop_mapper214, (LOADADDR (.mapper214) + SIZEOF (.mapper214)))

.mapper215      0x00008000        0x0
 *(.mapper215_text)
 *(.mapper215_data)
                0x00008000                PROVIDE (__load_start_mapper215, LOADADDR (.mapper215))
                0x00008000                PROVIDE (__load_stop_mapper215, (LOADADDR (.mapper215) + SIZEOF (.mapper215)))

.mapper216      0x00008000        0x0
 *(.mapper216_text)
 *(.mapper216_data)
                0x00008000                PROVIDE (__load_start_mapper216, LOADADDR (.mapper216))
                0x00008000                PROVIDE (__load_stop_mapper216, (LOADADDR (.mapper216) + SIZEOF (.mapper216)))

.mapper217      0x00008000        0x0
 *(.mapper217_text)
 *(.mapper217_data)
                0x00008000                PROVIDE (__load_start_mapper217, LOADADDR (.mapper217))
                0x00008000                PROVIDE (__load_stop_mapper217, (LOADADDR (.mapper217) + SIZEOF (.mapper217)))

.mapper218      0x00008000        0x0
 *(.mapper218_text)
 *(.mapper218_data)
                0x00008000                PROVIDE (__load_start_mapper218, LOADADDR (.mapper218))
                0x00008000                PROVIDE (__load_stop_mapper218, (LOADADDR (.mapper218) + SIZEOF (.mapper218)))

.mapper219      0x00008000        0x0
 *(.mapper219_text)
 *(.mapper219_data)
                0x00008000                PROVIDE (__load_start_mapper219, LOADADDR (.mapper219))
                0x00008000                PROVIDE (__load_stop_mapper219, (LOADADDR (.mapper219) + SIZEOF (.mapper219)))

.mapper220      0x00008000        0x0
 *(.mapper220_text)
 *(.mapper220_data)
                0x00008000                PROVIDE (__load_start_mapper220, LOADADDR (.mapper220))
                0x00008000                PROVIDE (__load_stop_mapper220, (LOADADDR (.mapper220) + SIZEOF (.mapper220)))

.mapper221      0x00008000        0x0
 *(.mapper221_text)
 *(.mapper221_data)
                0x00008000                PROVIDE (__load_start_mapper221, LOADADDR (.mapper221))
                0x00008000                PROVIDE (__load_stop_mapper221, (LOADADDR (.mapper221) + SIZEOF (.mapper221)))

.mapper222      0x00008000        0x0
 *(.mapper222_text)
 *(.mapper222_data)
                0x00008000                PROVIDE (__load_start_mapper222, LOADADDR (.mapper222))
                0x00008000                PROVIDE (__load_stop_mapper222, (LOADADDR (.mapper222) + SIZEOF (.mapper222)))

.mapper223      0x00008000        0x0
 *(.mapper223_text)
 *(.mapper223_data)
                0x00008000                PROVIDE (__load_start_mapper223, LOADADDR (.mapper223))
                0x00008000                PROVIDE (__load_stop_mapper223, (LOADADDR (.mapper223) + SIZEOF (.mapper223)))

.mapper224      0x00008000        0x0
 *(.mapper224_text)
 *(.mapper224_data)
                0x00008000                PROVIDE (__load_start_mapper224, LOADADDR (.mapper224))
                0x00008000                PROVIDE (__load_stop_mapper224, (LOADADDR (.mapper224) + SIZEOF (.mapper224)))

.mapper225      0x00008000        0x0
 *(.mapper225_text)
 *(.mapper225_data)
                0x00008000                PROVIDE (__load_start_mapper225, LOADADDR (.mapper225))
                0x00008000                PROVIDE (__load_stop_mapper225, (LOADADDR (.mapper225) + SIZEOF (.mapper225)))

.mapper226      0x00008000        0x0
 *(.mapper226_text)
 *(.mapper226_data)
                0x00008000                PROVIDE (__load_start_mapper226, LOADADDR (.mapper226))
                0x00008000                PROVIDE (__load_stop_mapper226, (LOADADDR (.mapper226) + SIZEOF (.mapper226)))

.mapper227      0x00008000        0x0
 *(.mapper227_text)
 *(.mapper227_data)
                0x00008000                PROVIDE (__load_start_mapper227, LOADADDR (.mapper227))
                0x00008000                PROVIDE (__load_stop_mapper227, (LOADADDR (.mapper227) + SIZEOF (.mapper227)))

.mapper228      0x00008000        0x0
 *(.mapper228_text)
 *(.mapper228_data)
                0x00008000                PROVIDE (__load_start_mapper228, LOADADDR (.mapper228))
                0x00008000                PROVIDE (__load_stop_mapper228, (LOADADDR (.mapper228) + SIZEOF (.mapper228)))

.mapper229      0x00008000        0x0
 *(.mapper229_text)
 *(.mapper229_data)
                0x00008000                PROVIDE (__load_start_mapper229, LOADADDR (.mapper229))
                0x00008000                PROVIDE (__load_stop_mapper229, (LOADADDR (.mapper229) + SIZEOF (.mapper229)))

.mapper230      0x00008000        0x0
 *(.mapper230_text)
 *(.mapper230_data)
                0x00008000                PROVIDE (__load_start_mapper230, LOADADDR (.mapper230))
                0x00008000                PROVIDE (__load_stop_mapper230, (LOADADDR (.mapper230) + SIZEOF (.mapper230)))

.mapper231      0x00008000        0x0
 *(.mapper231_text)
 *(.mapper231_data)
                0x00008000                PROVIDE (__load_start_mapper231, LOADADDR (.mapper231))
                0x00008000                PROVIDE (__load_stop_mapper231, (LOADADDR (.mapper231) + SIZEOF (.mapper231)))

.mapper232      0x00008000        0x0
 *(.mapper232_text)
 *(.mapper232_data)
                0x00008000                PROVIDE (__load_start_mapper232, LOADADDR (.mapper232))
                0x00008000                PROVIDE (__load_stop_mapper232, (LOADADDR (.mapper232) + SIZEOF (.mapper232)))

.mapper233      0x00008000        0x0
 *(.mapper233_text)
 *(.mapper233_data)
                0x00008000                PROVIDE (__load_start_mapper233, LOADADDR (.mapper233))
                0x00008000                PROVIDE (__load_stop_mapper233, (LOADADDR (.mapper233) + SIZEOF (.mapper233)))

.mapper234      0x00008000        0x0
 *(.mapper234_text)
 *(.mapper234_data)
                0x00008000                PROVIDE (__load_start_mapper234, LOADADDR (.mapper234))
                0x00008000                PROVIDE (__load_stop_mapper234, (LOADADDR (.mapper234) + SIZEOF (.mapper234)))

.mapper235      0x00008000        0x0
 *(.mapper235_text)
 *(.mapper235_data)
                0x00008000                PROVIDE (__load_start_mapper235, LOADADDR (.mapper235))
                0x00008000                PROVIDE (__load_stop_mapper235, (LOADADDR (.mapper235) + SIZEOF (.mapper235)))

.mapper236      0x00008000        0x0
 *(.mapper236_text)
 *(.mapper236_data)
                0x00008000                PROVIDE (__load_start_mapper236, LOADADDR (.mapper236))
                0x00008000                PROVIDE (__load_stop_mapper236, (LOADADDR (.mapper236) + SIZEOF (.mapper236)))

.mapper237      0x00008000        0x0
 *(.mapper237_text)
 *(.mapper237_data)
                0x00008000                PROVIDE (__load_start_mapper237, LOADADDR (.mapper237))
                0x00008000                PROVIDE (__load_stop_mapper237, (LOADADDR (.mapper237) + SIZEOF (.mapper237)))

.mapper238      0x00008000        0x0
 *(.mapper238_text)
 *(.mapper238_data)
                0x00008000                PROVIDE (__load_start_mapper238, LOADADDR (.mapper238))
                0x00008000                PROVIDE (__load_stop_mapper238, (LOADADDR (.mapper238) + SIZEOF (.mapper238)))

.mapper239      0x00008000        0x0
 *(.mapper239_text)
 *(.mapper239_data)
                0x00008000                PROVIDE (__load_start_mapper239, LOADADDR (.mapper239))
                0x00008000                PROVIDE (__load_stop_mapper239, (LOADADDR (.mapper239) + SIZEOF (.mapper239)))

.mapper240      0x00008000        0x0
 *(.mapper240_text)
 *(.mapper240_data)
                0x00008000                PROVIDE (__load_start_mapper240, LOADADDR (.mapper240))
                0x00008000                PROVIDE (__load_stop_mapper240, (LOADADDR (.mapper240) + SIZEOF (.mapper240)))

.mapper241      0x00008000        0x0
 *(.mapper241_text)
 *(.mapper241_data)
                0x00008000                PROVIDE (__load_start_mapper241, LOADADDR (.mapper241))
                0x00008000                PROVIDE (__load_stop_mapper241, (LOADADDR (.mapper241) + SIZEOF (.mapper241)))

.mapper242      0x00008000        0x0
 *(.mapper242_text)
 *(.mapper242_data)
                0x00008000                PROVIDE (__load_start_mapper242, LOADADDR (.mapper242))
                0x00008000                PROVIDE (__load_stop_mapper242, (LOADADDR (.mapper242) + SIZEOF (.mapper242)))

.mapper243      0x00008000        0x0
 *(.mapper243_text)
 *(.mapper243_data)
                0x00008000                PROVIDE (__load_start_mapper243, LOADADDR (.mapper243))
                0x00008000                PROVIDE (__load_stop_mapper243, (LOADADDR (.mapper243) + SIZEOF (.mapper243)))

.mapper244      0x00008000        0x0
 *(.mapper244_text)
 *(.mapper244_data)
                0x00008000                PROVIDE (__load_start_mapper244, LOADADDR (.mapper244))
                0x00008000                PROVIDE (__load_stop_mapper244, (LOADADDR (.mapper244) + SIZEOF (.mapper244)))

.mapper245      0x00008000        0x0
 *(.mapper245_text)
 *(.mapper245_data)
                0x00008000                PROVIDE (__load_start_mapper245, LOADADDR (.mapper245))
                0x00008000                PROVIDE (__load_stop_mapper245, (LOADADDR (.mapper245) + SIZEOF (.mapper245)))

.mapper246      0x00008000        0x0
 *(.mapper246_text)
 *(.mapper246_data)
                0x00008000                PROVIDE (__load_start_mapper246, LOADADDR (.mapper246))
                0x00008000                PROVIDE (__load_stop_mapper246, (LOADADDR (.mapper246) + SIZEOF (.mapper246)))

.mapper247      0x00008000        0x0
 *(.mapper247_text)
 *(.mapper247_data)
                0x00008000                PROVIDE (__load_start_mapper247, LOADADDR (.mapper247))
                0x00008000                PROVIDE (__load_stop_mapper247, (LOADADDR (.mapper247) + SIZEOF (.mapper247)))

.mapper248      0x00008000        0x0
 *(.mapper248_text)
 *(.mapper248_data)
                0x00008000                PROVIDE (__load_start_mapper248, LOADADDR (.mapper248))
                0x00008000                PROVIDE (__load_stop_mapper248, (LOADADDR (.mapper248) + SIZEOF (.mapper248)))

.mapper249      0x00008000        0x0
 *(.mapper249_text)
 *(.mapper249_data)
                0x00008000                PROVIDE (__load_start_mapper249, LOADADDR (.mapper249))
                0x00008000                PROVIDE (__load_stop_mapper249, (LOADADDR (.mapper249) + SIZEOF (.mapper249)))

.mapper250      0x00008000        0x0
 *(.mapper250_text)
 *(.mapper250_data)
                0x00008000                PROVIDE (__load_start_mapper250, LOADADDR (.mapper250))
                0x00008000                PROVIDE (__load_stop_mapper250, (LOADADDR (.mapper250) + SIZEOF (.mapper250)))

.mapper251      0x00008000        0x0
 *(.mapper251_text)
 *(.mapper251_data)
                0x00008000                PROVIDE (__load_start_mapper251, LOADADDR (.mapper251))
                0x00008000                PROVIDE (__load_stop_mapper251, (LOADADDR (.mapper251) + SIZEOF (.mapper251)))

.mapper252      0x00008000        0x0
 *(.mapper252_text)
 *(.mapper252_data)
                0x00008000                PROVIDE (__load_start_mapper252, LOADADDR (.mapper252))
                0x00008000                PROVIDE (__load_stop_mapper252, (LOADADDR (.mapper252) + SIZEOF (.mapper252)))

.mapper253      0x00008000        0x0
 *(.mapper253_text)
 *(.mapper253_data)
                0x00008000                PROVIDE (__load_start_mapper253, LOADADDR (.mapper253))
                0x00008000                PROVIDE (__load_stop_mapper253, (LOADADDR (.mapper253) + SIZEOF (.mapper253)))

.mapper254      0x00008000        0x0
 *(.mapper254_text)
 *(.mapper254_data)
                0x00008000                PROVIDE (__load_start_mapper254, LOADADDR (.mapper254))
                0x00008000                PROVIDE (__load_stop_mapper254, (LOADADDR (.mapper254) + SIZEOF (.mapper254)))

.mapper255      0x00008000        0x0
 *(.mapper255_text)
 *(.mapper255_data)
                0x00008000                PROVIDE (__load_start_mapper255, LOADADDR (.mapper255))
                0x00008000                PROVIDE (__load_stop_mapper255, (LOADADDR (.mapper255) + SIZEOF (.mapper255)))
                0x00008000                __mapper_start = ORIGIN (nes_text)
                0x00000000                __mapper000_len = SIZEOF (.mapper000)
                0x00000000                __mapper001_len = SIZEOF (.mapper001)
                0x00000000                __mapper002_len = SIZEOF (.mapper002)
                0x00000000                __mapper003_len = SIZEOF (.mapper003)
                0x00000000                __mapper004_len = SIZEOF (.mapper004)
                0x00000000                __mapper005_len = SIZEOF (.mapper005)
                0x00000000                __mapper006_len = SIZEOF (.mapper006)
                0x00000000                __mapper007_len = SIZEOF (.mapper007)
                0x00000000                __mapper008_len = SIZEOF (.mapper008)
                0x00000000                __mapper009_len = SIZEOF (.mapper009)
                0x00000000                __mapper010_len = SIZEOF (.mapper010)
                0x00000000                __mapper011_len = SIZEOF (.mapper011)
                0x00000000                __mapper012_len = SIZEOF (.mapper012)
                0x00000000                __mapper013_len = SIZEOF (.mapper013)
                0x00000000                __mapper014_len = SIZEOF (.mapper014)
                0x00000000                __mapper015_len = SIZEOF (.mapper015)
                0x00000000                __mapper016_len = SIZEOF (.mapper016)
                0x00000000                __mapper017_len = SIZEOF (.mapper017)
                0x00000000                __mapper018_len = SIZEOF (.mapper018)
                0x00000000                __mapper019_len = SIZEOF (.mapper019)
                0x00000000                __mapper020_len = SIZEOF (.mapper020)
                0x00000000                __mapper021_len = SIZEOF (.mapper021)
                0x00000000                __mapper022_len = SIZEOF (.mapper022)
                0x00000000                __mapper023_len = SIZEOF (.mapper023)
                0x00000000                __mapper024_len = SIZEOF (.mapper024)
                0x00000000                __mapper025_len = SIZEOF (.mapper025)
                0x00000000                __mapper026_len = SIZEOF (.mapper026)
                0x00000000                __mapper027_len = SIZEOF (.mapper027)
                0x00000000                __mapper028_len = SIZEOF (.mapper028)
                0x00000000                __mapper029_len = SIZEOF (.mapper029)
                0x00000000                __mapper030_len = SIZEOF (.mapper030)
                0x00000000                __mapper031_len = SIZEOF (.mapper031)
                0x00000000                __mapper032_len = SIZEOF (.mapper032)
                0x00000000                __mapper033_len = SIZEOF (.mapper033)
                0x00000000                __mapper034_len = SIZEOF (.mapper034)
                0x00000000                __mapper035_len = SIZEOF (.mapper035)
                0x00000000                __mapper036_len = SIZEOF (.mapper036)
                0x00000000                __mapper037_len = SIZEOF (.mapper037)
                0x00000000                __mapper038_len = SIZEOF (.mapper038)
                0x00000000                __mapper039_len = SIZEOF (.mapper039)
                0x00000000                __mapper040_len = SIZEOF (.mapper040)
                0x00000000                __mapper041_len = SIZEOF (.mapper041)
                0x00000000                __mapper042_len = SIZEOF (.mapper042)
                0x00000000                __mapper043_len = SIZEOF (.mapper043)
                0x00000000                __mapper044_len = SIZEOF (.mapper044)
                0x00000000                __mapper045_len = SIZEOF (.mapper045)
                0x00000000                __mapper046_len = SIZEOF (.mapper046)
                0x00000000                __mapper047_len = SIZEOF (.mapper047)
                0x00000000                __mapper048_len = SIZEOF (.mapper048)
                0x00000000                __mapper049_len = SIZEOF (.mapper049)
                0x00000000                __mapper050_len = SIZEOF (.mapper050)
                0x00000000                __mapper051_len = SIZEOF (.mapper051)
                0x00000000                __mapper052_len = SIZEOF (.mapper052)
                0x00000000                __mapper053_len = SIZEOF (.mapper053)
                0x00000000                __mapper054_len = SIZEOF (.mapper054)
                0x00000000                __mapper055_len = SIZEOF (.mapper055)
                0x00000000                __mapper056_len = SIZEOF (.mapper056)
                0x00000000                __mapper057_len = SIZEOF (.mapper057)
                0x00000000                __mapper058_len = SIZEOF (.mapper058)
                0x00000000                __mapper059_len = SIZEOF (.mapper059)
                0x00000000                __mapper060_len = SIZEOF (.mapper060)
                0x00000000                __mapper061_len = SIZEOF (.mapper061)
                0x00000000                __mapper062_len = SIZEOF (.mapper062)
                0x00000000                __mapper063_len = SIZEOF (.mapper063)
                0x00000000                __mapper064_len = SIZEOF (.mapper064)
                0x00000000                __mapper065_len = SIZEOF (.mapper065)
                0x00000000                __mapper066_len = SIZEOF (.mapper066)
                0x00000000                __mapper067_len = SIZEOF (.mapper067)
                0x00000000                __mapper068_len = SIZEOF (.mapper068)
                0x00000000                __mapper069_len = SIZEOF (.mapper069)
                0x00000000                __mapper070_len = SIZEOF (.mapper070)
                0x00000000                __mapper071_len = SIZEOF (.mapper071)
                0x00000000                __mapper072_len = SIZEOF (.mapper072)
                0x00000000                __mapper073_len = SIZEOF (.mapper073)
                0x00000000                __mapper074_len = SIZEOF (.mapper074)
                0x00000000                __mapper075_len = SIZEOF (.mapper075)
                0x00000000                __mapper076_len = SIZEOF (.mapper076)
                0x00000000                __mapper077_len = SIZEOF (.mapper077)
                0x00000000                __mapper078_len = SIZEOF (.mapper078)
                0x00000000                __mapper079_len = SIZEOF (.mapper079)
                0x00000000                __mapper080_len = SIZEOF (.mapper080)
                0x00000000                __mapper081_len = SIZEOF (.mapper081)
                0x00000000                __mapper082_len = SIZEOF (.mapper082)
                0x00000000                __mapper083_len = SIZEOF (.mapper083)
                0x00000000                __mapper084_len = SIZEOF (.mapper084)
                0x00000000                __mapper085_len = SIZEOF (.mapper085)
                0x00000000                __mapper086_len = SIZEOF (.mapper086)
                0x00000000                __mapper087_len = SIZEOF (.mapper087)
                0x00000000                __mapper088_len = SIZEOF (.mapper088)
                0x00000000                __mapper089_len = SIZEOF (.mapper089)
                0x00000000                __mapper090_len = SIZEOF (.mapper090)
                0x00000000                __mapper091_len = SIZEOF (.mapper091)
                0x00000000                __mapper092_len = SIZEOF (.mapper092)
                0x00000000                __mapper093_len = SIZEOF (.mapper093)
                0x00000000                __mapper094_len = SIZEOF (.mapper094)
                0x00000000                __mapper095_len = SIZEOF (.mapper095)
                0x00000000                __mapper096_len = SIZEOF (.mapper096)
                0x00000000                __mapper097_len = SIZEOF (.mapper097)
                0x00000000                __mapper098_len = SIZEOF (.mapper098)
                0x00000000                __mapper099_len = SIZEOF (.mapper099)
                0x00000000                __mapper100_len = SIZEOF (.mapper100)
                0x00000000                __mapper101_len = SIZEOF (.mapper101)
                0x00000000                __mapper102_len = SIZEOF (.mapper102)
                0x00000000                __mapper103_len = SIZEOF (.mapper103)
                0x00000000                __mapper104_len = SIZEOF (.mapper104)
                0x00000000                __mapper105_len = SIZEOF (.mapper105)
                0x00000000                __mapper106_len = SIZEOF (.mapper106)
                0x00000000                __mapper107_len = SIZEOF (.mapper107)
                0x00000000                __mapper108_len = SIZEOF (.mapper108)
                0x00000000                __mapper109_len = SIZEOF (.mapper109)
                0x00000000                __mapper110_len = SIZEOF (.mapper110)
                0x00000000                __mapper111_len = SIZEOF (.mapper111)
                0x00000000                __mapper112_len = SIZEOF (.mapper112)
                0x00000000                __mapper113_len = SIZEOF (.mapper113)
                0x00000000                __mapper114_len = SIZEOF (.mapper114)
                0x00000000                __mapper115_len = SIZEOF (.mapper115)
                0x00000000                __mapper116_len = SIZEOF (.mapper116)
                0x00000000                __mapper117_len = SIZEOF (.mapper117)
                0x00000000                __mapper118_len = SIZEOF (.mapper118)
                0x00000000                __mapper119_len = SIZEOF (.mapper119)
                0x00000000                __mapper120_len = SIZEOF (.mapper120)
                0x00000000                __mapper121_len = SIZEOF (.mapper121)
                0x00000000                __mapper122_len = SIZEOF (.mapper122)
                0x00000000                __mapper123_len = SIZEOF (.mapper123)
                0x00000000                __mapper124_len = SIZEOF (.mapper124)
                0x00000000                __mapper125_len = SIZEOF (.mapper125)
                0x00000000                __mapper126_len = SIZEOF (.mapper126)
                0x00000000                __mapper127_len = SIZEOF (.mapper127)
                0x00000000                __mapper128_len = SIZEOF (.mapper128)
                0x00000000                __mapper129_len = SIZEOF (.mapper129)
                0x00000000                __mapper130_len = SIZEOF (.mapper130)
                0x00000000                __mapper131_len = SIZEOF (.mapper131)
                0x00000000                __mapper132_len = SIZEOF (.mapper132)
                0x00000000                __mapper133_len = SIZEOF (.mapper133)
                0x00000000                __mapper134_len = SIZEOF (.mapper134)
                0x00000000                __mapper135_len = SIZEOF (.mapper135)
                0x00000000                __mapper136_len = SIZEOF (.mapper136)
                0x00000000                __mapper137_len = SIZEOF (.mapper137)
                0x00000000                __mapper138_len = SIZEOF (.mapper138)
                0x00000000                __mapper139_len = SIZEOF (.mapper139)
                0x00000000                __mapper140_len = SIZEOF (.mapper140)
                0x00000000                __mapper141_len = SIZEOF (.mapper141)
                0x00000000                __mapper142_len = SIZEOF (.mapper142)
                0x00000000                __mapper143_len = SIZEOF (.mapper143)
                0x00000000                __mapper144_len = SIZEOF (.mapper144)
                0x00000000                __mapper145_len = SIZEOF (.mapper145)
                0x00000000                __mapper146_len = SIZEOF (.mapper146)
                0x00000000                __mapper147_len = SIZEOF (.mapper147)
                0x00000000                __mapper148_len = SIZEOF (.mapper148)
                0x00000000                __mapper149_len = SIZEOF (.mapper149)
                0x00000000                __mapper150_len = SIZEOF (.mapper150)
                0x00000000                __mapper151_len = SIZEOF (.mapper151)
                0x00000000                __mapper152_len = SIZEOF (.mapper152)
                0x00000000                __mapper153_len = SIZEOF (.mapper153)
                0x00000000                __mapper154_len = SIZEOF (.mapper154)
                0x00000000                __mapper155_len = SIZEOF (.mapper155)
                0x00000000                __mapper156_len = SIZEOF (.mapper156)
                0x00000000                __mapper157_len = SIZEOF (.mapper157)
                0x00000000                __mapper158_len = SIZEOF (.mapper158)
                0x00000000                __mapper159_len = SIZEOF (.mapper159)
                0x00000000                __mapper160_len = SIZEOF (.mapper160)
                0x00000000                __mapper161_len = SIZEOF (.mapper161)
                0x00000000                __mapper162_len = SIZEOF (.mapper162)
                0x00000000                __mapper163_len = SIZEOF (.mapper163)
                0x00000000                __mapper164_len = SIZEOF (.mapper164)
                0x00000000                __mapper165_len = SIZEOF (.mapper165)
                0x00000000                __mapper166_len = SIZEOF (.mapper166)
                0x00000000                __mapper167_len = SIZEOF (.mapper167)
                0x00000000                __mapper168_len = SIZEOF (.mapper168)
                0x00000000                __mapper169_len = SIZEOF (.mapper169)
                0x00000000                __mapper170_len = SIZEOF (.mapper170)
                0x00000000                __mapper171_len = SIZEOF (.mapper171)
                0x00000000                __mapper172_len = SIZEOF (.mapper172)
                0x00000000                __mapper173_len = SIZEOF (.mapper173)
                0x00000000                __mapper174_len = SIZEOF (.mapper174)
                0x00000000                __mapper175_len = SIZEOF (.mapper175)
                0x00000000                __mapper176_len = SIZEOF (.mapper176)
                0x00000000                __mapper177_len = SIZEOF (.mapper177)
                0x00000000                __mapper178_len = SIZEOF (.mapper178)
                0x00000000                __mapper179_len = SIZEOF (.mapper179)
                0x00000000                __mapper180_len = SIZEOF (.mapper180)
                0x00000000                __mapper181_len = SIZEOF (.mapper181)
                0x00000000                __mapper182_len = SIZEOF (.mapper182)
                0x00000000                __mapper183_len = SIZEOF (.mapper183)
                0x00000000                __mapper184_len = SIZEOF (.mapper184)
                0x00000000                __mapper185_len = SIZEOF (.mapper185)
                0x00000000                __mapper186_len = SIZEOF (.mapper186)
                0x00000000                __mapper187_len = SIZEOF (.mapper187)
                0x00000000                __mapper188_len = SIZEOF (.mapper188)
                0x00000000                __mapper189_len = SIZEOF (.mapper189)
                0x00000000                __mapper190_len = SIZEOF (.mapper190)
                0x00000000                __mapper191_len = SIZEOF (.mapper191)
                0x00000000                __mapper192_len = SIZEOF (.mapper192)
                0x00000000                __mapper193_len = SIZEOF (.mapper193)
                0x00000000                __mapper194_len = SIZEOF (.mapper194)
                0x00000000                __mapper195_len = SIZEOF (.mapper195)
                0x00000000                __mapper196_len = SIZEOF (.mapper196)
                0x00000000                __mapper197_len = SIZEOF (.mapper197)
                0x00000000                __mapper198_len = SIZEOF (.mapper198)
                0x00000000                __mapper199_len = SIZEOF (.mapper199)
                0x00000000                __mapper200_len = SIZEOF (.mapper200)
                0x00000000                __mapper201_len = SIZEOF (.mapper201)
                0x00000000                __mapper202_len = SIZEOF (.mapper202)
                0x00000000                __mapper203_len = SIZEOF (.mapper203)
                0x00000000                __mapper204_len = SIZEOF (.mapper204)
                0x00000000                __mapper205_len = SIZEOF (.mapper205)
                0x00000000                __mapper206_len = SIZEOF (.mapper206)
                0x00000000                __mapper207_len = SIZEOF (.mapper207)
                0x00000000                __mapper208_len = SIZEOF (.mapper208)
                0x00000000                __mapper209_len = SIZEOF (.mapper209)
                0x00000000                __mapper210_len = SIZEOF (.mapper210)
                0x00000000                __mapper211_len = SIZEOF (.mapper211)
                0x00000000                __mapper212_len = SIZEOF (.mapper212)
                0x00000000                __mapper213_len = SIZEOF (.mapper213)
                0x00000000                __mapper214_len = SIZEOF (.mapper214)
                0x00000000                __mapper215_len = SIZEOF (.mapper215)
                0x00000000                __mapper216_len = SIZEOF (.mapper216)
                0x00000000                __mapper217_len = SIZEOF (.mapper217)
                0x00000000                __mapper218_len = SIZEOF (.mapper218)
                0x00000000                __mapper219_len = SIZEOF (.mapper219)
                0x00000000                __mapper220_len = SIZEOF (.mapper220)
                0x00000000                __mapper221_len = SIZEOF (.mapper221)
                0x00000000                __mapper222_len = SIZEOF (.mapper222)
                0x00000000                __mapper223_len = SIZEOF (.mapper223)
                0x00000000                __mapper224_len = SIZEOF (.mapper224)
                0x00000000                __mapper225_len = SIZEOF (.mapper225)
                0x00000000                __mapper226_len = SIZEOF (.mapper226)
                0x00000000                __mapper227_len = SIZEOF (.mapper227)
                0x00000000                __mapper228_len = SIZEOF (.mapper228)
                0x00000000                __mapper229_len = SIZEOF (.mapper229)
                0x00000000                __mapper230_len = SIZEOF (.mapper230)
                0x00000000                __mapper231_len = SIZEOF (.mapper231)
                0x00000000                __mapper232_len = SIZEOF (.mapper232)
                0x00000000                __mapper233_len = SIZEOF (.mapper233)
                0x00000000                __mapper234_len = SIZEOF (.mapper234)
                0x00000000                __mapper235_len = SIZEOF (.mapper235)
                0x00000000                __mapper236_len = SIZEOF (.mapper236)
                0x00000000                __mapper237_len = SIZEOF (.mapper237)
                0x00000000                __mapper238_len = SIZEOF (.mapper238)
                0x00000000                __mapper239_len = SIZEOF (.mapper239)
                0x00000000                __mapper240_len = SIZEOF (.mapper240)
                0x00000000                __mapper241_len = SIZEOF (.mapper241)
                0x00000000                __mapper242_len = SIZEOF (.mapper242)
                0x00000000                __mapper243_len = SIZEOF (.mapper243)
                0x00000000                __mapper244_len = SIZEOF (.mapper244)
                0x00000000                __mapper245_len = SIZEOF (.mapper245)
                0x00000000                __mapper246_len = SIZEOF (.mapper246)
                0x00000000                __mapper247_len = SIZEOF (.mapper247)
                0x00000000                __mapper248_len = SIZEOF (.mapper248)
                0x00000000                __mapper249_len = SIZEOF (.mapper249)
                0x00000000                __mapper250_len = SIZEOF (.mapper250)
                0x00000000                __mapper251_len = SIZEOF (.mapper251)
                0x00000000                __mapper252_len = SIZEOF (.mapper252)
                0x00000000                __mapper253_len = SIZEOF (.mapper253)
                0x00000000                __mapper254_len = SIZEOF (.mapper254)
                0x00000000                __mapper255_len = SIZEOF (.mapper255)
                0x0009ca00                __mapper000_addr = LOADADDR (.mapper000)
                0x00008000                __mapper001_addr = LOADADDR (.mapper001)
                0x00008000                __mapper002_addr = LOADADDR (.mapper002)
                0x00008000                __mapper003_addr = LOADADDR (.mapper003)
                0x00008000                __mapper004_addr = LOADADDR (.mapper004)
                0x00008000                __mapper005_addr = LOADADDR (.mapper005)
                0x00008000                __mapper006_addr = LOADADDR (.mapper006)
                0x00008000                __mapper007_addr = LOADADDR (.mapper007)
                0x00008000                __mapper008_addr = LOADADDR (.mapper008)
                0x00008000                __mapper009_addr = LOADADDR (.mapper009)
                0x00008000                __mapper010_addr = LOADADDR (.mapper010)
                0x00008000                __mapper011_addr = LOADADDR (.mapper011)
                0x00008000                __mapper012_addr = LOADADDR (.mapper012)
                0x00008000                __mapper013_addr = LOADADDR (.mapper013)
                0x00008000                __mapper014_addr = LOADADDR (.mapper014)
                0x00008000                __mapper015_addr = LOADADDR (.mapper015)
                0x00008000                __mapper016_addr = LOADADDR (.mapper016)
                0x00008000                __mapper017_addr = LOADADDR (.mapper017)
                0x00008000                __mapper018_addr = LOADADDR (.mapper018)
                0x00008000                __mapper019_addr = LOADADDR (.mapper019)
                0x00008000                __mapper020_addr = LOADADDR (.mapper020)
                0x00008000                __mapper021_addr = LOADADDR (.mapper021)
                0x00008000                __mapper022_addr = LOADADDR (.mapper022)
                0x00008000                __mapper023_addr = LOADADDR (.mapper023)
                0x00008000                __mapper024_addr = LOADADDR (.mapper024)
                0x00008000                __mapper025_addr = LOADADDR (.mapper025)
                0x00008000                __mapper026_addr = LOADADDR (.mapper026)
                0x00008000                __mapper027_addr = LOADADDR (.mapper027)
                0x00008000                __mapper028_addr = LOADADDR (.mapper028)
                0x00008000                __mapper029_addr = LOADADDR (.mapper029)
                0x00008000                __mapper030_addr = LOADADDR (.mapper030)
                0x00008000                __mapper031_addr = LOADADDR (.mapper031)
                0x00008000                __mapper032_addr = LOADADDR (.mapper032)
                0x00008000                __mapper033_addr = LOADADDR (.mapper033)
                0x00008000                __mapper034_addr = LOADADDR (.mapper034)
                0x00008000                __mapper035_addr = LOADADDR (.mapper035)
                0x00008000                __mapper036_addr = LOADADDR (.mapper036)
                0x00008000                __mapper037_addr = LOADADDR (.mapper037)
                0x00008000                __mapper038_addr = LOADADDR (.mapper038)
                0x00008000                __mapper039_addr = LOADADDR (.mapper039)
                0x00008000                __mapper040_addr = LOADADDR (.mapper040)
                0x00008000                __mapper041_addr = LOADADDR (.mapper041)
                0x00008000                __mapper042_addr = LOADADDR (.mapper042)
                0x00008000                __mapper043_addr = LOADADDR (.mapper043)
                0x00008000                __mapper044_addr = LOADADDR (.mapper044)
                0x00008000                __mapper045_addr = LOADADDR (.mapper045)
                0x00008000                __mapper046_addr = LOADADDR (.mapper046)
                0x00008000                __mapper047_addr = LOADADDR (.mapper047)
                0x00008000                __mapper048_addr = LOADADDR (.mapper048)
                0x00008000                __mapper049_addr = LOADADDR (.mapper049)
                0x00008000                __mapper050_addr = LOADADDR (.mapper050)
                0x00008000                __mapper051_addr = LOADADDR (.mapper051)
                0x00008000                __mapper052_addr = LOADADDR (.mapper052)
                0x00008000                __mapper053_addr = LOADADDR (.mapper053)
                0x00008000                __mapper054_addr = LOADADDR (.mapper054)
                0x00008000                __mapper055_addr = LOADADDR (.mapper055)
                0x00008000                __mapper056_addr = LOADADDR (.mapper056)
                0x00008000                __mapper057_addr = LOADADDR (.mapper057)
                0x00008000                __mapper058_addr = LOADADDR (.mapper058)
                0x00008000                __mapper059_addr = LOADADDR (.mapper059)
                0x00008000                __mapper060_addr = LOADADDR (.mapper060)
                0x00008000                __mapper061_addr = LOADADDR (.mapper061)
                0x00008000                __mapper062_addr = LOADADDR (.mapper062)
                0x00008000                __mapper063_addr = LOADADDR (.mapper063)
                0x00008000                __mapper064_addr = LOADADDR (.mapper064)
                0x00008000                __mapper065_addr = LOADADDR (.mapper065)
                0x00008000                __mapper066_addr = LOADADDR (.mapper066)
                0x00008000                __mapper067_addr = LOADADDR (.mapper067)
                0x00008000                __mapper068_addr = LOADADDR (.mapper068)
                0x00008000                __mapper069_addr = LOADADDR (.mapper069)
                0x00008000                __mapper070_addr = LOADADDR (.mapper070)
                0x00008000                __mapper071_addr = LOADADDR (.mapper071)
                0x00008000                __mapper072_addr = LOADADDR (.mapper072)
                0x00008000                __mapper073_addr = LOADADDR (.mapper073)
                0x00008000                __mapper074_addr = LOADADDR (.mapper074)
                0x00008000                __mapper075_addr = LOADADDR (.mapper075)
                0x00008000                __mapper076_addr = LOADADDR (.mapper076)
                0x00008000                __mapper077_addr = LOADADDR (.mapper077)
                0x00008000                __mapper078_addr = LOADADDR (.mapper078)
                0x00008000                __mapper079_addr = LOADADDR (.mapper079)
                0x00008000                __mapper080_addr = LOADADDR (.mapper080)
                0x00008000                __mapper081_addr = LOADADDR (.mapper081)
                0x00008000                __mapper082_addr = LOADADDR (.mapper082)
                0x00008000                __mapper083_addr = LOADADDR (.mapper083)
                0x00008000                __mapper084_addr = LOADADDR (.mapper084)
                0x00008000                __mapper085_addr = LOADADDR (.mapper085)
                0x00008000                __mapper086_addr = LOADADDR (.mapper086)
                0x00008000                __mapper087_addr = LOADADDR (.mapper087)
                0x00008000                __mapper088_addr = LOADADDR (.mapper088)
                0x00008000                __mapper089_addr = LOADADDR (.mapper089)
                0x00008000                __mapper090_addr = LOADADDR (.mapper090)
                0x00008000                __mapper091_addr = LOADADDR (.mapper091)
                0x00008000                __mapper092_addr = LOADADDR (.mapper092)
                0x00008000                __mapper093_addr = LOADADDR (.mapper093)
                0x00008000                __mapper094_addr = LOADADDR (.mapper094)
                0x00008000                __mapper095_addr = LOADADDR (.mapper095)
                0x00008000                __mapper096_addr = LOADADDR (.mapper096)
                0x00008000                __mapper097_addr = LOADADDR (.mapper097)
                0x00008000                __mapper098_addr = LOADADDR (.mapper098)
                0x00008000                __mapper099_addr = LOADADDR (.mapper099)
                0x00008000                __mapper100_addr = LOADADDR (.mapper100)
                0x00008000                __mapper101_addr = LOADADDR (.mapper101)
                0x00008000                __mapper102_addr = LOADADDR (.mapper102)
                0x00008000                __mapper103_addr = LOADADDR (.mapper103)
                0x00008000                __mapper104_addr = LOADADDR (.mapper104)
                0x00008000                __mapper105_addr = LOADADDR (.mapper105)
                0x00008000                __mapper106_addr = LOADADDR (.mapper106)
                0x00008000                __mapper107_addr = LOADADDR (.mapper107)
                0x00008000                __mapper108_addr = LOADADDR (.mapper108)
                0x00008000                __mapper109_addr = LOADADDR (.mapper109)
                0x00008000                __mapper110_addr = LOADADDR (.mapper110)
                0x00008000                __mapper111_addr = LOADADDR (.mapper111)
                0x00008000                __mapper112_addr = LOADADDR (.mapper112)
                0x00008000                __mapper113_addr = LOADADDR (.mapper113)
                0x00008000                __mapper114_addr = LOADADDR (.mapper114)
                0x00008000                __mapper115_addr = LOADADDR (.mapper115)
                0x00008000                __mapper116_addr = LOADADDR (.mapper116)
                0x00008000                __mapper117_addr = LOADADDR (.mapper117)
                0x00008000                __mapper118_addr = LOADADDR (.mapper118)
                0x00008000                __mapper119_addr = LOADADDR (.mapper119)
                0x00008000                __mapper120_addr = LOADADDR (.mapper120)
                0x00008000                __mapper121_addr = LOADADDR (.mapper121)
                0x00008000                __mapper122_addr = LOADADDR (.mapper122)
                0x00008000                __mapper123_addr = LOADADDR (.mapper123)
                0x00008000                __mapper124_addr = LOADADDR (.mapper124)
                0x00008000                __mapper125_addr = LOADADDR (.mapper125)
                0x00008000                __mapper126_addr = LOADADDR (.mapper126)
                0x00008000                __mapper127_addr = LOADADDR (.mapper127)
                0x00008000                __mapper128_addr = LOADADDR (.mapper128)
                0x00008000                __mapper129_addr = LOADADDR (.mapper129)
                0x00008000                __mapper130_addr = LOADADDR (.mapper130)
                0x00008000                __mapper131_addr = LOADADDR (.mapper131)
                0x00008000                __mapper132_addr = LOADADDR (.mapper132)
                0x00008000                __mapper133_addr = LOADADDR (.mapper133)
                0x00008000                __mapper134_addr = LOADADDR (.mapper134)
                0x00008000                __mapper135_addr = LOADADDR (.mapper135)
                0x00008000                __mapper136_addr = LOADADDR (.mapper136)
                0x00008000                __mapper137_addr = LOADADDR (.mapper137)
                0x00008000                __mapper138_addr = LOADADDR (.mapper138)
                0x00008000                __mapper139_addr = LOADADDR (.mapper139)
                0x00008000                __mapper140_addr = LOADADDR (.mapper140)
                0x00008000                __mapper141_addr = LOADADDR (.mapper141)
                0x00008000                __mapper142_addr = LOADADDR (.mapper142)
                0x00008000                __mapper143_addr = LOADADDR (.mapper143)
                0x00008000                __mapper144_addr = LOADADDR (.mapper144)
                0x00008000                __mapper145_addr = LOADADDR (.mapper145)
                0x00008000                __mapper146_addr = LOADADDR (.mapper146)
                0x00008000                __mapper147_addr = LOADADDR (.mapper147)
                0x00008000                __mapper148_addr = LOADADDR (.mapper148)
                0x00008000                __mapper149_addr = LOADADDR (.mapper149)
                0x00008000                __mapper150_addr = LOADADDR (.mapper150)
                0x00008000                __mapper151_addr = LOADADDR (.mapper151)
                0x00008000                __mapper152_addr = LOADADDR (.mapper152)
                0x00008000                __mapper153_addr = LOADADDR (.mapper153)
                0x00008000                __mapper154_addr = LOADADDR (.mapper154)
                0x00008000                __mapper155_addr = LOADADDR (.mapper155)
                0x00008000                __mapper156_addr = LOADADDR (.mapper156)
                0x00008000                __mapper157_addr = LOADADDR (.mapper157)
                0x00008000                __mapper158_addr = LOADADDR (.mapper158)
                0x00008000                __mapper159_addr = LOADADDR (.mapper159)
                0x00008000                __mapper160_addr = LOADADDR (.mapper160)
                0x00008000                __mapper161_addr = LOADADDR (.mapper161)
                0x00008000                __mapper162_addr = LOADADDR (.mapper162)
                0x00008000                __mapper163_addr = LOADADDR (.mapper163)
                0x00008000                __mapper164_addr = LOADADDR (.mapper164)
                0x00008000                __mapper165_addr = LOADADDR (.mapper165)
                0x00008000                __mapper166_addr = LOADADDR (.mapper166)
                0x00008000                __mapper167_addr = LOADADDR (.mapper167)
                0x00008000                __mapper168_addr = LOADADDR (.mapper168)
                0x00008000                __mapper169_addr = LOADADDR (.mapper169)
                0x00008000                __mapper170_addr = LOADADDR (.mapper170)
                0x00008000                __mapper171_addr = LOADADDR (.mapper171)
                0x00008000                __mapper172_addr = LOADADDR (.mapper172)
                0x00008000                __mapper173_addr = LOADADDR (.mapper173)
                0x00008000                __mapper174_addr = LOADADDR (.mapper174)
                0x00008000                __mapper175_addr = LOADADDR (.mapper175)
                0x00008000                __mapper176_addr = LOADADDR (.mapper176)
                0x00008000                __mapper177_addr = LOADADDR (.mapper177)
                0x00008000                __mapper178_addr = LOADADDR (.mapper178)
                0x00008000                __mapper179_addr = LOADADDR (.mapper179)
                0x00008000                __mapper180_addr = LOADADDR (.mapper180)
                0x00008000                __mapper181_addr = LOADADDR (.mapper181)
                0x00008000                __mapper182_addr = LOADADDR (.mapper182)
                0x00008000                __mapper183_addr = LOADADDR (.mapper183)
                0x00008000                __mapper184_addr = LOADADDR (.mapper184)
                0x00008000                __mapper185_addr = LOADADDR (.mapper185)
                0x00008000                __mapper186_addr = LOADADDR (.mapper186)
                0x00008000                __mapper187_addr = LOADADDR (.mapper187)
                0x00008000                __mapper188_addr = LOADADDR (.mapper188)
                0x00008000                __mapper189_addr = LOADADDR (.mapper189)
                0x00008000                __mapper190_addr = LOADADDR (.mapper190)
                0x00008000                __mapper191_addr = LOADADDR (.mapper191)
                0x00008000                __mapper192_addr = LOADADDR (.mapper192)
                0x00008000                __mapper193_addr = LOADADDR (.mapper193)
                0x00008000                __mapper194_addr = LOADADDR (.mapper194)
                0x00008000                __mapper195_addr = LOADADDR (.mapper195)
                0x00008000                __mapper196_addr = LOADADDR (.mapper196)
                0x00008000                __mapper197_addr = LOADADDR (.mapper197)
                0x00008000                __mapper198_addr = LOADADDR (.mapper198)
                0x00008000                __mapper199_addr = LOADADDR (.mapper199)
                0x00008000                __mapper200_addr = LOADADDR (.mapper200)
                0x00008000                __mapper201_addr = LOADADDR (.mapper201)
                0x00008000                __mapper202_addr = LOADADDR (.mapper202)
                0x00008000                __mapper203_addr = LOADADDR (.mapper203)
                0x00008000                __mapper204_addr = LOADADDR (.mapper204)
                0x00008000                __mapper205_addr = LOADADDR (.mapper205)
                0x00008000                __mapper206_addr = LOADADDR (.mapper206)
                0x00008000                __mapper207_addr = LOADADDR (.mapper207)
                0x00008000                __mapper208_addr = LOADADDR (.mapper208)
                0x00008000                __mapper209_addr = LOADADDR (.mapper209)
                0x00008000                __mapper210_addr = LOADADDR (.mapper210)
                0x00008000                __mapper211_addr = LOADADDR (.mapper211)
                0x00008000                __mapper212_addr = LOADADDR (.mapper212)
                0x00008000                __mapper213_addr = LOADADDR (.mapper213)
                0x00008000                __mapper214_addr = LOADADDR (.mapper214)
                0x00008000                __mapper215_addr = LOADADDR (.mapper215)
                0x00008000                __mapper216_addr = LOADADDR (.mapper216)
                0x00008000                __mapper217_addr = LOADADDR (.mapper217)
                0x00008000                __mapper218_addr = LOADADDR (.mapper218)
                0x00008000                __mapper219_addr = LOADADDR (.mapper219)
                0x00008000                __mapper220_addr = LOADADDR (.mapper220)
                0x00008000                __mapper221_addr = LOADADDR (.mapper221)
                0x00008000                __mapper222_addr = LOADADDR (.mapper222)
                0x00008000                __mapper223_addr = LOADADDR (.mapper223)
                0x00008000                __mapper224_addr = LOADADDR (.mapper224)
                0x00008000                __mapper225_addr = LOADADDR (.mapper225)
                0x00008000                __mapper226_addr = LOADADDR (.mapper226)
                0x00008000                __mapper227_addr = LOADADDR (.mapper227)
                0x00008000                __mapper228_addr = LOADADDR (.mapper228)
                0x00008000                __mapper229_addr = LOADADDR (.mapper229)
                0x00008000                __mapper230_addr = LOADADDR (.mapper230)
                0x00008000                __mapper231_addr = LOADADDR (.mapper231)
                0x00008000                __mapper232_addr = LOADADDR (.mapper232)
                0x00008000                __mapper233_addr = LOADADDR (.mapper233)
                0x00008000                __mapper234_addr = LOADADDR (.mapper234)
                0x00008000                __mapper235_addr = LOADADDR (.mapper235)
                0x00008000                __mapper236_addr = LOADADDR (.mapper236)
                0x00008000                __mapper237_addr = LOADADDR (.mapper237)
                0x00008000                __mapper238_addr = LOADADDR (.mapper238)
                0x00008000                __mapper239_addr = LOADADDR (.mapper239)
                0x00008000                __mapper240_addr = LOADADDR (.mapper240)
                0x00008000                __mapper241_addr = LOADADDR (.mapper241)
                0x00008000                __mapper242_addr = LOADADDR (.mapper242)
                0x00008000                __mapper243_addr = LOADADDR (.mapper243)
                0x00008000                __mapper244_addr = LOADADDR (.mapper244)
                0x00008000                __mapper245_addr = LOADADDR (.mapper245)
                0x00008000                __mapper246_addr = LOADADDR (.mapper246)
                0x00008000                __mapper247_addr = LOADADDR (.mapper247)
                0x00008000                __mapper248_addr = LOADADDR (.mapper248)
                0x00008000                __mapper249_addr = LOADADDR (.mapper249)
                0x00008000                __mapper250_addr = LOADADDR (.mapper250)
                0x00008000                __mapper251_addr = LOADADDR (.mapper251)
                0x00008000                __mapper252_addr = LOADADDR (.mapper252)
                0x00008000                __mapper253_addr = LOADADDR (.mapper253)
                0x00008000                __mapper254_addr = LOADADDR (.mapper254)
                0x00008000                __mapper255_addr = LOADADDR (.mapper255)
                0x00000016                _boot_code_len = ((SIZEOF (.boot_code) + 0x1ff) / 0x200)
                0x00000001                _boot_code_sec = (LOADADDR (.boot_code) / 0x200)
                0x02000000                _text_start = _onsdram_start
                0x00000017                _text_sec = (LOADADDR (.on_sdram) / 0x200)
                0x0000003d                _text_len = ((SIZEOF (.on_sdram) + 0x1ff) / 0x200)
                0x00000001                ASSERT (((_sdram_remian_addr - ORIGIN (sdram)) < __sdram_size), No memroy for sdram)
                0x001e5440                __sdram_remain_size = ((ORIGIN (sdram) + __sdram_size) - _sdram_remian_addr)
                0x00001400                __stack_size = 0x1400
                0x00000001                ASSERT ((((ORIGIN (ram_user) + 0x7000) - __sram_end) >= __stack_size), No memroy for stack)
                0x02200000                __bss_end = (_sdram_remian_addr + __sdram_remain_size)
                0x00000000                __mp3_text_len = SIZEOF (.mp3_text)
                0x0009ca00                __mp3_text_addr = LOADADDR (.mp3_text)
                0x00000000                __mp3_code_len = SIZEOF (.mp3_code)
                0x0009ca00                __mp3_code_addr = LOADADDR (.mp3_code)
                0x00000000                __nes_text_len = SIZEOF (.nes_com_text)
                0x0009ca00                __nes_text_addr = LOADADDR (.nes_com_text)
                0x00000000                __nes_code_len = SIZEOF (.nes_code)
                0x0009ca00                __nes_code_addr = LOADADDR (.nes_code)
                0x00092a00                _lcd_res_lma = LOADADDR (.lcd_resource)
                0x00002ae0                _lcd_res_size = SIZEOF (.lcd_resource)
                0x000954e0                _sensor_resource_start_addr = LOADADDR (.sensor_resource)
                0x00000154                _res_sensor_header_len = (_res_sensor_header_item_end - _res_sensor_header_item_start)
                0x00006ffc                PROVIDE (__stack, ((ORIGIN (ram_user) + 0x7000) - 0x4))
LOAD ..\lib\libboot.a
LOAD ..\lib\libmcu.a
LOAD ..\lib\libisp.a
LOAD ..\lib\libhusb.a
LOAD ..\lib\libjpg.a
LOAD ..\lib\liblcd.a
LOAD ..\lib\libmultimedia.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a
OUTPUT(bin\Debug\hx330x_sdk.exe elf32-or1k)

.comment        0x00000000       0x11
 .comment       0x00000000       0x11 obj\Debug\dev\battery\src\battery_api.o
                                 0x12 (size before relaxing)
 .comment       0x00000011       0x12 obj\Debug\dev\dev_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\diskio.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ff.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ffunicode.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\fs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .comment       0x00000011       0x12 obj\Debug\dev\ir\src\ir_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\key\src\key_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_3030B.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_GC9307.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_JD9853.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .comment       0x00000011       0x12 obj\Debug\dev\led\src\led_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .comment       0x00000011       0x12 obj\Debug\dev\sd\src\sd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF314A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H63P.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0329.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_tab.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_adc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_auadc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_csi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dac.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dmauart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_eeprom.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_gpio.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_iic.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_int.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_md.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpAEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpBEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpDecode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_rtc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi1.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_stream.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_sys.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_timer.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_uart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_watermark.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_wdt.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmbox.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmsgq.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xos.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xwork.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_record.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_decode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_encode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_record.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num0_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_gif\src\res_gif_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_init.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\main.o
 .comment       0x00000011       0x12 obj\Debug\app\resource\user_res.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuAutoPowerOffMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUsbDeviceSelectMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVideoResolutionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\msg_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_bat_charge\src\taskBatCharge.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_bat_charge\src\taskBatChargeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3DirOpsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3FileOpsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3MainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_mp3\src\taskPlayMp3SubMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\windows_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\mbedtls_md5.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_tab.o
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot.o)
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot_lib.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_adc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_auadc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_csi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dac.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dma.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dmauart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_gpio.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_iic.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_int.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdui.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_md.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_mipi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_misc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_rtc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi0.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi1.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sys.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_timer.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_tminf.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_uart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_usb.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_wdt.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_emi.o)
 .comment       0x00000011       0x12 ..\lib\libisp.a(hal_isp.o)
 .comment       0x00000011       0x12 ..\lib\libhusb.a(husb_enum.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_step_jpg.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcd.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdMem.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUi.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(lcd_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(api_multimedia.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_std_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(gif_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_pcm.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_info     0x00000000     0x1afa
 .debug_info    0x00000000      0x113 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_info    0x00000113      0x131 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_info    0x00000244      0x117 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_info    0x0000035b      0x10e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_info    0x00000469      0x74f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_info    0x00000bb8      0x71e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_info    0x000012d6      0x76b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_info    0x00001a41       0xb9 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_abbrev   0x00000000      0x73a
 .debug_abbrev  0x00000000       0x7f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x0000007f       0xab E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x0000012a       0x9f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_abbrev  0x000001c9       0x92 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x0000025b      0x176 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_abbrev  0x000003d1      0x170 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000541      0x19c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_abbrev  0x000006dd       0x5d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_loc      0x00000000     0x40a5
 .debug_loc     0x00000000      0x102 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_loc     0x00000102      0x2ed E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_loc     0x000003ef      0x1ba E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_loc     0x000005a9       0xc7 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_loc     0x00000670     0x14e7 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_loc     0x00001b57     0x1537 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_loc     0x0000308e     0x1017 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)

.debug_aranges  0x00000000       0xf8
 .debug_aranges
                0x00000000       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000020       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000040       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_aranges
                0x00000060       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000080       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_aranges
                0x000000a0       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x000000c0       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_aranges
                0x000000e0       0x18 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_line     0x00000000      0x9c6
 .debug_line    0x00000000      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_line    0x0000015d      0x164 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_line    0x000002c1      0x16d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_line    0x0000042e       0xf5 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_line    0x00000523      0x17d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_line    0x000006a0      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_line    0x000007fd      0x163 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_line    0x00000960       0x66 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_str      0x00000000      0x4e5
 .debug_str     0x00000000      0x14f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                                0x188 (size before relaxing)
 .debug_str     0x0000014f       0x7b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                                0x1af (size before relaxing)
 .debug_str     0x000001ca       0x68 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                                0x19c (size before relaxing)
 .debug_str     0x00000232       0x54 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00000286      0x1db E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
                                0x283 (size before relaxing)
 .debug_str     0x00000461        0xa E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x0000046b        0xa E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x00000475       0x70 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                                0x1cb (size before relaxing)

.debug_frame    0x00000000       0xa0
 .debug_frame   0x00000000       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000028       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000050       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_frame   0x00000078       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)

.debug_ranges   0x00000000      0x4d8
 .debug_ranges  0x00000000      0x1b8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_divdi3.o)
 .debug_ranges  0x000001b8      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_ranges  0x00000348      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
