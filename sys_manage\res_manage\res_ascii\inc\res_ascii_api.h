/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_ASCII_API_H
#define RES_ASCII_API_H


extern const unsigned char * const ascii_num0_table[];
extern const unsigned char * const ascii_num1_table[];
extern const unsigned char * const ascii_num2_table[];
extern const unsigned char * const ascii_num3_table[];
extern const unsigned char * const ascii_num4_table[];



/*******************************************************************************
* Function Name  : res_ascii_get
* Description    : ascii table get
* Input          : char c : char value to draw
* 				   unsigned char *width: font width
*                  unsigned char *heigth:font height
*                  unsigned char font: font num
* Output         : None
* Return         : None
*******************************************************************************/
const u8 *res_ascii_get(u8 c,u16 *width,u16 *heigth,u8 font);
/*******************************************************************************
* Function Name  : R_getAsciiCharSize
* Description    : get ascii char size        
* Input          :  char c : char value
                       unsigned short *width : width
                       unsigned short *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiCharSize(u8 c,u16 *width,u16 *heigth,u8 font);
/*******************************************************************************
* Function Name  : res_getAsciiStringSize
* Description    : get ascii string size        
* Input          : char c : string
				   unsigned short *width : width
				   unsigned short *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiStringSize(u8 *str,u16 *width,u16 *heigth,u8 font);



#endif
