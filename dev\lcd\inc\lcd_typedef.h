/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  LCD_TYPEDEF_H
    #define  LCD_TYPEDEF_H

typedef struct lcddev_s {
    
    u8 lcd_bus_type; // LCD_IF_GET()
    u8 scan_mode;    //LCD_DISPLAY_ROTATE_0, LCD_DISPLAY_ROTATE_90, LCD_DISPLAY_ROTATE_180, LCD_DISPLAY_ROTATE_270,
                     //LCD_DISPLAY_V_MIRROR, LCD_DISPLAY_H_MIRROR
    u8 te_mode; // lcd_mcu_te_mode_e
    u8 delay_line;

    u8 spi_cpol; //CPOL = 0,C<PERSON><PERSON> is "Low" when inactive
    u8 spi_cpha; //CPHA = 0,sample edge is first edge
    u8 spi_order;//ORDER = 0,MSB
    u8 spi_bits; //number of data bits

    u32 io_data_pin; // lcd_data_pin_en_e

    u8 pclk_div; // LCD_PCLK_DIV()
    u8 clk_per_pixel;
    u8 even_order; // lcd_data_order_e
    u8 odd_order;

    u8 pclk_edge; // lcd_pclk_trans_edge_e
    u8 de_level; // lcd_signal_active_level_e
    u8 hs_level; // lcd_signal_active_level_e
    u8 vs_level; // lcd_signal_active_level_e

    u8 vlw;
    u8 vbp;
    u8 vfp;
    u8 vrev;

    u8 hlw;
    u8 hbp;
    u8 hfp;
    u8 hrev;



    
    u32 data_mode;
    u32 data_mode1;

    u16 screen_w,screen_h;

    //video layer在屏幕上的坐标和尺寸
    u16 video_x, video_y,video_w,video_h;
    // 0 : value will set to video_w  , use for 4:3 LCD +16:9 sensor show UPDOWN BLACK
    u16 ui_x,ui_y,ui_w,ui_h;
    u16 video_scaler_w, video_scaler_h;
	
    u8 dma_prio; // 特殊用途，请勿使用该字段
    s8 brightness; // [-128,127)
    u8 contrast; // lcd_contrast_e
    u8 saturation; // lcd_saturation_e
    
    u8 contra_index;
    u8 gamma_index[3];

    u8 asawtooth_index[2];
    u8 lcd_saj[5];
    u8 lcd_reserve8;
    
    u32 lcd_ccm[12];
    char name[32];
    u32 *init_table,
        *uninit_table,
        init_table_size,
        uninit_table_size;
} lcddev_t;
typedef struct{
    u32 checksum;
    u32 size;
    u32 start_index;
    u32 auto_check; //0: use start index, 1: auto check 
}LCD_DRIVER_HEAD;
typedef struct{
    u32 read_id_cmd;
    u32 id;
    u16 cmd_width; //for mcu:8: 8bit cmd, 16: 16bit cmd
    u16 data_pin;  //for mcu: if cmd_width == 16, when data_pin = 8, send 16bit cmd twice; when data_pin = 16, send 16bit cmd once
   
    u8 lcd_bus_type; // LCD_IF_GET()
    u8 scan_mode;    //LCD_DISPLAY_ROTATE_0, LCD_DISPLAY_ROTATE_90, LCD_DISPLAY_ROTATE_180, LCD_DISPLAY_ROTATE_270,
                     //LCD_DISPLAY_V_MIRROR, LCD_DISPLAY_H_MIRROR
    u8 te_mode; // lcd_mcu_te_mode_e
    u8 delay_line;

    u8 spi_cpol; //CPOL = 0,CLK is "Low" when inactive
    u8 spi_cpha; //CPHA = 0,sample edge is first edge
    u8 spi_order;//ORDER = 0,MSB
    u8 spi_bits; //number of data bits
}LCD_DRIVER_TAB_PROBE;

#define LCD_IF_TYPE_MASK		0xFF000000
#define LCD_IF_TYPE_RGB			(LCD_BUS_RGB << 24)
#define LCD_IF_TYPE_MCU			(LCD_BUS_MCU << 24)
#define LCD_IF_TYPE_SPI			(LCD_BUS_SPI << 24)

#define LCD_IF_GET()			(LCD_TAG_SELECT >> 24)
#define LCD_IF_IS_RGB()			((LCD_TAG_SELECT & LCD_IF_TYPE_MASK) == LCD_IF_TYPE_RGB)
#define LCD_IF_IS_MCU()			((LCD_TAG_SELECT & LCD_IF_TYPE_MASK) == LCD_IF_TYPE_MCU)
#define LCD_IF_IS_SPI()			((LCD_TAG_SELECT & LCD_IF_TYPE_MASK) == LCD_IF_TYPE_SPI)

#define LCD_CMD_TYPE_MCU_CMD8   0x00000000
#define LCD_CMD_TYPE_MCU_DAT8   0x01000000
#define LCD_CMD_TYPE_MCU_CMD16  0x02000000
#define LCD_CMD_TYPE_MCU_DAT16  0x03000000
#define LCD_CMD_TYPE_RGB_DAT    0x04000000
#define LCD_CMD_TYPE_DELAY_MS   0x05000000

#define LCD_CMD_TYPE_MASK       0xFF000000
#define LCD_CMD_VALUE_MASK      0x00FFFFFF
#define LCD_CMD_TAB_END         0xFFFFFFFF

#define LCD_CMD_MCU_CMD8(v)     (LCD_CMD_TYPE_MCU_CMD8 | (v))
#define LCD_CMD_MCU_DAT8(v)     (LCD_CMD_TYPE_MCU_DAT8 | (v))
#define LCD_CMD_MCU_CMD16(v)    (LCD_CMD_TYPE_MCU_CMD16 | (v))
#define LCD_CMD_MCU_DAT16(v)    (LCD_CMD_TYPE_MCU_DAT16 | (v))
#define LCD_CMD_RGB_DAT(v)      (LCD_CMD_TYPE_RGB_DAT | (v))
#define LCD_CMD_DELAY_MS(v)     (LCD_CMD_TYPE_DELAY_MS | (v))

#define LCD_PCLK_DIV(pclk)		(LCD_IF_IS_RGB() ? (PLL_CLK/(pclk)-1) : (PLL_CLK/(pclk)/2-1))

enum lcd_data_order_e {
    LCD_RGB=0,
    LCD_RBG,
    LCD_GBR,
    LCD_GRB,
    LCD_BRG,
    LCD_BGR
};

enum lcd_mcu_te_mode_e {
    LCD_MCU_TE_DISABLE,
    LCD_MCU_TE_ENABLE,          // 在TE上升沿触发硬件自动发送帧
    LCD_MCU_TE_ENABLE_FALLING,  // 在TE下降沿触发硬件自动发送帧
    LCD_MCU_TE_ENABLE_MANUALLY, // 在TE中断中手动发送帧
};

enum lcd_data_mode0_e {
    LCD_DATA_MODE0_24BIT_RGB888 = 0x00000000,
    LCD_DATA_MODE0_16BIT_RGB565 = 0x02000000,
    LCD_DATA_MODE0_8BIT_RGB565  = 0x02100008,
    LCD_DATA_MODE0_8BIT_RGB888  = 0x00600000,
    LCD_DATA_MODE0_SPI_8BIT_RGB565 = 0x02100C28,
    LCD_DATA_MODE0_SPI_8BIT_RGB888 = 0x00630C30,
    LCD_DATA_MODE0_SPI_16BIT_RGB565 = 0x02000028,
};

enum lcd_signal_active_level_e {
    LCD_SIG_ACT_LEVEL_HIGH,
    LCD_SIG_ACT_LEVEL_LOW,
    LCD_SIG_DISABLE,
};

enum lcd_pclk_trans_edge_e {
    LCD_PCLK_EDGE_RISING,  // 上升沿发送
    LCD_PCLK_EDGE_FALLING, // 下降沿发送
    LCD_PCLK_DISABLE,
};

enum lcd_data_pin_en_e {
    LCD_DPIN_EN_ONLY_D0  = 0x00000001,
    LCD_DPIN_EN_8BIT_NO_D0 = 0x000000FE,
    LCD_DPIN_EN_24BIT_OUT565_HIGH = 0x00F8FCF8,
    LCD_DPIN_EN_DEFAULT_8  = 0x000000FF,
    LCD_DPIN_EN_DEFAULT_16 = 0x0000FFFF,
    LCD_DPIN_EN_DEFAULT_24 = 0x00FEFFFE,
};

enum lcd_contrast_e {
    LCD_CONTRAST_65, LCD_CONTRAST_70, LCD_CONTRAST_75, LCD_CONTRAST_80,
    LCD_CONTRAST_85, LCD_CONTRAST_90, LCD_CONTRAST_95, LCD_CONTRAST_100,
    LCD_CONTRAST_105, LCD_CONTRAST_110, LCD_CONTRAST_115, LCD_CONTRAST_120,
    LCD_CONTRAST_125, LCD_CONTRAST_130, LCD_CONTRAST_135,
    LCD_CONTRAST_DEFAULT = LCD_CONTRAST_100,
};

enum lcd_saturation_e {
    LCD_SATURATION_0, LCD_SATURATION_10, LCD_SATURATION_25, LCD_SATURATION_40,
    LCD_SATURATION_55, LCD_SATURATION_70, LCD_SATURATION_85, LCD_SATURATION_100,
    LCD_SATURATION_115, LCD_SATURATION_130, LCD_SATURATION_145, LCD_SATURATION_160,
    LCD_SATURATION_175, LCD_SATURATION_190, LCD_SATURATION_200,
    LCD_SATURATION_DEFAULT = LCD_SATURATION_100,
};

#define LCD_CCM_DEFAULT				{0x100,0x000,0x000,0x000,0x100,0x000,0x000,0x000,0x100,0x000,0x000,0x000}
#define LCD_SAJ_DEFAULT				{0x40,0x40,0x40,0x40,0x40}


#define LCD_OP_SECTION              SECTION(".lcd_res.struct")
#define LCD_RES_SECTION             SECTION(".lcd_res")

#define LCD_INIT_TAB_BEGIN()        LCD_RES_SECTION static u32 init_tab[] = {
#define LCD_INIT_TAB_END()          LCD_CMD_TAB_END,};

#define LCD_UNINIT_TAB_BEGIN()      LCD_RES_SECTION static u32 uninit_tab[] = {
#define LCD_UNINIT_TA_ENDB()        LCD_CMD_TAB_END,};

#define LCD_DESC_BEGIN()            LCD_OP_SECTION lcddev_t __lcd_desc = {
#define LCD_DESC_END()              };

#define INIT_TAB_INIT               .init_table = init_tab, \
                                    .init_table_size = sizeof(init_tab),
#define UNINIT_TAB_INIT             .uninit_table = uninit_tab, \
                                    .uninit_table_size = sizeof(uninit_tab),

#define LCD_SPI_DEFAULT(bits)       .spi_cpol = 1, \
                                    .spi_cpha = 1, \
                                    .spi_order = 0, \
                                    .spi_bits = (bits)



#endif

