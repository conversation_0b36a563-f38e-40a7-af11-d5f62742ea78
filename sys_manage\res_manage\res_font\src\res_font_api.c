/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

//RES FONT CHAR: [u32 char max, u32 align],[Font_Char_T 0], [Font_Char_T 1].......
//				 Font_Char_T.offset -> [char data], 
//				 Font_Char_T.width*height/8 ->char data len

//RES FONT LAN: ['F', 'O', 'N', lan max],[u32 align], [Font_LAN_T 0], [Font_LAN_T 1]......
//				Font_LAN_T (offset)-> [Font_Str_T 0], [Font_Str_T 1] .....[Font_Str_T Font_LAN_T(index >>8)] (Font_LAN_T(index&0xff)->cur lan)
//				
typedef struct Font_Char_S
{
	INT16U width;
	INT16U height;
	INT32U offset;
}Font_Char_T;

typedef struct Font_LAN_S
{
	INT32U index;
	INT32U offset;
}Font_LAN_T;
typedef struct Font_Str_S
{
	INT16U width;
	INT16U height;
	INT16U number;
	INT16U offset;
}Font_Str_T;

typedef struct Font_Ctrl_S
{
	INT8U 	lanMax;		//language num max
	INT8U 	strMax;		//str num max
	INT8U 	curLan;		//cur language
	INT8U 	curStr;		//cur str
    INT32U 	curChar;	//cur char
	INT32U 	charMax;
	
	INT32U 	addrLan;	//language start addr
	INT32U 	addrChar;	//char start addr
	INT32U 	curOffset;  //cur lan start offset to language start addr
	
	Font_Str_T strInfo;
	Font_Char_T charInfo;
	INT16U strCache[128];
    INT8U  charCache[1024];
	
	R_STRING_T 	*R_String_Table;
	u32		  	 R_String_ID_Max;

}Font_Ctrl_T;

ALIGNED(4) static Font_Ctrl_T res_font_Ctrl;

/*******************************************************************************
* Function Name  : res_font_Init
* Description    : font initial for res font read
* Input          : INT16U char_indx: resource id
*                  INT16U lan_indx: resource id
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_font_Init(INT16U char_indx, INT16U lan_indx)
{
	int addr;
	
	res_font_Ctrl.lanMax 	= 0;
	res_font_Ctrl.strMax 	= 0;
	res_font_Ctrl.curLan 	= 0xff;
	res_font_Ctrl.curStr 	= 0xff;
	res_font_Ctrl.curChar	= 0xffffffff;
	res_font_Ctrl.addrLan	= 0;	//language start addr
	res_font_Ctrl.addrChar 	= 0;	//char start addr
	
	//font char init
	addr = nv_open(char_indx);
	if(addr < 0)
	{
		deg_Printf("[RES] font char init err\n");
		return -1;
	}
	res_font_Ctrl.addrChar = addr;
	
	nv_read(addr,&res_font_Ctrl.charMax,4);  // get counter of char
	//font language init
	addr = nv_open(lan_indx);
	if(addr<0)
	{
		deg_Printf("[RES] font index num err\n");
		return -1;
	}
	res_font_Ctrl.addrLan = addr;
    nv_read(res_font_Ctrl.addrLan,&addr,4); 
	if((addr&0x00ffffff) != 0x004E4F46)
	{
		deg_Printf("[RES] font index lanmax err\n");
		return -2;
	}
    res_font_Ctrl.lanMax = (addr>>24)&0xff;
	//CH_INV_W 		= (addr>>16)&0xff;
	deg_Printf("[RES] font init ok. max lan = %d, max char = %d\n",res_font_Ctrl.lanMax,res_font_Ctrl.charMax);
    return res_font_Ctrl.lanMax;	
}
/*******************************************************************************
* Function Name  : res_font_SetLanguage
* Description    : set current lanaguage
* Input          : u8 num : lan index 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_font_SetLanguage(u8 num)
{
	Font_LAN_T font_lan;
	if(res_font_Ctrl.addrLan == 0)
		return -1;
	if(num >= res_font_Ctrl.lanMax)
		return -2;
	
	if(num == res_font_Ctrl.curLan)
		return 0;
	nv_read(res_font_Ctrl.addrLan + (num+1)*sizeof(Font_LAN_T), &font_lan, sizeof(Font_LAN_T)); 
	
	if(num != (font_lan.index&0xff))
		return -3;
	res_font_Ctrl.strMax 	= (font_lan.index>>8)&0xff;	
	res_font_Ctrl.curLan 	= num;
	res_font_Ctrl.curOffset = font_lan.offset;
	
// all cache is invliad	
	res_font_Ctrl.curStr 	= 0xff;
	res_font_Ctrl.curChar	= 0xffffffff;
	deg_Printf("[RES] font set language = %d, str max = %d\n", num, res_font_Ctrl.strMax);

	return res_font_Ctrl.curLan;
}
/*******************************************************************************
* Function Name  : res_font_GetCharData
* Description    : get char data  to draw 
* Input          : INT8U num : char offset 
				   INT8U *buffer : dest buffer
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
static int res_font_GetCharData(INT32U num,INT8U *buffer)
{
	int temp;
	if(res_font_Ctrl.addrChar == 0)
		return -1;
	if(num >= res_font_Ctrl.charMax)
		return -2;

	if(num != res_font_Ctrl.curChar)
	{
		nv_read(res_font_Ctrl.addrChar + (num+1)*sizeof(Font_Char_T), &res_font_Ctrl.charInfo,sizeof(Font_Char_T));
		res_font_Ctrl.curChar = num;
	}
    temp = res_font_Ctrl.charInfo.height * ((res_font_Ctrl.charInfo.width+7)>>3);
	if(temp&0x0f)
		temp = (temp&(~0x0f))+0x10;
	if(temp > 1024)
		temp = 1024;
    nv_read(res_font_Ctrl.addrChar + res_font_Ctrl.charInfo.offset,buffer,temp); // read data

	return 0;
}
/*******************************************************************************
* Function Name  : res_font_GetString
* Description    : res_font_GetString, save string to res_font_Ctrl.strCache, str max len 128
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u32 res_font_GetString(u8 num,u16 *width,u16 *height)
{
	int temp;
	if(res_font_Ctrl.addrLan == 0)
		return 0;
	if(num >= res_font_Ctrl.strMax)
		return 0;
	if(num != res_font_Ctrl.curStr)
	{
		nv_read(res_font_Ctrl.addrLan + res_font_Ctrl.curOffset+(num+1)*sizeof(Font_Str_T),&res_font_Ctrl.strInfo,sizeof(Font_Str_T));
		if(res_font_Ctrl.strInfo.number > 128)
			res_font_Ctrl.strInfo.number = 128;	
		temp = res_font_Ctrl.strInfo.number<<1; //中文字符，一个字符2bytes
		if(temp&0x0f)
			temp = (temp&(~0x0f))+0x10;	
		nv_read(res_font_Ctrl.addrLan + res_font_Ctrl.curOffset + res_font_Ctrl.strInfo.offset, res_font_Ctrl.strCache,temp);		
		res_font_Ctrl.curStr = num;
	}
	if(width)
		*width = res_font_Ctrl.strInfo.width;
	if(height)
		*height = res_font_Ctrl.strInfo.height;
	return res_font_Ctrl.strInfo.number;
}
/*******************************************************************************
* Function Name  : res_font_GetChar
* Description    : res_font_GetChar, save string to res_font_Ctrl.strCache, str max len 128
* Input          : u8 str_num: str num
*                  u8 index: char index in str
*                  u16 *width
*                  u16 *height
*                  u8* special
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u8* res_font_GetChar(u8 str_num,u8 index,u16 *width,u16 *height,u8* special)
{
	u16 height_temp;
	u8  special_temp;

    INT8U *charCache = res_font_Ctrl.charCache;

	if(res_font_GetString(str_num,NULL,NULL) == 0)
		return NULL;
	if(index >= res_font_Ctrl.strInfo.number)
		return NULL;
		
	if(res_font_GetCharData(res_font_Ctrl.strCache[index]&0x3fff,charCache)<0)
		return NULL;
	if(width)
		*width = res_font_Ctrl.charInfo.width;	
	
	if((res_font_Ctrl.strCache[index]&0xc000) == 0xc000) //下标
	{
		height_temp 	= res_font_Ctrl.charInfo.height;
		special_temp 	= 2;
	}else if((res_font_Ctrl.strCache[index]&0x8000) == 0x8000) //上标
	{
		height_temp 	= (res_font_Ctrl.charInfo.height>>2)+1;
		special_temp	= 1;
	}else
	{
		height_temp 	= res_font_Ctrl.charInfo.height;
		special_temp	= 0;
	}
	if(height)
		*height	= height_temp;
	if(special)
		*special= special_temp;
		
	return charCache;
}
/*******************************************************************************
* Function Name  : R_loadResource
* Description    : load resource table             
* Input          : R_ICON_T *res : resource table  
                   unsigned long int r_id : resource id
				   unsigned long int cnt  : table length
* Output         : 
* Return         : none
*******************************************************************************/
void res_font_StringTableInit(R_STRING_T *res,u32 r_id_max)
{
	u32 r_id;
	u32 i;
	res_font_Ctrl.R_String_Table = NULL;
	if(res == NULL)
		return;
	r_id = *((u32 *)res);
	
	if(RES_ID_IS_STR(r_id))
	{
		R_STRING_T 	*string_table 		= (R_STRING_T*)res;
		res_font_Ctrl.R_String_Table  	= (R_STRING_T*)res;
		res_font_Ctrl.R_String_ID_Max 	= r_id_max;
		for(i = 0; i < RES_IDNUM_GET(r_id_max); i++ )
		{
			string_table->r_addr = 0;
			string_table->width   = 0;
			string_table->height  = 0;
			string_table++;
		}
	}
}

/*******************************************************************************
* Function Name  : R_iconGetIndex
* Description    : get icon index        
* Input          :  unsigned long int r_id : icon id
* Output        : 
* Return         : int  -1 : fail
                            :index  
******************************************************************************/
static R_STRING_T* res_font_GetStringTab(u32 r_id)
{
    u32 index,i;
	if(RES_ID_IS_STR(r_id))
	{
		R_STRING_T * string_table;
	    if(r_id >= res_font_Ctrl.R_String_ID_Max || res_font_Ctrl.R_String_Table == NULL)
			return NULL;
		index = RES_IDNUM_GET(r_id);
		string_table = res_font_Ctrl.R_String_Table + index;
		if(string_table->r_id == r_id)
			return string_table;
		for(i=0;i < RES_IDNUM_GET(res_font_Ctrl.R_String_ID_Max);i++)
		{
			if(string_table->r_id == r_id)
				return string_table;
		}		
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : R_iconGetDataAndSize
* Description    : get icon data and size        
* Input          :  unsigned long int r_id : icon id
                       unsigned short *width : width
                       unsigned short *heigth:height
* Output        : 
* Return         : int  0 : fail
                            :data  
*******************************************************************************/
u32 res_font_GetAddrAndSize(u32 r_id,u16 *width,u16 *height)
{
	R_STRING_T * string_table = (R_STRING_T*)res_font_GetStringTab(r_id);
	if(string_table)
	{
		if(string_table->width == 0 || string_table->height == 0)
		{
			res_font_GetString(RES_IDNUM_GET(r_id),&(string_table->width),&(string_table->height));
		}
		if(string_table->r_addr == 0)
			string_table->r_addr = 0;
		return string_table->r_addr;
	}
	return 0;
}





