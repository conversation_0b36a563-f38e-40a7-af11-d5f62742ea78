/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../hal/inc/hal.h"

#if  MEDIA_VIDEO_ENCODE_EN > 0
typedef struct MediaVideoCTL_S
{
	u32*		   junkbuf;
	VideoRec_Ctl_T videoCtl[VIDEO_CH_MAX];
}MediaVideoCTL_T;
ALIGNED(4) MediaVideoCTL_T	mediaVideoCtl;


/*******************************************************************************
* Function Name  : videoRecordInit
* Description    : initial video record
* Input          : VIDEO_ARG_T *arg : video initial argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordInit(VIDEO_ARG_T *arg)
{
	u32 i;
	if(arg == NULL)
		return STATUS_FAIL;
	//-------------------------video information-------------------------------------------
	memset((void *)&mediaVideoCtl,0, sizeof(mediaVideoCtl));
	for(i = 0; i < VIDEO_CH_MAX;i++)
	{
		memcpy((void *)&mediaVideoCtl.videoCtl[i].arg,(void *)arg,sizeof(VIDEO_ARG_T));
		mediaVideoCtl.videoCtl[i].ch			= i;
		mediaVideoCtl.videoCtl[i].stat			= MEDIA_STAT_STOP;
		mediaVideoCtl.videoCtl[i].sync_stat		= MEDIA_SYNC_INIT;
		mediaVideoCtl.videoCtl[i].arg.avi_arg.media_ch = -1;
		mediaVideoCtl.videoCtl[i].photoFd		= -1;
	}
	mediaVideoCtl.videoCtl[VIDEO_CH_A].videoFrameGet 	= hal_mjpA_RawBufferGet;
	mediaVideoCtl.videoCtl[VIDEO_CH_A].audioFrameGet 	= hal_auadcBufferGet;
	mediaVideoCtl.videoCtl[VIDEO_CH_A].videoFrameFree 	= hal_mjpA_RawBufferfree;
	mediaVideoCtl.videoCtl[VIDEO_CH_A].audioFrameFree 	= hal_auadcBufferRelease;

	mediaVideoCtl.videoCtl[VIDEO_CH_B].videoFrameGet 	= hal_mjpB_RawBufferGet;
	mediaVideoCtl.videoCtl[VIDEO_CH_B].audioFrameGet 	= hal_auadcBufferGetB;
	mediaVideoCtl.videoCtl[VIDEO_CH_B].videoFrameFree 	= hal_mjpB_RawBufferfree;
	mediaVideoCtl.videoCtl[VIDEO_CH_B].audioFrameFree 	= hal_auadcBufferReleaseB;

	mediaVideoCtl.junkbuf = (u32*)hal_sysMemMalloc(32*1024L);
	if(mediaVideoCtl.junkbuf == NULL)
	{
		deg_Printf("video junk buf malloc fail\n");
		return STATUS_FAIL;
	}
	memset((void *)mediaVideoCtl.junkbuf, 0, 32*1024L);
	mediaVideoCtl.videoCtl[VIDEO_CH_A].arg.avi_arg.avi_cache = (u32*)mediaVideoCtl.junkbuf;
	mediaVideoCtl.videoCtl[VIDEO_CH_B].arg.avi_arg.avi_cache = (u32*)mediaVideoCtl.junkbuf;


	hal_jpg_watermark_init();

    //hal_mjpA_EncodeInit();

    hal_csiEnable(1);

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordUninit
* Description    : uninitial video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordUninit(void)
{
    hal_jpg_watermark_uinit();
	if(mediaVideoCtl.junkbuf)
	{
		hal_sysMemFree(mediaVideoCtl.junkbuf);
		mediaVideoCtl.junkbuf = NULL;
	}
    //hal_auadcStop();
	//hal_mjpA_EncodeUninit();
    //hal_csiEnable(0);

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStart
* Description    : start video record start for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStart(VideoRec_Ctl_T *ctrl)
{
	int ret;
	int fd[4] = {-1,-1,-1,-1};

	MEDIA_NULL_CHECK(ctrl,STATUS_FAIL);
    MEDIA_NULL_CHECK(ctrl->arg.callback,STATUS_FAIL);

	ctrl->stat 		= MEDIA_STAT_START;
	ctrl->photoStat = MEDIA_STAT_STOP;

	ret = ctrl->arg.callback(ctrl->ch,CMD_VIDEO_RECORD_START,(INT32U)&fd[0]); // user create file
	if(ret<0)
	{
		VIDEOREC_DBG("[video rec %d]: create file fail.\n",ctrl->ch);
		return STATUS_FAIL;
	}
    ctrl->space = ret; // kb
   // deg_Printf("space:%d\n",ctrl->space);
    ctrl->arg.avi_arg.fd[0] = fd[0];

    if(ctrl->arg.ftype == MEDIA_AVI_ODML)
    {
		ctrl->arg.avi_arg.fd[1] = fd[0]; // using the same file when fast write enable
		VIDEOREC_DBG("[video rec %d]: AVI_TYPE_OPENDML.\n",ctrl->ch);

    }
    else if(ctrl->arg.ftype == MEDIA_AVI_STD)
    {
		ctrl->arg.avi_arg.fd[1] = fd[1];
		VIDEOREC_DBG("[video rec %d]: AVI_TYPE_STANDARD.\n",ctrl->ch);
    }
	else
	{
		VIDEOREC_DBG("[video rec %d]: AVI_TYPE UNKNOW %d.\n",ctrl->ch,ctrl->arg.ftype);
		return STATUS_FAIL;
	}
	ret = api_multimedia_init(MEDIA_ENCODE, ctrl->arg.ftype);
	if(ret < 0)
	{
		VIDEOREC_DBG("[video rec %d]: aviEncodeInit fail.%d\n",ctrl->ch,ret);
		return STATUS_FAIL;
	}
	ctrl->arg.avi_arg.media_ch = ret;
	ret = api_multimedia_start(ctrl->arg.avi_arg.media_ch,(void*)&ctrl->arg.avi_arg);
	if(ret<0)
	{
		api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
		ctrl->arg.avi_arg.media_ch = -1;
		VIDEOREC_DBG("[video rec %d]: encode parse fail %d .\n",ctrl->ch,ret);
		return STATUS_FAIL;
	}
	ctrl->filstat		= 0;
	if(ctrl->ch == VIDEO_CH_A)
		ctrl->sync_stat = MEDIA_SYNC_WRITING;
	else
		ctrl->sync_stat = MEDIA_SYNC_INIT;
	ctrl->abchangetime 	= XOSTimeGet();
    return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileStop(VideoRec_Ctl_T *ctrl)
{
	int totaltime;
    MEDIA_NULL_CHECK(ctrl,STATUS_FAIL);
	MEDIA_NULL_CHECK(ctrl->arg.callback,STATUS_FAIL);

	VIDEOREC_DBG("[video rec %d]: ready to stop.\n",ctrl->ch);
	if(ctrl->stat != MEDIA_STAT_START)
		return STATUS_OK;
	api_multimedia_gettime(ctrl->arg.avi_arg.media_ch, &totaltime, NULL);
	VIDEOREC_DBG("[video rec %d]: stop.[%d ms].\n",ctrl->ch,	totaltime);
	api_multimedia_end(ctrl->arg.avi_arg.media_ch);
	api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
    if(ctrl->arg.callback)
    {
		ctrl->arg.callback(ctrl->ch,CMD_VIDEO_RECORD_STOP,(INT32U)&ctrl->arg.avi_arg.fd[0]);
    }

	ctrl->stat 				= MEDIA_STAT_STOP;
	ctrl->sync_stat 		= MEDIA_SYNC_INIT;
	ctrl->arg.avi_arg.fd[0] = -1;
	ctrl->arg.avi_arg.fd[1] = -1;
	ctrl->arg.avi_arg.media_ch 	= -1;

	//sd_api_Stop();

   // sd_api_unlock();
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordFileStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordFileError(VideoRec_Ctl_T *ctrl)
{
	api_multimedia_uninit(ctrl->arg.avi_arg.media_ch);
	if(ctrl->arg.callback)
    {
		ctrl->arg.callback(ctrl->ch,CMD_COM_ERROR,(INT32U)&ctrl->arg.avi_arg.fd[0]);
	}
	ctrl->arg.avi_arg.media_ch 	= -1;
	ctrl->arg.avi_arg.fd[0] = -1;
	ctrl->arg.avi_arg.fd[1] = -1;

	ctrl->stat 				= MEDIA_STAT_STOP;
	ctrl->sync_stat	 		= MEDIA_SYNC_INIT;
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordError(void)
{
    hal_mjpA_Enc_Stop();
	hal_mjpB_Enc_Stop();
	hal_auadcStop();
	hal_jpg_watermarkStart(0,0,0);
	videoRecordFileError(&mediaVideoCtl.videoCtl[VIDEO_CH_A]);
	videoRecordFileError(&mediaVideoCtl.videoCtl[VIDEO_CH_B]);
	mediaVideoCtl.videoCtl[VIDEO_CH_A].arg.mdtime = 0;
	mediaVideoCtl.videoCtl[VIDEO_CH_B].arg.mdtime = 0;
	sd_api_Stop();

    sd_api_unlock();
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordStart
* Description    : start video record
* Input          : AUDIO_RECORD_ARG_T *arg : video record argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStart(void)
{
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	VideoRec_Ctl_T * ctlb = &mediaVideoCtl.videoCtl[VIDEO_CH_B];
	sd_api_lock();
	ctla->photoFd 	= -1;
	ctla->photoStat = MEDIA_STAT_STOP;
    if(videoRecordFileStart(ctla) != STATUS_OK)
    {
		goto VIDEO_REC_ERROR;
    }

	if (husb_api_usensor_tran_sta())  //when unsensor in
	{
		husb_api_usensor_res_get(&ctlb->arg.avi_arg.width, &ctlb->arg.avi_arg.height);
		if(videoRecordFileStart(ctlb) != STATUS_OK)
		{
			goto VIDEO_REC_ERROR;
		}
	}

	if(false == hal_auadcStart(PCM_REC_TYPE_AVI,ctla->arg.avi_arg.samplerate,ctla->arg.avi_arg.audio_en * MEDIA_CFG_MIC_VOLUME))// set adc for audio sample rate
	{
		VIDEOREC_DBG("[video rec 0 ]: auadc start fail\n");
		goto VIDEO_REC_ERROR;
	}
	if(false == hal_mjpA_LineBuf_MenInit(MJPEG_TYPE_VIDEO, ctla->arg.avi_arg.width, ctla->arg.avi_arg.height))
	{
		VIDEOREC_DBG("[video rec 0 ]: linebuf malloc fail\n");
		goto VIDEO_REC_ERROR;
	}
	if(husb_api_usensor_tran_sta())
	{
		if(false == hal_auadcStartB(PCM_REC_TYPE_AVI,ctla->arg.avi_arg.samplerate,ctla->arg.avi_arg.audio_en * MEDIA_CFG_MIC_VOLUME))
		{
			VIDEOREC_DBG("[video rec 1 ]: auadc start fail\n");
			goto VIDEO_REC_ERROR;
		}

		if(false == hal_mjpB_LineBuf_MenInit())
		{
			VIDEOREC_DBG("[video rec 1 ]: linebuf malloc fail\n");
			goto VIDEO_REC_ERROR;
		}
		if(false == hal_mjpB_buf_MenInit(MJPEG_TYPE_VIDEO)) // mjpeg memory malloc
		{
			VIDEOREC_DBG("[video rec 1 ]: mjpbuf malloc fail\n");
			goto VIDEO_REC_ERROR;
		}
		hal_jpgB_watermarkPos_Adjust(ctlb->arg.avi_arg.height);
	}

	if(hal_mjpA_buf_MenInit(MJPEG_TYPE_VIDEO) == false) // mjpeg memory malloc
	{
		VIDEOREC_DBG("[video rec 0 ]: mjpbuf malloc fail\n");
		goto VIDEO_REC_ERROR;
	}
    if(false == hal_mjpA_Enc_Video_Start(MJPEG_TYPE_VIDEO, ctla->arg.avi_arg.width, ctla->arg.avi_arg.height, ctla->arg.quality, ctla->arg.timestramp))
    {
		goto VIDEO_REC_ERROR;
    }
	VIDEOREC_DBG("[video rec 0 ]: [%d - %d]start\n",ctla->arg.avi_arg.width, ctla->arg.avi_arg.height);
	if (husb_api_usensor_tran_sta())
	{

		if(hal_mjpB_Enc_Start(MJPEG_TYPE_VIDEO, ctlb->arg.avi_arg.width, ctlb->arg.avi_arg.height, JPEG_Q_62) == false)
		{
			goto VIDEO_REC_ERROR;
		}
		VIDEOREC_DBG("[video rec 1 ]: [%d - %d]start\n",ctlb->arg.avi_arg.width, ctlb->arg.avi_arg.height);
	}
	//hal_sysMemPrint();
	return STATUS_OK;
VIDEO_REC_ERROR:
	//ctla->stat = MEDIA_STAT_STOP;
	//ctlb->stat = MEDIA_STAT_STOP;
	videoRecordError();
	VIDEOREC_DBG("[video rec]: start fail\n");
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoRecordStop
* Description    : stop video record
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordStop(void)
{
	int ret;
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	VideoRec_Ctl_T * ctlb = &mediaVideoCtl.videoCtl[VIDEO_CH_B];
    hal_mjpA_Enc_Stop();
	hal_mjpB_Enc_Stop();
	hal_auadcStop();
	hal_jpg_watermarkStart(0,0,0);
	if(ctla->sync_stat & MEDIA_SYNC_WRITING)
	{
		ctla->sync_stat |= MEDIA_SYNC_JUNK;
	}else if(ctlb->sync_stat & MEDIA_SYNC_WRITING)
	{
		ctlb->sync_stat |= MEDIA_SYNC_JUNK;
	}
	if((videoRecordJunkSync(ctla) != STATUS_OK) || (videoRecordJunkSync(ctlb) != STATUS_OK))
	{
		ret = STATUS_FAIL;
	}
#if 0
	if((videoRecordFileStop(&mediaVideoCtl.videoCtl[VIDEO_CH_A]) != STATUS_OK) || (videoRecordFileStop(&mediaVideoCtl.videoCtl[VIDEO_CH_B]) != STATUS_OK))
	{
		ret =  STATUS_FAIL;
	}
#else
	if((videoRecordFileStop(&mediaVideoCtl.videoCtl[VIDEO_CH_B]) != STATUS_OK) || (videoRecordFileStop(&mediaVideoCtl.videoCtl[VIDEO_CH_A]) != STATUS_OK))
	{
		ret =  STATUS_FAIL;
	}
#endif

	sd_api_Stop();

    sd_api_unlock();
	ret = STATUS_OK;
	return ret;
}

/*******************************************************************************
* Function Name  : videoRecordGetTimeSec
* Description    : videoRecordGetTimeSec
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
uint32 videoRecordGetTimeSec(void)
{
	int curtime;
	api_multimedia_gettime(mediaVideoCtl.videoCtl[VIDEO_CH_A].arg.avi_arg.media_ch,NULL, &curtime);
	return (curtime/1000 + 1);

}
/*******************************************************************************
* Function Name  : videoRecordRestart
* Description    : pause video record restart for loop recording
* Input          : u32 mjp_stop: 0: mjp not stop, 1: mjp stop
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordRestart(u32 mjp_stop)
{
	int stime  = XOSTimeGet();
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	VideoRec_Ctl_T * ctlb = &mediaVideoCtl.videoCtl[VIDEO_CH_B];
	if(husb_api_usensor_tran_sta())
	{
		if(ctlb->stat != MEDIA_STAT_START)
			mjp_stop = 1;
	}else
	{
		if(ctlb->stat == MEDIA_STAT_START)
			mjp_stop = 1;
	}
	if(mjp_stop)
	{
		if(videoRecordStop() == STATUS_OK)
		{
			if(videoRecordStart() != STATUS_OK)
				goto VIDEO_REC_RESTART_ERR;
		}
	}else
	{
		if(ctla->sync_stat & MEDIA_SYNC_WRITING)
		{
			ctla->sync_stat |= MEDIA_SYNC_JUNK;
		}else if(ctlb->sync_stat & MEDIA_SYNC_WRITING)
		{
			ctlb->sync_stat |= MEDIA_SYNC_JUNK;
		}
		if((videoRecordJunkSync(ctla) != STATUS_OK) || (videoRecordJunkSync(ctlb) != STATUS_OK))
		{
			goto VIDEO_REC_RESTART_ERR;
		}
	#if 0
		if((videoRecordFileStop(ctla) != STATUS_OK) || (videoRecordFileStop(ctlb) != STATUS_OK))
		{
			goto VIDEO_REC_RESTART_ERR;
		}
	#else
		if((videoRecordFileStop(ctlb) != STATUS_OK) || (videoRecordFileStop(ctla) != STATUS_OK))
		{
			goto VIDEO_REC_RESTART_ERR;
		}
	#endif
		VIDEOREC_DBG("[video rec]: stop %dms\n", XOSTimeGet()-stime);
		stime  = XOSTimeGet();
		if(videoRecordFileStart(ctla) != STATUS_OK)
		{
			goto VIDEO_REC_RESTART_ERR;
		}
		if (husb_api_usensor_tran_sta())  //when unsensor in
		{
			if(videoRecordFileStart(ctlb) != STATUS_OK)
			{
				goto VIDEO_REC_RESTART_ERR;
			}
		}
	}
	VIDEOREC_DBG("[video rec]: %d restart %dms\n", mjp_stop, XOSTimeGet()-stime);
	return STATUS_OK;


VIDEO_REC_RESTART_ERR:
	VIDEOREC_DBG("[video rec]: restart fail\n");
	videoRecordError(); // stop
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoRecordGetStatus
* Description    : get video record
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordGetStatus(void)
{
	return mediaVideoCtl.videoCtl[VIDEO_CH_A].stat;
}
/*******************************************************************************
* Function Name  : videoRecordFileError
* Description    : start video record error for file handler
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoRecordJunkSync(VideoRec_Ctl_T *ctrl)
{
	if((ctrl->sync_stat & MEDIA_SYNC_JUNK) == MEDIA_SYNC_JUNK)
	{
		if(api_multimedia_addjunk(ctrl->arg.avi_arg.media_ch) < 0)
			return STATUS_FAIL;
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordFrameProcess(VideoRec_Ctl_T *ctrl)
{

	if(!(ctrl->sync_stat & MEDIA_SYNC_WRITING))
	{
		return STATUS_OK;
	}
	u32 vids_size, vids_sync_cur, vids_sync_next;
	u32 auds_size, auds_sync_cur, auds_sync_next;
	int res = 0, curtime;
	void * vids_buf = ctrl->videoFrameGet(&vids_size,&vids_sync_cur, &vids_sync_next);
	void * auds_buf = ctrl->audioFrameGet(&auds_size,&auds_sync_cur, &auds_sync_next);
	if((!vids_buf) && (!auds_buf))
	{
		return STATUS_OK;
	}
#if 0
	if(vids_buf)
	{
		VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
		//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
	}
	if(auds_buf)
	{
		//VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
		//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
	}
#endif

	if(!ctrl->filstat)
	{
		if(auds_buf)
		{
			//case1: ex. file fisrt write, auds start before vids, drop auds
			if((!vids_buf)||(vids_sync_cur >= auds_sync_cur))
			{
				VIDEOREC_DBG("[Warn:%d]audio start before video[V:%d][A:%d]\n",ctrl->ch,vids_sync_cur,auds_sync_cur);
				ctrl->audioFrameFree();
				return STATUS_OK;
			}
			//case2: ex. file fisrt write, vids have not auds frame, drop vids
			else if((vids_buf) &&(vids_sync_next < auds_sync_cur))
			{
				VIDEOREC_DBG("[Warn:%d]drop vids[V:%d][A:%d]\n",ctrl->ch,vids_sync_cur,auds_sync_cur);
				ctrl->videoFrameFree();
				return STATUS_OK;
			}
		}
		if(vids_buf)  //tsync init
		{
			ctrl->tsync_cur  = vids_sync_cur;
			ctrl->tsync_next = vids_sync_next;
			ctrl->iframe_cnt = 0;
			ctrl->sync_cnt   = vids_sync_next - vids_sync_cur;
		}
	}else
	{
		//case 3:ex. vids_sync_cur = 15, auds_sync_cur = 7 //when drop too many vids frame
		if(auds_buf && ( (vids_buf&&(ctrl->tsync_next!=auds_sync_cur)&&(vids_sync_cur>=auds_sync_cur)) || ((!vids_buf)&&(ctrl->tsync_cur>=auds_sync_cur))))
		{
			VIDEOREC_DBG("[Warn:%d]drop auds[%d %d]<V:%d,A:%d>\n",ctrl->ch,ctrl->tsync_cur,ctrl->tsync_next, vids_sync_cur,auds_sync_cur);
			ctrl->audioFrameFree();
			//ctrl->tsync_cur  = vids_sync_cur;
			//ctrl->tsync_next = vids_sync_next;
			//ctrl->iframe_cnt = 0;
			//ctrl->sync_cnt   = vids_sync_next - vids_sync_cur;
			//auds_buf = NULL;
			//goto VIDS_WRITE;
			return STATUS_OK;
		}
		//case 4 :ex vids_sync_next = 7, auds_sync_cur = 14   when auds drop
		else if(vids_buf && auds_buf &&(vids_sync_next<auds_sync_cur))
		{
			VIDEOREC_DBG("[Warn:%d]drop vids[%d %d]<V:%d,A:%d>\n",ctrl->ch,ctrl->tsync_cur,ctrl->tsync_next, vids_sync_cur,auds_sync_cur);
			ctrl->videoFrameFree();
			ctrl->sync_cnt = ctrl->tsync_next - ctrl->tsync_cur - ctrl->sync_cnt;  //可能是一个负值
			return STATUS_OK;
		}
	}
	ctrl->filstat &= ~2; //clear auds write flag
//---------------------video frame ------------------------------------------------------------
//VIDS_WRITE:
	if(vids_buf)
	{

		//VIDEOREC_DBG("c[%d, %d, %d, %d]\n",ctrl->iframe_cnt,ctrl->sync_cnt,ctrl->tsync_cur,ctrl->tsync_next);
		if(ctrl->tsync_cur == vids_sync_cur) //还是当前tsync_cur
		{
			if(ctrl->sync_cnt <= 0)
			{
				//VIDEOREC_DBG("[Warn:%d]drop vids 3 <V:%d %d>\n",ctrl->ch,vids_sync_cur,vids_sync_next);
				ctrl->videoFrameFree();
				goto AUDS_WRITE;
			}
		}else
		{
			ctrl->iframe_cnt = (ctrl->sync_cnt > 0)?ctrl->sync_cnt:0;
			ctrl->sync_cnt   = vids_sync_next - vids_sync_cur + ctrl->sync_cnt;
			ctrl->tsync_cur  = vids_sync_cur;
			ctrl->tsync_next = vids_sync_next;
		}
VIDS_SYNC_IFRAME:

		if(api_multimedia_encodeframe(ctrl->arg.avi_arg.media_ch,(void*)vids_buf, vids_size, AVI_FRAME_DC_VIDEO) < 0)
		{
			ctrl->videoFrameFree();
			res = -1;
			goto VIDEO_FRAME_ERROR;
		}
        if((ctrl->photoStat == MEDIA_STAT_START)&&(vids_size>0))
        {
			fs_write(ctrl->photoFd,(void *)(vids_buf+8),vids_size-8);
			ctrl->photoStat = MEDIA_STAT_READY;
        }
		ctrl->filstat |= 1;  // set video start flag
		ctrl->sync_cnt--;
		while(ctrl->iframe_cnt > 0)
		{
			ctrl->iframe_cnt--;
		#if AVI_IFRAME_REAL == 0
			vids_size = 0;
		#endif
			//VIDEOREC_DBG("|");
			goto VIDS_SYNC_IFRAME;
		}
		ctrl->videoFrameFree();
	}
//---------------------audio frame ------------------------------------------------------------
AUDS_WRITE:
	if(auds_buf && (ctrl->tsync_cur >= auds_sync_cur))
	{
		hal_wdtClear();
		//VIDEOREC_DBG("A[%d, %d, %d, %d]\n",ctrl->iframe_cnt,ctrl->sync_cnt,ctrl->tsync_cur,ctrl->tsync_next);
		//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
		//if(auds_size==0) VIDEOREC_DBG("<audio size error>\n");
		if(api_multimedia_encodeframe(ctrl->arg.avi_arg.media_ch,(void*)auds_buf, auds_size, AVI_FRAME_WD_AUDIO) < 0)
		{
			ctrl->audioFrameFree();
			res = -2;
			goto VIDEO_FRAME_ERROR;
		}
		ctrl->filstat |= 2;  // audio frame ok
		ctrl->audioFrameFree();
	}
	//------------------------------time loop check---------------------------------------------------------
	if(ctrl->sync_stat & MEDIA_SYNC_PRERESTART)
	{
		if(ctrl->filstat & 0x02)
		{
			ctrl->sync_stat = (ctrl->sync_stat &~(MEDIA_SYNC_WRITING|MEDIA_SYNC_PRERESTART))|MEDIA_SYNC_JUNK|MEDIA_SYNC_RESTART;
			if(vids_buf)
			{
				VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
				//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
			}
			if(auds_buf)
			{
				VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
				//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
			}
			VIDEOREC_DBG("[Video %d] pre to loop.\n",ctrl->ch);
		}
	}else{

		api_multimedia_gettime(ctrl->arg.avi_arg.media_ch,NULL, &curtime);
		if((ctrl->arg.rectime*1000) <= curtime || (ctrl->arg.mdtime && ctrl->arg.mdtime <= curtime))
		{
			if(ctrl->filstat & 0x02) //auds had writed
			{
				ctrl->sync_stat = (ctrl->sync_stat &~MEDIA_SYNC_WRITING)|MEDIA_SYNC_JUNK|MEDIA_SYNC_RESTART;
				VIDEOREC_DBG("[Video %d] loop.\n",ctrl->ch);


			}else
			{
				ctrl->sync_stat |= MEDIA_SYNC_PRERESTART;
				if(vids_buf)
				{
					VIDEOREC_DBG("vsync[%d, %d]\n",vids_sync_cur,vids_sync_next);
					//VIDEOREC_DBG("[%x, %d]\n",vids_buf,vids_size);
				}
				if(auds_buf)
				{
					VIDEOREC_DBG("async[%d, %d]\n",auds_sync_cur,auds_sync_next);
					//VIDEOREC_DBG("[%x, %d]\n",auds_buf,auds_size);
				}
				VIDEOREC_DBG("[Video %d] pre loop.\n",ctrl->ch);
			}
		}
	}

    return STATUS_OK;
VIDEO_FRAME_ERROR:
	VIDEOREC_DBG("[Video %d Err] res = %d.\n",ctrl->ch,res);
    //videoRecordStop();  // recorder can not handler this error.only stop recording

	return STATUS_FAIL;

}
/*******************************************************************************
* Function Name  : videoRecordService
* Description    : video record service
* Input          : none
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordService(void)
{
	int res = 0;
	u32 curtime;
	u32 size,size2;
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	VideoRec_Ctl_T * ctlb = &mediaVideoCtl.videoCtl[VIDEO_CH_B];
	hal_wdtClear();
	if(ctla->stat != MEDIA_STAT_START)
		return ctla->stat;
//----------------space check-------------------
	size = fs_tell(ctla->arg.avi_arg.fd[0]);
	if(ctla->arg.ftype == MEDIA_AVI_STD)
	{
		size += fs_size(ctla->arg.avi_arg.fd[1]);
	}
	if(ctlb->stat == MEDIA_STAT_START)
	{
		size2 = fs_tell(ctlb->arg.avi_arg.fd[0]);
		if(ctlb->arg.ftype == MEDIA_AVI_STD)
		{
			size2 += fs_tell(ctlb->arg.avi_arg.fd[0]);
		}
	}else
	{
		size2 = 0;
	}
	size >>= 10;
	size2 >>= 10;
	if( (ctla->space  <(size + 1024)) || (size2 && (ctlb->space<(size2+1024))))
	{
		VIDEOREC_DBG("[video rec]: A[space %d,size:%d],B[space %d,size:%d]\n",ctla->space,size, ctlb->space,size2);
		goto VIDEO_RECORD_RESTART;
	}
//-----------------VIDEO A & B coop control-----------------------------------
	if(ctlb->stat == MEDIA_STAT_START)
	{
		if((ctla->sync_stat & MEDIA_SYNC_RESTART) &&  (ctlb->sync_stat & MEDIA_SYNC_RESTART)) //restart
		{
			goto VIDEO_RECORD_RESTART;
		}
	}else
	{
		if(ctla->sync_stat & MEDIA_SYNC_RESTART)
		{
			goto VIDEO_RECORD_RESTART;
		}
	}
//-----------------video sync control-----------------------------------
//VIDEO_RECORD_SYNC:
/*	if((!ctla->filstat)||(!ctlb->filstat))
	{
		ctla->sync_stat |= MEDIA_SYNC_WRITING; //至少一个配置为WRITING
		ctlb->sync_stat &= ~MEDIA_SYNC_WRITING;
	}*/
	if(ctlb->stat == MEDIA_STAT_START)
	{
		if(husb_api_detech_check())
		{
			VIDEOREC_DBG("[video rec]: lowerpower, stop rec\n");
/*			if(ctla->sync_stat & MEDIA_SYNC_WRITING)
			{
				ctla->sync_stat &= ~MEDIA_SYNC_WRITING;
				if(ctla->filstat)
					ctla->sync_stat |= MEDIA_SYNC_JUNK;
			}else if(ctlb->sync_stat & MEDIA_SYNC_WRITING)
			{
				ctlb->sync_stat &= ~MEDIA_SYNC_WRITING;
				if(ctlb->filstat)
					ctlb->sync_stat |= MEDIA_SYNC_JUNK;
			}*/
			res = -2;
			goto VIDEO_RECORD_STOP;
		}
		if(!husb_api_usensor_tran_sta())
		{
			//ctla->sync_stat |= MEDIA_SYNC_JUNK;
			//ctlb->sync_stat |= MEDIA_SYNC_JUNK;
			goto VIDEO_RECORD_RESTART;
		}
		if(ctla->sync_stat & MEDIA_SYNC_RESTART) // A wait restart, B writing
		{
			ctlb->sync_stat |= MEDIA_SYNC_WRITING;
		}else if(ctlb->sync_stat & MEDIA_SYNC_RESTART)// B wait restart, A writing
		{
			ctla->sync_stat |= MEDIA_SYNC_WRITING;
		}else{
			if(hal_mjpA_Buffer_prefull()||hal_adcBuffer_prefull())
			{
				ctla->sync_stat |= MEDIA_SYNC_FULL;
			}else if(!hal_mjpA_Buffer_halffull())
			{
				ctla->sync_stat &= ~MEDIA_SYNC_FULL;
			}
			if(hal_mjpB_Buffer_prefull()||hal_adcBufferB_prefull())
			{
				ctlb->sync_stat |= MEDIA_SYNC_FULL;
			}else if(!hal_mjpB_Buffer_halffull())
			{
				ctlb->sync_stat &= ~MEDIA_SYNC_FULL;
			}
			if(ctla->sync_stat & MEDIA_SYNC_WRITING)
			{

				if(!(ctla->sync_stat & MEDIA_SYNC_FULL) && (ctlb->sync_stat & MEDIA_SYNC_FULL))
				{
					ctla->sync_stat &= ~MEDIA_SYNC_WRITING;
					ctlb->sync_stat |=  MEDIA_SYNC_WRITING;
					if(ctla->filstat)
						ctla->sync_stat |= MEDIA_SYNC_JUNK;
					ctla->abchangetime = XOSTimeGet();
					//VIDEOREC_DBG("[video rec]: A->B time:%dms\n",ctla->abchangetime - ctlb->abchangetime);
				}
			}else if(ctlb->sync_stat & MEDIA_SYNC_WRITING)
			{
				//if((ctla->sync_stat & MEDIA_SYNC_FULL) && (!(ctlb->sync_stat & MEDIA_SYNC_FULL)))
				if(ctla->sync_stat & MEDIA_SYNC_FULL)
				{
					ctla->sync_stat |=  MEDIA_SYNC_WRITING;
					ctlb->sync_stat &= ~MEDIA_SYNC_WRITING;
					if(ctlb->filstat)
						ctlb->sync_stat |= MEDIA_SYNC_JUNK;
					ctlb->abchangetime = XOSTimeGet();
					//VIDEOREC_DBG("[video rec]: B->A time:%dms\n",ctlb->abchangetime - ctla->abchangetime);
				}
			}else
			{
				VIDEOREC_DBG("[video rec err]: no this case\n");
			}
		}
	}
	else
	{
		if(husb_api_usensor_tran_sta())
		{
			//ctla->sync_stat |= MEDIA_SYNC_JUNK;
			goto VIDEO_RECORD_RESTART;
		}
		//ctla->sync_stat |= MEDIA_SYNC_WRITING;
	}
//-----------------frame write control-----------------------------------
//VIDEO_RECORD_VIDEO_FRAME:
	if((videoRecordJunkSync(ctla) != STATUS_OK) || (videoRecordJunkSync(ctlb) != STATUS_OK))
	{
		res = -3;
		goto VIDEO_RECORD_ERROR;
	}
	if((videoRecordFrameProcess(ctla) !=STATUS_OK ) ||(videoRecordFrameProcess(ctlb) !=STATUS_OK ))
	{
		res = -4;
		goto VIDEO_RECORD_ERROR;
	}

    return ctla->stat;
VIDEO_RECORD_RESTART:
	//if((videoRecordJunkSync(ctla) != STATUS_OK) || (videoRecordJunkSync(ctlb) != STATUS_OK))
	//{
	//	res = -5;
	//	goto VIDEO_RECORD_ERROR;
	//}

	api_multimedia_gettime(ctla->arg.avi_arg.media_ch,NULL, &curtime);
	if(ctla->arg.mdtime)
	{
		if(ctla->arg.mdtime <= curtime)
		{
			ctla->arg.mdtime = 0;
			if(ctlb->arg.mdtime)
				ctlb->arg.mdtime = 0;
			return videoRecordStop();
		}else
		{
			ctla->arg.mdtime -= curtime;
			if(ctlb->arg.mdtime)
				ctlb->arg.mdtime -= curtime;
		}
	}
	if(ctla->tsync_cur > 0xffffff00)
	{
		videoRecordRestart(1); //stop mjp restart
	}else{
		videoRecordRestart(0);
	}
	return ctla->stat;
VIDEO_RECORD_STOP:
	//if((videoRecordJunkSync(ctla) != STATUS_OK) || (videoRecordJunkSync(ctlb) != STATUS_OK))
	//{
	//	res = -6;
	//	goto VIDEO_RECORD_ERROR;
	//}
	VIDEOREC_DBG("[video rec stop]: res %d.space=%dKB\n",res,ctla->space);
    videoRecordStop();  // recorder can not handler this error.only stop recording
	return ctla->stat;
VIDEO_RECORD_ERROR:
	VIDEOREC_DBG("[video rec err]: res no %d.space=%dKB\n",res,ctla->space);
    videoRecordError();  // recorder can not handler this error.only stop recording
	return ctla->stat;
}
/*******************************************************************************
* Function Name  : videoRecordCmdSet
* Description    : video record set paramater
* Input          : INT32U cmd : command
                      INT32U para : paramater
* Output         : none
* Return         : int

*******************************************************************************/
int videoRecordCmdSet(INT32U cmd,INT32U para)
{
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	VideoRec_Ctl_T * ctlb = &mediaVideoCtl.videoCtl[VIDEO_CH_B];
	if((ctla->stat != MEDIA_STAT_STOP) && (cmd != CMD_COM_AUDIOEN))
		return STATUS_FAIL;
	if(cmd == CMD_COM_LOOPTIME)
	{
		ctla->arg.rectime = para;
		ctlb->arg.rectime = para;
	}
	else if(cmd == CMD_COM_LOOPREC)
	{
		ctla->arg.looprecord = para;
		ctlb->arg.looprecord = para;
	}
	else if(cmd == CMD_COM_TIMESTRAMP)
	{
		ctla->arg.timestramp = para;
		ctlb->arg.timestramp = para;
		//watermark_enable(mediaVideoCtrl.arg.timestramp);
	}
	else if(cmd == CMD_COM_AUDIOEN)
	{
		ctla->arg.avi_arg.audio_en = para;
		ctlb->arg.avi_arg.audio_en = para;
	}
	else if(cmd == CMD_COM_RESOLUTIONN)
	{
		ctla->arg.avi_arg.width = para>>16;
		ctla->arg.avi_arg.height = para &0xffff;
	}
	else if(cmd == CMD_COM_QUALITY)
	{
		ctla->arg.quality = para;
	}else if(cmd == CMD_COM_MDTIME)
	{
		ctla->arg.mdtime = para;
		ctlb->arg.mdtime = para;
	}
	else
		return STATUS_FAIL;

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoRecordSizePreSec
* Description    : video record size pre second
* Input          : INT32U time_s : time,second
                      int channel : video channel ,A,B
* Output         : none
* Return         : int   kb

*******************************************************************************/
int videoRecordSizePreSec(int channel,INT32U time_s)
{
	INT32U res,size;
	VideoRec_Ctl_T * ctl = &mediaVideoCtl.videoCtl[channel];
	if(channel == VIDEO_CH_B)
		res = hal_mjpB_Sizecalculate(ctl->arg.quality, ctl->arg.avi_arg.width, ctl->arg.avi_arg.height) >> 10;  // 30k
    else if(channel == VIDEO_CH_A)
    {
		res = hal_mjpA_Sizecalculate(ctl->arg.quality, ctl->arg.avi_arg.width, ctl->arg.avi_arg.height) >> 10;
    }
    else
		return 0;

	size = (res * ctl->arg.avi_arg.fps)*time_s;

	res = (size>>10)/10;  // 10M bytes

	if(res == 0)
		return size;

	//if(channel == VIDEO_CH_A)	// add more size for save
	{
		u32 add_size = 0;
		if(time_s > 1)
		{
			add_size = 2*((time_s+59)/60);	//720P  add 20M / min for save
		}
		if(ctl->arg.avi_arg.width <= 640)
		{
			res+=add_size/2;
		}
		else if(ctl->arg.avi_arg.width <= 720)
		{
			res+=add_size;
		}
		else if(ctl->arg.avi_arg.width <= 1920)
		{
			res+=add_size*2;
		}
    }

	size = (((res*10)<<10) + 0x1f)&~(0x1f);  //KB align 32KB
	return size;
}
/*******************************************************************************
* Function Name  : videoRecordTakePhoto
* Description    : video record take photo when recording
* Input          : int fd : file handle
* Output         : none
* Return         : int : status

*******************************************************************************/
void videoRecordTakePhotoCfg(u32 stat,int fd)
{
	VideoRec_Ctl_T * ctla = &mediaVideoCtl.videoCtl[VIDEO_CH_A];
	ctla->photoStat = stat;
	ctla->photoFd   = fd;
}
/*******************************************************************************
* Function Name  : videoRecordTakePhotoStatus
* Description    : video record tabke photo status
* Input          : none
* Output         : none
* Return         : int : status

*******************************************************************************/
int videoRecordTakePhotoStatus(void)
{
	return mediaVideoCtl.videoCtl[VIDEO_CH_A].photoStat;
}
void videoRecordSetPhotoStatus(INT32S status)
{
	mediaVideoCtl.videoCtl[VIDEO_CH_A].photoStat = status;
}
int videoRecordTakePhotoFd(void)
{
	return mediaVideoCtl.videoCtl[VIDEO_CH_A].photoFd;
}
#endif
