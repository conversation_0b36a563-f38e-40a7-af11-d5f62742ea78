/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_SPI0_H
    #define HX330X_SPI0_H

#define  SPI0            0


/*******************************************************************************
* Function Name  : hx330x_spi0Init
* Description	 : spi0 initial
* Input 		 : u32 u32SPIBaud : spi0 baudrate set
*                  u8 bus_mode : SPI0_1_LINE/SPI0_2_LINE0/SPI0_2_LINE1/SPI0_4_LINE
* Output		 : None
* Return		 : none 
*******************************************************************************/
void hx330x_spi0ManualInit(u32 u32SPIBaud,u8 bus_mode);
/*******************************************************************************
* Function Name  : hx330x_spi0SendByte
* Description	 : spi0 send one byte
* Input 		 : u8 byte : data
* Output		 : None
* Return		 : u8 : 
*******************************************************************************/
void hx330x_spi0SendByte(u8 byte);
/*******************************************************************************
* Function Name  : hx330x_spi0RecvByte
* Description	 : spi0 recv one byte
* Input 		 : none
* Output		 : None
* Return		 : u8 : 
*******************************************************************************/
u8 hx330x_spi0RecvByte(void);
/*******************************************************************************
* Function Name  : hx330x_spi0Send0
* Description	 : spi0 send date by DMA
* Input 		 : u32 pDataBuf : data buffer
                   u32 u32DataLen : data len,>=8
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_spi0Send(void *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hx330x_spi0Recv0
* Description	 : spi0 recv date by DMA
* Input 		 : u32 pDataBuf : data buffer
                   u32 u32DataLen : data len,>=8
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_spi0Recv(void *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hx330x_spi0CS0Config
* Description	 : spi0 cs(flash) config
* Input 		 : u8 level : 0:cs is low
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_spi0CS0Config(u8 level);

/*******************************************************************************
* Function Name  : hx330x_spi0AutoModeInit
* Description	 : spi0 init for auto  mode
* Input 		 : u32 u32SPIBaud : spi0 baudrate set
*                  u8 bus_mode : SPI0_1_LINE/SPI0_2_LINE0/SPI0_2_LINE1/SPI0_4_LINE
                   u8 read_mode: SPI0_1_LINE 0:standard mode, 1: fast mode
* Output		 : None
* Return		 : none 
*******************************************************************************/
void hx330x_spi0AutoModeInit(u32 u32SPIBaud,u8 bus_mode, u8 read_mode);

/*******************************************************************************
* Function Name  : hx330x_spi0ExitAutoMode
* Description	 : spi0 exit auto mode 
* Input 		 : None
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_spi0ExitAutoMode(void);


#endif
