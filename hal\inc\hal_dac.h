/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  HAL_DAC_H
    #define  HAL_DAC_H


#define HAL_VOLUME_MAKE(x)          ((x*0x7fff)/100)
	

/*******************************************************************************
* Function Name  : hal_dacInit
* Description    : hal layer .initial dac
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dacInit(void);
/*******************************************************************************
* Function Name  : hal_dacPlayStart
* Description    : hal layer .start dac play
* Input          : u32 sampleRate : sample rate
				   u32 buffer : buffer
				   u32 size : size
* Output         : None
* Return         : bool : true - success; false - fail
*******************************************************************************/
bool hal_dacPlayInit(u32 sampleRate);
/*******************************************************************************
* Function Name  : hal_dacPlayStart
* Description    : hal layer .start dac play
* Input          : u32 sampleRate : sample rate
				   u32 buffer : buffer
				   u32 size : size
* Output         : None
* Return         : bool : true - success; false - fail
*******************************************************************************/
bool hal_dacPlayStart(u32 sampleRate,u32 buffer,u32 size);
/*******************************************************************************
* Function Name  : hal_dacPlayStop
* Description    : hal layer .stop dac play
* Input          : none
* Output         : None
* Return         : none 
*******************************************************************************/
void hal_dacPlayStop(void);
/*******************************************************************************
* Function Name  : hal_dacSetVolume
* Description    : hal layer .set dac volume
* Input          : u8 volume : dac volume
* Output         : None
* Return         : none 
*******************************************************************************/
void hal_dacSetVolume(u8 volume);
/*******************************************************************************
* Function Name  : hal_dacSetBuffer
* Description    : hal layer .set dac buffer
* Input          : u32 buffer : buffer addr
				   u32 size : size
* Output         : None
* Return         : none
*******************************************************************************/
//void hal_dacSetBuffer(u32 buffer,u32 size);
#define hal_dacSetBuffer				hx330x_dacBufferSet
/*******************************************************************************
* Function Name  : hal_dacFlush
*Bref: void hal_dacFlush(u32 size)
* Description    : 
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
#define hal_dacFlush  hx330x_dacBufferFlush
/*******************************************************************************
* Function Name  : hal_dacCallBackRegister
* Description    : hal layer .dac callback register
* Input          : void (*callback)(int flag) : callback
* Output         : None
* Return         : none
*******************************************************************************/
void hal_dacCallBackRegister(void (*callback)(int flag));
/*******************************************************************************
* Function Name  : hal_dacHPSet
* Description    : hal layer .dac hp vdd output
* Input          :  u8 en : enable.1->enable,0-disable
                       u32 level : vdd level.SEE HP_VDD_E
* Output         : None
* Return         : none
*******************************************************************************/
//void hal_dacHPSet(u8 en,u32 level);
#define hal_dacHPSet					hx330x_dacHPSet



#endif


