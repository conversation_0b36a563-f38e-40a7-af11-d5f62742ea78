/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num3 :33*64
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
const unsigned char ascii_num4_32[]= // ' '
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_33[]= // '!'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,
   0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,
   0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,
   0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,
   0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,
   0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_35[]= // '#'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x1f,0x80,0xf0,0x00,0x00,0x1f,0x81,0xf8,0x00,0x00,0x1f,0x81,
   0xf8,0x00,0x00,0x1f,0x81,0xf8,0x00,0x00,0x1f,0x81,0xf8,0x00,0x00,0x3f,0x83,0xf8,
   0x00,0x00,0x3f,0x03,0xf0,0x00,0x00,0x3f,0x03,0xf0,0x00,0x00,0x3f,0x03,0xf0,0x00,
   0x00,0x3f,0x03,0xf0,0x00,0x00,0x3f,0x03,0xf0,0x00,0x00,0x3f,0x03,0xf0,0x00,0x00,
   0x7f,0x07,0xe0,0x00,0x00,0x7e,0x07,0xe0,0x00,0x00,0x7e,0x07,0xe0,0x00,0x1f,0xff,
   0xff,0xff,0x80,0x1f,0xff,0xff,0xff,0x80,0x1f,0xff,0xff,0xff,0x80,0x1f,0xff,0xff,
   0xff,0x80,0x00,0x7c,0x0f,0xc0,0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,0xfc,0x0f,0xc0,
   0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,0xfc,0x1f,0xc0,0x00,
   0x00,0xfc,0x1f,0x80,0x00,0x00,0xfc,0x1f,0x80,0x00,0x00,0xf8,0x1f,0x80,0x00,0x01,
   0xf8,0x1f,0x80,0x00,0x01,0xf8,0x1f,0x80,0x00,0x7f,0xff,0xff,0xfe,0x00,0x7f,0xff,
   0xff,0xfe,0x00,0x7f,0xff,0xff,0xfe,0x00,0x7f,0xff,0xff,0xfe,0x00,0x01,0xf8,0x3f,
   0x00,0x00,0x03,0xf8,0x7f,0x00,0x00,0x03,0xf0,0x7f,0x00,0x00,0x03,0xf0,0x7e,0x00,
   0x00,0x03,0xf0,0x7e,0x00,0x00,0x03,0xf0,0x7e,0x00,0x00,0x03,0xf0,0x7e,0x00,0x00,
   0x03,0xf0,0x7e,0x00,0x00,0x03,0xf0,0x7e,0x00,0x00,0x07,0xf0,0xfe,0x00,0x00,0x07,
   0xe0,0xfc,0x00,0x00,0x07,0xe0,0xfc,0x00,0x00,0x07,0xe0,0xfc,0x00,0x00,0x07,0xe0,
   0xfc,0x00,0x00,0x07,0xc0,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_36[]= // '$'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x1f,0xfe,
   0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x03,0xff,0xff,0xe0,0x00,0x07,0xff,0xff,0xf0,
   0x00,0x0f,0xff,0xff,0xf0,0x00,0x0f,0xf3,0xf7,0xf8,0x00,0x1f,0xc3,0xf3,0xf8,0x00,
   0x1f,0x83,0xf1,0xfc,0x00,0x3f,0x83,0xf1,0xfc,0x00,0x3f,0x03,0xf0,0xfc,0x00,0x3f,
   0x03,0xf0,0xfc,0x00,0x3f,0x03,0xf0,0xfc,0x00,0x3f,0x03,0xf0,0xfc,0x00,0x3f,0x03,
   0xf0,0x38,0x00,0x3f,0x83,0xf0,0x00,0x00,0x3f,0x83,0xf0,0x00,0x00,0x1f,0xc3,0xf0,
   0x00,0x00,0x1f,0xe3,0xf0,0x00,0x00,0x0f,0xf3,0xf0,0x00,0x00,0x07,0xff,0xf0,0x00,
   0x00,0x03,0xff,0xf0,0x00,0x00,0x01,0xff,0xfc,0x00,0x00,0x00,0x7f,0xff,0x00,0x00,
   0x00,0x0f,0xff,0xc0,0x00,0x00,0x03,0xff,0xf0,0x00,0x00,0x03,0xff,0xf8,0x00,0x00,
   0x03,0xf3,0xfc,0x00,0x00,0x03,0xf1,0xfe,0x00,0x00,0x03,0xf0,0xfe,0x00,0x3c,0x03,
   0xf0,0x7f,0x00,0x7e,0x03,0xf0,0x7f,0x00,0x7e,0x03,0xf0,0x3f,0x00,0x7e,0x03,0xf0,
   0x3f,0x00,0x7e,0x03,0xf0,0x3f,0x00,0x7e,0x03,0xf0,0x3f,0x00,0x7e,0x03,0xf0,0x3f,
   0x00,0x7f,0x03,0xf0,0x7f,0x00,0x3f,0x03,0xf0,0x7e,0x00,0x3f,0x83,0xf0,0xfe,0x00,
   0x3f,0xc3,0xf1,0xfe,0x00,0x1f,0xe3,0xf3,0xfc,0x00,0x0f,0xff,0xff,0xf8,0x00,0x07,
   0xff,0xff,0xf0,0x00,0x03,0xff,0xff,0xe0,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0x1f,
   0xfe,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_37[]= // '%'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x07,0xf0,0x07,0xe0,0x00,0x0f,0xf8,0x07,0xc0,0x00,0x1f,0xfc,0x07,0xc0,
   0x00,0x3f,0xfe,0x0f,0xc0,0x00,0x3f,0x7e,0x0f,0x80,0x00,0x3f,0x7e,0x0f,0x80,0x00,
   0x7e,0x3f,0x1f,0x80,0x00,0x7e,0x3f,0x1f,0x00,0x00,0x7e,0x3f,0x1f,0x00,0x00,0x7e,
   0x3f,0x3f,0x00,0x00,0x7e,0x3f,0x3e,0x00,0x00,0x7e,0x3f,0x3e,0x00,0x00,0x7e,0x3f,
   0x7e,0x00,0x00,0x7e,0x3f,0x7c,0x00,0x00,0x7e,0x3f,0x7c,0x00,0x00,0x3f,0x7e,0xfc,
   0x00,0x00,0x3f,0x7e,0xf8,0x00,0x00,0x3f,0xff,0xf8,0x00,0x00,0x1f,0xfd,0xf8,0x00,
   0x00,0x0f,0xf9,0xf0,0x00,0x00,0x07,0xf3,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xe3,0xf8,0x00,0x00,0x07,0xe7,0xfc,0x00,0x00,0x07,0xef,0xfe,0x00,0x00,
   0x07,0xdf,0xff,0x00,0x00,0x0f,0xdf,0xbf,0x00,0x00,0x0f,0x9f,0xbf,0x00,0x00,0x0f,
   0xbf,0x3f,0x80,0x00,0x1f,0xbf,0x1f,0x80,0x00,0x1f,0x3f,0x1f,0x80,0x00,0x1f,0x3f,
   0x1f,0x80,0x00,0x3f,0x3f,0x1f,0x80,0x00,0x3e,0x3f,0x1f,0x80,0x00,0x3e,0x3f,0x1f,
   0x80,0x00,0x7e,0x3f,0x1f,0x80,0x00,0x7c,0x3f,0x1f,0x80,0x00,0x7c,0x3f,0xbf,0x80,
   0x00,0xfc,0x1f,0xbf,0x00,0x00,0xf8,0x1f,0xbf,0x00,0x00,0xf8,0x1f,0xff,0x00,0x01,
   0xf8,0x0f,0xfe,0x00,0x01,0xf0,0x07,0xfc,0x00,0x01,0xf0,0x03,0xf8,0x00,0x01,0xf0,
   0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_38[]= // '&'
{
   0x21,0x40,
   0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,
   0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,
   0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,
   0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,
   0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,
   0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,
   0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,
   0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,
   0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,
   0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,
   0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,
   0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,
   0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,
   0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,
   0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,
   0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,
   0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,
   0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,
   0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,
   0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,
};
const unsigned char ascii_num4_39[]= // '''
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,
   0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,
   0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x00,0x00,0x01,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_40[]= // '('
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,
   0xfe,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x03,0xf8,
   0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,
   0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x3f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,
   0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,
   0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,
   0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,
   0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,
   0x07,0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,
   0xf8,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,0xfe,0x00,0x00,0x00,0x00,0xff,
   0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_41[]= // ')'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x1f,
   0xc0,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x07,0xf0,
   0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,
   0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0xfe,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,
   0x00,0x7e,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x7f,0x00,
   0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,
   0x00,0x00,0xfe,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,
   0x03,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,
   0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,0x3f,0xc0,
   0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x3f,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_42[]= // '*'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x01,0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x03,0x83,0xf0,0x00,0x00,0x07,0xf3,0xf3,0xfc,0x00,0x0f,0xff,0xff,0xfc,
   0x00,0x0f,0xff,0xff,0xfc,0x00,0x07,0xff,0xff,0xfc,0x00,0x01,0xff,0xff,0xf0,0x00,
   0x00,0x3f,0xff,0x80,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,0x00,
   0x1f,0xff,0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x7f,0xbf,0x80,0x00,0x00,0x7f,
   0x3f,0xc0,0x00,0x00,0xfe,0x1f,0xc0,0x00,0x00,0xfe,0x0f,0xe0,0x00,0x00,0xfc,0x0f,
   0xe0,0x00,0x00,0xf8,0x07,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_43[]= // '+'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x3f,0xff,0xff,0xff,0x00,0x7f,0xff,0xff,0xff,0x00,
   0x7f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_44[]= // ','
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,
   0xf8,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x03,0xe0,
   0x00,0x00,0x00,0x07,0xc0,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x0f,0x00,0x00,
   0x00,0x00,0x0e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_45[]= // '-'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xff,0xff,0x00,0x7f,0xff,0xff,0xff,0x00,
   0x7f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_46[]= // '.'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,
   0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_47[]= // '/'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,
   0x01,0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x00,0x00,0x07,0xc0,0x00,0x00,0x00,0x07,0xc0,0x00,0x00,0x00,0x07,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x0f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,
   0x7e,0x00,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xf8,0x00,0x00,0x00,0x00,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,
   0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x07,0xc0,0x00,0x00,0x00,0x07,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0x80,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x00,
   0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,
   0x00,0x7c,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,
   0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_48[]= // '0'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf8,
   0x00,0x00,0x00,0x3f,0xfe,0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0xc0,
   0x00,0x01,0xfe,0x3f,0xc0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x07,0xf0,0x07,0xf0,0x00,
   0x07,0xe0,0x03,0xf0,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,
   0xc0,0x01,0xf8,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x3f,0x80,0x00,0xfc,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0xc0,0x01,0xfc,
   0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xe0,0x03,0xf8,0x00,
   0x07,0xe0,0x03,0xf0,0x00,0x07,0xf0,0x07,0xf0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x01,
   0xfe,0x3f,0xc0,0x00,0x01,0xff,0xff,0xc0,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0x3f,
   0xfe,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_49[]= // '1'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,
   0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x01,0xff,0x00,0x00,
   0x00,0x3f,0xff,0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,
   0x3f,0xff,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_50[]= // '2'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfe,
   0x00,0x00,0x00,0x3f,0xff,0xc0,0x00,0x00,0xff,0xff,0xe0,0x00,0x01,0xff,0xff,0xf8,
   0x00,0x03,0xfe,0x0f,0xf8,0x00,0x07,0xf8,0x01,0xfc,0x00,0x07,0xe0,0x00,0xfe,0x00,
   0x0f,0xe0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x7f,0x00,0x0f,
   0xc0,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,
   0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x0f,0x80,0x00,0x7f,0x00,0x00,0x00,0x00,
   0x7f,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x01,0xfe,0x00,0x00,0x00,0x03,0xfc,
   0x00,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x3f,0xf0,0x00,
   0x00,0x00,0xff,0xe0,0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x07,0xff,0x00,0x00,0x00,
   0x0f,0xfc,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x7f,0xe0,0x00,0x00,0x00,0xff,
   0xc0,0x00,0x00,0x01,0xff,0x00,0x00,0x00,0x03,0xfe,0x00,0x00,0x00,0x03,0xfc,0x00,
   0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,
   0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0xff,0xff,0xfe,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,
   0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_51[]= // '3'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xfc,
   0x00,0x00,0x00,0x7f,0xff,0x00,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xff,0xff,0xe0,
   0x00,0x07,0xfc,0x1f,0xf0,0x00,0x07,0xf8,0x07,0xf0,0x00,0x0f,0xe0,0x03,0xf8,0x00,
   0x0f,0xe0,0x01,0xf8,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,
   0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x0f,0x00,0x00,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,
   0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x3f,0xf0,
   0x00,0x00,0x07,0xff,0xe0,0x00,0x00,0x07,0xff,0xc0,0x00,0x00,0x07,0xff,0xe0,0x00,
   0x00,0x07,0xff,0xf0,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x03,0xfc,0x00,0x00,
   0x00,0x01,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,
   0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x80,0x00,0xfe,0x00,0x3f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x01,0xfc,0x00,
   0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xe0,0x03,0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x07,
   0xfc,0x3f,0xf0,0x00,0x07,0xff,0xff,0xe0,0x00,0x01,0xff,0xff,0xc0,0x00,0x00,0xff,
   0xff,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_52[]= // '4'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,
   0xc0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x1f,0xe0,
   0x00,0x00,0x00,0x3f,0xe0,0x00,0x00,0x00,0x3f,0xe0,0x00,0x00,0x00,0x7f,0xe0,0x00,
   0x00,0x00,0x7f,0xe0,0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x01,0xff,0xe0,0x00,0x00,
   0x01,0xff,0xe0,0x00,0x00,0x03,0xff,0xe0,0x00,0x00,0x07,0xf7,0xe0,0x00,0x00,0x07,
   0xf7,0xe0,0x00,0x00,0x0f,0xe7,0xe0,0x00,0x00,0x1f,0xc7,0xe0,0x00,0x00,0x1f,0xc7,
   0xe0,0x00,0x00,0x3f,0x87,0xe0,0x00,0x00,0x7f,0x07,0xe0,0x00,0x00,0x7f,0x07,0xe0,
   0x00,0x00,0xfe,0x07,0xe0,0x00,0x01,0xfe,0x07,0xe0,0x00,0x01,0xfc,0x07,0xe0,0x00,
   0x03,0xf8,0x07,0xe0,0x00,0x07,0xf8,0x07,0xe0,0x00,0x07,0xf0,0x07,0xe0,0x00,0x0f,
   0xe0,0x07,0xe0,0x00,0x1f,0xe0,0x07,0xe0,0x00,0x1f,0xc0,0x07,0xe0,0x00,0x3f,0xc0,
   0x07,0xe0,0x00,0x3f,0x80,0x07,0xe0,0x00,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,
   0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x3f,0xff,0xff,0xff,0x80,0x00,0x00,0x07,0xe0,
   0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,
   0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,
   0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x07,0xe0,0x00,0x00,0x00,0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_53[]= // '5'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xff,0xff,
   0xfc,0x00,0x01,0xff,0xff,0xfc,0x00,0x01,0xff,0xff,0xfc,0x00,0x03,0xff,0xff,0xfc,
   0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x07,
   0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,
   0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe7,0xfc,0x00,0x00,0x07,0xff,0xff,
   0x80,0x00,0x0f,0xff,0xff,0xc0,0x00,0x0f,0xff,0xff,0xe0,0x00,0x0f,0xfe,0x1f,0xf0,
   0x00,0x0f,0xf8,0x07,0xf8,0x00,0x0f,0xf0,0x03,0xfc,0x00,0x0f,0xe0,0x01,0xfc,0x00,
   0x0f,0xc0,0x00,0xfe,0x00,0x07,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,
   0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x1e,0x00,0x00,0x7f,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x80,0x00,0xfe,0x00,
   0x3f,0x80,0x01,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0xf0,0x07,0xf8,0x00,0x0f,
   0xfc,0x1f,0xf0,0x00,0x07,0xff,0xff,0xe0,0x00,0x03,0xff,0xff,0xc0,0x00,0x00,0xff,
   0xff,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_54[]= // '6'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xfe,
   0x00,0x00,0x00,0x1f,0xff,0xc0,0x00,0x00,0x3f,0xff,0xe0,0x00,0x00,0x7f,0xff,0xf0,
   0x00,0x00,0xff,0x0f,0xf8,0x00,0x01,0xfc,0x03,0xfc,0x00,0x03,0xf8,0x01,0xfc,0x00,
   0x03,0xf0,0x01,0xfc,0x00,0x07,0xf0,0x00,0xfc,0x00,0x07,0xe0,0x00,0xfe,0x00,0x0f,
   0xe0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x7c,0x00,0x0f,0xc0,0x00,0x00,0x00,0x1f,0xc0,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x87,0xfe,
   0x00,0x00,0x1f,0x9f,0xff,0x80,0x00,0x1f,0x7f,0xff,0xe0,0x00,0x3f,0xff,0xff,0xf0,
   0x00,0x3f,0xfe,0x0f,0xf8,0x00,0x3f,0xf8,0x03,0xfc,0x00,0x3f,0xf0,0x01,0xfc,0x00,
   0x3f,0xe0,0x00,0xfe,0x00,0x3f,0xc0,0x00,0xfe,0x00,0x3f,0x80,0x00,0x7e,0x00,0x3f,
   0x80,0x00,0x7f,0x00,0x3f,0x80,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,
   0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,
   0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x1f,0x00,0x00,0x3f,0x00,0x1f,0x80,0x00,0x7f,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0xfe,0x00,
   0x0f,0xe0,0x01,0xfc,0x00,0x0f,0xf0,0x03,0xfc,0x00,0x07,0xf8,0x07,0xf8,0x00,0x03,
   0xfe,0x1f,0xf0,0x00,0x01,0xff,0xff,0xe0,0x00,0x00,0xff,0xff,0xc0,0x00,0x00,0x7f,
   0xff,0x80,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_55[]= // '7'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xff,
   0xfe,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,
   0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x01,0xfe,0x00,0x00,0x00,0x01,0xfc,0x00,
   0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,
   0x00,0x0f,0xe0,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,
   0x3f,0x80,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x7e,
   0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,
   0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x0f,0xe0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x1f,
   0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,
   0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,
   0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,
   0x00,0x7e,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_56[]= // '8'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xfc,
   0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xff,0xff,0xe0,
   0x00,0x07,0xfc,0x1f,0xf0,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x0f,0xe0,0x03,0xf8,0x00,
   0x1f,0xc0,0x01,0xf8,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,
   0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x0f,0xe0,0x03,
   0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x07,0xfc,0x1f,0xf0,0x00,0x03,0xff,0xff,0xf0,
   0x00,0x01,0xff,0xff,0xe0,0x00,0x03,0xff,0xff,0xc0,0x00,0x07,0xff,0xff,0xe0,0x00,
   0x0f,0xfc,0x1f,0xf0,0x00,0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,
   0xc0,0x00,0xfc,0x00,0x3f,0x80,0x00,0xfe,0x00,0x3f,0x80,0x00,0x7e,0x00,0x3f,0x80,
   0x00,0x7f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,
   0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,
   0x00,0x3f,0x80,0x00,0x7f,0x00,0x3f,0x80,0x00,0x7f,0x00,0x1f,0x80,0x00,0xfe,0x00,
   0x1f,0xc0,0x00,0xfe,0x00,0x1f,0xe0,0x01,0xfe,0x00,0x0f,0xf0,0x03,0xfc,0x00,0x07,
   0xfc,0x1f,0xf8,0x00,0x03,0xff,0xff,0xf0,0x00,0x01,0xff,0xff,0xe0,0x00,0x00,0xff,
   0xff,0xc0,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_57[]= // '9'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xfc,
   0x00,0x00,0x00,0x7f,0xff,0x00,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xff,0xff,0xe0,
   0x00,0x07,0xfc,0x3f,0xe0,0x00,0x07,0xf0,0x0f,0xf0,0x00,0x0f,0xe0,0x03,0xf8,0x00,
   0x1f,0xc0,0x03,0xf8,0x00,0x1f,0xc0,0x01,0xf8,0x00,0x1f,0x80,0x00,0xfc,0x00,0x3f,
   0x80,0x00,0xfc,0x00,0x3f,0x80,0x00,0xfc,0x00,0x3f,0x00,0x00,0x7c,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x80,0x00,0xfe,
   0x00,0x3f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x01,0xfe,0x00,0x1f,0xc0,0x03,0xfe,0x00,
   0x1f,0xe0,0x07,0xfe,0x00,0x0f,0xf0,0x0f,0xfe,0x00,0x07,0xfc,0x3f,0xfe,0x00,0x03,
   0xff,0xff,0xfe,0x00,0x01,0xff,0xff,0x7e,0x00,0x00,0xff,0xfc,0x7e,0x00,0x00,0x1f,
   0xf0,0xfe,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x1f,0x00,0x01,0xf8,
   0x00,0x3f,0x00,0x01,0xf8,0x00,0x3f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x03,0xf8,0x00,
   0x1f,0xc0,0x07,0xf0,0x00,0x1f,0xc0,0x0f,0xf0,0x00,0x1f,0xe0,0x1f,0xe0,0x00,0x0f,
   0xf8,0x7f,0xc0,0x00,0x07,0xff,0xff,0x80,0x00,0x03,0xff,0xff,0x00,0x00,0x01,0xff,
   0xfe,0x00,0x00,0x00,0x3f,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_58[]= // ':'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x07,0xf0,
   0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,
   0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_59[]= // ';'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x03,
   0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x07,0xf0,
   0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,
   0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,
   0x00,0x01,0xf0,0x00,0x00,0x00,0x01,0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,
   0x07,0xc0,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x00,0x0e,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_60[]= // '<'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x7f,0x80,
   0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x03,0xff,0x80,0x00,0x00,0x0f,0xfe,0x00,0x00,
   0x00,0x3f,0xf8,0x00,0x00,0x00,0x7f,0xf0,0x00,0x00,0x01,0xff,0xc0,0x00,0x00,0x07,
   0xff,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0xff,0xe0,
   0x00,0x00,0x01,0xff,0xc0,0x00,0x00,0x07,0xff,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,
   0x00,0x3f,0xf8,0x00,0x00,0x00,0x7f,0xe0,0x00,0x00,0x00,0x7f,0xc0,0x00,0x00,0x00,
   0x7f,0xc0,0x00,0x00,0x00,0x7f,0xe0,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0x0f,
   0xfe,0x00,0x00,0x00,0x07,0xff,0x00,0x00,0x00,0x01,0xff,0xc0,0x00,0x00,0x00,0x7f,
   0xf0,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,0x00,0x03,0xff,
   0x80,0x00,0x00,0x01,0xff,0xc0,0x00,0x00,0x00,0x7f,0xf0,0x00,0x00,0x00,0x1f,0xfc,
   0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,0x00,0x03,0xff,0x80,0x00,0x00,0x00,0xff,0x80,
   0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_61[]= // '='
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,
   0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x3f,0xff,
   0xff,0xff,0x00,0x3f,0xff,0xff,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_62[]= // '>'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x38,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,
   0x7f,0xe0,0x00,0x00,0x00,0x7f,0xf0,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x07,
   0xff,0x00,0x00,0x00,0x03,0xff,0x80,0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x00,0x3f,
   0xf8,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x07,0xff,0x00,0x00,0x00,0x01,0xff,
   0xc0,0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0x1f,0xfe,
   0x00,0x00,0x00,0x07,0xff,0x00,0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x00,0xff,0x80,
   0x00,0x00,0x00,0xff,0x80,0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x07,0xff,0x00,0x00,
   0x00,0x1f,0xfc,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x03,
   0xff,0x80,0x00,0x00,0x07,0xff,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x7f,0xf0,
   0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x03,0xff,0x80,0x00,0x00,0x0f,0xfe,0x00,0x00,
   0x00,0x1f,0xfc,0x00,0x00,0x00,0x7f,0xf0,0x00,0x00,0x00,0x7f,0xc0,0x00,0x00,0x00,
   0x7f,0x80,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_63[]= // '?'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,0x00,0x3f,0xff,0x80,0x00,0x00,0xff,0xff,
   0xe0,0x00,0x01,0xff,0xff,0xf0,0x00,0x03,0xfe,0x0f,0xf8,0x00,0x07,0xf8,0x03,0xfc,
   0x00,0x07,0xf0,0x01,0xfc,0x00,0x0f,0xe0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfe,0x00,
   0x1f,0xc0,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0xfe,0x00,0x0f,0x00,
   0x00,0xfe,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x03,
   0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,0x1f,0xe0,
   0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0xff,0x00,0x00,
   0x00,0x00,0xfe,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,
   0x01,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,
   0x0f,0xf0,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,0x0f,
   0xf0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_64[]= // '@'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xff,0x00,0x00,0x00,0x07,
   0xff,0xc0,0x00,0x00,0x1f,0xff,0xf0,0x00,0x00,0x3f,0x8f,0xf8,0x00,0x00,0x7e,0x01,
   0xf8,0x00,0x00,0xfc,0x00,0xfc,0x00,0x01,0xf8,0x00,0x7e,0x00,0x01,0xf0,0x00,0x7e,
   0x00,0x03,0xf0,0x00,0x3e,0x00,0x07,0xe0,0x00,0x3f,0x00,0x07,0xe0,0x00,0x1f,0x00,
   0x0f,0xc0,0x00,0x1f,0x00,0x0f,0xc0,0x00,0x1f,0x00,0x1f,0x80,0x00,0x1f,0x80,0x1f,
   0x81,0xff,0xcf,0x80,0x3f,0x03,0xff,0xcf,0x80,0x3f,0x07,0xff,0xcf,0x80,0x3f,0x07,
   0xbf,0xcf,0x80,0x7e,0x0f,0x3f,0x8f,0x80,0x7e,0x1f,0x1f,0x8f,0x80,0x7e,0x1f,0x1f,
   0x8f,0x80,0x7e,0x3e,0x1f,0x8f,0x80,0xfe,0x3e,0x1f,0x8f,0x80,0xfc,0x7e,0x1f,0x0f,
   0x80,0xfc,0x7e,0x1f,0x0f,0x80,0xfc,0x7c,0x3f,0x0f,0x80,0xfc,0xfc,0x3f,0x0f,0x80,
   0xfc,0xfc,0x3f,0x0f,0x80,0xfc,0xfc,0x3e,0x0f,0x80,0xfc,0xfc,0x3e,0x1f,0x00,0xfc,
   0xfc,0x3e,0x1f,0x00,0xfc,0xfc,0x7e,0x1f,0x00,0xfc,0xfc,0x7e,0x1f,0x00,0xfc,0xfc,
   0x7c,0x3e,0x00,0xfc,0xfc,0x7c,0x3e,0x00,0xfc,0xfc,0xfc,0x3e,0x00,0xfe,0x7e,0xfc,
   0x7c,0x00,0x7e,0x7f,0xfe,0xfc,0x00,0x7e,0x3f,0xff,0xf8,0x00,0x7e,0x3f,0xdf,0xf0,
   0x00,0x7e,0x0f,0x8f,0xc0,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x78,0x00,0x0f,
   0xc0,0x00,0xf8,0x00,0x0f,0xe0,0x01,0xf0,0x00,0x07,0xe0,0x03,0xe0,0x00,0x03,0xf8,
   0x0f,0xe0,0x00,0x03,0xfc,0x3f,0xc0,0x00,0x00,0xff,0xff,0x00,0x00,0x00,0x7f,0xfe,
   0x00,0x00,0x00,0x1f,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_65[]= // 'A'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,
   0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,
   0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x00,0x1f,0xfc,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,
   0x3f,0x7e,0x00,0x00,0x00,0x3f,0x7f,0x00,0x00,0x00,0x3f,0x3f,0x00,0x00,0x00,0x3f,
   0x3f,0x00,0x00,0x00,0x7e,0x3f,0x00,0x00,0x00,0x7e,0x1f,0x80,0x00,0x00,0x7e,0x1f,
   0x80,0x00,0x00,0xfc,0x1f,0x80,0x00,0x00,0xfc,0x1f,0x80,0x00,0x00,0xfc,0x0f,0xc0,
   0x00,0x00,0xfc,0x0f,0xc0,0x00,0x01,0xf8,0x0f,0xc0,0x00,0x01,0xf8,0x07,0xe0,0x00,
   0x01,0xf8,0x07,0xe0,0x00,0x03,0xf0,0x07,0xe0,0x00,0x03,0xf0,0x07,0xe0,0x00,0x03,
   0xf0,0x03,0xf0,0x00,0x03,0xff,0xff,0xf0,0x00,0x07,0xff,0xff,0xf0,0x00,0x07,0xff,
   0xff,0xf8,0x00,0x07,0xff,0xff,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,
   0xf8,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x7e,
   0x00,0x00,0x3f,0x00,0x7e,0x00,0x00,0x3f,0x00,0x7e,0x00,0x00,0x1f,0x80,0x7c,0x00,
   0x00,0x1f,0x80,0x7c,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_66[]= // 'B'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xff,0xfc,
   0x00,0x00,0x0f,0xff,0xff,0x00,0x00,0x1f,0xff,0xff,0x80,0x00,0x1f,0xff,0xff,0xc0,
   0x00,0x1f,0x80,0x1f,0xe0,0x00,0x1f,0x80,0x0f,0xf0,0x00,0x1f,0x80,0x07,0xf0,0x00,
   0x1f,0x80,0x03,0xf0,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x01,0xf8,0x00,0x1f,
   0x80,0x01,0xf8,0x00,0x1f,0x80,0x01,0xf8,0x00,0x1f,0x80,0x01,0xf8,0x00,0x1f,0x80,
   0x01,0xf8,0x00,0x1f,0x80,0x01,0xf8,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x03,
   0xf0,0x00,0x1f,0x80,0x07,0xf0,0x00,0x1f,0x80,0x0f,0xe0,0x00,0x1f,0x80,0x1f,0xe0,
   0x00,0x1f,0xff,0xff,0xc0,0x00,0x1f,0xff,0xff,0x80,0x00,0x1f,0xff,0xff,0xc0,0x00,
   0x1f,0xff,0xff,0xf0,0x00,0x1f,0x80,0x0f,0xf8,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,
   0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,
   0x00,0xfe,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,
   0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,
   0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0xc0,0x0f,0xf8,0x00,0x1f,
   0xff,0xff,0xf0,0x00,0x1f,0xff,0xff,0xe0,0x00,0x0f,0xff,0xff,0x80,0x00,0x07,0xff,
   0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_67[]= // 'C'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xfe,
   0x00,0x00,0x00,0x1f,0xff,0x80,0x00,0x00,0x7f,0xff,0xe0,0x00,0x00,0xff,0xff,0xf0,
   0x00,0x01,0xff,0x0f,0xf8,0x00,0x03,0xfc,0x07,0xf8,0x00,0x03,0xf8,0x03,0xfc,0x00,
   0x07,0xf0,0x01,0xfc,0x00,0x07,0xe0,0x00,0xfc,0x00,0x0f,0xe0,0x00,0xfe,0x00,0x0f,
   0xc0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x7e,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,
   0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,
   0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x00,0x00,0x00,0x3f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x1f,0xc0,0x00,0x7e,
   0x00,0x0f,0xc0,0x00,0xfe,0x00,0x0f,0xe0,0x00,0xfc,0x00,0x07,0xe0,0x00,0xfc,0x00,
   0x07,0xf0,0x01,0xfc,0x00,0x03,0xf8,0x03,0xf8,0x00,0x03,0xfc,0x07,0xf8,0x00,0x01,
   0xff,0x1f,0xf0,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x7f,0xff,0xe0,0x00,0x00,0x3f,
   0xff,0x80,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_68[]= // 'D'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xff,0xf8,
   0x00,0x00,0x0f,0xff,0xfe,0x00,0x00,0x0f,0xff,0xff,0x00,0x00,0x1f,0xff,0xff,0x80,
   0x00,0x1f,0xc0,0x3f,0xc0,0x00,0x1f,0x80,0x0f,0xe0,0x00,0x1f,0x80,0x07,0xf0,0x00,
   0x1f,0x80,0x07,0xf0,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x01,0xf8,0x00,0x1f,
   0x80,0x01,0xf8,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,
   0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xf8,
   0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x03,0xf8,0x00,0x1f,0x80,0x07,0xf0,0x00,
   0x1f,0x80,0x0f,0xf0,0x00,0x1f,0x80,0x1f,0xe0,0x00,0x1f,0xc0,0x7f,0xc0,0x00,0x1f,
   0xff,0xff,0xc0,0x00,0x0f,0xff,0xff,0x80,0x00,0x07,0xff,0xfe,0x00,0x00,0x03,0xff,
   0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_69[]= // 'E'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xff,0xff,
   0xfc,0x00,0x07,0xff,0xff,0xfe,0x00,0x0f,0xff,0xff,0xfe,0x00,0x0f,0xff,0xff,0xfc,
   0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0xff,0xff,0xf8,0x00,0x1f,0xff,0xff,0xfc,0x00,0x1f,0xff,0xff,0xfc,0x00,
   0x1f,0xff,0xff,0xf8,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,
   0xff,0xff,0xfc,0x00,0x0f,0xff,0xff,0xfe,0x00,0x0f,0xff,0xff,0xfe,0x00,0x03,0xff,
   0xff,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_70[]= // 'F'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xff,0xff,
   0xfc,0x00,0x07,0xff,0xff,0xfe,0x00,0x0f,0xff,0xff,0xfe,0x00,0x1f,0xff,0xff,0xfc,
   0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0xff,0xff,0xf0,0x00,0x1f,0xff,0xff,0xf0,0x00,
   0x1f,0xff,0xff,0xf0,0x00,0x1f,0xff,0xff,0xf0,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_71[]= // 'G'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xfe,
   0x00,0x00,0x00,0x1f,0xff,0x80,0x00,0x00,0x7f,0xff,0xe0,0x00,0x00,0xff,0xff,0xf0,
   0x00,0x01,0xff,0x1f,0xf8,0x00,0x01,0xfc,0x07,0xf8,0x00,0x03,0xf8,0x03,0xfc,0x00,
   0x07,0xf0,0x01,0xfc,0x00,0x07,0xf0,0x01,0xfc,0x00,0x0f,0xe0,0x00,0xfe,0x00,0x0f,
   0xc0,0x00,0xfe,0x00,0x0f,0xc0,0x00,0x7e,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,
   0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,
   0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x00,0x00,0x00,0x3f,0x00,0x7f,0xff,0x00,
   0x3f,0x00,0x7f,0xff,0x00,0x3f,0x00,0x7f,0xff,0x00,0x3f,0x00,0x7f,0xff,0x00,0x3f,
   0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x80,
   0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,
   0x7f,0x00,0x1f,0x80,0x00,0x7f,0x00,0x1f,0xc0,0x00,0x7f,0x00,0x0f,0xc0,0x00,0x7f,
   0x00,0x0f,0xe0,0x00,0xff,0x00,0x0f,0xe0,0x00,0xff,0x00,0x07,0xf0,0x01,0xff,0x00,
   0x07,0xf0,0x01,0xff,0x00,0x03,0xf8,0x03,0xff,0x00,0x01,0xfc,0x07,0xff,0x00,0x01,
   0xff,0x1f,0xff,0x00,0x00,0xff,0xff,0xff,0x00,0x00,0x7f,0xff,0xbf,0x00,0x00,0x1f,
   0xff,0x3e,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_72[]= // 'H'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x7c,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0xff,0xff,0xfe,
   0x00,0x3f,0xff,0xff,0xfe,0x00,0x3f,0xff,0xff,0xfe,0x00,0x3f,0xff,0xff,0xfe,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x1e,0x00,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_73[]= // 'I'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xe0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_74[]= // 'J'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,
   0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x07,0xe0,0x03,0xf0,0x00,0x07,0xe0,0x03,0xf0,0x00,0x07,0xe0,0x03,0xf0,
   0x00,0x07,0xe0,0x03,0xf0,0x00,0x07,0xe0,0x03,0xf0,0x00,0x07,0xe0,0x03,0xf0,0x00,
   0x07,0xf0,0x07,0xf0,0x00,0x07,0xf0,0x07,0xe0,0x00,0x03,0xf0,0x0f,0xe0,0x00,0x03,
   0xfc,0x3f,0xc0,0x00,0x03,0xff,0xff,0xc0,0x00,0x01,0xff,0xff,0x80,0x00,0x00,0xff,
   0xfe,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_75[]= // 'K'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x7c,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x01,0xfc,
   0x00,0x3f,0x00,0x03,0xf8,0x00,0x3f,0x00,0x07,0xf0,0x00,0x3f,0x00,0x0f,0xe0,0x00,
   0x3f,0x00,0x0f,0xc0,0x00,0x3f,0x00,0x1f,0xc0,0x00,0x3f,0x00,0x3f,0x80,0x00,0x3f,
   0x00,0x7f,0x00,0x00,0x3f,0x00,0xfe,0x00,0x00,0x3f,0x00,0xfc,0x00,0x00,0x3f,0x01,
   0xf8,0x00,0x00,0x3f,0x03,0xf8,0x00,0x00,0x3f,0x07,0xf0,0x00,0x00,0x3f,0x0f,0xe0,
   0x00,0x00,0x3f,0x1f,0xc0,0x00,0x00,0x3f,0x1f,0xc0,0x00,0x00,0x3f,0x3f,0xc0,0x00,
   0x00,0x3f,0x7f,0xe0,0x00,0x00,0x3f,0xff,0xe0,0x00,0x00,0x3f,0xff,0xf0,0x00,0x00,
   0x3f,0xfb,0xf0,0x00,0x00,0x3f,0xf3,0xf8,0x00,0x00,0x3f,0xe1,0xf8,0x00,0x00,0x3f,
   0xc1,0xfc,0x00,0x00,0x3f,0x80,0xfe,0x00,0x00,0x3f,0x80,0x7e,0x00,0x00,0x3f,0x00,
   0x7f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x80,0x00,0x3f,0x00,0x1f,
   0x80,0x00,0x3f,0x00,0x1f,0xc0,0x00,0x3f,0x00,0x0f,0xc0,0x00,0x3f,0x00,0x0f,0xe0,
   0x00,0x3f,0x00,0x07,0xf0,0x00,0x3f,0x00,0x07,0xf0,0x00,0x3f,0x00,0x03,0xf8,0x00,
   0x3f,0x00,0x01,0xf8,0x00,0x3f,0x00,0x01,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,
   0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7f,0x00,0x3f,0x00,
   0x00,0x3f,0x00,0x1e,0x00,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_76[]= // 'L'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xff,0xff,0xfe,0x00,0x0f,0xff,0xff,0xff,0x00,0x07,0xff,0xff,0xff,0x00,0x03,0xff,
   0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_77[]= // 'M'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x3f,0xc0,0x01,0xfe,0x00,0x3f,0xe0,0x01,0xfe,0x00,0x3f,0xe0,0x03,0xfe,
   0x00,0x3f,0xe0,0x03,0xfe,0x00,0x3f,0xe0,0x03,0xfe,0x00,0x3f,0xe0,0x03,0xfe,0x00,
   0x3f,0xe0,0x03,0xfe,0x00,0x3f,0xf0,0x07,0xfe,0x00,0x3f,0xf0,0x07,0xfe,0x00,0x3f,
   0xf0,0x07,0xfe,0x00,0x3f,0xf0,0x07,0xfe,0x00,0x3f,0xf0,0x07,0xfe,0x00,0x3f,0xf8,
   0x0f,0xfe,0x00,0x3f,0xf8,0x0f,0xfe,0x00,0x3f,0xf8,0x0f,0xfe,0x00,0x3f,0xf8,0x0f,
   0xfe,0x00,0x3f,0xf8,0x0f,0xfe,0x00,0x3f,0xfc,0x0f,0xfe,0x00,0x3f,0xfc,0x1f,0xfe,
   0x00,0x3f,0xfc,0x1f,0xfe,0x00,0x3f,0x7c,0x1f,0x7e,0x00,0x3f,0x7c,0x1f,0x7e,0x00,
   0x3f,0x7c,0x1f,0x7e,0x00,0x3f,0x7e,0x3f,0x7e,0x00,0x3f,0x7e,0x3f,0x7e,0x00,0x3f,
   0x3e,0x3f,0x7e,0x00,0x3f,0x3e,0x3e,0x7e,0x00,0x3f,0x3e,0x3e,0x7e,0x00,0x3f,0x3f,
   0x7e,0x7e,0x00,0x3f,0x3f,0x7e,0x7e,0x00,0x3f,0x3f,0x7e,0x7e,0x00,0x3f,0x1f,0x7c,
   0x7e,0x00,0x3f,0x1f,0x7c,0x7e,0x00,0x3f,0x1f,0xfc,0x7e,0x00,0x3f,0x1f,0xfc,0x7e,
   0x00,0x3f,0x1f,0xfc,0x7e,0x00,0x3f,0x0f,0xf8,0x7e,0x00,0x3f,0x0f,0xf8,0x7e,0x00,
   0x3f,0x0f,0xf8,0x7e,0x00,0x3f,0x0f,0xf8,0x7e,0x00,0x3f,0x0f,0xf8,0x7e,0x00,0x3f,
   0x0f,0xf8,0x7e,0x00,0x3f,0x07,0xf0,0x7e,0x00,0x3f,0x07,0xf0,0x7e,0x00,0x3f,0x07,
   0xf0,0x7e,0x00,0x1e,0x03,0xe0,0x3c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_78[]= // 'N'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0xf8,0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xe0,0x00,0xfc,0x00,0x1f,0xe0,0x00,0xfc,
   0x00,0x1f,0xf0,0x00,0xfc,0x00,0x1f,0xf0,0x00,0xfc,0x00,0x1f,0xf0,0x00,0xfc,0x00,
   0x1f,0xf8,0x00,0xfc,0x00,0x1f,0xf8,0x00,0xfc,0x00,0x1f,0xfc,0x00,0xfc,0x00,0x1f,
   0xfc,0x00,0xfc,0x00,0x1f,0xfc,0x00,0xfc,0x00,0x1f,0xfe,0x00,0xfc,0x00,0x1f,0xbe,
   0x00,0xfc,0x00,0x1f,0xbf,0x00,0xfc,0x00,0x1f,0x9f,0x00,0xfc,0x00,0x1f,0x9f,0x80,
   0xfc,0x00,0x1f,0x8f,0x80,0xfc,0x00,0x1f,0x8f,0x80,0xfc,0x00,0x1f,0x8f,0xc0,0xfc,
   0x00,0x1f,0x87,0xc0,0xfc,0x00,0x1f,0x87,0xe0,0xfc,0x00,0x1f,0x83,0xe0,0xfc,0x00,
   0x1f,0x83,0xf0,0xfc,0x00,0x1f,0x81,0xf0,0xfc,0x00,0x1f,0x81,0xf0,0xfc,0x00,0x1f,
   0x81,0xf8,0xfc,0x00,0x1f,0x80,0xf8,0xfc,0x00,0x1f,0x80,0xfc,0xfc,0x00,0x1f,0x80,
   0x7c,0xfc,0x00,0x1f,0x80,0x7e,0xfc,0x00,0x1f,0x80,0x7e,0xfc,0x00,0x1f,0x80,0x3e,
   0xfc,0x00,0x1f,0x80,0x3f,0xfc,0x00,0x1f,0x80,0x1f,0xfc,0x00,0x1f,0x80,0x1f,0xfc,
   0x00,0x1f,0x80,0x0f,0xfc,0x00,0x1f,0x80,0x0f,0xfc,0x00,0x1f,0x80,0x0f,0xfc,0x00,
   0x1f,0x80,0x07,0xfc,0x00,0x1f,0x80,0x07,0xfc,0x00,0x1f,0x80,0x03,0xfc,0x00,0x1f,
   0x80,0x03,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x00,
   0x01,0xf8,0x00,0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_79[]= // 'O'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf8,
   0x00,0x00,0x00,0x3f,0xfe,0x00,0x00,0x00,0x7f,0xff,0x00,0x00,0x00,0xff,0xff,0x80,
   0x00,0x01,0xfe,0x3f,0xc0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x03,0xf0,0x07,0xe0,0x00,
   0x07,0xf0,0x07,0xf0,0x00,0x07,0xe0,0x03,0xf0,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,
   0xc0,0x01,0xf8,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x3f,0x80,0x00,
   0xfe,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,
   0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0xc0,0x01,0xf8,
   0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x07,0xe0,0x03,0xf0,0x00,
   0x07,0xf0,0x07,0xf0,0x00,0x03,0xf0,0x07,0xe0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x01,
   0xfe,0x3f,0xc0,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0x7f,0xff,0x00,0x00,0x00,0x3f,
   0xfe,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_80[]= // 'P'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xff,0xff,
   0x80,0x00,0x07,0xff,0xff,0xe0,0x00,0x0f,0xff,0xff,0xf0,0x00,0x1f,0xff,0xff,0xf8,
   0x00,0x1f,0xc0,0x07,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,
   0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0x7f,0x00,0x1f,0x80,0x00,0x7f,0x00,0x1f,
   0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,
   0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,0x3f,0x00,0x1f,0x80,0x00,
   0x7f,0x00,0x1f,0x80,0x00,0x7f,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfe,
   0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x07,0xfc,0x00,0x1f,0xff,0xff,0xf8,0x00,
   0x1f,0xff,0xff,0xf0,0x00,0x1f,0xff,0xff,0xe0,0x00,0x1f,0xff,0xff,0x80,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_81[]= // 'Q'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xf8,
   0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x7f,0xff,0x80,0x00,0x00,0xff,0xff,0xc0,
   0x00,0x01,0xfe,0x1f,0xe0,0x00,0x03,0xfc,0x0f,0xf0,0x00,0x03,0xf8,0x07,0xf0,0x00,
   0x07,0xf0,0x03,0xf8,0x00,0x07,0xe0,0x01,0xf8,0x00,0x0f,0xe0,0x01,0xfc,0x00,0x0f,
   0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x3f,0x80,0x00,
   0x7f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,
   0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,
   0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,
   0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,
   0x00,0x3f,0x00,0x3f,0x80,0x00,0x7f,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x7c,0x7e,0x00,0x1f,0xc0,0xfe,0x7e,0x00,0x0f,0xc0,0xff,0xfc,
   0x00,0x0f,0xc0,0x7f,0xfc,0x00,0x0f,0xe0,0x7f,0xfc,0x00,0x07,0xe0,0x3f,0xf8,0x00,
   0x07,0xf0,0x1f,0xf8,0x00,0x03,0xf8,0x0f,0xf0,0x00,0x03,0xfc,0x0f,0xf8,0x00,0x01,
   0xfe,0x3f,0xfc,0x00,0x00,0xff,0xff,0xfe,0x00,0x00,0x7f,0xff,0xff,0x00,0x00,0x3f,
   0xfe,0x7f,0x00,0x00,0x07,0xf8,0x3f,0x00,0x00,0x00,0x00,0x1e,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_82[]= // 'R'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xff,0xfc,
   0x00,0x00,0x0f,0xff,0xff,0x80,0x00,0x1f,0xff,0xff,0xe0,0x00,0x3f,0xff,0xff,0xf0,
   0x00,0x3f,0x80,0x0f,0xf8,0x00,0x3f,0x00,0x03,0xf8,0x00,0x3f,0x00,0x01,0xfc,0x00,
   0x3f,0x00,0x01,0xfc,0x00,0x3f,0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,0xfe,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,
   0xfc,0x00,0x3f,0x00,0x01,0xfc,0x00,0x3f,0x00,0x01,0xfc,0x00,0x3f,0x00,0x03,0xf8,
   0x00,0x3f,0x00,0x0f,0xf0,0x00,0x3f,0xff,0xff,0xe0,0x00,0x3f,0xff,0xff,0xc0,0x00,
   0x3f,0xff,0xff,0xe0,0x00,0x3f,0xff,0xff,0xf0,0x00,0x3f,0x00,0x07,0xf8,0x00,0x3f,
   0x00,0x03,0xf8,0x00,0x3f,0x00,0x01,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,
   0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,
   0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,
   0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,
   0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,0xfe,0x00,0x3f,
   0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x3e,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_83[]= // 'S'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xfe,
   0x00,0x00,0x00,0x7f,0xff,0x80,0x00,0x01,0xff,0xff,0xe0,0x00,0x03,0xff,0xff,0xf0,
   0x00,0x07,0xfc,0x1f,0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x0f,0xe0,0x01,0xfc,0x00,
   0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,
   0x00,0x3c,0x00,0x1f,0xc0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xf0,0x00,
   0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,0xfe,0x00,0x00,0x00,0x01,0xff,0x80,0x00,
   0x00,0x00,0xff,0xe0,0x00,0x00,0x00,0x3f,0xf8,0x00,0x00,0x00,0x0f,0xfe,0x00,0x00,
   0x00,0x03,0xff,0x80,0x00,0x00,0x00,0xff,0xe0,0x00,0x00,0x00,0x3f,0xf0,0x00,0x00,
   0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,
   0x01,0xfc,0x00,0x00,0x00,0x00,0xfe,0x00,0x1e,0x00,0x00,0xfe,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,
   0x00,0x3f,0x80,0x00,0x7e,0x00,0x3f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,0x00,
   0x1f,0xc0,0x01,0xfc,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x07,
   0xfc,0x1f,0xf0,0x00,0x03,0xff,0xff,0xe0,0x00,0x01,0xff,0xff,0xc0,0x00,0x00,0xff,
   0xff,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_84[]= // 'T'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xff,0xff,
   0xff,0x00,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x3f,0xff,0xff,0xff,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_85[]= // 'U'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x00,
   0x7c,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,
   0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,
   0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,
   0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x0f,0xc0,0x00,0xfc,0x00,
   0x0f,0xe0,0x01,0xfc,0x00,0x0f,0xf0,0x03,0xf8,0x00,0x07,0xf8,0x07,0xf8,0x00,0x03,
   0xfe,0x1f,0xf0,0x00,0x01,0xff,0xff,0xe0,0x00,0x00,0xff,0xff,0xc0,0x00,0x00,0x7f,
   0xff,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_86[]= // 'V'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x3e,0x00,0x3e,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,0x00,0x3f,0x00,0x00,0x3f,
   0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,
   0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x0f,
   0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,
   0x01,0xf8,0x00,0x07,0xe0,0x01,0xf8,0x00,0x07,0xe0,0x03,0xf0,0x00,0x07,0xe0,0x03,
   0xf0,0x00,0x03,0xe0,0x03,0xf0,0x00,0x03,0xf0,0x03,0xf0,0x00,0x03,0xf0,0x07,0xe0,
   0x00,0x03,0xf0,0x07,0xe0,0x00,0x01,0xf8,0x07,0xe0,0x00,0x01,0xf8,0x07,0xc0,0x00,
   0x01,0xf8,0x0f,0xc0,0x00,0x01,0xf8,0x0f,0xc0,0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,
   0xfc,0x0f,0x80,0x00,0x00,0xfc,0x1f,0x80,0x00,0x00,0x7c,0x1f,0x80,0x00,0x00,0x7e,
   0x1f,0x80,0x00,0x00,0x7e,0x1f,0x00,0x00,0x00,0x7e,0x3f,0x00,0x00,0x00,0x3e,0x3f,
   0x00,0x00,0x00,0x3f,0x3e,0x00,0x00,0x00,0x3f,0x7e,0x00,0x00,0x00,0x1f,0x7e,0x00,
   0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,
   0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,
   0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_87[]= // 'W'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x03,0xe0,
   0x0f,0x80,0xfc,0x03,0xf0,0x1f,0x80,0xfc,0x07,0xf0,0x1f,0x80,0x7c,0x07,0xf0,0x1f,
   0x80,0x7c,0x07,0xf8,0x1f,0x80,0x7e,0x07,0xf8,0x1f,0x80,0x7e,0x07,0xf8,0x1f,0x80,
   0x7e,0x07,0xf8,0x1f,0x80,0x7e,0x0f,0xf8,0x1f,0x00,0x7e,0x0f,0xf8,0x1f,0x00,0x7e,
   0x0f,0xfc,0x3f,0x00,0x7e,0x0f,0xfc,0x3f,0x00,0x3e,0x0f,0xfc,0x3f,0x00,0x3e,0x0f,
   0xfc,0x3f,0x00,0x3e,0x1f,0xfc,0x3f,0x00,0x3f,0x1f,0xfc,0x3f,0x00,0x3f,0x1f,0x7c,
   0x3f,0x00,0x3f,0x1f,0x7e,0x3e,0x00,0x3f,0x1f,0x7e,0x3e,0x00,0x3f,0x1f,0x3e,0x3e,
   0x00,0x1f,0x1f,0x3e,0x7e,0x00,0x1f,0x3f,0x3e,0x7e,0x00,0x1f,0x3f,0x3e,0x7e,0x00,
   0x1f,0x3e,0x3e,0x7e,0x00,0x1f,0xbe,0x3f,0x7e,0x00,0x1f,0xbe,0x3f,0x7c,0x00,0x1f,
   0xbe,0x1f,0x7c,0x00,0x1f,0xbe,0x1f,0x7c,0x00,0x1f,0xfe,0x1f,0x7c,0x00,0x0f,0xfe,
   0x1f,0x7c,0x00,0x0f,0xfc,0x1f,0xfc,0x00,0x0f,0xfc,0x1f,0xfc,0x00,0x0f,0xfc,0x1f,
   0xfc,0x00,0x0f,0xfc,0x0f,0xfc,0x00,0x0f,0xfc,0x0f,0xf8,0x00,0x0f,0xfc,0x0f,0xf8,
   0x00,0x0f,0xfc,0x0f,0xf8,0x00,0x0f,0xf8,0x0f,0xf8,0x00,0x07,0xf8,0x0f,0xf8,0x00,
   0x07,0xf8,0x07,0xf8,0x00,0x07,0xf8,0x07,0xf8,0x00,0x07,0xf8,0x07,0xf8,0x00,0x07,
   0xf8,0x07,0xf0,0x00,0x07,0xf0,0x07,0xf0,0x00,0x07,0xf0,0x07,0xf0,0x00,0x07,0xf0,
   0x07,0xf0,0x00,0x03,0xe0,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_88[]= // 'X'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x00,
   0x7c,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xc0,0x01,0xfc,
   0x00,0x0f,0xe0,0x01,0xfc,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x07,0xf0,0x03,0xf0,0x00,
   0x07,0xf0,0x07,0xf0,0x00,0x03,0xf8,0x07,0xe0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x01,
   0xfc,0x0f,0xc0,0x00,0x01,0xfc,0x1f,0xc0,0x00,0x00,0xfe,0x1f,0x80,0x00,0x00,0x7e,
   0x3f,0x80,0x00,0x00,0x7f,0x3f,0x00,0x00,0x00,0x3f,0x7f,0x00,0x00,0x00,0x3f,0xfe,
   0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,
   0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x1f,0xfc,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x3f,0xfe,0x00,0x00,0x00,0x3f,
   0x7e,0x00,0x00,0x00,0x7f,0x7f,0x00,0x00,0x00,0x7e,0x3f,0x00,0x00,0x00,0xfe,0x3f,
   0x80,0x00,0x00,0xfc,0x1f,0x80,0x00,0x01,0xfc,0x1f,0xc0,0x00,0x01,0xf8,0x0f,0xc0,
   0x00,0x03,0xf8,0x0f,0xe0,0x00,0x03,0xf0,0x07,0xf0,0x00,0x07,0xf0,0x07,0xf0,0x00,
   0x07,0xe0,0x03,0xf8,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x1f,
   0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x3f,0x80,0x00,0xfe,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x1f,0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_89[]= // 'Y'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x00,
   0x1f,0x00,0x3f,0x00,0x00,0x3f,0x80,0x3f,0x00,0x00,0x3f,0x80,0x3f,0x80,0x00,0x3f,
   0x00,0x3f,0x80,0x00,0x7f,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,
   0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xe0,0x01,0xfc,0x00,0x07,0xe0,0x01,0xf8,0x00,0x07,
   0xf0,0x03,0xf8,0x00,0x03,0xf0,0x03,0xf0,0x00,0x03,0xf8,0x07,0xf0,0x00,0x01,0xf8,
   0x07,0xe0,0x00,0x01,0xfc,0x0f,0xe0,0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,0xfe,0x1f,
   0xc0,0x00,0x00,0x7e,0x1f,0x80,0x00,0x00,0x7f,0x3f,0x80,0x00,0x00,0x3f,0x3f,0x00,
   0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,
   0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,
   0x07,0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_90[]= // 'Z'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xff,
   0xff,0x00,0x1f,0xff,0xff,0xff,0x80,0x1f,0xff,0xff,0xff,0x80,0x0f,0xff,0xff,0xff,
   0x80,0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0xfe,0x00,
   0x00,0x00,0x01,0xfe,0x00,0x00,0x00,0x03,0xfc,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,
   0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,
   0x1f,0xe0,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x7f,
   0x80,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x01,0xfe,0x00,
   0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x03,0xfc,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,
   0x1f,0xc0,0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x7f,
   0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,0x01,0xfe,0x00,
   0x00,0x00,0x03,0xfc,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,
   0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,
   0x1f,0xc0,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0x7f,
   0xff,0xff,0xff,0x00,0x7f,0xff,0xff,0xff,0x80,0x7f,0xff,0xff,0xff,0x80,0x3f,0xff,
   0xff,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_91[]= // '['
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0x80,0x00,0x00,
   0x0f,0xff,0xc0,0x00,0x00,0x0f,0xff,0xc0,0x00,0x00,0x0f,0xff,0x80,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xff,
   0x80,0x00,0x00,0x0f,0xff,0xc0,0x00,0x00,0x0f,0xff,0xc0,0x00,0x00,0x0f,0xff,0x80,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_92[]= // '\'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x00,0x00,0x00,0x7e,0x03,0xf0,
   0x1f,0x00,0x7e,0x07,0xf0,0x1f,0x00,0x7e,0x07,0xf0,0x1f,0x00,0x7e,0x07,0xf8,0x3f,
   0x00,0x7e,0x07,0xf8,0x3f,0x00,0x3e,0x07,0xf8,0x3f,0x00,0x3e,0x07,0xf8,0x3f,0x00,
   0x3e,0x07,0xf8,0x3f,0x00,0x3f,0x0f,0xf8,0x3f,0x00,0x3f,0x0f,0xf8,0x3f,0x00,0x3f,
   0x0f,0xfc,0x3f,0x00,0x3f,0x0f,0xfc,0x3e,0x00,0x3f,0x0f,0xfc,0x3e,0x00,0x3f,0x0f,
   0xfc,0x7e,0x00,0x1f,0x1f,0xfc,0x7e,0x00,0x1f,0x1f,0xfc,0x7e,0x00,0xff,0xff,0xff,
   0xff,0x80,0xff,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0xff,0x80,0x1f,0x9f,0x7e,0x7c,
   0x00,0x1f,0x9f,0x3e,0x7c,0x00,0x1f,0xbf,0x3e,0x7c,0x00,0x1f,0xbf,0x3e,0x7c,0x00,
   0x1f,0xbf,0x3e,0xfc,0x00,0x0f,0xbe,0x3f,0xfc,0x00,0x0f,0xbe,0x3f,0xfc,0x00,0x0f,
   0xbe,0x3f,0xfc,0x00,0x0f,0xbe,0x1f,0xfc,0x00,0x0f,0xfe,0x1f,0xfc,0x00,0x0f,0xfe,
   0x1f,0xf8,0x00,0x0f,0xfe,0x1f,0xf8,0x00,0x0f,0xfc,0x1f,0xf8,0x00,0x0f,0xfc,0x1f,
   0xf8,0x00,0x07,0xfc,0x1f,0xf8,0x00,0x07,0xfc,0x1f,0xf8,0x00,0x07,0xfc,0x0f,0xf8,
   0x00,0x07,0xfc,0x0f,0xf8,0x00,0x07,0xfc,0x0f,0xf8,0x00,0x07,0xfc,0x0f,0xf0,0x00,
   0x07,0xf8,0x0f,0xf0,0x00,0x07,0xf8,0x0f,0xf0,0x00,0x07,0xf8,0x0f,0xf0,0x00,0x07,
   0xf8,0x07,0xf0,0x00,0x03,0xf8,0x07,0xf0,0x00,0x03,0xf8,0x07,0xf0,0x00,0x03,0xf0,
   0x07,0xf0,0x00,0x01,0xf0,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_93[]= // ']'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,
   0x3f,0xfc,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x3f,0xfc,
   0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x3f,0xfc,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_94[]= // '^'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x07,0xf0,
   0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,
   0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x3f,0xfe,0x00,0x00,0x00,0x3f,0x3f,0x00,0x00,
   0x00,0x7e,0x3f,0x80,0x00,0x00,0xfe,0x1f,0x80,0x00,0x00,0xfc,0x1f,0xc0,0x00,0x01,
   0xfc,0x0f,0xe0,0x00,0x03,0xf8,0x07,0xe0,0x00,0x03,0xf0,0x07,0xf0,0x00,0x07,0xf0,
   0x03,0xf8,0x00,0x07,0xe0,0x03,0xf8,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x00,
   0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x3f,0x80,0x00,0x7f,0x00,0x3f,0x00,0x00,0x3f,
   0x00,0x3e,0x00,0x00,0x3e,0x00,0x1e,0x00,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_95[]= // '_'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xff,0xff,0xff,0x80,0xff,0xff,0xff,
   0xff,0x80,0xff,0xff,0xff,0xff,0x80,0xff,0xff,0xff,0xff,0x80,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_96[]= // '`'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0xc0,
   0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0x7c,0x00,0x00,0x00,0x00,0x3c,0x00,0x00,0x00,0x00,0x3c,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_97[]= // 'a'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x7f,
   0xff,0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xfc,0x1f,
   0xe0,0x00,0x07,0xf0,0x0f,0xe0,0x00,0x07,0xe0,0x07,0xf0,0x00,0x07,0xe0,0x07,0xf0,
   0x00,0x0f,0xc0,0x03,0xf0,0x00,0x0f,0xc0,0x03,0xf0,0x00,0x07,0xc0,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x1f,0xf0,0x00,0x00,
   0x01,0xff,0xf0,0x00,0x00,0x3f,0xff,0xf0,0x00,0x00,0xff,0xff,0xf0,0x00,0x01,0xff,
   0xff,0xf0,0x00,0x07,0xff,0x83,0xf0,0x00,0x07,0xf8,0x03,0xf0,0x00,0x0f,0xe0,0x03,
   0xf0,0x00,0x0f,0xc0,0x03,0xf0,0x00,0x1f,0xc0,0x03,0xf0,0x00,0x1f,0x80,0x03,0xf0,
   0x00,0x1f,0x80,0x03,0xf0,0x00,0x1f,0x80,0x03,0xf0,0x00,0x1f,0x80,0x03,0xf0,0x00,
   0x1f,0xc0,0x07,0xf0,0x00,0x1f,0xc0,0x0f,0xf0,0x00,0x0f,0xe0,0x3f,0xf8,0x00,0x0f,
   0xf0,0xff,0xff,0x00,0x07,0xff,0xff,0xff,0x00,0x03,0xff,0xfd,0xff,0x00,0x01,0xff,
   0xf8,0xff,0x00,0x00,0x7f,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_98[]= // 'b'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x87,0xfc,0x00,0x00,0x1f,0x9f,
   0xff,0x00,0x00,0x1f,0xbf,0xff,0x80,0x00,0x1f,0xff,0xff,0xc0,0x00,0x1f,0xfe,0x1f,
   0xe0,0x00,0x1f,0xfc,0x0f,0xf0,0x00,0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xf0,0x03,0xf8,
   0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,
   0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0xc0,0x00,0xfc,
   0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,
   0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xfc,0x0f,0xf0,0x00,0x1f,
   0xfe,0x1f,0xe0,0x00,0x1f,0xff,0xff,0xc0,0x00,0x1f,0xbf,0xff,0x80,0x00,0x0f,0x1f,
   0xff,0x00,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_99[]= // 'c'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x3f,
   0xff,0x80,0x00,0x00,0x7f,0xff,0xc0,0x00,0x00,0xff,0xff,0xe0,0x00,0x01,0xff,0x1f,
   0xf0,0x00,0x03,0xfc,0x07,0xf8,0x00,0x03,0xf8,0x03,0xf8,0x00,0x07,0xf0,0x01,0xfc,
   0x00,0x07,0xe0,0x01,0xfc,0x00,0x0f,0xe0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,
   0x0f,0xc0,0x00,0xf8,0x00,0x1f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x3c,0x00,0x1f,0xc0,0x00,0x7e,0x00,0x0f,0xc0,0x00,0x7e,
   0x00,0x0f,0xc0,0x00,0x7e,0x00,0x0f,0xe0,0x00,0xfe,0x00,0x0f,0xe0,0x00,0xfe,0x00,
   0x07,0xf0,0x00,0xfc,0x00,0x07,0xf8,0x01,0xfc,0x00,0x03,0xfc,0x03,0xf8,0x00,0x01,
   0xff,0x0f,0xf8,0x00,0x00,0xff,0xff,0xf0,0x00,0x00,0x7f,0xff,0xe0,0x00,0x00,0x3f,
   0xff,0x80,0x00,0x00,0x07,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_100[]= // 'd'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x7c,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,
   0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,
   0x00,0x00,0x7e,0x00,0x00,0x00,0x00,0x7e,0x00,0x00,0x0f,0xf8,0x7e,0x00,0x00,0x3f,
   0xfe,0x7e,0x00,0x00,0x7f,0xff,0x7e,0x00,0x00,0xff,0xff,0xfe,0x00,0x01,0xfe,0x1f,
   0xfe,0x00,0x03,0xf8,0x0f,0xfe,0x00,0x07,0xf0,0x03,0xfe,0x00,0x07,0xf0,0x03,0xfe,
   0x00,0x0f,0xe0,0x01,0xfe,0x00,0x0f,0xe0,0x01,0xfe,0x00,0x0f,0xc0,0x00,0xfe,0x00,
   0x0f,0xc0,0x00,0xfe,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x0f,0xc0,0x00,0xfe,
   0x00,0x0f,0xc0,0x00,0xfe,0x00,0x0f,0xe0,0x01,0xfe,0x00,0x0f,0xe0,0x01,0xfe,0x00,
   0x07,0xf0,0x03,0xfe,0x00,0x07,0xf0,0x03,0xfe,0x00,0x03,0xf8,0x0f,0xfe,0x00,0x01,
   0xfe,0x1f,0xfe,0x00,0x00,0xff,0xff,0xfe,0x00,0x00,0x7f,0xff,0x7e,0x00,0x00,0x3f,
   0xfe,0x3c,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_101[]= // 'e'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x3f,
   0xfe,0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xfe,0x3f,
   0xe0,0x00,0x03,0xf8,0x0f,0xf0,0x00,0x07,0xf0,0x07,0xf0,0x00,0x07,0xe0,0x03,0xf8,
   0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,
   0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0xff,0xff,0xfe,0x00,0x1f,0xff,0xff,0xfe,0x00,0x1f,0xff,
   0xff,0xfe,0x00,0x1f,0xff,0xff,0xfe,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0xfc,
   0x00,0x1f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x0f,0xe0,0x01,0xfc,0x00,
   0x07,0xe0,0x03,0xfc,0x00,0x07,0xf0,0x07,0xf8,0x00,0x03,0xf8,0x0f,0xf0,0x00,0x03,
   0xfe,0x3f,0xf0,0x00,0x01,0xff,0xff,0xe0,0x00,0x00,0xff,0xff,0xc0,0x00,0x00,0x3f,
   0xff,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_102[]= // 'f'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,
   0x80,0x00,0x00,0x01,0xff,0xc0,0x00,0x00,0x03,0xff,0xc0,0x00,0x00,0x07,0xff,0x80,
   0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,
   0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0xff,
   0xff,0xc0,0x00,0x00,0xff,0xff,0xc0,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0x07,0xe0,
   0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,
   0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,
   0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,
   0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,
   0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,
   0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,
   0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,
   0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,
   0xe0,0x00,0x00,0x00,0x03,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_103[]= // 'g'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xf8,0xf8,0x00,0x00,0x7f,
   0xfe,0xfc,0x00,0x00,0xff,0xff,0xfc,0x00,0x01,0xff,0xff,0xfc,0x00,0x03,0xfc,0x3f,
   0xfc,0x00,0x07,0xf8,0x1f,0xfc,0x00,0x0f,0xf0,0x0f,0xfc,0x00,0x0f,0xe0,0x07,0xfc,
   0x00,0x0f,0xc0,0x03,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,
   0x1f,0x80,0x01,0xfc,0x00,0x3f,0x80,0x01,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,
   0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,
   0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,
   0xfc,0x00,0x3f,0x80,0x01,0xfc,0x00,0x3f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xfc,
   0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,0x0f,0xe0,0x07,0xfc,0x00,
   0x0f,0xe0,0x0f,0xfc,0x00,0x07,0xf8,0x1f,0xfc,0x00,0x03,0xfc,0x3f,0xfc,0x00,0x01,
   0xff,0xff,0xfc,0x00,0x00,0xff,0xff,0xfc,0x00,0x00,0x7f,0xfc,0xfc,0x00,0x1f,0x9f,
   0xf0,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xe0,0x03,
   0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x0f,0xfc,0x1f,0xf0,0x00,0x07,0xff,0xff,0xe0,
   0x00,0x01,0xff,0xff,0xc0,0x00,0x00,0xff,0xff,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,
};
const unsigned char ascii_num4_104[]= // 'h'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,
   0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,
   0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x83,0xfe,0x00,0x00,0x1f,0x9f,
   0xff,0xc0,0x00,0x1f,0xbf,0xff,0xe0,0x00,0x1f,0xff,0xff,0xf0,0x00,0x1f,0xff,0x0f,
   0xf8,0x00,0x1f,0xfc,0x03,0xf8,0x00,0x1f,0xf8,0x01,0xfc,0x00,0x1f,0xe0,0x01,0xfc,
   0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,
   0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,
   0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,
   0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,
   0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,
   0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x0f,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_105[]= // 'i'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,
   0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_106[]= // 'j'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,
   0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0xff,0xe0,0x00,
   0x00,0x01,0xff,0xc0,0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x00,0xfe,0x00,0x00,0x00,
};
const unsigned char ascii_num4_107[]= // 'k'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x78,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,
   0x01,0xfc,0x00,0x0f,0xc0,0x03,0xfc,0x00,0x0f,0xc0,0x07,0xf8,0x00,0x0f,0xc0,0x0f,
   0xf0,0x00,0x0f,0xc0,0x1f,0xe0,0x00,0x0f,0xc0,0x3f,0xc0,0x00,0x0f,0xc0,0x7f,0x80,
   0x00,0x0f,0xc0,0xff,0x00,0x00,0x0f,0xc1,0xfe,0x00,0x00,0x0f,0xc3,0xfc,0x00,0x00,
   0x0f,0xc7,0xf8,0x00,0x00,0x0f,0xcf,0xf0,0x00,0x00,0x0f,0xdf,0xe0,0x00,0x00,0x0f,
   0xff,0xf0,0x00,0x00,0x0f,0xff,0xf8,0x00,0x00,0x0f,0xff,0xf8,0x00,0x00,0x0f,0xfd,
   0xfc,0x00,0x00,0x0f,0xf8,0xfe,0x00,0x00,0x0f,0xf0,0xfe,0x00,0x00,0x0f,0xe0,0x7f,
   0x00,0x00,0x0f,0xc0,0x7f,0x80,0x00,0x0f,0xc0,0x3f,0x80,0x00,0x0f,0xc0,0x1f,0xc0,
   0x00,0x0f,0xc0,0x1f,0xe0,0x00,0x0f,0xc0,0x0f,0xe0,0x00,0x0f,0xc0,0x07,0xf0,0x00,
   0x0f,0xc0,0x07,0xf8,0x00,0x0f,0xc0,0x03,0xf8,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x0f,
   0xc0,0x01,0xfe,0x00,0x0f,0xc0,0x00,0xfe,0x00,0x0f,0xc0,0x00,0x7e,0x00,0x0f,0xc0,
   0x00,0x7e,0x00,0x07,0x80,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_108[]= // 'l'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xe0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_109[]= // 'm'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x7f,0x0f,0xf0,0x00,0x1f,0xff,
   0xdf,0xf8,0x00,0x1f,0xff,0xff,0xfc,0x00,0x1f,0xff,0xff,0xfc,0x00,0x1f,0xef,0xfe,
   0xfe,0x00,0x1f,0xc7,0xf8,0xfe,0x00,0x1f,0xc3,0xf8,0x7e,0x00,0x1f,0x83,0xf0,0x7e,
   0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,
   0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,
   0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,
   0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,
   0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,
   0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,
   0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,
   0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,0xf0,0x7e,0x00,0x1f,0x83,
   0xf0,0x7e,0x00,0x0f,0x01,0xe0,0x3c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_110[]= // 'n'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x83,0xfe,0x00,0x00,0x0f,0xdf,
   0xff,0x80,0x00,0x0f,0xff,0xff,0xe0,0x00,0x0f,0xff,0xff,0xe0,0x00,0x0f,0xff,0x0f,
   0xf0,0x00,0x0f,0xfc,0x07,0xf0,0x00,0x0f,0xf8,0x03,0xf8,0x00,0x0f,0xf0,0x01,0xf8,
   0x00,0x0f,0xe0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,
   0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,
   0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,
   0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,
   0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,
   0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,
   0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,
   0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,
   0x01,0xf8,0x00,0x07,0x80,0x01,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_111[]= // 'o'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x7f,
   0xff,0x00,0x00,0x00,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xfe,0x3f,
   0xe0,0x00,0x07,0xf8,0x0f,0xf0,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x0f,0xe0,0x03,0xf8,
   0x00,0x1f,0xc0,0x01,0xf8,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,
   0x1f,0x80,0x00,0xfc,0x00,0x3f,0x80,0x00,0xfe,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,
   0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x00,0x00,
   0x7e,0x00,0x3f,0x00,0x00,0x7e,0x00,0x3f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,0xfc,
   0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xf8,0x00,
   0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xf0,0x07,0xf8,0x00,0x07,0xf8,0x0f,0xf0,0x00,0x03,
   0xfe,0x3f,0xe0,0x00,0x01,0xff,0xff,0xc0,0x00,0x00,0xff,0xff,0x80,0x00,0x00,0x7f,
   0xff,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_112[]= // 'p'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x0f,0xfc,0x00,0x00,0x1f,0xbf,
   0xff,0x00,0x00,0x1f,0xff,0xff,0x80,0x00,0x1f,0xff,0xff,0xc0,0x00,0x1f,0xfe,0x1f,
   0xe0,0x00,0x1f,0xfc,0x0f,0xf0,0x00,0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xf0,0x03,0xf8,
   0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,
   0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,
   0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,
   0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0xc0,0x00,0xfc,
   0x00,0x1f,0xc0,0x00,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xe0,0x01,0xfc,0x00,
   0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xf0,0x03,0xf8,0x00,0x1f,0xfc,0x0f,0xf0,0x00,0x1f,
   0xfe,0x1f,0xe0,0x00,0x1f,0xff,0xff,0xc0,0x00,0x1f,0xbf,0xff,0x80,0x00,0x1f,0x9f,
   0xff,0x00,0x00,0x1f,0x87,0xfc,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,
   0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,
   0x00,0x1f,0x80,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x0f,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_113[]= // 'q'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xf8,0x78,0x00,0x00,0x7f,
   0xfe,0xfc,0x00,0x00,0xff,0xff,0xfc,0x00,0x01,0xff,0xff,0xfc,0x00,0x03,0xfc,0x3f,
   0xfc,0x00,0x07,0xf0,0x1f,0xfc,0x00,0x0f,0xe0,0x07,0xfc,0x00,0x0f,0xe0,0x07,0xfc,
   0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0x80,0x01,0xfc,0x00,
   0x1f,0x80,0x01,0xfc,0x00,0x3f,0x80,0x01,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,
   0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,
   0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x00,0x00,
   0xfc,0x00,0x3f,0x00,0x00,0xfc,0x00,0x3f,0x80,0x01,0xfc,0x00,0x1f,0x80,0x01,0xfc,
   0x00,0x1f,0x80,0x01,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,0x1f,0xc0,0x03,0xfc,0x00,
   0x0f,0xe0,0x07,0xfc,0x00,0x0f,0xe0,0x07,0xfc,0x00,0x07,0xf0,0x1f,0xfc,0x00,0x03,
   0xfc,0x3f,0xfc,0x00,0x01,0xff,0xff,0xfc,0x00,0x00,0xff,0xfe,0xfc,0x00,0x00,0x7f,
   0xfc,0xfc,0x00,0x00,0x1f,0xf0,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x78,0x00,
};
const unsigned char ascii_num4_114[]= // 'r'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x7f,0x00,0x00,0x00,0xfd,
   0xff,0x00,0x00,0x00,0xff,0xff,0x00,0x00,0x00,0xff,0xff,0x00,0x00,0x00,0xff,0xe0,
   0x00,0x00,0x00,0xff,0x80,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0xfe,0x00,0x00,
   0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,
   0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,
   0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,
   0x00,0x00,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_115[]= // 's'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0xff,
   0xff,0x80,0x00,0x01,0xff,0xff,0xc0,0x00,0x03,0xff,0xff,0xe0,0x00,0x07,0xfc,0x1f,
   0xf0,0x00,0x0f,0xf0,0x07,0xf0,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x0f,0xc0,0x03,0xf8,
   0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xc0,0x01,0xf8,0x00,0x0f,0xe0,0x01,0xf8,0x00,
   0x0f,0xf0,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xfe,0x00,0x00,0x00,0x03,
   0xff,0xe0,0x00,0x00,0x00,0xff,0xfc,0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x0f,
   0xff,0xc0,0x00,0x00,0x00,0xff,0xf0,0x00,0x00,0x00,0x1f,0xf0,0x00,0x00,0x00,0x07,
   0xf8,0x00,0x00,0x00,0x03,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x0f,0x80,0x00,0xfc,
   0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,0x1f,0x80,0x00,0xfc,0x00,
   0x1f,0xc0,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x0f,0xe0,0x07,0xf8,0x00,0x0f,
   0xfc,0x1f,0xf8,0x00,0x07,0xff,0xff,0xf0,0x00,0x03,0xff,0xff,0xe0,0x00,0x00,0xff,
   0xff,0x80,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_116[]= // 't'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x80,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x01,0xff,0xff,0x00,0x00,0x01,0xff,
   0xff,0x80,0x00,0x01,0xff,0xff,0x80,0x00,0x01,0xff,0xff,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xe0,0x00,0x00,0x00,0x0f,0xff,0x00,0x00,0x00,0x07,0xff,0x80,0x00,0x00,0x03,
   0xff,0x80,0x00,0x00,0x01,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_117[]= // 'u'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x07,0x80,0x00,0x78,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,
   0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,
   0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,
   0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,
   0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,
   0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,
   0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,
   0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x00,0xfc,
   0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x0f,0xc0,0x03,0xfc,0x00,
   0x0f,0xe0,0x03,0xfc,0x00,0x0f,0xe0,0x07,0xfc,0x00,0x07,0xf0,0x1f,0xfc,0x00,0x07,
   0xfc,0x7f,0xfc,0x00,0x03,0xff,0xff,0xfc,0x00,0x01,0xff,0xfe,0xfc,0x00,0x00,0xff,
   0xfc,0xfc,0x00,0x00,0x1f,0xf0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_118[]= // 'v'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x7c,0x00,0x3f,0x00,
   0x00,0x7e,0x00,0x3f,0x80,0x00,0x7e,0x00,0x1f,0x80,0x00,0xfe,0x00,0x1f,0x80,0x00,
   0xfc,0x00,0x1f,0xc0,0x00,0xfc,0x00,0x0f,0xc0,0x01,0xfc,0x00,0x0f,0xe0,0x01,0xf8,
   0x00,0x0f,0xe0,0x03,0xf8,0x00,0x07,0xe0,0x03,0xf8,0x00,0x07,0xf0,0x03,0xf0,0x00,
   0x03,0xf0,0x07,0xf0,0x00,0x03,0xf0,0x07,0xf0,0x00,0x03,0xf8,0x07,0xe0,0x00,0x01,
   0xf8,0x0f,0xe0,0x00,0x01,0xf8,0x0f,0xc0,0x00,0x01,0xfc,0x0f,0xc0,0x00,0x00,0xfc,
   0x1f,0xc0,0x00,0x00,0xfc,0x1f,0x80,0x00,0x00,0xfe,0x1f,0x80,0x00,0x00,0x7e,0x3f,
   0x80,0x00,0x00,0x7e,0x3f,0x00,0x00,0x00,0x7f,0x3f,0x00,0x00,0x00,0x3f,0x7f,0x00,
   0x00,0x00,0x3f,0x7e,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,
   0x00,0x1f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
   0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x07,
   0xf0,0x00,0x00,0x00,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_119[]= // 'w'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7c,0x03,0xe0,0x1f,0x00,0x7e,0x07,
   0xf0,0x1f,0x80,0x7e,0x07,0xf0,0x1f,0x80,0x7e,0x07,0xf8,0x1f,0x00,0x7e,0x07,0xf8,
   0x3f,0x00,0x7e,0x07,0xf8,0x3f,0x00,0x7e,0x0f,0xf8,0x3f,0x00,0x3f,0x0f,0xfc,0x3f,
   0x00,0x3f,0x0f,0xfc,0x3f,0x00,0x3f,0x0f,0xfc,0x3f,0x00,0x3f,0x1f,0xfc,0x3e,0x00,
   0x3f,0x1f,0xfc,0x7e,0x00,0x3f,0x1f,0xfe,0x7e,0x00,0x1f,0x1f,0xfe,0x7e,0x00,0x1f,
   0x9f,0x7e,0x7e,0x00,0x1f,0xbf,0x7e,0x7e,0x00,0x1f,0xbf,0x3e,0x7c,0x00,0x1f,0xbf,
   0x3f,0xfc,0x00,0x1f,0xbf,0x3f,0xfc,0x00,0x0f,0xfe,0x3f,0xfc,0x00,0x0f,0xfe,0x3f,
   0xfc,0x00,0x0f,0xfe,0x1f,0xfc,0x00,0x0f,0xfe,0x1f,0xfc,0x00,0x0f,0xfe,0x1f,0xf8,
   0x00,0x0f,0xfc,0x1f,0xf8,0x00,0x0f,0xfc,0x1f,0xf8,0x00,0x07,0xfc,0x0f,0xf8,0x00,
   0x07,0xfc,0x0f,0xf8,0x00,0x07,0xf8,0x0f,0xf8,0x00,0x07,0xf8,0x0f,0xf0,0x00,0x07,
   0xf8,0x07,0xf0,0x00,0x07,0xf8,0x07,0xf0,0x00,0x03,0xf8,0x07,0xf0,0x00,0x03,0xf0,
   0x07,0xf0,0x00,0x03,0xf0,0x03,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_120[]= // 'x'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x00,0xf8,0x00,0x0f,0xc0,
   0x01,0xf8,0x00,0x0f,0xe0,0x01,0xf8,0x00,0x0f,0xe0,0x03,0xf8,0x00,0x07,0xf0,0x07,
   0xf8,0x00,0x07,0xf8,0x07,0xf0,0x00,0x03,0xf8,0x0f,0xe0,0x00,0x01,0xfc,0x1f,0xe0,
   0x00,0x00,0xfe,0x1f,0xc0,0x00,0x00,0xfe,0x3f,0x80,0x00,0x00,0x7f,0x3f,0x00,0x00,
   0x00,0x3f,0xff,0x00,0x00,0x00,0x3f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,
   0x0f,0xfc,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf0,0x00,0x00,0x00,0x0f,
   0xf8,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x3f,0xfe,
   0x00,0x00,0x00,0x3f,0xff,0x00,0x00,0x00,0x7f,0x7f,0x00,0x00,0x00,0xfe,0x3f,0x80,
   0x00,0x00,0xfe,0x1f,0xc0,0x00,0x01,0xfc,0x1f,0xc0,0x00,0x01,0xfc,0x0f,0xe0,0x00,
   0x03,0xf8,0x0f,0xe0,0x00,0x07,0xf0,0x07,0xf0,0x00,0x07,0xf0,0x03,0xf8,0x00,0x0f,
   0xe0,0x03,0xf8,0x00,0x1f,0xe0,0x01,0xfc,0x00,0x1f,0xc0,0x01,0xfc,0x00,0x1f,0x80,
   0x00,0xfc,0x00,0x0f,0x80,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_121[]= // 'y'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1f,0x00,0x00,0x7e,0x00,0x1f,0x80,
   0x00,0x7e,0x00,0x1f,0x80,0x00,0x7e,0x00,0x1f,0xc0,0x00,0xfe,0x00,0x1f,0xc0,0x00,
   0xfc,0x00,0x0f,0xc0,0x00,0xfc,0x00,0x0f,0xe0,0x01,0xfc,0x00,0x0f,0xe0,0x01,0xf8,
   0x00,0x07,0xe0,0x03,0xf8,0x00,0x07,0xf0,0x03,0xf8,0x00,0x03,0xf0,0x03,0xf0,0x00,
   0x03,0xf0,0x07,0xf0,0x00,0x03,0xf8,0x07,0xe0,0x00,0x01,0xf8,0x07,0xe0,0x00,0x01,
   0xf8,0x0f,0xe0,0x00,0x01,0xfc,0x0f,0xc0,0x00,0x00,0xfc,0x0f,0xc0,0x00,0x00,0xfe,
   0x1f,0x80,0x00,0x00,0x7e,0x1f,0x80,0x00,0x00,0x7e,0x3f,0x80,0x00,0x00,0x7f,0x3f,
   0x00,0x00,0x00,0x3f,0x3f,0x00,0x00,0x00,0x3f,0x7e,0x00,0x00,0x00,0x3f,0xfe,0x00,
   0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,0x00,
   0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,
   0x07,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x07,0xe0,0x00,0x00,0x00,0x07,
   0xe0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x1f,0xc0,
   0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,0x00,0x0f,0xff,0x00,0x00,
   0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xfc,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,
};
const unsigned char ascii_num4_122[]= // 'z'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0xff,0xff,0xf8,0x00,0x1f,0xff,
   0xff,0xfc,0x00,0x1f,0xff,0xff,0xfc,0x00,0x0f,0xff,0xff,0xfc,0x00,0x00,0x00,0x03,
   0xfc,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,0xf8,0x00,0x00,0x00,0x0f,0xf0,
   0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x7f,0xc0,0x00,
   0x00,0x00,0xff,0x80,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x01,0xfe,0x00,0x00,0x00,
   0x03,0xfc,0x00,0x00,0x00,0x07,0xfc,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,0x0f,
   0xf0,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x7f,0xc0,
   0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x01,0xfe,0x00,0x00,
   0x00,0x03,0xfe,0x00,0x00,0x00,0x03,0xfc,0x00,0x00,0x00,0x07,0xf8,0x00,0x00,0x00,
   0x0f,0xf0,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,0x1f,0xe0,0x00,0x00,0x00,0x1f,
   0xff,0xff,0xfe,0x00,0x1f,0xff,0xff,0xfe,0x00,0x1f,0xff,0xff,0xfe,0x00,0x0f,0xff,
   0xff,0xfe,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_123[]= // '{'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xc0,0x00,0x00,
   0x03,0xff,0xc0,0x00,0x00,0x03,0xff,0xc0,0x00,0x00,0x07,0xff,0xc0,0x00,0x00,0x0f,
   0xe0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x1f,0x80,0x00,0x00,0x00,0x3f,0x80,0x00,
   0x00,0x00,0x7f,0x00,0x00,0x00,0x1f,0xfe,0x00,0x00,0x00,0x1f,0xf8,0x00,0x00,0x00,
   0x1f,0xfe,0x00,0x00,0x00,0x1f,0xff,0x00,0x00,0x00,0x00,0x7f,0x80,0x00,0x00,0x00,
   0x3f,0x80,0x00,0x00,0x00,0x1f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,
   0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,
   0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,
   0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,
   0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,
   0xc0,0x00,0x00,0x00,0x0f,0xc0,0x00,0x00,0x00,0x0f,0xe0,0x00,0x00,0x00,0x07,0xff,
   0xc0,0x00,0x00,0x07,0xff,0xc0,0x00,0x00,0x03,0xff,0xc0,0x00,0x00,0x01,0xff,0xc0,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_124[]= // '|'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xe0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,
   0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,
   0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,
   0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,
   0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,0x00,0x00,0x03,0xf0,0x00,
   0x00,0x00,0x01,0xe0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_125[]= // '}'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xff,0x80,0x00,0x00,0x01,
   0xff,0xe0,0x00,0x00,0x01,0xff,0xe0,0x00,0x00,0x01,0xff,0xf0,0x00,0x00,0x00,0x03,
   0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,
   0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,
   0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,
   0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,
   0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,
   0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0xfc,0x00,
   0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x0f,0xfc,0x00,
   0x00,0x00,0x3f,0xfc,0x00,0x00,0x00,0x7f,0xfc,0x00,0x00,0x00,0xff,0x00,0x00,0x00,
   0x00,0xfc,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,
   0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,
   0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,
   0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,
   0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,
   0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,0xf8,0x00,0x00,0x00,0x01,
   0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x00,0x03,0xf8,0x00,0x00,0x01,0xff,0xf0,
   0x00,0x00,0x01,0xff,0xf0,0x00,0x00,0x01,0xff,0xe0,0x00,0x00,0x01,0xff,0x80,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num4_126[]= // '~'
{
   0x21,0x40,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xfc,0x00,0x00,0x00,0x07,0xff,0x00,0x00,
   0x00,0x0f,0xff,0x80,0x1e,0x00,0x1f,0xff,0xc0,0x3e,0x00,0x3f,0x9f,0xf0,0x7e,0x00,
   0x3f,0x07,0xf8,0xfe,0x00,0x3e,0x03,0xff,0xfc,0x00,0x00,0x01,0xff,0xf8,0x00,0x00,
   0x00,0xff,0xf0,0x00,0x00,0x00,0x3f,0xc0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
//-------------------------ascii table----------------------
ALIGNED(4) const unsigned char * const ascii_num4_table[]=
{
   ascii_num4_32,// ' '
   ascii_num4_33,// '!'
   ascii_num4_35,// '#'
   ascii_num4_36,// '$'
   ascii_num4_37,// '%'
   ascii_num4_38,// '&'
   ascii_num4_39,// '''
   ascii_num4_40,// '('
   ascii_num4_41,// ')'
   ascii_num4_42,// '*'
   ascii_num4_43,// '+'
   ascii_num4_44,// ','
   ascii_num4_45,// '-'
   ascii_num4_46,// '.'
   ascii_num4_47,// '/'
   ascii_num4_48,// '0'
   ascii_num4_49,// '1'
   ascii_num4_50,// '2'
   ascii_num4_51,// '3'
   ascii_num4_52,// '4'
   ascii_num4_53,// '5'
   ascii_num4_54,// '6'
   ascii_num4_55,// '7'
   ascii_num4_56,// '8'
   ascii_num4_57,// '9'
   ascii_num4_58,// ':'
   ascii_num4_59,// ';'
   ascii_num4_60,// '<'
   ascii_num4_61,// '='
   ascii_num4_62,// '>'
   ascii_num4_63,// '?'
   ascii_num4_64,// '@'
   ascii_num4_65,// 'A'
   ascii_num4_66,// 'B'
   ascii_num4_67,// 'C'
   ascii_num4_68,// 'D'
   ascii_num4_69,// 'E'
   ascii_num4_70,// 'F'
   ascii_num4_71,// 'G'
   ascii_num4_72,// 'H'
   ascii_num4_73,// 'I'
   ascii_num4_74,// 'J'
   ascii_num4_75,// 'K'
   ascii_num4_76,// 'L'
   ascii_num4_77,// 'M'
   ascii_num4_78,// 'N'
   ascii_num4_79,// 'O'
   ascii_num4_80,// 'P'
   ascii_num4_81,// 'Q'
   ascii_num4_82,// 'R'
   ascii_num4_83,// 'S'
   ascii_num4_84,// 'T'
   ascii_num4_85,// 'U'
   ascii_num4_86,// 'V'
   ascii_num4_87,// 'W'
   ascii_num4_88,// 'X'
   ascii_num4_89,// 'Y'
   ascii_num4_90,// 'Z'
   ascii_num4_91,// '['
   ascii_num4_92,// '\'
   ascii_num4_93,// ']'
   ascii_num4_94,// '^'
   ascii_num4_95,// '_'
   ascii_num4_96,// '`'
   ascii_num4_97,// 'a'
   ascii_num4_98,// 'b'
   ascii_num4_99,// 'c'
   ascii_num4_100,// 'd'
   ascii_num4_101,// 'e'
   ascii_num4_102,// 'f'
   ascii_num4_103,// 'g'
   ascii_num4_104,// 'h'
   ascii_num4_105,// 'i'
   ascii_num4_106,// 'j'
   ascii_num4_107,// 'k'
   ascii_num4_108,// 'l'
   ascii_num4_109,// 'm'
   ascii_num4_110,// 'n'
   ascii_num4_111,// 'o'
   ascii_num4_112,// 'p'
   ascii_num4_113,// 'q'
   ascii_num4_114,// 'r'
   ascii_num4_115,// 's'
   ascii_num4_116,// 't'
   ascii_num4_117,// 'u'
   ascii_num4_118,// 'v'
   ascii_num4_119,// 'w'
   ascii_num4_120,// 'x'
   ascii_num4_121,// 'y'
   ascii_num4_122,// 'z'
   ascii_num4_123,// '{'
   ascii_num4_124,// '|'
   ascii_num4_125,// '}'
   ascii_num4_126,// '~'
};