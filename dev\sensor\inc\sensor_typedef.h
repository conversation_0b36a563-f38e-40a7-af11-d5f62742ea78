/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  SENSOR_TYPEDEF_H
    #define  SENSOR_TYPEDEF_H
	
typedef struct {
	void (*fp_rotate)(u32 rotate);
	void (*fp_hvblank)(u32 h_len,u32 v_len);
	void (*fp_exp_gain_wr)(u32 exp,u32 gain);
} P_Fun_Adapt;

typedef struct {
	u32     sensor_isp_switch_en;
	void	*sensor_isp_switch_addr;
	u32 	sensor_isp_switch_size;
} P_ISP_STRUCT_SWITCH;
typedef struct Sensor_Adpt_S{
	u32 			typ;  //0: raw 1:yuv 2:mipi
	u32 			mclk;
	u32				mclk_src; 	//MCLK_SRC_SYSPLL = 0,MCLK_SRC_USBPLL = 1,
	u32 			pclk_dig_fir_step;
	u32 			pclk_ana_fir_step;
	u32 			pclk_inv_en;
	u32 			csi_tun;
				
	u32 			senPixelw; 
	u32 			senPixelh;
	u32 			senCropW_St;
	u32 			senCropW_Ed;
	u32 			senCropH_St;
	u32 			senCropH_Ed;
	u32 			senCropMode;	
				
	u32 			pixelw; 
	u32 			pixelh;
	u32 			colrarray; 
	u32 			hsyn; 
	u32 			vsyn;

	u32 			sensorCore;
	U32 			sensorIo;
	MIPI_Adapt    	mipi_adapt;
	Rotate_Adapt 	rotate_adapt;
	Hvb_Adapt 		hvb_adapt;
	volatile u32 	isp_all_mod;
	
	BLC_Adapt 		blc_adapt;
	DDC_Adapt 		ddc_adapt;
	AWB_Adapt 		awb_adapt;
	CCM_Adapt 		ccm_adapt;
	AE_Adapt 		ae_adapt;
	RGB_DGAIN_Adapt rgbdgain_adapt;
	YGAMA_Adapt 	ygama_adapt;
	RGB_GAMA_Adapt 	rgbgama_adapt;
	CH_Adapt 		ch_adapt;
	VDE_Adapt 		vde_adapt;
	EE_Adapt 		ee_adapt;
	CCF_Adapt 		cfd_adapt;
	SAJ_Adapt 		saj_adapt;
	MD_Adapt 		md_adapt;
	P_Fun_Adapt 	p_fun_adapt;
	P_ISP_STRUCT_SWITCH isp_switch_adapt;
} Sensor_Adpt_T;
typedef struct Sensor_Ident_S
{
	void	*sensor_struct_addr;
	u32 	sensor_struct_size;
	void 	*sensor_init_tab_adr;
	u32 	sensor_init_tab_size;
	void 	*lsc_tab_adr;
	u32 	lsc_tab_size;
	u8 		sensor_name[32];
	u8 		w_cmd,r_cmd,addr_num,data_num;
	u16		id;
	u16 	id_reg;
	u8		reset_en, reset_valid;
	u8      pwdn_en,  pwdn_valid;     
}Sensor_Ident_T;

typedef struct Sensor_Header_S
{
	u32 header_items_addr;
	u32 header_items_total_size;
	u32 header_item_size;
	u32 align[13];
}Sensor_Header_T;	
typedef struct SENSOR_API_S{
	s32 			sensor_index;	//0xffff: not ident	
	Sensor_Header_T sensorHead;
	Sensor_Ident_T	sensorIdent;
	Sensor_Adpt_T	sensorAdpt;
	u8				*psensor_init_tab;
	u16 			user_lsc_tab[572]; 
	//u8				user_contrast_tab[256];
	u16				user_ygamma_tab[_YGAMA_STEPS_*2];
	u8				user_rgb_gamma_tab[_RGB_GAMA_STEPS_*2];
}SENSOR_API_T;

#define SENSOR_HEADER_SECTION              	SECTION(".sensor_res.header")
#define SENSOR_HEADER_ITEM_SECTION         	SECTION(".sensor_res.header.items")

#define SENSOR_OP_SECTION                  	SECTION(".sensor_res.struct")  
#define SENSOR_INIT_SECTION                	SECTION(".sensor_res.init_tab") static 

#define SENSOR_YGAMMA_TAB_SECTION       	SECTION(".sensor_res.isp_tab")  
#define SENSOR_RGBGAMMA_TAB_SECTION			SECTION(".sensor_res.isp_tab")  
#define SENSOR_LSC_TAB_SECTION				SECTION(".sensor_res.isp_tab")  static
#define SENSOR_AWB_TAB_SECTION				SECTION(".sensor_res.isp_tab")  static

#define SENSOR_YGAMMA_CLASSES    			18
#define SENSOR_RGBGAMMA_CLASSES    			18



extern int _sensor_resource_start_addr;
#define SEN_RES_FLASH_ADDR(x)         		((u32)(x)  + ((u32)&_sensor_resource_start_addr))

#define SENSOR_TAB_END						0xff, 0xff
enum{
	SENSOR_FILTER_NONE = 0,
	SENSOR_FILTER_NOCOLOR,

	SENSOR_FILTER_MAX,
}SENSOR_FILTER_TYPE;
#endif


