/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef TYPEDEF_H
#define TYPEDEF_H

#include <stdint.h>
#include <stdbool.h>

#ifndef __OR1KND__
#define __hx330x_spr
#define UNUSED
#define WEAK
#define ALIGNED(x)
#define SECTION(x)
#else
#define UNUSED					__attribute__((unused))
#define WEAK					__attribute__((weak))
#define ALIGNED(x) 				__attribute__((aligned(x)))
#define SECTION(x)				__attribute__((section(x)))
#endif

#define TRUE                    1
#define FALSE	                0
#define NULL                    ((void *)0)
#define ON   	                1
#define OFF	                    0

#define BIT(x)     	            (1UL<<(x))

#define	SETB(REG,BIT)			((REG) |= (1UL << (BIT)))
#define	CLRB(REG,BIT)			((REG) &= (~(1UL << (BIT))))
#define	XRLB(REG,BIT)			((REG) ^= (1UL << (BIT)))
#define CHKB(REG,BIT)			(REG & (1UL << BIT))
#define BIT_CK_1(REG,BIT)		(REG & (1UL << BIT))
#define BIT_CK_0(REG,BIT)		(!(REG & (1UL << BIT)))
#define bic_orr(reg,clr,set)    (reg) = ((reg) & ~(clr)) | (set)

#define my_abs(a)				((a) >= 0  ? (a) : (-(a)))
#define	my_min(a,b)             ((a) > (b) ? (b) : (a))
#define my_max(a,b)             ((a) > (b) ? (a) : (b))
#define ROUND_UP(n0,n1)	        (((n0) + (n1) - 1) / (n1))
#define VALUE_ALIGN(n0,n1)	    (((n0) + (n1) - 1) & (~((n1)-1)))
#define ARRAY_NUM(array)		(sizeof(array)/sizeof(array[0]))



typedef volatile __hx330x_spr unsigned long __sfr32_t;

typedef unsigned char	u8, U8, uint8, UINT8, INT8U, u8_t, uchar, UCHAR, BYTE;
typedef signed   char	s8, S8, int8, int8_t, INT8, INT8S, s8_t;

typedef unsigned short	u16, U16, uint16, UINT16, INT16U, u16_t, ushort, USHORT, WORD, WCHAR;
typedef signed   short	s16, S16, int16, INT16, INT16S, s16_t, SHORT;

typedef int				INT;
typedef unsigned int 	uint,UINT;
typedef unsigned long	u32, U32, uint32, UINT32, INT32U, u32_t, ulong, ULONG, DWORD;
typedef signed   long	s32, S32, int32, INT32, INT32S, s32_t, LONG;


typedef unsigned long long	u64, U64, uint64, UINT64, INT64U, u64_t, QWORD;
typedef signed long long	s64, S64, int64, INT64, INT64S, s64_t;

typedef bool BOOL;

#define barrier() __asm__ __volatile__("": : :"memory")
#endif
