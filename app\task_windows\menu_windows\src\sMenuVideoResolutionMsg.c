/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuVideoResolutionWin.c"

typedef struct{
	u32 strid;
	u32 value;
}VideoResolutionCtl;

ALIGNED(4) const VideoResolutionCtl videoResolutionTab[] = {
	{R_ID_STR_RES_FHD, 1},
	{R_ID_STR_RES_HD, 2},
	//{R_ID_STR_RES_VGA, 3},
};

ALIGNED(4) u32 videoResolution_config;

/*******************************************************************************
* Function Name  : getVideoResolutionResInfor
* Description    : getVideoResolutionResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getVideoResolutionResInfor(u32 item,u32* image,u32* str)
{
	if(item < ARRAY_NUM(videoResolutionTab))
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = videoResolutionTab[item].strid;
	}else
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = INVALID_RES_ID;	
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionKeyMsgOk
* Description    : videoResolutionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;

	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,VIDEO_RES_SELECT_ID));
		if(item < ARRAY_NUM(videoResolutionTab))
		{
			videoResolution_config = videoResolutionTab[item].strid;
			user_config_set(CONFIG_ID_RESOLUTION, videoResolutionTab[item].strid);
			user_config_cfgSys(CONFIG_ID_RESOLUTION);
			user_config_save();	
		}	
		uiWinDestroy(&handle);			
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionKeyMsgUp
* Description    : videoResolutionKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,VIDEO_RES_SELECT_ID));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionKeyMsgDown
* Description    : videoResolutionKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
		
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,VIDEO_RES_SELECT_ID));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionKeyMsgMenu
* Description    : videoResolutionKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionKeyMsgMode
* Description    : videoResolutionKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionOpenWin
* Description    : videoResolutionOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoResolutionOpenWin\n");
	
	// 使用平均间距函数设置选项高度
	uiItemManageSetHeightAvgGap(winItem(handle,VIDEO_RES_SELECT_ID),Rh(34));
	
	uiItemManageCreateItem(		winItem(handle,VIDEO_RES_SELECT_ID),uiItemCreateMenuOption,getVideoResolutionResInfor,ARRAY_NUM(videoResolutionTab));
	uiItemManageSetCharInfor(	winItem(handle,VIDEO_RES_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,VIDEO_RES_SELECT_ID),R_ID_LINE);
	uiItemManageSetUnselectColor(winItem(handle,VIDEO_RES_SELECT_ID),R_ID_PALETTE_White);

	videoResolution_config = user_config_get(CONFIG_ID_RESOLUTION);
	u32 itemNum = 0;
	while(itemNum < ARRAY_NUM(videoResolutionTab))
	{
		if(videoResolutionTab[itemNum].strid == videoResolution_config)
			break;
		itemNum++;
	}
	if(itemNum >= ARRAY_NUM(videoResolutionTab))
		itemNum = 0;
	uiItemManageSetCurItem(		winItem(handle,VIDEO_RES_SELECT_ID),itemNum);
	uiWinSetResid(winItem(handle,VIDEO_RES_TITLE_ID),R_ID_STR_SET_RESOLUTION);
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionCloseWin
* Description    : videoResolutionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoResolutionCloseWin\n");
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionWinChildClose
* Description    : videoResolutionWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoResolutionWinChildClose\n");
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionTouchWin
* Description    : videoResolutionTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		return 0;
	}
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == VIDEO_RES_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : videoResolutionTouchSlideOff
* Description    : videoResolutionTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoResolutionTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;
	if(parame[0] == TP_DIR_UP)
		uiItemManageNextPage(winItem(handle,VIDEO_RES_SELECT_ID));
	else if(parame[0] == TP_DIR_DOWN)
		uiItemManagePrePage(winItem(handle,VIDEO_RES_SELECT_ID));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	return 0;
}

ALIGNED(4) msgDealInfor videoResolutionMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	videoResolutionOpenWin},
	{SYS_CLOSE_WINDOW,	videoResolutionCloseWin},
	{SYS_CHILE_COLSE,	videoResolutionWinChildClose},
	{SYS_TOUCH_WINDOW,  videoResolutionTouchWin},
	{SYS_TOUCH_SLIDE_OFF,videoResolutionTouchSlideOff},
	{KEY_EVENT_OK,		videoResolutionKeyMsgOk},
	{KEY_EVENT_UP,		videoResolutionKeyMsgUp},
	{KEY_EVENT_DOWN,	videoResolutionKeyMsgDown},
	{KEY_EVENT_MENU,	videoResolutionKeyMsgMenu},
	{KEY_EVENT_MODE,	videoResolutionKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(videoResolutionWindow,videoResolutionMsgDeal,videoResolutionWin) 