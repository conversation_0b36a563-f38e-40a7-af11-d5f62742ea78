/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_ICON_API_H
#define RES_ICON_API_H

typedef struct R_ICON_S
{
    u32  r_id;
    u32  r_addr;
    u16  r_width;
    u16  r_height;
    u32  r_tcolor;
}R_ICON_T;
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_iconInit(INT16U idx,R_ICON_T *res,u32 r_id_max);
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
void res_iconBuffInit(void);
/*******************************************************************************
* Function Name  : res_iconGetSize
* Description    : get icon size
* Input          : int num : icon index
				   INT16U *width : width
				   INT16U *height: height
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_iconGetSizeByIndex(int num,u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : res_iconGetAddr
* Description    : get icon addr
* Input          : int num : icon index                      
* Output         : none                                            
* Return         : int : address
*******************************************************************************/
int res_iconGetAddrByIndex(int num);
/*******************************************************************************
* Function Name  : R_iconGetDataAndSize
* Description    : get icon data and size        
* Input          :  unsigned long int r_id : icon id
                       unsigned short *width : width
                       unsigned short *heigth:height
* Output        : 
* Return         : int  0 : fail
                            :data  
*******************************************************************************/
u32 res_icon_GetAddrAndSize(u32 r_id,u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : R_iconGetTColor
* Description    : free icon t color       
* Input          :  unsigned long int r_id : icon id
* Output        : 
* Return         : int 
                            :color  
*******************************************************************************/
u32 res_icon_GetTColor(u32 r_id);
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
void res_iconBuffTimeUpdate(void);
/*******************************************************************************
* Function Name  : res_iconInit
* Description    : icon initial
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u8* res_iconGetData(u32 id,u32 resaddr,u32 width);
/*******************************************************************************
* Function Name  : res_iconGetPalette
* Description    : get icon palette
* Input          : u8 *buffer : buffer 
* Output         : none                                            
* Return         : int : palette size
*******************************************************************************/
int res_iconGetPalette(INT16U idx, u8* buffer);


#endif
