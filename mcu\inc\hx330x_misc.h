/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_MISC_H
    #define HX330X_MISC_H

/*******************************************************************************
* Function Name  : hx330x_sin
* Description    : get sin value *100
* Input          : s32 angle : angle               
* Output         : 
* Return         : 
*******************************************************************************/
s8 hx330x_sin(s32 angle);
/*******************************************************************************
* Function Name  : hx330x_sin
* Description    : get sin value *100
* Input          : s32 angle : angle               
* Output         : 
* Return         : 
*******************************************************************************/
s8 hx330x_cos(s32 angle);
/*******************************************************************************
* Function Name  : hx330x_abs
* Description    : abs value
* Input          : s32 a             
* Output         : 
* Return         : 
*******************************************************************************/
u32 hx330x_abs(s32 a);
/*******************************************************************************
* Function Name  : hx330x_dif_abs
* Description    : abs value
* Input          : s32 a             
* Output         : 
* Return         : 
*******************************************************************************/
u32 hx330x_dif_abs(u32 a, u32 b);
/*******************************************************************************
* Function Name  : hx330x_min
* Description    : hx330x_min
* Input          : u32 a, u32 b
* Output         : None
* Return         : u32
*******************************************************************************/ 
u32 hx330x_min(u32 a, u32 b);
/*******************************************************************************
* Function Name  : hx330x_max
* Description    : hx330x_max
* Input          : u32 a, u32 b
* Output         : None
* Return         : u32
*******************************************************************************/ 
u32 hx330x_max(u32 a, u32 b);
/*******************************************************************************
* Function Name  : hx330x_clip
* Description    : hx330x_clip
* Input          : u32 value, u32 min, u32 max
* Output         : None
* Return         : u32
*******************************************************************************/ 
u32 hx330x_clip(u32 value, u32 min, u32 max);
/*******************************************************************************
* Function Name  : str_cpy
* Description    : string copy
* Input          : char *des : destination
*                  char *src : source
* Output         : None
* Return         : char * : destination
*******************************************************************************/
char *hx330x_str_cpy(char *des,char *src);
/*******************************************************************************
* Function Name  : str_ncpy
* Description    : string copy on lenght
* Input          : char *des : destination
*                  char *src : source
*                  u32_t n   : copy lenght
* Output         : None
* Return         : char * : destination
*******************************************************************************/
char *hx330x_str_ncpy(char *des,char *src,int n);
/*******************************************************************************
* Function Name  : str_char
* Description    : find char 
* Input          : char *des : destination
*                  char  c   : char to be found
* Output         : None
* Return         : char * : destination
*******************************************************************************/
char *hx330x_str_char(char *src,char c);
/*******************************************************************************
* Function Name  : str_cmp
* Description    : string compare
* Input          : char *des : destination
*                  char *src : source
* Output         : None
* Return         : s32_t : 
*                        0 : equal
*******************************************************************************/
int hx330x_str_cmp(char *str1,char *str2);
/*******************************************************************************
* Function Name  : str_ncmp
* Description    : string compare on lenght
* Input          : char *des : destination
*                  char *src : source
*                  u32_t n   : compare lenght
* Output         : None
* Return         : s32_t : 
*                        0 : equal
*******************************************************************************/
int hx330x_str_ncmp(char *str1,char *str2,int n);
/*******************************************************************************
* Function Name  : str_len
* Description    : get string lenght
* Input          : char *str : source
* Output         : None
* Return         : s32_t : 
*                        string lenght
*******************************************************************************/
int hx330x_str_len(char *str);
/*******************************************************************************
* Function Name  : str_cat
* Description    : cat string
* Input          : char *des : destination
*                  char *src : source
* Output         : None
* Return         :  char *:destination                      
*******************************************************************************/
char *hx330x_str_cat(char *des,char *src);
/*******************************************************************************
* Function Name  : hx330x_str_seek
* Description    : seek for the obj string or char
* Input          : void * src : string
*                  const char *obj
* Output         : none
* Return         : u8 * obj the start address
*******************************************************************************/
u8 *hx330x_str_seek(void * src,const char * obj);

/*******************************************************************************
* Function Name  : strTransform
* Description    : string transform
* Input          : char *str : string
                     u32_t len : length
* Output         : des
* Return         : 0
*******************************************************************************/
void hx330x_strTransform(char *str,u32 len);

/*******************************************************************************
* Function Name  : hx330x_dec_num2str
* Description    : dec value to string
* Input          : u8 src value
*                  u8* dest : string address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dec_num2str(u8 src , u8* dest);
/*******************************************************************************
* Function Name  : hx330x_char2hex
* Description    : char to hex
* Input          : u8 val : char value
* Output         : none
* Return         : hex
*******************************************************************************/
u8 hx330x_char2hex(u8 val);
/*******************************************************************************
* Function Name  : hx330x_str2num
* Description    : char to hex
* Input          : u8 val : char value
* Output         : none
* Return         : hex
*******************************************************************************/
int hx330x_str2num(char *str, u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_char2hex
* Description    : char to hex
* Input          : u8 val : char value
* Output         : none
* Return         : hex
*******************************************************************************/
void hx330x_hex2str(char *str, u32 value, u32 len);
/*******************************************************************************
* Function Name  : hx330x_char2hex
* Description    : char to hex
* Input          : u8 val : char value
* Output         : none
* Return         : hex
*******************************************************************************/
void hx330x_num2str(char *str, u32 value, int cnt);
/*******************************************************************************
* Function Name  : hx330x_char2hex
* Description    : char to hex
* Input          : u8 val : char value
* Output         : none
* Return         : hex
*******************************************************************************/
u32 hx330x_num2str_cnt(char *str, u32 value, int cnt);
/*******************************************************************************
* Function Name  : hx330x_data_check
* Description    : check data in range
* Input          : u32 data,u32 min,u32 max
* Output         : none
* Return         : true: within [min,max]
*******************************************************************************/
bool hx330x_data_check(u32 data,u32 min,u32 max);
/*******************************************************************************
* Function Name  : CountToString
* Description    : change count to string type
* Input          : char *string : destination
*                    u16_t count1 : count 1
                     u16_t count2 : count 2
* Output         : string
* Return         : 0
*******************************************************************************/
void hx330x_CountToString(char *string,u16 count1,u16 count2);

/*******************************************************************************
* Function Name  : stringInster
* Description    : string inster
* Input          : char *des : destination
                      char *src : source
*                    u32_t offset : offset
                     u32_t len : length
* Output         : des
* Return         : 0
*******************************************************************************/
void hx330x_str_noftcpy(char *des,char *src,u32_t offset,u32_t len);

/*******************************************************************************
* Function Name  : hx330x_greatest_divisor
* Description    : cal the greatest divisor between a and b
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
u32 hx330x_greatest_divisor(u32 a, u32 b);

#endif

