/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_ADC_H
   #define HX330X_ADC_H

#define  ADC_CH_PA4      	0
#define  ADC_CH_PA6      	1
#define  ADC_CH_PA10     	2
#define  ADC_CH_PA15     	3
#define  ADC_CH_PD0      	4
#define  ADC_CH_PF8      	5
#define  ADC_CH_PE0      	6
#define  ADC_CH_VDDSD    	7
#define  ADC_CH_VDDHP    	8
#define  ADC_CH_MVOUT    	9
#define  ADC_CH_VPTAT    	10
#define  ADC_CH_BGOP     	11


#define  ADC_EN       		0x10
#define  ADC_TICK     		0x20
#define  ADC_EOC      		0x40
#define  ADC_GO      		0x80




/*******************************************************************************
* Function Name  : hx330x_adcEnable
* Description    : enable adc channel
* Input          : u8 ch : channel
                      u8 en : 0->disable,1-enable,
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_adcEnable(u8 ch,u8 en);
/*******************************************************************************
* Function Name  : hx330x_adcSetBaudrate
* Description    : adc set baudrate
* Input          : u32 baudrate : baudrate,must <2M HZ 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_adcSetBaudrate(u32 baudrate);
/*******************************************************************************
* Function Name  : hx330x_adcConverStart
* Description    : adc start once conversion
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_adcConverStart(void);
/*******************************************************************************
* Function Name  : hx330x_adcRead
* Description    : adc read value
* Input          : 
* Output         : None
* Return         : u16 value
*******************************************************************************/
u16 hx330x_adcRead(void);


















#endif
