/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	FORMAT_TIPS_ID=0,
	FORMAT_SELECT_ID,
	FORMAT_TIPS1_ID,
	FORMAT_TIPS2_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor formatWin[] =
{
#if UI_SHOW_SMALL_PANEL == 0
	createFrameWin(						Rx(40),	<PERSON>y(42), <PERSON>w(240),<PERSON>h(142),R_ID_GREY_W,WIN_ABS_POS),
	createStringIcon(FORMAT_TIPS_ID,	Rx(0),	Ry(10), 	Rw(240),Rh(40),R_ID_STR_FMT_FORMAT,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(FORMAT_TIPS1_ID,	Rx(0),	Ry(50), 	Rw(240),Rh(40),R_ID_STR_FMT_FORMAT1,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(FORMAT_TIPS2_ID,	Rx(0),	Ry(90), 	Rw(240),Rh(40),R_ID_STR_FMT_FORMAT2,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	//createItemManage(FORMAT_SELECT_ID,	Rx(0),	Ry(100),Rw(220),Rh(40),	INVALID_COLOR),
#else
	createFrameWin(						Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_DimGray,WIN_ABS_POS),
	createStringIcon(FORMAT_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_FMT_FORMAT,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createItemManage(FORMAT_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40), INVALID_COLOR),
#endif
	widgetEnd(),
};



