/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	BAT_CHARGE_RECT_ID = 0,
	//BAT_CHARGE_BAR_ID   = 0,
	//BAT_CHARGE_RECT1_ID,
	//BAT_CHARGE_RECT2_ID,
	BAT_CHARGE_STR_ID,
	//BAT_CHARGE_MAX_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor batChargeWin[] =
{
	createFrameWin( 							Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createRect(BAT_CHARGE_RECT_ID,         	 	Rx(92),  Ry(92),  Rw(128), Rh(56), R_ID_PALETTE_Transparent),
	//createRect(BAT_CHARGE_RECT2_ID,         	 Rx(244), Ry(110), Rw(8),   Rh(20),  R_ID_PALETTE_White),
	//createProgressBar(BAT_CHARGE_BAR_ID,         Rx(80),  Ry(90),  Rw(160), Rh(60), R_ID_PALETTE_Black,R_ID_PALETTE_DoderBlue),
	createStringIcon(BAT_CHARGE_STR_ID, 		Rx(92),  Ry(92),  Rw(128), Rh(56),  "",  ALIGNMENT_CENTER, R_ID_PALETTE_White, DEFAULT_FONT),
	widgetEnd(),
};


/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
//static void batChargeBarShow(winHandle handle)
//{
//	uiWinSetProgressRate(winItem(handle,BAT_CHARGE_BAR_ID), batChargeOp.bat_bar_rate);
//}
