{"configurations": [{"name": "HX330x_SDK", "includePath": ["${workspaceFolder}/**"], "defines": ["HX330X_SDK", "USER_UI_WIDTH=320", "USER_UI_HEIGHT=240"], "compilerPath": "E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin/or1k-unknown-elf-gcc.exe", "cStandard": "c99", "cppStandard": "c++11", "intelliSenseMode": "gcc-x64", "compilerArgs": ["-Wall", "-<PERSON><PERSON>", "-mno-delay", "-mhard-div"], "configurationProvider": "ms-vscode.cpptools"}], "version": 4}