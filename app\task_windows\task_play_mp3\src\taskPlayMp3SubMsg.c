/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#if FUN_MP3_PLAY_EN
#include "taskPlayMp3SubWin.c"
static int resSum;
static int startShowNum;
/*******************************************************************************
* Function Name  : playMp3SubPlayLrcShow
* Description    : playMp3SubPlayLrcShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playMp3SubLrcShow(winHandle handle)
{
	int i,end,cnt;
	char* str;
	if(mp3_lrc_showOn() == 0)
	{
/*		if(mp3_title_get())
		{
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_title_get()),		0, 0);
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_orchestra_get()),	1, 0);
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(mp3_Album_get()),		2, 0);

			uiWinSetResSum(winItem(handle,PLAYMP3SUB_LRC_ID),3);
		}
		else*/
		{
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(task_playMp3_op.playfilename),	0, 0);
			uiWinSetResSum(winItem(handle,PLAYMP3SUB_LRC_ID),1);
		}
		return;
	}
	for(i = startShowNum; i<=0; i++)
	{
		if(mp3_lrc_string_get(i))
			break;
	}
	//start 	= i;
	end		= i + resSum;
	cnt		= 0;
	for(;i <= end;i++)
	{
		str = (char *) mp3_lrc_string_get(i);
		if(str == NULL)
			str= " ";
		//else
		//	deg_Printf("str:%x, %s\n",(u32)str, str);
		if(i == 0)
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(str),	cnt++, 1);
		else
			uiWinSetResidByNum(winItem(handle,PLAYMP3SUB_LRC_ID),RAM_ID_MAKE(str),	cnt++, 0);
	}
	uiWinSetResSum(winItem(handle,PLAYMP3SUB_LRC_ID),resSum);
}

/*******************************************************************************
* Function Name  : playMp3SubOpenWin
* Description    : playMp3SubOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3SubOpenWin\n");
	resSum = uiWinGetResSum(winItem(handle,PLAYMP3SUB_LRC_ID));
	startShowNum = 0 - resSum/2;
	playMp3SubLrcShow(handle);
	playMp3SubPlayRateShow(handle);
	playMp3SubPlayCurTimeShow(handle);
	playMp3SubPlayTotalTimeShow(handle);
	playMp3SubPlayStatShow(handle);
	playMp3SubPlayModeShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubCloseWin
* Description    : playMp3SubCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3SubOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubWinChildClose
* Description    : playMp3SubWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3SubWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubKeyMsgOk
* Description    : playMp3SubKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(mp3_dec_sta() >= MP3_DEC_START ) //当前正在播放的MP3文件，可能是PAUSE或者RESUME或者START
		{
			switch(mp3_dac_sta())
			{
				case MP3_DAC_START:
				case MP3_DAC_RESUME: 	mp3_dac_pause(); break;
				//case MP3_DAC_STOP:
				case MP3_DAC_PAUSE:		mp3_dac_resume(); break;
				default: break;
						
			}
		}else
		{
			taskPlayMp3MainStart(SysCtrl.file_index);
		}
		playMp3SubPlayStatShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubKeyMsgUp
* Description    : playMp3SubKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int preIndex;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(SysCtrl.file_index == 0)
			preIndex = task_playMp3_op.openListTotal - 1;
		else
			preIndex = SysCtrl.file_index - 1;
		preIndex = taskPlayMp3DirFindFile(preIndex, SysCtrl.file_index, 0); //try to find pre file
		if(preIndex >= 0)
		{
			task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
			deg_Printf("preIndex:%d, %d\n", preIndex, SysCtrl.file_index);
			taskPlayMp3MainStart(preIndex);
		}else 
		{
			if(mp3_dec_sta() == MP3_DEC_STOP ) 
				taskPlayMp3MainStart(SysCtrl.file_index);
		}
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubKeyMsgDown
* Description    : playMp3SubKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int nextIndex;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		nextIndex = taskPlayMp3DirFindFile(SysCtrl.file_index + 1, SysCtrl.file_index, 1); //try to find pre file
		if(nextIndex >= 0)
		{
			task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
			deg_Printf("nextIndex:%d, %d\n", nextIndex, SysCtrl.file_index);
			taskPlayMp3MainStart(nextIndex);
		}else 
		{
			if(mp3_dec_sta() == MP3_DEC_STOP ) 
				taskPlayMp3MainStart(SysCtrl.file_index);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubKeyMsgMode
* Description    : playMp3SubKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3SubKeyMsgMode
* Description    : playMp3SubKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskPlayMp3ModeChange();
		playMp3SubPlayModeShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgSD
* Description    : playAudioSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) 
	{
		uiWinDestroy(&handle);
	}
	return 0;
}


/*******************************************************************************
* Function Name  : playMp3SubSysMsgPlay
* Description    : playMp3SubSysMsgPlay
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3SubSysMsgPlay(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 msg_event = UPDATE_NONE;
	if(parameNum == 1)
		msg_event = parame[0];
	if(msg_event & UPDATE_PLAYTIME)
	{
		playMp3SubPlayRateShow(handle);
		playMp3SubPlayCurTimeShow(handle);
		playMp3SubPlayTotalTimeShow(handle);
		playMp3SubPlayStatShow(handle);
	} 
	if(msg_event & UPDATE_LRCSHOW)
	{
		playMp3SubLrcShow(handle);
	}
	
	return 0;
}

ALIGNED(4) msgDealInfor playMp3SubMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playMp3SubOpenWin},
	{SYS_CLOSE_WINDOW,		playMp3SubCloseWin},
	{SYS_CHILE_COLSE,		playMp3SubWinChildClose},
	{KEY_EVENT_OK,			playMp3SubKeyMsgOk},
	{KEY_EVENT_UP,			playMp3SubKeyMsgUp},
	{KEY_EVENT_DOWN,		playMp3SubKeyMsgDown},
	{KEY_EVENT_MODE,		playMp3SubKeyMsgMode},
	{KEY_EVENT_MENU,		playMp3SubKeyMsgMenu},
	{SYS_EVENT_SDC,			playMp3SubSysMsgSD},
	//{SYS_EVENT_TIME_UPDATE,	playMp3SubSysMsgTimeUpdate},
	{SYS_EVENT_PLAY,		playMp3SubSysMsgPlay},
	{EVENT_MAX,NULL},
};

WINDOW(playMp3SubWindow,playMp3SubMsgDeal,playMp3SubWin)
#endif

