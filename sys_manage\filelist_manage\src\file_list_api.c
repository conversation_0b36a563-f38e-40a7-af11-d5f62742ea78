/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

ALIGNED(4) static FILELIST_CTRL_T* filelist_op[FILE_LIST_MAX];

/*******************************************************************************
* Function Name  : filelist_listFlush
* Description    : file list flush if need
* Input          : int list_num
* Output         : none                                            
* Return         : int : list name count
*******************************************************************************/
static FILELIST_CTRL_T* filelist_op_check(int list)
{
	u8 list_num = list & 0xff;
	if(list < 0 || list_num >= FILE_LIST_MAX || filelist_op[list_num] == NULL)
		return NULL;
	return 	filelist_op[list_num];
}
/*******************************************************************************
* Function Name  : filelist_listFlush
* Description    : file list flush if need
* Input          : int list_num
* Output         : none                                            
* Return         : int : list name count
*******************************************************************************/
static FILELIST_NODE_T* filenode_op_check(int list)
{
	u8 node_num = (list >> 8) & 0xff;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist)
	{
		if(node_num < FILE_NODE_NUM && filelist->node[node_num].valid > 0)
			return &filelist->node[node_num];
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : filelist_ordermin
* Description    : filelist_ordermin, find the oldest file index
* Input          : FILELIST_CTRL_T *filelist, int start_index
* Output         : none                                            
* Return         : int: return oldest file index
*******************************************************************************/
static int filelist_ordermin(FILELIST_CTRL_T *filelist, int start_index)
{
	int i;
	//u32 ftime_min 	= filelist->name[start_index].datatime;
	int i_min 		= start_index;
#if 0
	for(i = (start_index + 1); i < filelist->count; i++)
	{
		if(filelist->name[i].datatime < filelist->name[i_min].datatime)
		{
			i_min = i;
		}else if(filelist->name[i].datatime == filelist->name[i_min].datatime)
		{
			if((filelist->name[i].index & FILELIST_INDEX_MASK) < (filelist->name[i_min].index & FILELIST_INDEX_MASK))
				i_min = i;
			else if((filelist->name[i].index & FILELIST_INDEX_MASK) == (filelist->name[i_min].index & FILELIST_INDEX_MASK))
			{
				if((filelist->name[i].index & FILELIST_FLAG_AB) > (filelist->name[i_min].index & FILELIST_FLAG_AB))
				{
					i_min = i;
				}
			}
		}
	}
	return i_min;		
#elif 0
	u32 index_min   = filelist->name[start_index].index & FILELIST_INDEX_MASK;
	u32 index;
	u32 index_sub;
	for(i = (start_index + 1); i < filelist->count; i++)
	{
		index = filelist->name[i].index & FILELIST_INDEX_MASK;
		if(index == index_min)
		{
			if((filelist->name[i].index & FILELIST_FLAG_AB) > (filelist->name[i_min].index & FILELIST_FLAG_AB))
			{
				i_min = i;
			}else if((filelist->name[i].index & FILELIST_FLAG_AB) == (filelist->name[i_min].index & FILELIST_FLAG_AB))
			{
				if(filelist->name[i].datatime < filelist->name[i_min].datatime)
				{
					i_min = i;
				}
			}
		}else
		{
			index_sub = (index > index_min) ?(index - index_min): (index_min - index);
			if(index_sub > (filelist->list_index_max + FILE_INDEX_MIN))
			{
				if(index > index_min)
				{
					i_min = i;
					index_min = index;
				}		
			}else
			{
				if(filelist->name[i].datatime < filelist->name[i_min].datatime)
				{
					i_min = i;
				}else if(index < index_min)
				{
					i_min = i;
					index_min = index;
				}
			}
		}
	}

	return i_min;
#else
	u32 index_min   = filelist->name[start_index].index & FILELIST_INDEX_MASK;
	u32 index;
	//deg_Printf("start [%d] index:%x, datatime:%x\n", start_index,  filelist->name[start_index].index,  filelist->name[start_index].datatime);
	for(i = (start_index + 1); i < filelist->count; i++)
	{
		//deg_Printf("[%d] index:%x, datatime:%x\n", i,  filelist->name[i].index,  filelist->name[i].datatime);
		index = filelist->name[i].index & FILELIST_INDEX_MASK;
		if((filelist->name[i].index & FILELIST_TYPE_MASK ) ==  (filelist->name[i_min].index & FILELIST_TYPE_MASK))
		{
			u32 index_sub = (index > index_min) ?(index - index_min): (index_min - index);
			if(index_sub > (filelist->list_index_max + FILE_INDEX_MIN))
			{
				if(index > index_min)
				{
					i_min = i;
					index_min = index;
				}	
			}else{
				if(index < index_min)
				{
					i_min = i;
					index_min = index;
				}
			}
		}else
		{
			if(filelist->name[i].datatime < filelist->name[i_min].datatime)
			{
				i_min = i;
			}
		}
		//deg_Printf("i_min = %d \n",i_min);
	}
	return i_min;
#endif






}
/*******************************************************************************
* Function Name  : filelist_orderflush
* Description    : filelist_orderflush, oder filelist by datatime(AS)
* Input          : FILELIST_CTRL_T *filelist
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void filelist_orderflush(FILELIST_CTRL_T *filelist)
{
	int i,j;
	FILELIST_NAME_T name_temp;
	if(filelist == NULL)
		return;
	filelist_listFlush(filelist);
	for(i = 0; i < filelist->count; i++)
	{
		j = filelist_ordermin(filelist, i);
		if(i != j)
		{
			name_temp.index				= filelist->name[i].index;
			name_temp.datatime			= filelist->name[i].datatime;
			filelist->name[i].index		= filelist->name[j].index;
			filelist->name[i].datatime	= filelist->name[j].datatime;
			filelist->name[j].index		= name_temp.index;
			filelist->name[j].datatime	= name_temp.datatime;
			if(filelist->plist_name)
			{
				hx330x_str_cpy(filelist->file_fullname,           &filelist->plist_name->name[i][0]);
				hx330x_str_cpy(&filelist->plist_name->name[i][0], &filelist->plist_name->name[j][0]);
				hx330x_str_cpy(&filelist->plist_name->name[j][0], filelist->file_fullname);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : filelist_listFlush
* Description    : file list flush if need
* Input          : int list_num
* Output         : none                                            
* Return         : int : list name count
*******************************************************************************/
int filelist_listFlush(FILELIST_CTRL_T *filelist)
{
	int i, j;
	if(filelist == NULL)
		return -1;
	if(filelist->dirty == 0)
	{
		return filelist->count;
	}
	//deg_Printf("flush:count:%d\n",filelist->count);
	for(i = 0, j = 0; i < filelist->count; i++)
	{
		if(!(filelist->name[i].index & FILELIST_FLAG_IVL))
		{
			if(i != j)
			{
				filelist->name[j].index 	= filelist->name[i].index;
				filelist->name[j].datatime 	= filelist->name[i].datatime;
				filelist->name[i].index		= FILELIST_FLAG_IVL;
				if(filelist->plist_name)
				{
					hx330x_str_cpy(&filelist->plist_name->name[j][0],&filelist->plist_name->name[i][0]);
					filelist->plist_name->name[i][0] = 0;
				}
			}
			j++;
		}
	}
	//deg_Printf("filelist: dirty:%d,flush:[%d,%d]\n",filelist->dirty,j,filelist->count);
	filelist->dirty = 0;
	filelist->count = j;
	return j;
}
/*******************************************************************************
* Function Name  : filelist_Init
* Description    : initial resource manager
* Input          : 
* Output         : none                                            
* Return         : handle
*******************************************************************************/
void filelist_api_Init(void)
{
    int i;
	for(i = 0; i < FILE_LIST_MAX; i++ )
	{
		filelist_op[i] = NULL;
	}
}
/*******************************************************************************
* Function Name  : filelist_api_nodecreate
* Description    : create a filenode or filelist
* Input          : char *path: filenode path
*                  INT32U type: filenode type
*                  int list: base filelist num, if < 0, create a new filelist
* Output         : none                                            
* Return         : filenode num
*******************************************************************************/
int filelist_api_nodecreate(char *path,INT32U type,int list)
{
    int i,j;
	
	if(list < 0)
	{
		for(i = 0; i < FILE_LIST_MAX; i++)
		{
			if(filelist_op[i] == NULL)
			{
				filelist_op[i] = (FILELIST_CTRL_T *)hal_sysMemMallocLast(sizeof(FILELIST_CTRL_T));
				if(filelist_op[i])
				{
					filelist_op[i]->dirty			= 0;
					filelist_op[i]->count			= 0;					
					if(type & (FILELIST_TYPE_JPG|FILELIST_TYPE_AVI))
					{
						filelist_op[i]->list_index_max	= FILE_NUM_MAX;
					}else
					{
						filelist_op[i]->list_index_max	= FILE_NUM_MAX_2;
					}
					
					filelist_op[i]->file_fullname[0]= 0;
					for(j = 0; j < FILE_NODE_NUM; j++)
					{
						filelist_op[i]->node[j].valid = 0;
					}	
					filelist_op[i]->name = (FILELIST_NAME_T*)hal_sysMemMallocLast(sizeof(FILELIST_NAME_T) *filelist_op[i]->list_index_max );
					if(filelist_op[i]->name == NULL)
					{
						deg_Printf("[FILELIST INDEX TAB] malloc fail\n");
						hal_sysMemFree((void*)filelist_op[i]);
						filelist_op[i] = NULL;
						return -1;
					}
					for(j = 0; j < filelist_op[i]->list_index_max;j++)
					{
						filelist_op[i]->name[j].index = FILELIST_FLAG_IVL;
					}
					filelist_op[i]->plist_name = NULL;

					//deg_Printf("dirty:%x, count:%x\n",filelist_op[i]->dirty,filelist_op[i]->count);
				}else
				{
					deg_Printf("[FILELIST] malloc fail\n");
					return -1;
				}
				break;
			}
		}
		list = i;
	}else
	{
		list = list & 0xff;
	}
	if(filelist_op_check(list) == NULL)
	{
		deg_Printf("[FILELIST] list invalid\n");
		return -2;
	}
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist_op[list]->node[i].valid == 0)
		{
			break;
		}
	}
	if(i >= FILE_NODE_NUM)
	{
		deg_Printf("[FILELIST] %d node full\n",list);
		return -3;
	}
	if(type & (FILELIST_TYPE_MP3 | FILELIST_TYPE_NES))
	{
		if(filelist_op[list]->plist_name == NULL)
		{
			filelist_op[list]->plist_name = (FILE_NAME_LIST_T *)hal_sysMemMalloc(sizeof(FILE_NAME_LIST_T));
			if(filelist_op[list]->plist_name == NULL)
			{
				deg_Printf("[FILELIST] %d file name list fail\n",list);
			}else
			{
				for(j = 0; j < FILE_NUM_MAX_2; j++)
				{
					filelist_op[list]->plist_name->name[j][0] = 0;
				}
			}
			
		}

	}
	filelist_op[list]->node[i].valid 	= 1;
	filelist_op[list]->node[i].plist 	= (void*)filelist_op[list];
	filelist_op[list]->node[i].count 	= 0;
	filelist_op[list]->node[i].node_type = type | FILE_NAME_DEFAULT_TYPE;
	if(type & FILELIST_TYPE_AVI)
	{
		filelist_op[list]->node[i].min_space = REMAIN_MIN_VIDEO;
	}else if(type & FILELIST_TYPE_JPG)
	{
		filelist_op[list]->node[i].min_space = REMAIN_MIN_PHOTO;
	}else if(type & FILELIST_TYPE_WAV)
	{
		filelist_op[list]->node[i].min_space = REMAIN_MIN_MUSIC;
	}else
	{
		filelist_op[list]->node[i].min_space = 0;
	}
	hx330x_str_cpy(filelist_op[list]->node[i].path,path);
	filelist_op[list]->node[i].file[0]  = 0;
	//deg_Printf("list:%x\n", (list|i<<8));
	return (list|i<<8);
}
/*******************************************************************************
* Function Name  : filelist_api_nodedestory
* Description    : destory a filelist or filelist node
* Input          : int exp : handle
* Output         : none                                            
* Return         : int
*******************************************************************************/
int filelist_api_nodedestory(int list)
{
	u8 list_num, node_num,i;
	list_num = list & 0xff;
	node_num = (list >>8)&0xff;
	
	if(list < 0 || list_num >= FILE_LIST_MAX || filelist_op[list_num] == NULL || node_num >= FILE_NODE_NUM )
		return -1;
	filelist_op[list_num]->node[node_num].valid = 0;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist_op[list_num]->node[i].valid > 0)
			break;
	}
	if(i >= FILE_NODE_NUM)
	{
		hal_sysMemFree((void*)filelist_op[list_num]->plist_name);
		hal_sysMemFree((void*)filelist_op[list_num]->name);
		hal_sysMemFree((void*)filelist_op[list_num]);
		//filelist_op[list_num]->plist_name = NULL;
		filelist_op[list_num] = NULL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : filelist_api_scan
* Description    : scan all filenode of filelist
* Input          : int list: filelist num
* Output         : none                                            
* Return         : handle
*******************************************************************************/
int filelist_api_scan(int list)
{
	int i;
	FILELIST_CTRL_T* filelist = filelist_op_check(list);
	if(filelist == NULL)
		return -1;
	deg_Printf("[FILELIST] Scan List:%d\n",list &0xff);
	//for(i = 0; i < FILE_NUM_MAX;i++)
	//{
	//	filelist->name[i].index = FILELIST_FLAG_IVL;
	//}
	filelist->count = 0;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		filenode_Scan(&filelist->node[i]);
	}
	filelist_orderflush(filelist);
	filelist->file_fullname[0] = 0;
	deg_Printf("[FILELIST] Scan List End:%d\n",filelist->count);
	return 0;
}
/*******************************************************************************
* Function Name  : filenode_api_CountGet
* Description    : get a filenode's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filenode count
*******************************************************************************/
int filenode_api_CountGet(int list)
{
	FILELIST_NODE_T*  filenode = filenode_op_check(list);
	if(filenode == NULL)
		return 0;
	return filenode->count;
}
/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filelist_api_CountGet(int list)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return 0;
	filelist_listFlush(filelist);	
	return filelist->count;
}
/*******************************************************************************
* Function Name  : filelist_api_MaxCountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filelist_api_MaxCountGet(int list)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return 0;
	return filelist->list_index_max;
}
/*******************************************************************************
* Function Name  : filelist_GetFileNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileNameByIndex(int list, int index)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL || index < 0 || index >= filelist->count)
		return NULL;
	if(filelist->plist_name)
	{
		//deg_Printf("[%d]%s\n", index, filelist->plist_name->name[index]);
		return filelist->plist_name->name[index];
	}
	else
		return NULL;
}
/*******************************************************************************
* Function Name  : filelist_GetFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileFullNameByIndex(int list, int index, int *file_type)
{
	int i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	FILELIST_NODE_T * filenode = NULL;
	char* name;
	if(filelist == NULL || index < 0 || index >= filelist->count)
		return NULL;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && (( filelist->name[index].index & filelist->node[i].node_type) == (filelist->name[index].index & FILENODE_TYPE_MASK)) )
		//if(filelist->node[i].valid > 0 && ((filelist->node[i].node_type&FILENODE_TYPE_MASK) & filelist->name[index].index))
		{
			filenode = &filelist->node[i];
			break;
		}
	}
	if(filenode == NULL)
		return NULL;
	if(file_type)
		*file_type = filelist->name[index].index & (FILELIST_TYPE_MASK|FILELIST_FLAG_LOK|FILELIST_FLAG_AB);
	name = filelist_GetFileNameByIndex(list, index);
	if(name)
	{
		hx330x_str_cpy(filelist->file_fullname,filenode->path);
		hx330x_str_cat(filelist->file_fullname,name);	
		return filelist->file_fullname;
	}else
		return filenode_filefullname_CreateByFname(filenode,&filelist->name[index]);
}
/*******************************************************************************
* Function Name  : filelist_GetFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileShortNameByIndex(int list, int index, int *file_type)
{
	int i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	FILELIST_NODE_T * filenode = NULL;
	char* name;
	if(filelist == NULL || index < 0 || index >= filelist->count)
		return NULL;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && (( filelist->name[index].index & filelist->node[i].node_type) == (filelist->name[index].index & FILENODE_TYPE_MASK)) )
		//if(filelist->node[i].valid > 0 && ((filelist->node[i].node_type&FILENODE_TYPE_MASK) & filelist->name[index].index))
		{
			filenode = &filelist->node[i];
			break;
		}
	}
	if(filenode == NULL)
		return NULL;
	if(file_type)
		*file_type = filelist->name[index].index & (FILELIST_TYPE_MASK|FILELIST_FLAG_LOK|FILELIST_FLAG_AB);
	name = filelist_GetFileNameByIndex(list, index);
	if(name)
	{
		return name;
	}else
		return filenode_filename_CreateByFname(filenode,&filelist->name[index]);
}
/*******************************************************************************
* Function Name  : filelist_GetFileIndexByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index
* Output         : none                                            
* Return         : 
*******************************************************************************/
u32 filelist_GetFileIndexByIndex(int list, int index)
{
	int i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	FILELIST_NODE_T * filenode = NULL;
	if(filelist == NULL || index < 0 || index >= filelist->count)
	{
		//deg_Printf("filelist_GetFileIndexByIndex 111111111\n");
		return (u32)-1;
	}
		
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && (( filelist->name[index].index & filelist->node[i].node_type) == (filelist->name[index].index & FILENODE_TYPE_MASK)) )
		//if(filelist->node[i].valid > 0 && ((filelist->node[i].node_type&FILENODE_TYPE_MASK) & filelist->name[index].index))
		{
			filenode = &filelist->node[i];
			break;
		}
	}
	if(filenode == NULL)
	{
		//deg_Printf("filelist_GetFileIndexByIndex 2222222\n");
		return (u32)-1;
	}
	//deg_Printf("filelist_GetFileIndexByIndex:%x\n",filelist->name[index].index);
	return filelist->name[index].index & FILELIST_INDEX_MASK;
}
/*******************************************************************************
* Function Name  : filenode_findFirstFileName
* Description    : find a filenode's first file
* Input          : int list_num
*                  int *index: filelist index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_findFirstFileName(int list, FILELIST_NAME_T *fname,int *index)
{
	int ret;
	FILELIST_NODE_T* filenode = filenode_op_check(list);
	if(filenode == NULL)
		return NULL;
	ret = filenode_api_findfirst(filenode, fname);
	if(ret < 0)
		return NULL;
	if(index)
		*index = ret;
	
	return filenode_filefullname_CreateByFname(filenode,fname);
}
/*******************************************************************************
* Function Name  : filenode_findFirstFileName
* Description    : find a filenode's first file
* Input          : int list_num
*                  int *index: filelist index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_findFileNameByFname(int list, FILELIST_NAME_T *fname, int *index)
{
	int ret,i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return NULL;
	FILELIST_NODE_T* filenode = NULL;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && filenode_fname_check(&filelist->node[i],fname) >= 0)
		{
			filenode = &filelist->node[i];
			break;
		}
	}
	if(filenode == NULL)
		return NULL;
	ret = filenode_api_findByFname(filenode, fname);
	if(ret < 0)
		return NULL;
	if(index)
		*index = ret;
	return filenode_filefullname_CreateByFname(filenode,&filelist->name[ret]);
}
/*******************************************************************************
* Function Name  : filelist_delFileByIndex
* Description    : del a file(but not lock file) by filelist index
* Input          : int list_num
*                  int index: filelist index
* Output         : none                                            
* Return         : int : < 0 fail, 0:success
*******************************************************************************/
int filelist_delFileByIndex(int list, int index)
{
	int i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL || index < 0 || index >= filelist->count)
		return -1;
	if(filelist->name[index].index & FILELIST_FLAG_LOK) //not del lock file
		return 0;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && filenode_fname_check(&filelist->node[i],&filelist->name[index]) >= 0)
		{
			filelist->node[i].count--;
			break;
		}
	}
	filelist->name[index].index = FILELIST_FLAG_IVL;
	filelist->dirty = 1;
	filelist_listFlush(filelist);
	
	
	return filelist->count;		
}
/*******************************************************************************
* Function Name  : filelist_delFileByFname
* Description    : del a file(but not lock file) by filelist fname
* Input          : int list_num
*                  FILELIST_NAME_T *fname
* Output         : none                                            
* Return         : int : < 0 fail, 0:success
*******************************************************************************/
int filelist_delFileByFname(int list, FILELIST_NAME_T *fname)
{
	int ret,i;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return -1;
	FILELIST_NODE_T* filenode = NULL;
	for(i = 0; i < FILE_NODE_NUM; i++)
	{
		if(filelist->node[i].valid > 0 && filenode_fname_check(&filelist->node[i],fname) >= 0)
		{
			filenode = &filelist->node[i];
			break;
		}
	}
	if(filenode == NULL)
		return -2;
	ret = filenode_api_findByFname(filenode, fname);
	if(ret < 0)
		return -3;
	return filelist_delFileByIndex(list,ret);	
}
/*******************************************************************************
* Function Name  : filelist_listDelAll
* Description    : del all file of filelist(but not lock file)
* Input          : int list: filelist num
* Output         : none                                            
* Return         : int : < 0 fail, >=0 : filelist file cnt
*******************************************************************************/
int filelist_listDelAll(int list)
{
	int i,j;
	FILELIST_CTRL_T * filelist = filelist_op_check(list);

	if(filelist == NULL)
		return -1;
	if(filelist->count <= 0)
		return 0;
	for(j = 0; j < FILE_NODE_NUM; j++)
	{
		if(filelist->node[j].valid > 0 )
		{
			filelist->node[j].count = 0;
		}
	}
	for(i = 0;i < filelist->count; i++)
	{
		if(filelist->name[i].index & FILELIST_FLAG_LOK) //not del lock file
		{
			for(j = 0; j < FILE_NODE_NUM; j++)
			{
				if(filelist->node[j].valid > 0 && filenode_fname_check(&filelist->node[j],&filelist->name[i]) >= 0)
				{
					filelist->node[j].count++;
					break;
				}
			}
			continue;
		}
		filelist->name[i].index = FILELIST_FLAG_IVL;
		if(filelist->plist_name)
		{
			filelist->plist_name->name[i][0] = 0;
		}
	}
	filelist->dirty = 1;
	filelist_listFlush(filelist);
	return filelist->count;
}
/*******************************************************************************
* Function Name  : filenode_createNewFileFullName
* Description    : try to create a new file full name 
* Input          : int list_num
*                  int *index: index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_createNewFileFullName(int list, FILELIST_NAME_T *fname)
{
	FILELIST_NODE_T* filenode = filenode_op_check(list);
	if(filenode == NULL)
		return NULL;	
	if(filenode_fname_createNew(filenode,fname) < 0)
		return NULL;
	return filenode_filefullname_CreateByFname(filenode,fname);
}
/*******************************************************************************
* Function Name  : filenode_createNewFileFullNameByIndex
* Description    : create a new file full name by index
* Input          : int list_num
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_createNewFileFullNameByFname(int list, FILELIST_NAME_T *fname)
{
	FILELIST_NODE_T* filenode = filenode_op_check(list);
	if(filenode == NULL)
		return NULL;	
	
	return filenode_filefullname_CreateByFname(filenode,fname);	
}


/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_addFileByFname(int list, FILELIST_NAME_T *fname)
{
	FILELIST_NODE_T* filenode = filenode_op_check(list);
	if(filenode == NULL)
		return -1;	
	return filenode_AddFileByFname(filenode, fname, NULL);
}
/*******************************************************************************
* Function Name  : filenode_filefullnameLock
* Description    : change a file full name as lock
* Input          : char * name: full name buf
* Output         : none                                            
* Return         : none
*******************************************************************************/
void filenode_filefullnameLock(char *name)
{
	int i = 0;
	char *file = name;
	while(name[i])
	{
		if(name[i] == '/')
			file = &name[i+1];
		i++;
	}
	if(file[0] == 0)
		return;
	if(hx330x_str_ncmp(file,PREFIX_AVI,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_LOK_AVI,PREFIX_LEN);
	}else
	if(hx330x_str_ncmp(file,PREFIX_JPG,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_LOK_JPG,PREFIX_LEN);
	}
	else
	if(hx330x_str_ncmp(file,PREFIX_SPI,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_LOK_SPI,PREFIX_LEN);
	}
}

/*******************************************************************************
* Function Name  : filenode_filefullnameUnlock
* Description    : change a file full name as unlock
* Input          : char * name: full name buf
* Output         : none                                            
* Return         : none
*******************************************************************************/
void filenode_filefullnameUnlock(char *name)
{
	int i = 0;
	char *file = name;
	while(name[i])
	{
		if(name[i] == '/')
			file = &name[i+1];
		i++;
	}
	if(file[0] == 0)
		return;
	if(hx330x_str_ncmp(file,PREFIX_LOK_AVI,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_AVI,PREFIX_LEN);
	}else
	if(hx330x_str_ncmp(file,PREFIX_LOK_JPG,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_JPG,PREFIX_LEN);
	}
	else
	if(hx330x_str_ncmp(file,PREFIX_LOK_SPI,PREFIX_LEN) == 0)
	{
		hx330x_str_ncpy(file,PREFIX_SPI,PREFIX_LEN);
	}
}

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_fnameLockByIndex(int list, int index)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return -1;
	if(index >= filelist->count)
		return -2;
	if(filelist->name[index].index & FILELIST_FLAG_IVL)
		return 0;
	filelist->name[index].index |= FILELIST_FLAG_LOK;
	return 0;
		
}

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_fnameUnlockByIndex(int list, int index)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return -1;
	if(index >= filelist->count)
		return -2;
	if(filelist->name[index].index & FILELIST_FLAG_IVL)
		return 0;
	//if(filelist->name[index].index & FILELIST_TYPE_AVI)
	{
		filelist->name[index].index &= ~FILELIST_FLAG_LOK;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filelist_fnameChecklockByIndex(int list, int index)
{
	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL)
		return -1;
	if(index < 0 || index >= filelist->count)
		return -2;
	if(filelist->name[index].index & FILELIST_FLAG_IVL)
		return -3;
	if(filelist->name[index].index & (FILELIST_TYPE_AVI|FILELIST_TYPE_JPG|FILELIST_TYPE_SPI))
	{
		if(filelist->name[index].index & FILELIST_FLAG_LOK)
		{
			return 1;
		}else
		{
			return 0;
		}
	}
	return -4;

}
/*******************************************************************************
* Function Name  : filenode_parentdir_check
* Description    : try to parent dir of find dir or file
* Input          : int list_num
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char *filenode_parentdir_get(int list)
{
	FILELIST_CTRL_T * filelist;
	FILELIST_NODE_T * filenode = filenode_op_check(list);
	char *parentdir;
	u32  i, path_i;
	if(filenode == NULL)
		return NULL;
	filelist = (FILELIST_CTRL_T *)filenode->plist;
	//if(hx330x_str_cmp(filelist->path, filenode->path) == 0)
	//	return NULL;
	hx330x_str_cpy(filelist->file_fullname,filenode->path);
	parentdir = filelist->file_fullname;
	path_i = 0;
	for(i = 0; parentdir[i] != 0; i++)
	{
		if(parentdir[i] == '/')
		{
			if(parentdir[i+1] == 0)
				break;
			else
				path_i = i + 1;
		}
	}
	if(path_i == 0)
	{
		return NULL;
	}
	parentdir[path_i] = 0;
	return parentdir;
}
/*******************************************************************************
* Function Name  : filelist_GetLrcFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetLrcFileFullNameByIndex(int list, int index)
{

	FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filelist == NULL || index < 0 || index >= filelist->count)
		return NULL;
	if(filelist_GetFileFullNameByIndex(list, index, NULL) == NULL)
		return NULL;
	if(filelist_NameChangeSufType(filelist->file_fullname, SUFFIX_LRC) < 0)
		return NULL;
	return filelist->file_fullname;

}
/*******************************************************************************
* Function Name  : managerSpaceCheck
* Description    : check sdc free sapce & delete file
* Input          : 
*                  remain_space: kb
* Output         : none                                            
* Return         : int： <0 fail, > 0 : freespace, need to modify SysCtrl.sd_freesize deamon_fsFreeSize
*******************************************************************************/
int filelist_SpaceCheck(int list,u32* freespace,int need_space)
{
	int i, index,ftype;
	FILELIST_NAME_T fname;
	char *filename;
	FILINFO Finfo;
	FRESULT res = FR_OK;
	FILELIST_NODE_T* filenode = filenode_op_check(list);
	if(filenode == NULL || filenode->node_type & (FILELIST_TYPE_SPI|FILELIST_TYPE_NES|FILELIST_TYPE_MP3|FILELIST_TYPE_DIR))
		return -1;	
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T *)filenode->plist;
	if(filelist->count <= 0)
		return *freespace;
	if(need_space == 0)
	{
		if(*freespace > filenode->min_space)
			return *freespace;
		else
			return -1;
	}
	need_space += filenode->min_space;
	deg_Printf("[FILELIST]space check: need_space %dM %dKB\n",need_space>>10,need_space&0x3ff);
	i  = -1;
	while(1)
	{
		i++;
		if(*freespace >= need_space && filelist->count < filelist->list_index_max) 
			break;
		// no space or file count max ,need delete file
	#if  0 //将会删除同一list中的文件，如AVI和JPG
		if(filelist->count <= 0)
		{
			deg_Printf("[Filelist %d]no file\n",list);
			break;
		}
	#else //只删除该类型文件
		if(filenode->count <= 0)
		{
			deg_Printf("[Filenode %d]no file\n",list);
			break;
		}
		if(i >= filelist->count)
		{
			break;
		}
		if(filenode_fname_check(filenode,&filelist->name[i]) < 0)
		{
			continue;
		}
	#endif
		//if(filenode_findFirstFileName(list, &findex) == NULL)
		//	break;
		filename = filelist_GetFileFullNameByIndex(list, i, &ftype);
		if(filename == NULL)
			break;
		if(ftype & FILELIST_FLAG_LOK)
		{
			continue;
		}
		fname.index = filelist->name[i].index;
		//del filelist file
		if(filelist_delFileByIndex(list, i) < 0)
		{
			deg_Printf("filelist_delFile fail\n");
			break;
		}
		//del filesystem file
		res = f_stat(filename, &Finfo);
		if(res == FR_OK)
		{
			res = f_unlink(filename);  // delete file now
		}
		if(res != FR_OK)
		{
			deg_Printf("[FILE] del 0: %s fail,%d\n",filename,res);
		}else
		{
			*freespace += Finfo.fsize>>10;
		}
		if(fname.index & FILELIST_TYPE_AVI)
		{
			if(fname.index & FILELIST_FLAG_AB)
				fname.index &=~FILELIST_FLAG_AB;
			else
				fname.index |= FILELIST_FLAG_AB;
			
			filename = filelist_findFileNameByFname(list, &fname, &index);
			if(filename)
			{
				if(filelist_delFileByIndex(list, index) < 0)
					break;			
				//del filesystem file
				res = f_stat(filename, &Finfo);
				if(res == FR_OK)
				{
					deg_Printf("[FILE] del file: %sn",filename);
					res = f_unlink(filename);  // delete file now
				}
				if(res != FR_OK)
				{
					deg_Printf("[FILE] del 1: %s fail,%d\n",filename,res);
				}else
				{
					*freespace += Finfo.fsize>>10;
				}				
			}
		}
		
	}
    deg_Printf("[FILELIST] free space : %dG %dMB\n",*freespace>>20,(*freespace>>10)&0x3ff);
	
	if(*freespace >= need_space)
		return *freespace;
	else
	{
		return -1; // no more space		
	}
}