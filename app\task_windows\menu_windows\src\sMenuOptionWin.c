/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	OPTION_TITLE_ID=0,
	OPTION_RECT_ID,
	OPTION_LINE_ID,
	OPTION_SELECT_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor menuOptionWin[] =
{
	createFrameWin(						Rx(70),	Ry(42), Rw(180),<PERSON>h(176),R_ID_PALETTE_Black, WIN_ABS_POS),
	createRect(OPTION_LINE_ID,         	Rx(1),	<PERSON>y(1),  <PERSON>w(178),<PERSON>h(174), R_ID_LINE),
	createStringIcon(OPTION_TITLE_ID,	Rx(1), 	Ry(1), 	Rw(178),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	createRect(OPTION_RECT_ID,       	Rx(1),	Ry(33), Rw(178),Rh(1),	R_ID_PALETTE_DarkGreen),
	createItemManage(OPTION_SELECT_ID,	Rx(1),	Ry(34), Rw(178),Rh(141),INVALID_COLOR),
	// createFrameWin(						Rx(70),	Ry(70), Rw(180),Rh(120),R_ID_LINE, WIN_ABS_POS),
	// createRect(OPTION_RECT_ID,         	Rx(1),	Ry(1),  Rw(178),Rh(118), R_ID_LINE),
	// createItemManage(OPTION_SELECT_ID,	Rx(1),	Ry(1), Rw(178),Rh(118),INVALID_COLOR),
	widgetEnd(),
};



