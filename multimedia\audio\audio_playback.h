/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  AUDIO_PLAYBACK_H
    #define  AUDIO_PLAYBACK_H



#define SAMPLE_RATE_8000     8000
#define SAMPLE_RATE_11025   11025
#define SAMPLE_RATE_12000   12000
#define SAMPLE_RATE_16000   16000
#define SAMPLE_RATE_22050   22050
#define SAMPLE_RATE_24000   24000
#define SAMPLE_RATE_32000   32000
#define SAMPLE_RATE_44100   44100
#define SAMPLE_RATE_48000   48000
#define SAMPLE_RATE_AUTO    0   // sample rate get from music file





#if DBG_AUDIO_PLY_EN
    #define  AUDIO_PLY_DBG    deg_Printf
#else
    #define  AUDIO_PLY_DBG(...)
#endif


/*******************************************************************************
* Function Name  : audioPlaybackInit
* Description    : initial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackInit(void);
/*******************************************************************************
* Function Name  : audioPlaybackUninit
* Description    : uninitial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackUninit(void);
/*******************************************************************************
* Function Name  : audioPlaybackStart
* Description    : Start audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackParse(WAV_PARA_T *arg);
/*******************************************************************************
* Function Name  : audioPlaybackStart
* Description    : Start audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackStart(WAV_PARA_T *arg);
/*******************************************************************************
* Function Name  : audioPlaybackStop
* Description    : Stop audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackStop(void);
/*******************************************************************************
* Function Name  : audioPlaybackPuase
* Description    : Puase audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackPause(void);
/*******************************************************************************
* Function Name  : audioPlaybackPause
* Description    : Puase audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackFirstPause(void);
/*******************************************************************************
* Function Name  : audioPlaybackResume
* Description    : Resume audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackResume(void);
/*******************************************************************************
* Function Name  : audioPlaybackGetStatus
* Description    : get audio Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackGetStatus(void);
/*******************************************************************************
* Function Name  : videoPlaybackGetTime
* Description    : get video Playback time
* Input          : INT32U *total : total time
                     INT32U *curr : current time
* Output         : INT32U *total : total time
                     INT32U *curr : current time
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackGetTime(INT32U *total,INT32U *curr);
/*******************************************************************************
* Function Name  : audioPlaybackSetVolume
* Description    :set audio Playback volume
* Input          : INT8U volume : 0-100
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackSetVolume(INT8U volume);
/*******************************************************************************
* Function Name  : audioPlaybackGetVolume
* Description    : get audio Playback volume
* Input          : 
* Return         : INT8U volume : 0-100 
                      
*******************************************************************************/
int audioPlaybackGetVolume(void);
/*******************************************************************************
* Function Name  : audioPlaybackService
* Description    : audio play back services
* Input          : 
* Output         : 
* Return         :  
                      
*******************************************************************************/
void audioPlaybackService(void);
















#endif

