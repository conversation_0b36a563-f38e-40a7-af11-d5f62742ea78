/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/


#ifndef UI_WIN_WIDGET_H
#define UI_WIN_WIDGET_H

#define INVALID_WIDGET_ID    	0xffff
typedef enum
{
	WIDGET_ITEM_MANAGE = 1,
	WIDGET_TYPE_MAX
}widgetType;

typedef struct
{
	uiWinObj   win;
	u16    	   id;
	u16  	   type;
}uiWidgetObj;


/*******************************************************************************
* Function Name  : uiWidgetProc
* Description    : uiWidgetProc:support MSG_WIDGET_GET_ID and MSG_WIDGET_SET_ID opt
* Input          : none
* Output         : none                                            
* Return         : true: msg process success
*******************************************************************************/
bool uiWidgetProc(uiWinMsg* msg);
/*******************************************************************************
* Function Name  : uiWidgetSetType
* Description    : uiWidgetSetType: set widget's type
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWidgetSetType(winHandle hWin,u16 type);
/*******************************************************************************
* Function Name  : uiWidgetSetType
* Description    : uiWidgetSetType: set widget's type
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u16 uiWidgetGetType(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWidgetGetId
* Description    : uiWidgetGetId: get widget's id
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u16 uiWidgetGetId(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWidgetSetId
* Description    : uiWidgetSetId: set widget's id
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWidgetSetId(winHandle hWin,u16 id);
#endif
