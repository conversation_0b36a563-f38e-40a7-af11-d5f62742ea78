/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  AVI_TYPEDEF_H
#define  AVI_TYPEDEF_H


//AVI TAG DEFINE
#define  AVI_RIFF_TAG     			0x46464952
#define  AVI_RIFF_JUNK    			0x4B4E554A
#define  AVI_RIFF_LIST    			0x5453494c
#define  AVI_RIFF_AVI     			0x20495641
#define  AVI_RIFF_avih    			0x68697661
#define  AVI_RIFF_hdrl    			0x6C726468
#define  AVI_RIFF_movi    			0x69766F6D
#define  AVI_RIFF_idx1    			0x31786469
#define  AVI_RIFF_strl    			0x6C727473
#define  AVI_RIFF_strh    			0x68727473
#define  AVI_RIFF_strf    			0x66727473
#define  AVI_RIFF_00dc    			0x63643030
#define  AVI_RIFF_01wb    			0x62773130
#define  AVI_RIFF_00DB    			0x62643030
#define  AVI_RIFF_vids    			0x73646976
#define  AVI_RIFF_auds    			0x73647561
#define  AVI_RIFF_strd    			0x64727473
#define  AVI_RIFF_mjpg    			0x67706A6D
#define  AVI_RIFF_MJPG    			0x47504A4D
#define  AVI_RIFF_indx    			0x78646e69
#define  AVI_RIFF_vprp    			0x70727076
#define  AVI_RIFF_odml    			0x6c6d646f
#define  AVI_RIFF_dmlh    			0x686c6d64
#define  AVI_RIFF_ix01    			0x31307869
#define  AVI_RIFF_ix00    			0x30307869
#define  AVI_RIFF_AVIX    			0X58495641


//AVI FILE SIZE DEFINE
#define  FIL_SIZE_4G  				0XF85A2000
#define  FIL_SIZE_3G				0XDC758000
#define  FIL_SIZE_2G				0X7D000000
#define  FIL_SIZE_1G				0X3E800000

//AVI FRAME TYPE
#define  AVI_FRAME_DC_VIDEO      	0x01   //压缩的视频帧
#define  AVI_FRAME_WD_AUDIO     	0x02   //音频数据
#define  AVI_FRAME_DB_VIDEO      	0x04   //未压缩的视频帧
#define  AVI_FRAME_JP_VIDEO      	0x08
#define  AVI_FRAME_JP_AUDIO      	0x10
#define  AVI_FRAME_MM_NULL      	0x20

//AVI FLAGS DEFINE
#define  AVI_KEY_FRAME    			0x00000010
#define  AVIF_HASINDEX 				0x10
#define  AVIF_MUSTUSEINDEX  		0x20
#define  AVIF_ISINTERLEAVED			0x100
#define  AVIF_WASCAPTUREFILE		0x10000
#define  AVIF_COPYRIGHTED			0x20000
#define  AVI_KNOWN_FLAGS 			0x30130


//AVI INDEX TYPE
#define  AVI_INDEX_OF_INDEXES       0x00
#define  AVI_INDEX_OF_CHUNKS        0x01
#define  AVI_INDEX_OF_TIMED_CHUNKS  0x02
#define  AVI_INDEX_OF_SUB_2FIELD    0x03
#define  AVI_INDEX_IS_DATA          0x80

//AVI ERROR DEFINE
#define AVI_ERR_NONE          	 0   // no error
#define AVI_ERR_GENERAL       	-1   // general error
#define AVI_ERR_READ          	-2   // avi read callback.no read callback function found or read fail
#define AVI_ERR_WRITE         	-3  // avi write callback.no write callback function found or write fail
#define AVI_ERR_MEMORY        	-4  // memory error
#define AVI_ERR_TAG          	-5   // AVI TAG error
#define AVI_ERR_HDRL          	-6   // hdrl error,can't find vliad hdrl
#define AVI_ERR_AVIH			-7   // avih error
#define AVI_ERR_STRH			-8	 // strh error
#define AVI_ERR_VIDS          	-9   // vids mjpeg ,can't support this type of vids
#define AVI_ERR_AUDS          	-10   // auds pcm,can't support this type of auds
#define AVI_ERR_INDX          	-11   // indx for OPENDML,can't support this type of OPENDML
#define AVI_ERR_IDX1          	-12   // idx1 ,can't find vliad idx1 table
#define AVI_ERR_MOVI			-13	  //movi err
#define AVI_ERR_END           	-14  // end of avi vids and auds
#define AVI_ERR_INIT          	-15   // decoder initial,decoder initial fail.
#define AVI_ERR_VFRAME        	-16  // inster vids frame error
#define AVI_ERR_AFRAME        	-17  // add auds frame error
#define AVI_ERR_AVIX          	-18  // avix fail





typedef struct 
{
	DWORD dwFourCC;
	DWORD dwSize;
} AVI_CHUNK;
typedef struct 
{
	DWORD offset;
	DWORD Size;
} AVI_FRAME_CHUNK;
typedef struct
{ 
	DWORD fcc;
	DWORD cb;
	DWORD fcctype;
} LIST;


typedef struct 
{
	DWORD ckid;
	DWORD dwFlags;
	DWORD dwChunkOffset;
	DWORD dwChunkLength;
} AVIINDEXENTRY;

typedef struct AVI_SUPERINDX_S 
{
	 UINT64 qwOffset;	 // absolute file offset
	 DWORD  dwSize;		 // size of index chunk at this offset include stdindx head
	 DWORD  dwDuration;	 // time span in stream ticks
} __attribute__((packed)) AVI_SUPERINDX_T;	 

typedef struct AVI_IX_HEADER_S 
{
	 DWORD 	fcc; 
	 DWORD 	cb;
	 WORD  	wLongsPerEntry; // must be sizeof(aIndex[0])/sizeof(DWORD)
	 BYTE  	bIndexSubType; // must be 0
	 BYTE  	bIndexType; // must be AVI_INDEX_OF_CHUNKS
	 DWORD 	nEntriesInUse; //
	 DWORD 	dwChunkId; // .
	 UINT64 qwBaseOffset; // all dwOffsets in aIndex array are
	 // relative to this
	 DWORD  dwReserved3; // must be 0
} __attribute__((packed)) AVI_IX_HEADER_T;

typedef struct AVI_IX_S 
{
	 DWORD dwOffset; // qwBaseOffset + this is absolute file offset
	 DWORD dwSize;   // bit 31 is set if this is NOT a keyframe
} AVI_STANDER_IX_T;

typedef struct 
{
	DWORD  fcc;//avih
	DWORD  cb;//size
	DWORD  dwMicroSecPerFrame;
	DWORD  dwMaxBytesPerSec;
	DWORD  dwPaddingGranularity;
	DWORD  dwFlags;
	DWORD  dwTotalFrames;
	DWORD  dwInitialFrames;
	DWORD  dwStreams;
	DWORD  dwSuggestedBufferSize;
	DWORD  dwWidth;
	DWORD  dwHeight;
	DWORD  dwReserved[4];
} AVIMAINHEADER;

typedef struct //64
{
	DWORD  fcc;//4//strh
	DWORD  cb;//4//size
	DWORD  fccType;//4//vids|auds
	DWORD  fccHandler;//4
	DWORD  dwFlags;//4
	WORD   wPriority;//2
	WORD   wLanguage;//2
	DWORD  dwInitialFrames;//4
	DWORD  dwScale;//4
	DWORD  dwRate; //4/* dwRate / dwScale == samples/second */
	DWORD  dwStart;//4
	DWORD  dwLength; //4/* In units above... */
	DWORD  dwSuggestedBufferSize;//4
	DWORD  dwQuality;//4
	DWORD  dwSampleSize;//4
    struct 
	{
		WORD left;//
		WORD top;//
		WORD right;//
		WORD bottom;//
    } rcFrame;

} AVISTREAMHEADER;

typedef struct tagBITMAPINFOHEADER 
{
	DWORD  biSize;
	DWORD  biWidth;//4
	DWORD  biHeight;//4
	WORD   biPlanes;
	WORD   biBitCount;
	DWORD  biCompression;
	DWORD  biSizeImage;
	DWORD  biXPelsPerMeter;
	DWORD  biYPelsPerMeter;
	DWORD  biClrUsed;
	DWORD  biClrImportant;
} BITMAPINFOHEADER;

typedef struct 
{
	WORD	wFormatTag;
	WORD	nChannels;
	DWORD   nSamplesPerSec;
	DWORD   nAvgBytesPerSec;
	WORD	nBlockAlign;
	WORD	wBitsPerSample;
	WORD	cbSize;
	WORD	reserve;
} WAVEFORMATEX;

typedef struct
{ 
	u32 rv[34];
}BITMAPAUDIODC;
typedef struct
{ 
	u32 rv[2];
}WAVEDECPR;


//AVI_STANDARD
typedef struct STD_VIDS_HEAD_S //268
{
	LIST 				strl_v;				//12 strl vids
	AVISTREAMHEADER 	strh_v; 			//64 stream vids CFG [dwRate = fps, right = width,bottom = height ] updata [dwLength = vframecnt]
	AVI_CHUNK 			strf_v;				//8 stream vids info
	BITMAPINFOHEADER 	bitmapinfo;			//40 CFG[biWidth = width, biHeight = height]
	AVI_CHUNK 			strd_v;				// 8
	BITMAPAUDIODC		bitmapvideodc;		// 136
}STD_VIDS_HEAD;
typedef struct STD_AUDS_HEAD_S //120
{
	LIST 				strl_a;				//12 strl auds
	AVISTREAMHEADER 	strh_a; 		    //64 stream auds CFG [dwRate = samperate<<1],updata [dwLength = aframecnt * audsize / 2]
	AVI_CHUNK 			strf_a;				//8 auds stream info
	WAVEFORMATEX  		wavinfo;			//20 wafe format CFG[nSamplesPerSec = samperate, nAvgBytesPerSec = samperate<<1]
	AVI_CHUNK 			strd_a;				//8
	WAVEDECPR			wavedcpr;			//8
}STD_AUDS_HEAD;




//AVI OPENDML
typedef struct  
{	 
	 WORD  wLongsPerEntry; // must be 4 (size of each entry in aIndex array)
	 BYTE  bIndexSubType; // must be 0 or AVI_INDEX_2FIELD
	 BYTE  bIndexType; // must be AVI_INDEX_OF_INDEXES
	 DWORD nEntriesInUse; // number of entries in aIndex array that
	 // are used
	 DWORD dwChunkId; 	// vids: 00dc;auds: 01wb
	 DWORD dwReserved[3]; // must be 0
} AVISUPERINDEXHEAD;
	 
	 
typedef struct 
{
	 DWORD CompressedBMHeight;
	 DWORD CompressedBMWidth;
	 DWORD ValidBMHeight;
	 DWORD ValidBMWidth;
	 DWORD ValidBMXOffset;
	 DWORD ValidBMYOffset;
	 DWORD VideoXOffsetInT;
	 DWORD VideoYValidStartLine;
} VIDEO_FIELD_DESC;
typedef struct 
{	 
	 DWORD VideoFormatToken;
	 DWORD VideoStandard;
	 DWORD dwVerticalRefreshRate;
	 DWORD dwHTotalInT;
	 DWORD dwVTotalInLines;
	 DWORD dwFrameAspectRatio; 
	 DWORD dwFrameWidthInPixels;
	 DWORD dwFrameHeightInLines;
	 DWORD nbFieldPerFrame;
	 VIDEO_FIELD_DESC FieldInfo;  //32bytes
} VideoPropHeader;
	 
typedef struct 
{
	 DWORD OpenDML_Header;
	 DWORD size;
	 DWORD dwTotalFrames; //video total frame + auds frame
	 DWORD dwFuture[61];
} ODMLExtendedAVIHeader;
typedef struct VIDS_VPRP_HEADER_S
{
	 AVI_CHUNK 			c_vprp;          //8bytes
	 VideoPropHeader 	vprp;			 // 68bytes, Video Properties Header
	 //AVI_CHUNK 		junk;
	 //u8 				fill[512-sizeof(VideoPropHeader) - 16];
}VIDS_VPRP_HEADER;	 
	 
typedef struct 
{
	 LIST riff; 		 //12 {"RIFF",dwsize,"AVIX "};
	 LIST movi; 		 //12 {"LIST",dwsize,"movi"};
} AVIXMOVILIST;

typedef struct ODML_MOVI_HEADER_S  //280
{
	 LIST 					obml;	  //12{"LIST",dwsize = sizeof(ODMLExtendedAVIHeader) + 4,"odml"}; 					
	 ODMLExtendedAVIHeader 	strodml;  //Extended AVI Header (dmlh) 256bytes
	 //AVI_CHUNK 				junk;     //8
	 //u8 					fill[224];   //- 
	 LIST 					list_movi; //12{"LIST",dwsize = qwoffset - ODML_FILE_HEADER + 4 ,"movi"};(updata dwsize) 
}AVIDML_MOVI_HEADER;
typedef struct 
{
	AVIXMOVILIST Avix;
	AVI_CHUNK 	 junkchunk;
	u8 		     fill[512-sizeof(AVIXMOVILIST)-8];
}AVIX_SECTOR_T;






typedef struct DECODE_Q_S
{
	INT8U in;
	INT8U out;
	INT8U cnt;
	INT8U busy;
	AVIINDEXENTRY *stack;
	
}DECODE_Q_T;



#define AVI_IX_HEADER_LEN      			(sizeof(AVI_IX_HEADER_T))    //   32-byte
#define AVI_IX_LEN            			(sizeof(AVI_STANDER_IX_T)) // 8
#define AVI_SUPERINDX_LEN     			(sizeof(AVI_SUPERINDX_T)) // 16

#define AVI_ODML_IX_NUM             	((512-AVI_IX_HEADER_LEN)/AVI_IX_LEN)  


#endif
