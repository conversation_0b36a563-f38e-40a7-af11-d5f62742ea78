/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_BAT_CHARGE_H
#define  __TASK_BAT_CHARGE_H


EXTERN_WINDOW(batChargeWindow);
typedef struct BAT_CHARGE_OP_S
{
    u32 bat_ui_show_time;
    u8  bat_lcd_on;
    u8  bar_win_show;
    u16 main_stride;
	u16 x, y;
	u16 ratio_w, ratio_h;
	u16 dest_w, dest_h;
    
    u16 sub_x, sub_y;
    u16 sub_w, sub_h;
    u16 sub_cur_w;
    u16 sub_step_w;
	u8* main_yaddr;
	u8* main_uvaddr;
	u8* sub_yaddr;
	u8* sub_uvaddr;

}BAT_CHARGE_OP_T;
extern sysTask_T taskBatCharge;
extern BAT_CHARGE_OP_T batChargeOp;

/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskBatChargeBarShowInit(uiRect *rect);
/*******************************************************************************
* Function Name  : batChargeKeyMsgOk
* Description    : batChargeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskBatChargeBarShow(void);



#endif
