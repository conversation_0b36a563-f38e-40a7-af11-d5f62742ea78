@set ScriptDir=%~dp0
@set TargetFile=%1
@set TargetFileDir=%~dp1
@set TargetFileName=%~n1
@set OutFileName=%TargetFileDir%%TargetFileName%

or1k-unknown-elf-objcopy -O binary "%TargetFile%" "%OutFileName%.bin"
::.\script\addreso -i "%OutFileName%.bin" -r .\resource\user_res.bin -o "%OutFileName%.bin"
.\script\addreso -i "%OutFileName%.bin" -r .\resource\user_res.bin -o "bin\Debug\M5.bin"
del "%OutFileName%.bin"
or1k-unknown-elf-size -A "%TargetFile%"
::or1k-unknown-elf-objdump -d "%TargetFile%" >"%OutFileName%.lst"
