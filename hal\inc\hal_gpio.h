/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_GPIO_H
    #define  HAL_GPIO_H




/*******************************************************************************
* Function Name  : hal_gpioInit
* Description    : hal layer .gpio initial for normal using
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GP<PERSON>_P<PERSON>,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir  :  dir. ->GPIO_OUTPUT,GPIO_INPUT
				   u8 pull : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hal_gpioInit(u8 ch,u32 pin,u8 dir,u8 pull);
/*******************************************************************************
* Function Name  : hal_gpioInit
* Description    : hal layer .gpio initial for HIGH VOL IO
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir  :  dir. ->GPIO_OUTPUT,GPIO_INPUT
				   u8 pull : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hal_gpioInit_io1d1(u8 ch,u32 pin,u8 dir,u8 pull);  //HIGH VOL  IO
/*******************************************************************************
* Function Name  : hal_io1d1_softstart
* Description    : hal_io1d1_softstart
* Input          : u8 chanel :IO1_CH_PD1, IO1_CH_PA7 
* 				   u8 soft_en: soft start enable/disable
*                  u8 soft_time: IO1_SOFTTIME_7MS, IO1_SOFTTIME_14MS 
*                  u8 io1pd_en : 200k pulldown enable/disable
* Output         : None
* Return         : None 
*******************************************************************************/
#define  hal_io1d1_softstart  		hx330x_io1d1_softstart
/*******************************************************************************
* Function Name  : hal_io1d1_softstart_clr
* Description    : hal_io1d1_softstart_clr
* Input          : 
* 				   u8 clr_en: 1 :enable softstart function, 0: reset softstart function
* Output         : None
* Return         : None 
*******************************************************************************/
#define hal_io1d1_softstart_clr 	exception_io1d1_softstart_clr
/*******************************************************************************
* Function Name  : hx330x_io1d1_pd_enable
* Description    : hx330x_io1d1_pd_enable
* Input          :
* 				   en: 1: enable pd200k, 0: disable pd200k
* Output         : None
* Return         : none
*******************************************************************************/
#define hal_io1d1_pd_enable			hx330x_io1d1_pd_enable

/*******************************************************************************
* Function Name  : hal_gpioLedInit
* Description    : hal layer .gpio initial for led control
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 pull : pull. ->GPIO_PULLE_FLOATING,GPIO_PULLE_UP,GPIO_PULLE_DOWN,GPIO_PULLE_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hal_gpioEPullSet(u8 ch,u32 pin,u8 led_pull);
/*******************************************************************************
* Function Name  : hal_gpioWrite
* Description    : hal layer .gpio write to output
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 data : data. ->GPIO_LOW,GPIO_HIGH
* Output         : None
* Return         : s32 : 0 success
                             <0 fail 
*******************************************************************************/
#define	hal_gpioWrite			hx330x_gpioDataSet
/*******************************************************************************
* Function Name  : hal_gpioRead
* Description    : hal layer .gpio read input data
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
* Output         : None
* Return         : u8 : 1->high,0->low
*******************************************************************************/
#define hal_gpioRead 			hx330x_gpioDataGet
/*******************************************************************************
* Function Name  : hal_gpioIntInit
* Description    : hal layer .gpio ext irq initial
* Input          : u8 ch : gpio channel->GPIO_INT0_PA8~GPIO_INT15_15
				   u8 trigger : trigger.TRIGGER_LEVEL,TRIGGER_EDGE_RISING,TRIGGER_EDGE_FALLING
				   void (*isr)(void) : call back
* Output         : None
* Return         : u8 : 1->high,0->low
*******************************************************************************/
#define hal_gpioIntInit			hx330x_gpioINTInit
/*******************************************************************************
* Function Name  : hal_gpioLedInit
* Description    : hal layer .gpio hardware led
* Input          : u8 ch : gpio channel->GPIO_LED0_PA8~GPIO_LED11_PF14
                   u8 dir : pull. ->GPIO_PULLE_FLOATING,GPIO_PULLE_UP,GPIO_PULLE_DOWN,GPIO_PULLE_UPDOWN
                   u8 soft : soft->0:soft control,1->hardware
* Output         : None
* Return         : u8 : 1->high,0->low
*******************************************************************************/
#define hal_gpioLedInit			hx330x_gpioLedInit
/*******************************************************************************
* Function Name  : hal_gpioSetDirection
* Bref           : void hal_gpioSetDirection(u8 ch,u32 pin,u8 dir)
* Description	 : hal layer .gpio set gpio direction
* Input 		 : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir  :  dir. ->GPIO_OUTPUT,GPIO_INPUT
* Output		 : None
* Return		 : none
*******************************************************************************/
#define hal_gpioSetDirection      hx330x_gpioDirSet
/*******************************************************************************
* Function Name  : hal_gpioSetPull
* Bref           : void hal_gpioSetPull(u8 ch,u32 pin,u8 pull)
* Description	 : hal layer .gpio set gpio direction
* Input 		 : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 pull : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output		 : None
* Return		 : s32 : 0 success
							 <0 fail 
*******************************************************************************/
#define hal_gpioSetPull          hx330x_gpioPullSet
/*******************************************************************************
* Function Name  : hal_gpioSetFunction
* Bref           : void hal_gpioSetFunction(u8 ch,u32 pin,u8 map)
* Description	 : hal layer .gpio set gpio direction
* Input 		 : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
*                  u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir : map. ->GPIO_FUNC_GPIO,GPIO_FUNC_SFR
* Output		 : None
* Return		 : s32 : 0 success
							 <0 fail 
*******************************************************************************/
#define hal_gpioSetFunction      hx330x_gpioMapSet
/*******************************************************************************
* Function Name  : hal_gpioSetSFR
* Bref           : void hal_gpioSetSFR(u8 type,u8 group)
* Description	 : hal layer .gpio set gpio direction
* Input 		 : u8 type : function table,see : GPIO_MAP_E
				   u8 group: function group
* Output		 : None
* Return		 : s32 : 0 success
							 <0 fail 
*******************************************************************************/
#define hal_gpioSetSFR             hx330x_gpioSFRSet
/*******************************************************************************
* Function Name  : hal_gpioDigitalEnable
* Bref           : void hal_gpioDigitalEnable(u8 ch,u32 pin,u8 en)
* Description    : set gpio digital set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : digital. ->GPIO_DIGITAL_DIS,GPIO_DIGITAL_EN
* Output         : None
* Return         : none
*******************************************************************************/
#define hal_gpioDigitalEnable  hx330x_gpioDigitalSet






#endif


