/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_MD_H
   #define HX330X_MD_H




/*******************************************************************************
* Function Name  : hx330x_mdEnable
* Description    : motion dection enable set
* Input          : u8 en : 1-enable,0-disable
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_mdEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_mdEnable_check
* Description    : mcu layer .motion dectetion enable  check
* Input          : None
* Output         : None
* Return         : bool : true :md is enable , false: md is disable
*******************************************************************************/
bool hx330x_mdEnable_check(void);
/*******************************************************************************
* Function Name  : hx330x_mdInit
* Description    : motion dection enable set
* Input          : u8 cnt : update pre cnt frame. // datasheet says
				   u8 num : block number
				   u8 pixel : pixel dc threashold
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_mdInit(u8 pixel,u8 num,u8 cnt);
/*******************************************************************************
* Function Name  : hx330x_mdXPos
* Description    : motion dection x position  set
* Input          : u16 start : x start position
				   u16 end  : x end position
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_mdXPos(u16 start,u16 end);
/*******************************************************************************
* Function Name  : hx330x_mdYPos
* Description    : motion dection y position set
* Input          : u16 start : y start position
				   u16 end  : y end position
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_mdYPos(u16 start,u16 end);










#endif