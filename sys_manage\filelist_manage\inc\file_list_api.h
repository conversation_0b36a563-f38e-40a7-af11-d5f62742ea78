/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef FILELIST_API_H
#define FILELIST_API_H

#include "file_list_typedef.h"

#define FILE_NAME_DEFAULT_TYPE		0 //0, FILELIST_FLAG_TIME
/*******************************************************************************
* Function Name  : filelist_NameChangeSufType
* Description    : change  file type by filename suffix
* Input          : char *name : file name
* Output         : none                                            
* Return         : type
*******************************************************************************/
int filelist_NameChangeSufType(char *name, char *dst_sufType);
/*******************************************************************************
* Function Name  : filenode_fname_createByIndex
* Description    : filenode_fname_createByIndex, create datetime info
* Input          : FILELIST_NODE_T* filenode: handle filenode
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  int index: fname index
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
int filenode_fname_check(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filenode_fname_createByIndex
* Description    : filenode_fname_createByIndex, create datetime info
* Input          : FILELIST_NODE_T* filenode: handle filenode
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  int index: fname index
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
int filenode_fname_createNew(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filelist_filename_CreateByFname
* Description    : filelist_filename_CreateByFname, if not have datetime info, create datetime
* Input          : char *string: file name string buf
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
* Output         : None
* Return         : None
*******************************************************************************/
char* filenode_filename_CreateByFname(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filenode_filefullname_CreateByFname
* Description    : filenode_filefullname_CreateByFname, if not have datetime info, create datetime
* Input          : char *string: file name string buf
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
* Output         : None
* Return         : None
*******************************************************************************/
char* filenode_filefullname_CreateByFname(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filenode_AddFileByFname
* Description    : add a file to filenode by Fname, if have not datetime info, create it
* Input          : int exp : handle
* Output         : none                                            
* Return         : int
*******************************************************************************/
int filenode_AddFileByFname(FILELIST_NODE_T *filenode,FILELIST_NAME_T *fname, char *name);
/*******************************************************************************
* Function Name  : filenode_Scan
* Description    : scan file node by dir, and add file to both filelist and filenode
* Input          : FILELIST_NODE_T *filenode
* Output         : none                                            
* Return         : int ,file count
*******************************************************************************/
int filenode_Scan(FILELIST_NODE_T *filenode);

/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filenode_api_findfirst(FILELIST_NODE_T *filenode, FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filenode_api_findByFname(FILELIST_NODE_T *filenode, FILELIST_NAME_T *fname);




/*******************************************************************************
* Function Name  : filelist_listFlush
* Description    : file list flush if need
* Input          : int list_num
* Output         : none                                            
* Return         : int : list name count
*******************************************************************************/
int filelist_listFlush(FILELIST_CTRL_T *filelist);
/*******************************************************************************
* Function Name  : filelist_Init
* Description    : initial resource manager
* Input          : 
* Output         : none                                            
* Return         : handle
*******************************************************************************/
void filelist_api_Init(void);
/*******************************************************************************
* Function Name  : filelist_api_nodecreate
* Description    : create a filenode or filelist
* Input          : char *path: filenode path
*                  INT32U type: filenode type
*                  int list: base filelist num, if < 0, create a new filelist
* Output         : none                                            
* Return         : filenode num
*******************************************************************************/
int filelist_api_nodecreate(char *path,INT32U type,int list);
/*******************************************************************************
* Function Name  : filelist_api_nodedestory
* Description    : destory a filelist or filelist node
* Input          : int exp : handle
* Output         : none                                            
* Return         : int
*******************************************************************************/
int filelist_api_nodedestory(int list);
/*******************************************************************************
* Function Name  : filelist_api_scan
* Description    : scan all filenode of filelist
* Input          : int list: filelist num
* Output         : none                                            
* Return         : handle
*******************************************************************************/
int filelist_api_scan(int list);
/*******************************************************************************
* Function Name  : filenode_api_CountGet
* Description    : get a filenode's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filenode count
*******************************************************************************/
int filenode_api_CountGet(int list);
/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filelist_api_CountGet(int list);
/*******************************************************************************
* Function Name  : filelist_api_MaxCountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filelist_api_MaxCountGet(int list);
/*******************************************************************************
* Function Name  : filelist_GetFileNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileNameByIndex(int list, int index);
/*******************************************************************************
* Function Name  : filelist_GetFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileFullNameByIndex(int list, int index, int *file_type);
/*******************************************************************************
* Function Name  : filelist_GetFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetFileShortNameByIndex(int list, int index, int *file_type);
/*******************************************************************************
* Function Name  : filelist_GetFileIndexByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index
* Output         : none                                            
* Return         : 
*******************************************************************************/
u32 filelist_GetFileIndexByIndex(int list, int index);
/*******************************************************************************
* Function Name  : filenode_findFirstFileName
* Description    : find a filenode's first file
* Input          : int list_num
*                  int *index: filelist index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_findFirstFileName(int list, FILELIST_NAME_T *fname,int *index);
/*******************************************************************************
* Function Name  : filenode_findFirstFileName
* Description    : find a filenode's first file
* Input          : int list_num
*                  int *index: filelist index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_findFileNameByFname(int list, FILELIST_NAME_T *fname, int *index);
/*******************************************************************************
* Function Name  : filelist_delFileByIndex
* Description    : del a file(but not lock file) by filelist index
* Input          : int list_num
*                  int index: filelist index
* Output         : none                                            
* Return         : int : < 0 fail, 0:success
*******************************************************************************/
int filelist_delFileByIndex(int list, int index);
/*******************************************************************************
* Function Name  : filelist_delFileByFname
* Description    : del a file(but not lock file) by filelist fname
* Input          : int list_num
*                  FILELIST_NAME_T *fname
* Output         : none                                            
* Return         : int : < 0 fail, 0:success
*******************************************************************************/
int filelist_delFileByFname(int list, FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filelist_listDelAll
* Description    : del all file of filelist(but not lock file)
* Input          : int list: filelist num
* Output         : none                                            
* Return         : int : < 0 fail, >=0 : filelist file cnt
*******************************************************************************/
int filelist_listDelAll(int list);
/*******************************************************************************
* Function Name  : filelist_createNewFileFullName
* Description    : try to create a new file full name 
* Input          : int list_num
*                  int *index: index
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_createNewFileFullName(int list, FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filelist_createNewFileFullNameByFname
* Description    : create a new file full name by index
* Input          : int list_num
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_createNewFileFullNameByFname(int list, FILELIST_NAME_T *fname);

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_addFileByFname(int list, FILELIST_NAME_T *fname);
/*******************************************************************************
* Function Name  : filenode_filefullnameLock
* Description    : change a file full name as lock
* Input          : char * name: full name buf
* Output         : none                                            
* Return         : none
*******************************************************************************/
void filenode_filefullnameLock(char *name);

/*******************************************************************************
* Function Name  : filenode_filefullnameUnlock
* Description    : change a file full name as unlock
* Input          : char * name: full name buf
* Output         : none                                            
* Return         : none
*******************************************************************************/
void filenode_filefullnameUnlock(char *name);

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_fnameLockByIndex(int list, int index);

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filenode_fnameUnlockByIndex(int list, int index);

/*******************************************************************************
* Function Name  : filenode_addFileByIndex
* Description    : add a file to filenode by index
* Input          : int list_num
*                  int index:if need lock , index | FILELIST_FLAG_LOK
* Output         : none                                            
* Return         : int : <0 fail, 0 success
*******************************************************************************/
int filelist_fnameChecklockByIndex(int list, int index);
/*******************************************************************************
* Function Name  : filenode_parentdir_check
* Description    : try to parent dir of find dir or file
* Input          : int list_num
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char *filenode_parentdir_get(int list);
/*******************************************************************************
* Function Name  : filelist_GetLrcFileFullNameByIndex
* Description    : get file full name by filelist index
* Input          : int list, int index, int *file_type
* Output         : none                                            
* Return         : char * : file full name buf
*******************************************************************************/
char* filelist_GetLrcFileFullNameByIndex(int list, int index);
/*******************************************************************************
* Function Name  : managerSpaceCheck
* Description    : check sdc free sapce & delete file
* Input          : 
*                  remain_space: kb
* Output         : none                                            
* Return         : int： <0 fail, > 0 : freespace, need to modify SysCtrl.sd_freesize deamon_fsFreeSize
*******************************************************************************/
int filelist_SpaceCheck(int list,u32* freespace,int need_space);

#endif
