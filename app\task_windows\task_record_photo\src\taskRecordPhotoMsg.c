/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordPhotoWin.c"
extern u8 flash_success;


static u8 show_version_count = 0;
static int recordPhotoKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		
		#if ADD_FORMAT_MENU_EN
			uiOpenWindow(&dateTimeWindow,0,0);
		#else
		if(++show_version_count>4)
		{
			task_com_tips_show(TIPS_VERSION);
			show_version_count = 0;
		}
		#endif

	}
	return 0;
}

static int recordPhotoKeyMsgPhotoFormat(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiOpenWindow(&formatWindow,0,0);

	}
	return 0;
}

static int recordPhotoKeyMsgPhotoSetTime(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiOpenWindow(&dateTimeWindow,0,0);

	}
	return 0;
}


static int recordPhotoKeyMsgPhotoSetPLAY(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	
		app_taskStart(TASK_PLAY_VIDEO,0);
	}
	return 0;
}




static int recordPhotoKeyMsgChangeLed(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_LedOnOffChange();

	}
	return 0;
}
static int recordPhotoKeyMsgLedAdd(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_LedPwm_ctrl_Add();
		recordPhotoLedShow(handle);
	}
	return 0;
}

static int recordPhotoKeyMsgLedDec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_LedPwm_ctrl_Add();
		recordPhotoLedShow(handle);
	}
	return 0;
}


static int recordPhotoKeyMsgPhotoRec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
		{	
			SysCtrl.start_rec_or_takephoto_enable = 1;
			app_taskStart(TASK_RECORD_VIDEO,0);
		}//else
		//	task_com_tips_insert_sd_start();
	}
	return 0;
}



/*******************************************************************************
* Function Name  : recordPhotoKeyMsgOk
* Description    : recordPhotoKeyMsgOk
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int ret;
	static u8 flag = 0;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY);
		//dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
	if(recordPhotoOp.file_remain > 0)//if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		recordPhotoTakePhotoSuccessShow(handle,1);
		app_draw_Service(1);
		flag = 1;
	}
		ret = taskRecordPhotoProcess();
	if(recordPhotoOp.file_remain > 0||flag)//if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		XOSTimeDly(100);
		recordPhotoTakePhotoSuccessShow(handle,0);
		app_draw_Service(1);		
	}		
		flag = 0;


		//dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);

		if((ret>=0)&&SysCtrl.dev_stat_keysound)
		{
			ret = res_music_start(R_ID_MUSIC_TAKE_PHOTO,1,FUN_KEYSOUND_VOLUME);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgUp
* Description    : recordPhotoKeyMsgUp
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED/*||keyState == KEY_CONTINUE*/)
	{
		// task_com_AF_ctrl(1);
		// recordPhotoAF_test_Show(handle);
		// return 0;
#if 1
	#if DEV_SENSOR_FILTER_EN
		app_sensor_filter_ctrl(SENSOR_FILTER_CHANGE_NEXT);
	#else		
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		{
			if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
                SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINB;
			else
				SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
            app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
		}else
		{
		#if (1 == LCDSHOW_CSI_SCALE)
			if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
			{
				while(hal_lcdWinUpdataCheckDone() < 0) hal_wdtClear();
				app_lcdVideoShowScaler_cfg(-1);
				recordPhotoscalerShow(handle);
			}
		#endif
		}
	#endif
#else
	static u32 test_add = 0;
		switch(test_add)
		{
			case 0: hal_sensor_wdr_set(1); deg_Printf("WDR ON\n");break;
			case 1: hal_sensor_wdr_set(0); deg_Printf("WDR OFF\n");break;

			case 2: hal_sensor_EV_set(0); deg_Printf("EV -3\n");break;
			case 3: hal_sensor_EV_set(1); deg_Printf("EV -2\n");break;
			case 4: hal_sensor_EV_set(2); deg_Printf("EV -1\n");break;
			case 5: hal_sensor_EV_set(3); deg_Printf("EV 0\n");break;
			case 6: hal_sensor_EV_set(4); deg_Printf("EV +1\n");break;
			case 7: hal_sensor_EV_set(5); deg_Printf("EV +2\n");break;
			case 8: hal_sensor_EV_set(6); deg_Printf("EV +3\n");break;
			case 9: hal_sensor_EV_set(3); deg_Printf("EV +0\n");break;

			case 10: hal_sensor_awb_scene_set(1);deg_Printf("AWB MODE 1\n");break;
			case 11: hal_sensor_awb_scene_set(2);deg_Printf("AWB MODE 2\n");break;
			case 12: hal_sensor_awb_scene_set(3);deg_Printf("AWB MODE 3\n");break;
			case 13: hal_sensor_awb_scene_set(4);deg_Printf("AWB MODE 4\n");break;
			case 14: hal_sensor_awb_scene_set(5);deg_Printf("AWB MODE 5\n");break;
			case 15: hal_sensor_awb_scene_set(0);deg_Printf("AWB MODE auto\n");break;

			case 16: hal_sensor_ISO_set(1); deg_Printf("ISO100\n");break;
			case 17: hal_sensor_ISO_set(2); deg_Printf("ISO200\n");break;
			case 18: hal_sensor_ISO_set(3); deg_Printf("ISO400\n");break;
			case 19: hal_sensor_ISO_set(0); deg_Printf("ISO AUTO\n");break;
			
			case 20: hal_sensor_scene_mode_set(1); deg_Printf("hgrm_adapt_portrait \n");break;
			case 21: hal_sensor_scene_mode_set(2); deg_Printf("hgrm_adapt_scene \n");break;
			case 22: hal_sensor_scene_mode_set(0); deg_Printf("hgrm_adapt auto \n");break;

			case 23: hal_sensor_sharp_set(0); deg_Printf("sharp low \n");break;
			case 24: hal_sensor_sharp_set(1); deg_Printf("sharp middle \n");break;
			case 25: hal_sensor_sharp_set(2); deg_Printf("sharp low \n");break;
			case 26: hal_sensor_sharp_set(1); deg_Printf("sharp middle \n");break;

			case 27: hal_sensor_beauty_set(1); deg_Printf("beauty on \n");break;
			case 28: hal_sensor_beauty_set(0); deg_Printf("beauty off \n");break;	

		}
		test_add++;
		if(test_add > 28)
		{
			test_add = 0;
		}
#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgDown
* Description    : recordPhotoKeyMsgDown
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED/*||keyState == KEY_CONTINUE*/)
	{
		// task_com_AF_ctrl(0);
		// recordPhotoAF_test_Show(handle);
		// return 0;
		#if (1 == LCDSHOW_CSI_SCALE)
			//== lcd scale==
			if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINA)
			{
				while(hal_lcdWinUpdataCheckDone() < 0) hal_wdtClear();
				app_lcdVideoShowScaler_cfg(1);
				recordPhotoscalerShow(handle);
			}
		#endif

	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgMenu
* Description    : recordPhotoKeyMsgMenu
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgMode
* Description    : recordPhotoKeyMsgMode
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
	//	app_taskStart(TASK_PLAY_VIDEO,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgSD
* Description    : recordPhotoSysMsgSD
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	recordPhotoSDShow(handle);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	//recordPhotoRemainShow(handle);
	//if(SysCtrl.spi_jpg_list < 0)
	{
		if(recordPhotoOp.capture_kick == 0)
		{
			if(SysCtrl.dev_stat_sdc ==SDC_STAT_ERROR )
			{
				uiOpenWindow(&formatWindow,0,0);
				return 0;
			}
				task_com_tips_show(TIPS_TYPE_SD);
			return 0;
		}
	}

	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgUSB
* Description    : recordPhotoSysMsgUSB
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgUSB(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoBatteryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgBattery
* Description    : recordPhotoSysMsgBattery
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgBattery(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		recordPhotoBatteryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgMD
* Description    : recordPhotoSysMsgMD
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgMD(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoMDShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgSec
* Description    : recordPhotoSysMsgSec
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsg1s(winHandle handle,uint32 parameNum,uint32* parame)
{
	static u8 pre_count = 0;
	recordPhotoPowerOnTimeShow(handle);
	recordPhotoSysTimeShow(handle);
	recordPhotoIrLEDShow(handle);
	recordPhotoBatteryShow(handle);
	/*if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if(uiWinIsVisible(winItem(handle,PHOTO_BATERRY_ID)))
			uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),0);
		else
		{
		    uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),1);
			uiWinSetResid(winItem(handle,PHOTO_BATERRY_ID),R_ID_ICON_MTBATTERY5);

		}
	}*/
	if(pre_count != show_version_count)
		pre_count = show_version_count;
	else
		show_version_count = 0;

	if(SysCtrl.poweronFirst)
	{
		SysCtrl.poweronFirst = 0;
		if(SysCtrl.dev_stat_sdc ==SDC_STAT_ERROR )
		{
			uiOpenWindow(&formatWindow,0,0);
			return 0;
		}
	}
	return 0;
}


static int recordPhotoSysMsg500ms(winHandle handle,uint32 parameNum,uint32* parame)
{	
	if(SysCtrl.start_rec_or_takephoto_enable == 2)
	{
		SysCtrl.start_rec_or_takephoto_enable = 0;
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}	
	return 0;
}

static int recordPhotoSysMsg100ms(winHandle handle,uint32 parameNum,uint32* parame)
{	
	



	if(SysCtrl.af_complete)
	{
		recordPhotoAFLineShow(handle,2);
	}else
	{
		recordPhotoAFLineShow(handle,0);
	}
	return 0;
}



static int recordPhotoSysMsgtrigle(winHandle handle,uint32 parameNum,uint32* parame)
{

	hal_lcdUiEnable(UI_LAYER0,1);
	return 0;
}




/*******************************************************************************
* Function Name  : recordPhotoOpenWin
* Description    : recordPhotoOpenWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoOpenWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoOpenWin\n");
	uiWinSetResid(winItem(handle,PHOTO_MODE_ID),R_ID_ICON_MTPHOTO);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	recordPhotoResShow(handle);
	recordPhotoPowerOnTimeShow(handle);
	recordPhotoBatteryShow(handle);
	recordPhotoSysTimeShow(handle);
	recordPhotoIrLEDShow(handle);
	recordPhotoMDShow(handle);
	recordPhotoMonitorShow(handle);
	recordPhotoSDShow(handle);
	recordPhotoMicShow(handle);

	recordPhotoscalerShow(handle);
	recordPhotoTakePhotoSuccessShow(handle,0);
	recordPhotoLedShow(handle);

#if (ENCRYPT_FUNC_SWITCH == 1)	
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL&&!flash_success){//wxn-非加密flash提示
		task_com_tips_show(TIPS_ERROR);
		app_taskStart(TASK_POWER_OFF,0);
	}
#endif

	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoCloseWin
* Description    : recordPhotoCloseWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoCloseWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoCloseWin\n");
	task_com_tips_insert_sd_clear();
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoTouchWin
* Description    : recordPhotoTouchWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoTouchWin(winHandle handle,uint32 parameNum,uint32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		deg_Printf("photoTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSlideOff
* Description    : recordPhotoSlideOff
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSlideOff(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(parameNum!=1)
	{
		deg_Printf("photoSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	if(parame[0] == TP_DIR_DOWN)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MENU,KEY_PRESSED));
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor photoEncodeMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	recordPhotoOpenWin},
	{SYS_CLOSE_WINDOW,	recordPhotoCloseWin},
	{SYS_CHILE_COLSE,	recordPhotoOpenWin},
	{SYS_TOUCH_WINDOW,  recordPhotoTouchWin},
	{SYS_TOUCH_SLIDE_OFF,recordPhotoSlideOff},
	{KEY_EVENT_LED_DEC,	recordPhotoKeyMsgOk},


	{KEY_EVENT_DOWN,	recordPhotoKeyMsgUp},
	{KEY_EVENT_UP,		recordPhotoKeyMsgDown},
	//{KEY_EVENT_MENU,	recordPhotoKeyMsgMenu},
	{KEY_EVENT_MODE,	recordPhotoKeyMsgMode},
//	{KEY_EVENT_LED_ADD,	recordPhotoKeyMsgLedAdd},
	//{KEY_EVENT_LED_DEC,	recordPhotoKeyMsgLedDec},
	{KEY_EVENT_POWER,	recordPhotoKeyMsgLedDec},



	{KEY_EVENT_OK,	recordPhotoKeyMsgChangeLed},


	{KEY_EVENT_PHOTO_REC,	recordPhotoKeyMsgPhotoRec},
	//{KEY_EVENT_CH_LED,	recordPhotoKeyMsgChangeLed},
	#if ADD_FORMAT1_MENU_EN
	{KEY_EVENT_DEL,	recordPhotoKeyMsgPhotoSetTime},
	#else
	{KEY_EVENT_DEL,	recordPhotoKeyMsgPhotoSetPLAY},
	#endif
	//{KEY_EVENT_POWER,	recordPhotoKeyMsgPower},


	
	{SYS_EVENT_SDC,		recordPhotoSysMsgSD},
	{SYS_EVENT_USBDEV,	recordPhotoSysMsgUSB},
	{SYS_EVENT_BAT,		recordPhotoSysMsgBattery},
	{SYS_EVENT_MD,		recordPhotoSysMsgMD},
	{SYS_EVENT_1S,		recordPhotoSysMsg1s},
	{SYS_EVENT_500MS,		recordPhotoSysMsg500ms},
	{SYS_EVENT_100MS,		recordPhotoSysMsg100ms},
	{KEY_EVENT_TRIGLE,		recordPhotoSysMsgtrigle},
	{EVENT_MAX,			NULL},
};
WINDOW(recordPhotoWindow,photoEncodeMsgDeal,recordPhotoWin)




