/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiRectProc(uiWinMsg* msg)
{
	winHandle 	hWin;
	uiRectObj	*prect;
	uiWinObj	*pWin;
	uiResInfo	*infor;
	if(uiWidgetProc(msg))
		return;
	hWin	= msg->curWin;
	prect 	= (uiRectObj*)uiHandleToPtr(hWin);
	pWin	= &(prect->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;
		case MSG_WIN_PAINT:
			if(prect->select == 0)
			{
				if(prect->color != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),prect->color);
			}
			else
			{
				if(prect->selectColor != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),prect->selectColor);
			}
			return;
		case MSG_WIN_UNSELECT:
			if(prect->select == 0)
				return;
			prect->select = 0;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			return;
		case MSG_WIN_SELECT:
			if(prect->select == 1)
				return;
			prect->select = 1;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			return;
		case MSG_WIN_SELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			prect->selectColor = infor->color;
			return;
		case MSG_WIN_UNSELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			prect->color = infor->color;
			return;
		case MSG_WIN_CHANGE_FG_COLOR:
			if(prect->color == msg->para.v)
				return;
			prect->color = msg->para.v;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin		= pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle	= hWin;
			((touchInfor *)(msg->para.p))->touchID		= prect->widget.id;
			((touchInfor *)(msg->para.p))->touchItem	= 0;
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiRectCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id)
{
	winHandle hrect;
	uiRectObj *prect;
	hrect = uiWinCreate(x0,y0,width,height,parent,uiRectProc,sizeof(uiRectObj),WIN_WIDGET|style);
	
	if(hrect != INVALID_HANDLE)
	{
		prect 			= (uiRectObj*)uiHandleToPtr(hrect);
		prect->select 	= 0;
		uiWidgetSetId(hrect,id);
	}
	return hrect;
}
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiRectCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 	hrect;
	uiRectObj	*prect;
	
	hrect = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiRectProc,sizeof(uiRectObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	if(hrect != INVALID_HANDLE)
	{
		prect = (uiRectObj*)uiHandleToPtr(hrect);
		prect->color  		= infor->fontColor;
		prect->selectColor	= infor->fontColorS;
		prect->select 		= 0;
		uiWidgetSetId(hrect,infor->id);
		uiWinSetbgColor(hrect, infor->bgColor);
		
	}
	return hrect;
}