/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_SYNTH_H
       #define  MP3_SYNTH_H
/* single channel PCM selector */
enum {
	MP3_PCM_CHANNEL_SINGLE = 0
};

/* dual channel PCM selector */
enum {
	MP3_PCM_CHANNEL_DUAL_1 = 0,
	MP3_PCM_CHANNEL_DUAL_2 = 1
};

/* stereo PCM selector */
enum {
	MP3_PCM_CHANNEL_STEREO_LEFT  = 0,
	MP3_PCM_CHANNEL_STEREO_RIGHT = 1
};

typedef struct MP3_PCM_S {
	u32			samplerate;             /* sampling frequency (Hz) */
	u32 		bitrate;              	/* sampling bitrate CBR bits/s */
	u16 		channels;              	/* number of channels */
	u16 		length;                	/* number of samples per channel */
	u16 		sizes;					/* the sizes per sample ( 16-bit)*/
	u16 		reserve;				/* */
	mad_pcm_t 	*samples;               /* PCM output samples ptr */
}MP3_PCM_T;

typedef struct MP3_SYNTH_S {
	u32 		phase;                   	/* current processing phase */
	MP3_PCM_T 	pcm;                   		/* PCM output */
	mad_fixed_t synth_filter[2][2][2][16][8]; /* [ch][eo][peo][s][v] */
}MP3_SYNTH_T;
/*******************************************************************************
* Function Name  : mp3_bsio_bit_init
* Description    : mp3_bsio_bit_init: mp3 bit stream init
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/
void mp3_synth_info_set(void);
/*******************************************************************************
* Function Name  : synth_full
* Description    : synth_full: perform full frequency PCM synthesis
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/ 
void synth_full(mad_fixed_t sbsample[18][32],
                u8 ch,  u8 ns, u32 pcm_size);
#endif
