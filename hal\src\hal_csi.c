/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

/*******************************************************************************
* Function Name  : hal_lcdSetRatio
* Description    : hal_lcdSetRatio
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : None
* Return         :
*******************************************************************************/
static void hal_sensorRatioResCal(u8 ratio_w,u8 ratio_h,u16 *width,u16 *height)
{
    int ratw,rath;
 	u16 tar_width, tar_height;
    if(width == NULL || height == NULL)
		return ;

	ratw = *width/ratio_w;
	rath = *height/ratio_h;
	if(ratw == rath)
		return;

	tar_height = ratw*ratio_h; //120*3 = 360
	if(tar_height > *height)
	{
		tar_width = rath*ratio_w; //91*4 = 364
		if(tar_width < *width)
			*width = tar_width;
	}
	else
		*height = tar_height;
    return;
}
/*******************************************************************************
* Function Name  : hal_SensorRatioResolutionGet
* Description    : hal_SensorRatioResolutionGet: when LCDSHOW_RATIO_BY_SENSOR cfg, recal ratio size
* Input          : u16 *width, u16 *height
* Output         : None
* Return         :
*******************************************************************************/
int hal_SensorRatioResolutionGet(u16 *width, u16 *height)
{
	u16 csi_ratio_w, csi_ratio_h;
	if(hal_SensorResolutionGet(&csi_ratio_w, &csi_ratio_h) < 0)
	{
		return -1;
	}
    u8 ratio_w = LCD_RATIO_GET_W(hardware_setup.sensor_ratio_cfg);
    u8 ratio_h = LCD_RATIO_GET_H(hardware_setup.sensor_ratio_cfg);
    if(ratio_w && ratio_h )
    {
		//deg_Printf("[Sensor Ratio][%d:%d] src[%d*%d]", ratio_w, ratio_h, *width, *height);
        hal_sensorRatioResCal(ratio_w,ratio_h, &csi_ratio_w, &csi_ratio_h);
    }
	if(width)
		*width = csi_ratio_w;
	if(height)
		*height = csi_ratio_h;
	//deg_Printf("hal_SensorRatioResolutionGet:%d,%d\n", csi_ratio_w, csi_ratio_h);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_CSI_Sensor_Input
* Description    : hal layer .CSI get data from DVP
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_CSI_Sensor_Input(void)
{
	u8 type;
	SENSOR_API_T 	*senapi 	= hal_SensorApiGet();
	Sensor_Adpt_T 	*senadpt 	= &senapi->sensorAdpt;
	if(senadpt == NULL)
		return;
	type = senadpt->typ & 0xff;
    //mjpAEncCtrl.csi_width = senradpt->pixelw;
	//mjpAEncCtrl.csi_height= senradpt->pixelh;
	hx330x_csiModeSet(0xffffffff,0);  // clear
	hx330x_csiSyncSet(senadpt->hsyn,senadpt->vsyn);
	hx330x_sen_Image_Size_Set(senadpt->senPixelw,senadpt->senPixelh);
	hx330x_csi_in_CropSet(senadpt->senCropW_St,senadpt->senCropW_Ed,senadpt->senCropH_St,senadpt->senCropH_Ed,senadpt->senCropMode);
	hx330x_csiSizeSet(senadpt->pixelw,senadpt->pixelh);
	hx330x_csiTypeSet(senadpt->typ);
	hx330x_csiPrioritySet(senadpt->colrarray);


    if(type == CSI_TYPE_YUV422)
	{
		deg_Printf("--CSI :YUV422\n");	
		if(senadpt->hvb_adapt.pclk > (hardware_setup.sys_clk/3)) //pclk比较高时，需要采用采样边沿模式，同时可能需要开滤波和CSI_TUN
		{
			hx330x_csiModeSet(CSI_MODE_SAMPLE,1);
			hx330x_pclk_digital_fir_Set(senadpt->pclk_dig_fir_step);
			hx330x_pclk_analog_Set(senadpt->pclk_ana_fir_step);
			hx330x_pclk_inv_Set(senadpt->pclk_inv_en);
       	 	hx330x_csi_clk_tun_Set(senadpt->csi_tun);
		}	
	}
	else if((type == CSI_TYPE_RAW8) || (type == CSI_TYPE_RAW10) || (type == CSI_TYPE_RAW12))
	{
		deg_Printf("--CSI : RAW\n");

		hx330x_ispModeSet(ISP_MODE_CFAEN,1);
		hx330x_csiModeSet(CSI_MODE_SAMPLE,1);
		hx330x_pclk_digital_fir_Set(senadpt->pclk_dig_fir_step);
		hx330x_pclk_analog_Set(senadpt->pclk_ana_fir_step);
		hx330x_pclk_inv_Set(senadpt->pclk_inv_en);
        hx330x_csi_clk_tun_Set(senadpt->csi_tun);
	}
	else if (type == CSI_TYPE_COLOR_BAR)
	{
		deg_Printf("--CSI : TEST IMG\n");
		hx330x_ispModeSet(ISP_MODE_CFAEN,1);
		hx330x_csiModeSet(CSI_MODE_SAMPLE|CSI_MODE_TEST_EN,1);
		u32 hblank = (senadpt->hvb_adapt.pclk/senadpt->hvb_adapt.fps/senadpt->hvb_adapt.v_len) - senadpt->pixelw;
		deg_Printf("v_len:%d,h_len:%d\n",senadpt->hvb_adapt.v_len - senadpt->pixelh,hblank);
		hx330x_csiTestModeSet(senadpt->pixelw,senadpt->pixelh,
							  senadpt->hvb_adapt.v_len - senadpt->pixelh, hblank,
							  CSI_TEST_COLORBARS);

	}
}

/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_init(void)
{
	hal_CSI_Sensor_Input();
	hal_isp_init();
}
/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/

#if HAL_CFG_PQTOOL_EN

#ifndef __ISP_MODE
#define __ISP_MODE *(__sfr32_t*)0xB004
#endif

typedef struct SAVE_RAW_CTL_S{
	int fd;
	int state;
	u8* save_buf;
	u32 save_len;
	u32 pre_ispmode;
	u8  uvc_start_flag;
	u8  save_type;  //0: raw, 1: yuv422
	u8  save_byte;
	u8  reserve;
}SAVE_RAW_CTL_T;
ALIGNED(4) static SAVE_RAW_CTL_T save_raw_op;
/*******************************************************************************
* Function Name  : hal_csi_save_raw_isr
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_save_raw_isr(void)
{
	if(save_raw_op.state != SAVE_RAW_START)
		return;
	hx330x_csiEnable(0);
	hx330x_csiOutputSet(CSI_OUTPUT_MJPGEN, 0);
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	int res = fs_write(save_raw_op.fd,(const void *)save_raw_op.save_buf,(UINT) save_raw_op.save_len);
	save_raw_op.state = SAVE_RAW_SUCESS;
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
	}
	res = fs_close(save_raw_op.fd);
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
	}
	if(save_raw_op.save_buf)
	{
		hal_sysMemFree((void*)save_raw_op.save_buf);
		save_raw_op.save_buf = NULL;
	}
	__ISP_MODE = save_raw_op.pre_ispmode;
	if(save_raw_op.uvc_start_flag) uvc_start();
	hx330x_csiEnable(1);

}
/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_csi_save_raw_start(char* filename)
{
	int res = 0;
	if(save_raw_op.state == SAVE_RAW_START)
	{
		return -1;
	}
	if(filename == NULL)
	{
		return -2;
	}
	save_raw_op.uvc_start_flag =  uvc_is_start();
	uvc_stop();
	hx330x_csiEnable(0);
	hal_mjpA_EncodeUninit();
	SENSOR_API_T 	*senapi 	= hal_SensorApiGet();
	Sensor_Adpt_T 	*senadpt 	= &senapi->sensorAdpt;
	save_raw_op.save_type = 0;
	save_raw_op.save_byte = 1;
	switch((senadpt->typ & 0xff))
	{
		case CSI_TYPE_YUV422: save_raw_op.save_type = 1; break;
		case CSI_TYPE_RAW10:  save_raw_op.save_byte = 2; break;
		case CSI_TYPE_RAW12:  return -3;
	}
	save_raw_op.save_len = senadpt->pixelh*senadpt->pixelw * save_raw_op.save_byte *(save_raw_op.save_type?2:1);
	save_raw_op.save_buf = (u8*)hal_sysMemMalloc(save_raw_op.save_len);
	if(save_raw_op.save_buf == NULL)
	{
		deg_Printf("save raw buf fail\n");
		res = -3;
		goto SAVE_RAW_START_FAIL;
	}

	save_raw_op.fd = fs_open(filename,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
	if(save_raw_op.fd < 0)
	{
		deg_Printf("[SAVE RAW] file create fail:%s\n",filename);
		res = -4;
		goto SAVE_RAW_START_FAIL;
	}
	save_raw_op.pre_ispmode = __ISP_MODE;
	__ISP_MODE = ISP_MODE_CSI_SAVEEN;
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_csi_save_raw_isr);
	hx330x_csiMJPEGFrameSet((u32)save_raw_op.save_buf, (u32)save_raw_op.save_buf + senadpt->pixelh*senadpt->pixelw ,
							senadpt->pixelh,senadpt->pixelw*(save_raw_op.save_type?1:2));
	hx330x_csiOutputSet(CSI_OUTPUT_MJPGEN, 1);

	save_raw_op.state = SAVE_RAW_START;
	hx330x_csiEnable(1);

SAVE_RAW_START_FAIL:
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
		if(save_raw_op.save_buf)
		{
			hal_sysMemFree((void*)save_raw_op.save_buf);
			save_raw_op.save_buf = NULL;
		}
		if(save_raw_op.uvc_start_flag) uvc_start();
	}

	return res;
}
/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    :
* Input          : None
* Output         : None
* Return         : SAVE_RAW_FAIL(失败), SAVE_RAW_SUCESS(成功), SAVE_RAW_START(正在进行)
*******************************************************************************/
int hal_csi_save_raw_state(void)
{
	return save_raw_op.state;
}
#endif
