/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiItemMenuOptionProc
* Description    : uiItemMenuOptionProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemMenuOptionProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiWinObj *pWin;
	uiResInfo *infor;
	uiItemMenuOptionObj *pItemMenuOption;
	if(uiWidgetProc(msg))
		return;	
	hWin			= msg->curWin;
	pItemMenuOption = (uiItemMenuOptionObj*)uiHandleToPtr(hWin);
	pWin			= &(pItemMenuOption->widget.win);

	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("menuOption win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pItemMenuOption->select)
			{
				if(pItemMenuOption->selectColor != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuOption->selectColor);
				if(pItemMenuOption->imageSelect.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenuOption->imageSelect);
			}else
			{
				if(pItemMenuOption->color != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenuOption->color);				
				if(pItemMenuOption->image.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenuOption->image);				
			}		
			return;
		case MSG_WIN_CHANGE_RESID:
			if(!RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenuOption->hStr,msg->para.v);
			return;		
		case MSG_WIN_INVALID_RESID:
			uiWinSetResid(pItemMenuOption->hStr,INVALID_RES_ID);
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuOption->imageSelect.id	   		= infor->image;
			pItemMenuOption->imageSelect.bgColor 	= infor->color;	
			pItemMenuOption->selectColor 			= infor->color;		
			uiWinSendMsg(pItemMenuOption->hStr,msg);
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuOption->image.id	   	= infor->image;
			pItemMenuOption->image.bgColor 	= infor->color;
			pItemMenuOption->color 			= infor->color;	
			uiWinSendMsg(pItemMenuOption->hStr,msg);
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			uiWinSendMsg(pItemMenuOption->hStr,msg);
			return;
		case MSG_WIN_SELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuOption->imageSelect.id	   		= infor->image;
			pItemMenuOption->imageSelect.bgColor 	= infor->color;	
			pItemMenuOption->selectColor 			= infor->color;
			return;
		case MSG_WIN_UNSELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenuOption->image.id	   	= infor->image;
			pItemMenuOption->image.bgColor 	= infor->color;
			pItemMenuOption->color 			= infor->color;
			return;
		case MSG_WIN_UNSELECT:
			if(pItemMenuOption->select == 0)
				return;
			pItemMenuOption->select = 0;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuOption->hStr,msg);
			return;
		case MSG_WIN_SELECT:
			if(pItemMenuOption->select)
				return;
			pItemMenuOption->select = 1;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenuOption->hStr,msg);
			return;
		case MSG_WIN_VISIBLE_SET:
			if(msg->para.v)
				uiWinSetVisible(pItemMenuOption->hStr,1);
			else
				uiWinSetVisible(pItemMenuOption->hStr,0);
			break;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiItemCreateMenuOption
* Description    : uiItemCreateMenuOption
* Input          : s16 x0,s16 y0,u16 width,u16 height
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateMenuOption(s16 x0,s16 y0,u16 width,u16 height, u16 style)
{
	winHandle 			 hItemMenuOption;
	uiItemMenuOptionObj *pItemMenuOption;
	hItemMenuOption  = uiWinCreate(x0,y0,width,height,INVALID_HANDLE,uiItemMenuOptionProc,sizeof(uiItemMenuOptionObj),WIN_WIDGET|WIN_NOT_ZOOM);
	if(hItemMenuOption != INVALID_HANDLE)
	{
		pItemMenuOption = (uiItemMenuOptionObj*)uiHandleToPtr(hItemMenuOption);
		
		pItemMenuOption->image.id 				= INVALID_RES_ID;
		pItemMenuOption->image.iconAlign		= ALIGNMENT_CENTER;
		pItemMenuOption->image.bgColor			= INVALID_COLOR;
		pItemMenuOption->image.iconColor		= INVALID_COLOR;
		pItemMenuOption->image.visible			= 1;
		pItemMenuOption->imageSelect.id 		= INVALID_RES_ID;
		pItemMenuOption->imageSelect.iconAlign 	= ALIGNMENT_CENTER;
		pItemMenuOption->imageSelect.bgColor	= INVALID_COLOR;
		pItemMenuOption->imageSelect.iconColor	= INVALID_COLOR;
		pItemMenuOption->imageSelect.visible	= 1;
		pItemMenuOption->hStr 					= uiStringIconCreateDirect(x0, y0, width, height, INVALID_HANDLE, WIN_NOT_ZOOM, INVALID_WIDGET_ID);
		pItemMenuOption->select					= 0;	
		pItemMenuOption->color					= INVALID_COLOR;
		pItemMenuOption->selectColor			= INVALID_COLOR;
		
		uiWidgetSetId(hItemMenuOption,INVALID_WIDGET_ID);
		uiWinSetbgColor(hItemMenuOption, INVALID_COLOR);
	}	
	return hItemMenuOption;
}
