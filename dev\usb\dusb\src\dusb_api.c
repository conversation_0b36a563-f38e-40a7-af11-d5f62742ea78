/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) USB_DEV_CTL usb_dev_ctl;



/*******************************************************************************
* Function Name  : dusb_api_online
* Description    : dusb_api_online
* Input          : None
* Output         : None
* Return         : u8 : >0 usb dev is connect
*******************************************************************************/
u8 dusb_api_online(void)
{
	return usb_dev_ctl.connect;	
}
/*******************************************************************************
* Function Name  : dusb_api_offline
* Description    : dusb_api_offline
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void dusb_api_offline(void)
{

	dusb_api_Uninit();
	usb_dev_ctl.connect = 0;
}
/*******************************************************************************
* Function Name  : dusb_api_Reset
* Description    : dusb_api_Reset
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
static void dusb_api_Reset(void)
{
	hx330x_usb20_dev_reset();
	dusb_ep0_cfg();
	msc_epx_cfg();
	uvc_epx_cfg();
	uac_epx_cfg();
}
/*******************************************************************************
* Function Name  : dusb_api_Suspend
* Description    : dusb_api_Suspend
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
static void dusb_api_Suspend(void)
{

}
/*******************************************************************************
* Function Name  : dusb_api_Resume
* Description    : dusb_api_Resume
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
static void dusb_api_Resume(void)
{

}
/*******************************************************************************
* Function Name  : dusb_api_fun_reg
* Description    : dusb_api_fun_reg
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
static void dusb_api_fun_reg(void)
{
    hx330x_usb20_CallbackRegister(USB_IRQ_RESET,dusb_api_Reset);
	hx330x_usb20_CallbackRegister(USB_IRQ_SUSPEND,dusb_api_Suspend);
	hx330x_usb20_CallbackRegister(USB_IRQ_RESUME,dusb_api_Resume);
	hx330x_usb20_CallbackRegister(USB_IRQ_EP0,dusb_ep0_process);
	hx330x_usb20_CallbackRegister(USB_IRQ_HANDLER,hx330x_usb20DevIRQHanlder);
	
//-------------mass storage---------------------	
	usb_dev_ctl.scsi.disk_rd_func 		= sd_api_Read;
	usb_dev_ctl.scsi.disk_wr_func 		= sd_api_Write;
	usb_dev_ctl.scsi.disk_online_func 	= sd_api_Exist; 
	usb_dev_ctl.scsi.disk_cap_func    	= sd_api_Capacity;
	usb_dev_ctl.scsi.disk_free_func 	= sd_api_unlock;
	usb_dev_ctl.scsi.disk_busy_func 	= sd_api_lock;
	usb_dev_ctl.scsi.disk_stop_func 	= sd_api_Stop;
}
/*******************************************************************************
* Function Name  : dusb_api_Init
* Description    : dusb_api_Init
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void dusb_api_Init(u8 mod)
{
	hx330x_usb20_dev_init();
	dusb_cfg_reg(mod);
	dusb_api_fun_reg();
	dusb_api_Reset();
	hx330x_intEnable(IRQ_USB20,1);	
}
/*******************************************************************************
* Function Name  : dusb_api_Uninit
* Description    : dusb_api_Uninit
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void dusb_api_Uninit(void)
{
	uac_stop();
	uvc_stop();
	hx330x_usb20_CallbackRegister(USB_IRQ_HANDLER,NULL);
	hx330x_intEnable(IRQ_USB20,0);
	hx330x_usb20_uinit();
}
/*******************************************************************************
* Function Name  : dusb_api_Process
* Description    : dev layer.usb device process 
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
bool dusb_api_Process(void)
{
	if(false == rbc_process())
	{
		//debg("4");
		return false;
	}
	if(usb_dev_ctl.uvc_st)
		uvc_isr_process();	
	//uvc_process();	
	if(usb_dev_ctl.returnmaskcombo == 1)
	{
		sdk_returnmask();
		return false;
	}
	return true;
}


/*******************************************************************************
* Function Name  : dev_dusb_init
* Description    : dev_dusb_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_dusb_init(void)
{
//#if DEV_USB_DEV_EN
	//hal_wki1InputEnable(1);
	hal_wki0InputEnable(1);
	hx330x_usb20_dev_init();
	hx330x_usb20_dev_check_init();
	hx330x_usb11_uinit();
	hx330x_sysCpuMsDelay(10);
//#endif
    return 0;
}
/*******************************************************************************
* Function Name  : dev_dusb_ioctrl
* Description    : dev_dusb_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_dusb_ioctrl(INT32U op,INT32U para)
{
	static u32 dev_dusb_state = 0;
	if(op == DEV_DUSB_PWR_CHECK)
	{
		if(para)
			*(INT32U *)para = hal_wki0Read();//hal_wki1Read();
		
	}else if(op == DEV_DUSB_HW_CON_CHECK)
	{
		if(para)
		{
			if(hardware_setup.usb_dev_en && hx330x_usb20_device_check())
				*(INT32U *)para = 1;
			else
				*(INT32U *)para = 0;
		}
	}else if(op == DEV_DUSB_SW_CON_CHECK)
	{
		if(para)
		{
			if(hardware_setup.usb_dev_en && dusb_api_online() && (dev_dusb_state == 0))
			{
				*(INT32U *)para = 1;
			}else
			{
				*(INT32U *)para = 0;
			}
		}
	}
	else if(op == DEV_DUSB_INIT)
	{
		if(hardware_setup.usb_dev_en)
			dusb_api_Init(para);
	}
	else if(op == DEV_DUSB_ONLINE_SET)
	{
		if(dev_dusb_state != para)
		{
			dev_dusb_state = para;
			if(para == 0)
			{
				if(hardware_setup.usb_dev_en)
					dusb_api_offline();
			}
		}
	}
	return 0;
}
