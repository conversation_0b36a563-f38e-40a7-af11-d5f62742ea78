/******************** (C) COPYRIGHT 2022 HX ************************
* File Name          : user_hardware_cfg_fpga
* Author             : WNJ
* Version            : v1
* Date               : 08/01/2022
* Description        : 用户需要根据自己的板子所使用的硬件接口来配置该文件
***************************************************************************/
#ifndef  USER_HARDWARE_CFG_H
    #define  USER_HARDWARE_CFG_H


/********************************************************************************************
*晶振使用情况                                                        
*1. 支持 晶振在线自动检测
* USE_RC：  不使用晶振
* USE_XOSC: 使用晶振
* AUTO_CHECK_XOSC ： 自动检测晶振
**********************************************************************************************/
#define USER_USE_XOSC						USE_XOSC	

/********************************************************************************************
* uart串口相关, 如果 定义宏 HAL_CFG_EN_DBG  为 0, 这里可以不配置                                                 
*1. 确认 uart 波特率：一般为115200
*2. 确认 uart tx pos：即发送给电脑用的IO口, UART0_POS_NONE代表不使用
*3. 如果使用uart rx， 需要再确认uart rx pos,  UART0_POS_NONE代表不使用
**********************************************************************************************/
#define USER_UART_BAUDRATE					115200
#define USER_UART_TX_POS					UART0_POS_PA14
#define USER_UART_RX_POS					UART0_POS_NONE
/********************************************************************************************
* LED 指示灯 , 复用 UART TX                                                
*1. 确认是否使用LED指示灯： led_en = 1， 使用； = 0， 不使用
*2. 确认LED灯极性： led_valid： 0：IO口拉低亮灯， 1： IO口拉高亮灯
*3. 确认LED灯使用的IO口： 
**********************************************************************************************/
#define USER_USE_LED						0 //0:不使用LED(LED其他参数可不管) ;  1: 使用LED
#define USER_LED_VALID						1 //0 - 低电平点亮， 1 - 高电平点亮
#define USER_LED_CH							GPIO_PA
#define USER_LED_PIN						GPIO_PIN14

/********************************************************************************************
*LCD屏相关接口                                                         
*1. 如果不使用LCD屏， 将 LCD_TAG_SELECT 配置为 LCD_NONE
*1. 确认LCD POS：即LCD使用了哪一组的IO mapping
*                可通过查看pin_func表格或该文件末尾定义
*2. 确认LCD 背光：定义LCD背光使用的IO口
*3. 如果是RGB屏： 定义SPI接口的CS，CLK， DAT使用的IO口. CLK和DAT可复用LCD的DATA脚
*4. 如果是MCU屏： 定义MCU 的RS使用的IO口
**********************************************************************************************/
#define		LCD_MAP_POS						LCD_POS9
#define		LCD_BACKLIGHT_CH				GPIO_PA
#define 	LCD_BACKLIGHT_PIN				GPIO_PIN6
//RGB屏使用的SPI接口定义
#define 	LCD_SPI_CS_CH					GPIO_PA	
#define 	LCD_SPI_CS_PIN					GPIO_PIN5
#define 	LCD_SPI_CLK_CH					GPIO_PD
#define		LCD_SPI_CLK_PIN					GPIO_PIN8
#define		LCD_SPI_DAT_CH					GPIO_PD
#define		LCD_SPI_DAT_PIN					GPIO_PIN7
//MCU屏使用的RS 接口定义
#define		LCD_MCU_RS_CH					GPIO_PA
#define 	LCD_MCU_RS_PIN					GPIO_PIN5

/********************************************************************************************
*CMOS-SENSOR相关接口                                                         
*1. 如果不使用CMOS-SENSOR， 将 USER_USE_CMOS_SENSOR 配置为0，将会呈现COLORBAR
*1. 确认CMOS SENSOR POS：   即 CMOS SENSOR DATA/MCLK/PCLK/HSYNC/VSYNC等使用了哪一组的IO mapping
*                           可通过查看pin_func表格或该文件末尾定义，注意CSI_POS2~CSI_POS4复用MIPI的IO口
*2. 确认IIC POS：定义SENSOR使用的IIC接口使用的IO MAP
********************************************************************************************/
#define     USER_USE_CMOS_SENSOR			1		//1：带CMOS-SENSOR， 0：不带 CMOS-SENSOR, 将显示COLOR-BAR		
#define		CMOS_SENSOR_POS					CSI_POS1
#define 	CMOS_SENSOR_IIC_POS				IIC0_POS_PE13PE15	
#define     CMOS_SENSOR_IIC_BAUDRATE		400000   // 400k
//CMOS_SENSOR_IIC_POS定义为 IIC0_POS_MAX 时，由软件指定IO口，配置以下参数
#define		CMOS_SENSOR_IIC_SOFT_DELAY		6
#define		CMOS_SENSOR_IIC_SOFT_SCL_CH		GPIO_PE
#define		CMOS_SENSOR_IIC_SOFT_SCL_PIN	GPIO_PIN13
#define		CMOS_SENSOR_IIC_SOFT_SDA_CH		GPIO_PE
#define		CMOS_SENSOR_IIC_SOFT_SDA_PIN	GPIO_PIN15

/********************************************************************************************
* AD-KEY 按键相关定义，                                                         
*1. 如果不使用 AD-KEY， 将 USER_USE_ADKEY 配置为0，将会呈现COLORBAR
*1. 确认CMOS SENSOR POS：   即 CMOS SENSOR DATA/MCLK/PCLK/HSYNC/VSYNC等使用了哪一组的IO mapping
*                           可通过查看pin_func表格或该文件末尾定义，注意CSI_POS2~CSI_POS4复用MIPI的IO口
*2. 确认IIC POS：定义SENSOR使用的IIC接口使用的IO MAP
*******************************************************************************************/
#define 	USER_USE_ADKEY					1		//1：有按键 ， 0：没有按键
#define 	ADKEY_POS						ADC_CH_PA15
#define     ADKEY_CH						GPIO_PA
#define     ADKEY_PIN						GPIO_PIN15
#define		POWERKEY_USE_ADC				1		//1: 与 ADKEY 使用同一个IO口, 0: 不与 ADKEY使用同一个IO口
//POWERKEY_USE_ADC 为 0时，需要指定 POWERKEY使用的IO口：  POWERKEY_CH， POWERKEY_PIN
#define		POWERKEY_VALID					1		//0：按下时拉低， 1： 按下时拉高
#define		POWERKEY_CH						GPIO_PA 	
#define		POWERKEY_PIN					GPIO_PIN14 	

#define		ADKEY_NUM						4		//4个按键
#define 	ADKEY_USE_INNER_PULLUP_10K		0		//如果外部没有上拉电阻时，将这里配置为1
#define     ADKEY_OUT_PULLUP_RESISTANCE		(10*1000)	//外部使用的上拉电阻阻值，单位(欧)
#define     ADKEY_VALUE_RANGE				(40)	//键值范围，如果电阻太接近，需要减小这个值

//根据板子上按键的电阻和 按键对应的功能定义以下参数，如果长按要转换为其他按键，需定义长按时间 ADKEY_x_LONGKEYTIME和对应的 ADKEY_x_LONGTYPE
//按键1
#define     ADKEY_1_RESITANCE				0				//按键使用的电阻阻值，单位(欧)
#define     ADKEY_1_TYPE					KEY_EVENT_OK	//按键对应的功能
#define		ADKEY_1_LONGTYPE				KEY_EVENT_POWER	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_1_LONGKEYTIME				10				//长按时间，单位30ms
//按键2
#define     ADKEY_2_RESITANCE				1000			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_2_TYPE					KEY_EVENT_DOWN	//按键对应的功能
#define		ADKEY_2_LONGTYPE				KEY_EVENT_POWEROFF	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_2_LONGKEYTIME				30				//长按时间，单位30ms
//按键3
#define     ADKEY_3_RESITANCE				3000			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_3_TYPE					KEY_EVENT_UP	//按键对应的功能
#define		ADKEY_3_LONGTYPE				KEY_EVENT_UP	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_3_LONGKEYTIME				0				//长按时间，单位30ms
//按键4
#define     ADKEY_4_RESITANCE				5600			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_4_TYPE					KEY_EVENT_MODE	//按键对应的功能
#define		ADKEY_4_LONGTYPE				KEY_EVENT_MENU	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_4_LONGKEYTIME				10				//长按时间，单位30ms

/********************************************************************************************
* SD-CARD 卡相关定义，                                                         
*1. 如果不使用 SD-CARD ， 将 USER_USE_SDCARD 配置为 0
*2. 确认SD 卡 IO使用的MAP:
*3. 确认SD 卡使用单线还是4线
*******************************************************************************************/
#define 	USER_USE_SDCARD					1		//1：支持SD卡， 0：不支持SD卡	
#define		SDCARD_POS						SD0_POS0
#define		SDCARD_BUS_WIDTH				SD_BUS_WIDTH4
/********************************************************************************************
* IR-LED 相关定义，                                                         
*1. 如果不使用 IR-LED ， 将 USER_USE_IR 配置为 0
*2. 确认 IR使用的IO口
*******************************************************************************************/
#define		USER_USE_IR						0
#define 	IR_LED_CH						GPIO_PA
#define 	IR_LED_PIN						GPIO_PIN9

/********************************************************************************************
* USB20 DEV 相关定义                                                       
*1. 如果不使用 USB20_DEV连接 PC ， 将 USER_USE_USB_DEV 配置为 0，则USB20 DEV从只做供电
*******************************************************************************************/
#define		USER_USE_USB_DEV				1
/********************************************************************************************
* USB HOST 相关定义 : 支持 USB20 或 USB11 作为主机接设备（例如后拉）
*1. 如果不使用 USB_HOST 连接 设备 ， 将 USER_USE_USB_HOST 配置为 0
*2. 如果使用 USB20 作为HOST，同时 USB20_DEV使用，需要定义 USB_HOST_DOUBLE_BOND为 1
*3. 确认 USB HOST使用 USB1.1 还是 USB2.0, 如果自动检测，则定义为 USBAUTO_CH
*4. 确认 设备 供电 方式， USB_HOST_POWER_IO
*(1) 配置 IO1_CH_NONE ： 表示外围硬件供电常开，没有控制电源开关的IO口
*(2) 配置 IO1_CH_PA7 或 IO1_CH_PD1: 表示使用IO控制电源开关
*5. 确认 设备 插入检测 方式， USB_HOST_DECTCTRL
*(1) 配置 0 ： 不使用硬件DECT IO口，仅由软件判断
*(2) 配置 1 : 使用硬件DECT IO口， 需要定义 USB_HOST_DET_CH 和 USB_HOST_DET_PIN
*******************************************************************************************/
#define		USER_USE_USB_HOST				1
#define		USB_HOST_DOUBLE_BOND			1		//USB20: USB DEV and USB HOST use same DPDM
#define		USB_HOST_CH						USB20_CH//USB20_CH, USB11_CH, AUTO_CH

#define		USB_HOST_POWER_IO				IO1_CH_PA7	//IO1_CH_NONE: POWER常开， IO1_CH_PD1,IO1_CH_PA7：通过这两个IO口控制
#define     USB_HOST_DECTCTRL               1       //1: 需要硬件DECT,    0:  使用软件DECT
#define		USB_HOST_DET_CH					GPIO_PA
#define		USB_HOST_DET_PIN				GPIO_PIN6

/********************************************************************************************
* G-SENSOR 相关定义 :                                                       
*1. 如果不使用 G-SENSOR ， 将 USER_USE_GSENSOR 配置为 0
*2. 确认GSENSOR使用的IIC1 POS: 一般复用UART TX口
*******************************************************************************************/
#define		USER_USE_GSENSOR				0	
#define		GSENSOR_IIC_POS					IIC1_POS_NONE
/********************************************************************************************
* BATTERY 相关定义 :                                                       
*1. 如果不使用 BATTERY 电池 ， 将 USER_USE_BATTERY 配置为 0

*******************************************************************************************/
#define		USER_USE_BATTERY				0

/********************************************************************************************
* SPI1 相关定义 :                                                       
*1. 如果使用SPI1， 需要定义SPI1使用的IO MAP
*******************************************************************************************/
#define		SPI1_POS						SPI1_POS_NONE





	







#endif




