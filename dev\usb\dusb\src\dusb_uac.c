/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) const u8 uac_vol_tbl[4] = {
         //cur
    0x00,//min
    0xff,//max
    0x01,//res
	0x00 //对齐4bytes
};
/*******************************************************************************
* Function Name  : timer_Timer1_Stop
* Description    : timer1 stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_epx_cfg(void)
{
	u32 index_temp  = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS 	= DEV_TXEP_UAC;		
	XSFR_USB20_SIE_TXPKGMAXL  	= 0x00;
	XSFR_USB20_SIE_TXPKGMAXH  	= 0x04;
	XSFR_USB20_SIE_EPTX_CTRL0 	= DUSB_EPTX_CLRDATATOG | DUSB_EPTX_FLUSHFIFO;
	XSFR_USB20_SIE_EPTX_CTRL1 	= DUSB_EPTX_SETASTX|DUSB_EPTX_ISOMODE;

	(&XSFR_USB20_EP1_TXADDR)[DEV_TXEP_UAC*2-2]  = (u32)_USB20_UAC_FIFO_;
	XSFR_USB20_SIE_EPS 	= index_temp;
	usb_dev_ctl.uac_vol_max = MIC_VOLUME_MAX-1;
	usb_dev_ctl.uac_vol_cur = MIC_VOLUME_MAX/2;
}
/*******************************************************************************
* Function Name  : uac_get_volume
* Description    : uac_get_volume
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_get_volume(u8 index)
{
    u8 value;

    /* 0x00=volume, 0x01=GET_MIN, 0x02=GET_MAX, 0x03=GET_RES */
    if(index == 0) {
        if(usb_dev_ctl.uac_vol_cur == 0) {
            value = 0x01;
        }
        else if(usb_dev_ctl.uac_vol_cur >= (usb_dev_ctl.uac_vol_max) ) {
            value = 0xfe;
        } else {
            value = (usb_dev_ctl.uac_vol_cur  * 0x100) / usb_dev_ctl.uac_vol_max;
        }
    } else {
        value = uac_vol_tbl[index-1];
    }
	usb_dev_ctl.uac_vol_buf[0] = value;
    if(index != 3) {
        usb_dev_ctl.uac_vol_buf[1] = 0x80;
    } else {
        usb_dev_ctl.uac_vol_buf[1] = 0x00;
    }
}
/*******************************************************************************
* Function Name  : uac_set_volume
* Description    : uac_set_volume
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_set_volume(u8 * rxbuf)
{
    u32 vol = (usb_dev_ctl.uac_vol_max*rxbuf[0] / 0x100);
    if(usb_dev_ctl.uac_vol_cur !=vol)
    {
        //debghex("set :\r\n",vol);
        usb_dev_ctl.uac_vol_cur = vol;
        hx330x_auadcGainSet(vol);//u8 again
    }
    return true;
}
/*******************************************************************************
* Function Name  : uac_set_mute
* Description    : uac_set_mute
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_set_mute(u8 * rxbuf)
{
	usb_dev_ctl.uac_mute = rxbuf[0];
	if(usb_dev_ctl.uac_mute)
	{
		hal_adc_volume_set(0);
	}else
	{
		hal_adc_volume_set(usb_dev_ctl.uac_vol_cur);
	}
	
    return true;
}
/*******************************************************************************
* Function Name  : uac_unit_ctl_hal
* Description    : uac_unit_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uac_unit_ctl_hal(u8 val, u8 rqu, u8 len)
{
	//UAC_FU_MUTE_CONTROL
	if(val == UAC_FU_MUTE_CONTROL){
		if(rqu == GET_CUR){
			return dusb_ep0_tx((u8*)&usb_dev_ctl.uac_mute,1);
		}
		else if(rqu == SET_CUR){
			dusb_ep0_recieve_set(uac_set_mute);
			return true;
		}
	}
	//UAC_FU_VOLUME_CONTROL
	else if(val == UAC_FU_VOLUME_CONTROL){
		switch(rqu)
		{
			case  GET_CUR:	//usb_ep0_tx(&UacVolCtrlSettings[0],2);break;
			case  GET_RES:  //usb_ep0_tx(&UacVolCtrlSettings[2],2);break;
			case  GET_MIN:  //usb_ep0_tx(&UacVolCtrlSettings[4],2);break;
			case  GET_MAX:  //usb_ep0_tx(&UacVolCtrlSettings[6],2);break;
					uac_get_volume((rqu&0x7f)-1);
					return dusb_ep0_tx(usb_dev_ctl.uac_vol_buf,2);
			case  SET_CUR:
			case  SET_MIN:
			case  SET_MAX:
			case  SET_RES:	
					dusb_ep0_recieve_set(uac_set_volume);
					return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : UacReceiveSetSamplingFreqCallback
* Description    : UacReceiveSetSamplingFreqCallback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool UacReceiveSetSamplingFreqCallback(u8* rxbuf)
{
	memcpy(usb_dev_ctl.uac_samplerate,rxbuf,3);
	debgbuf(usb_dev_ctl.uac_samplerate,3);
	uac_start();
	return true;	
}
/*******************************************************************************
* Function Name  : UacHandleToStreaming
* Description    : UacHandleToStreaming
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool UacHandleToStreaming(u8 val, u8 rqu, u8 len)
{
	//pSetReqCallback = NULL;
	if (rqu == SET_CUR){
		if (val== UAC_EP_SAMPLING_FREQ_CONTROL)
		{
			if (len == 3)
			{
				dusb_ep0_recieve_set(UacReceiveSetSamplingFreqCallback);
				return true;
			}
		}
	}
	if (rqu == GET_CUR)
	{
		if (val == UAC_EP_SAMPLING_FREQ_CONTROL)
		{
			return dusb_ep0_tx(usb_dev_ctl.uac_samplerate,3);
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : uac_start
* Description    : uac_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_start(void)
{
	//ledsta = RECORD;
	u32 frq = (usb_dev_ctl.uac_samplerate[2] << 16) + (usb_dev_ctl.uac_samplerate[1] << 8) + (usb_dev_ctl.uac_samplerate[0] << 0);
	deg_Printf("adc frq:%x\n",frq);
	if(hal_auadcStart(PCM_REC_TYPE_UAC, frq,usb_dev_ctl.uac_vol_cur) == true)
	{
		hx330x_usb20_eptx_register(1, DEV_TXEP_UAC,uac_isr_process);
		usb_dev_ctl.uac_iso_len = (frq*2)/1000;
		usb_dev_ctl.uac_frame_buf = NULL;
		usb_dev_ctl.uac_frame_len = 0;
		usb_dev_ctl.uac_on_flag = 1;
		hx330x_iso20_tx_kick(DEV_TXEP_UAC, (u32)hal_auadcmutebuf_get(), usb_dev_ctl.uac_iso_len);
	}
}
/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uac_stop(void)
{
	if(usb_dev_ctl.uac_on_flag)
	{
		hal_auadcStop();
		usb_dev_ctl.uac_on_flag = 0;
		hx330x_USB20_EPTX_Flush(DEV_TXEP_UAC);
		hx330x_usb20_eptx_register(0, DEV_TXEP_UAC,NULL);
	}

}
/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/

void uac_isr_process(void)
{
	if(usb_dev_ctl.uac_on_flag == 0)
	{
		return;
	}
	hx330x_intCriticalInit();
	hx330x_intCriticalEnter();
	
	if((usb_dev_ctl.uac_frame_buf == NULL)||(usb_dev_ctl.uac_frame_len == 0))
	{
		usb_dev_ctl.uac_frame_buf = hal_auadcBufferGet(&usb_dev_ctl.uac_frame_len,NULL,NULL);
	}
	if(usb_dev_ctl.uac_frame_buf)
	{
		
		if(usb_dev_ctl.uac_frame_len)
		{
			hx330x_iso20_tx_kick(DEV_TXEP_UAC, (u32)usb_dev_ctl.uac_frame_buf, usb_dev_ctl.uac_iso_len);
			usb_dev_ctl.uac_frame_len -= usb_dev_ctl.uac_iso_len;
			usb_dev_ctl.uac_frame_buf += usb_dev_ctl.uac_iso_len;
		}
		if(usb_dev_ctl.uac_frame_len == 0)
		{
			hal_auadcBufferRelease();
			usb_dev_ctl.uac_frame_buf = NULL;
		}
		
	}else
	{
		hx330x_iso20_tx_kick(DEV_TXEP_UAC, (u32)hal_auadcmutebuf_get(), usb_dev_ctl.uac_iso_len);
	}

	hx330x_intCriticalExit();
}













