/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#if FUN_MP3_PLAY_EN
#include "taskPlayMp3FileOpsWin.c"
static u32 cur_file_item;
static char *cur_file_name;
typedef enum{
	MP3_FILE_OPS_PLAY = 0,
	MP3_FILE_OPS_DELETE,
	MP3_FILE_OPS_PREDIR,
}MP3_FILE_OPS;
/*******************************************************************************
* Function Name  : getdelCurResInfor
* Description    : getdelCurResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getplayMp3FileOpsResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image = INVALID_RES_ID;
	if(str)
	{
		switch(item)
		{
			case MP3_FILE_OPS_PLAY:	 	*str = RAM_ID_MAKE("PLAY"); break;
			case MP3_FILE_OPS_DELETE:	*str = RAM_ID_MAKE("DELETE"); break;
			case MP3_FILE_OPS_PREDIR:  	*str = RAM_ID_MAKE("PRE DIR"); break;
			default: *str = INVALID_RES_ID; break;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsOpenWin
* Description    : playMp3FileOpsOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3FileOpsOpenWin\n");
	
	cur_file_item = parame[0];
	cur_file_name = (char*)parame[1];
	uiItemManageSetHeightAvgGap(winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	Rh(40));
	uiItemManageCreateItem(		winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	uiItemCreateMenuOption,	getplayMp3FileOpsResInfor, 3);
	uiItemManageSetCharInfor(	winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	DEFAULT_FONT,	ALIGNMENT_CENTER,	R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	R_ID_PALETTE_Gray);
	uiItemManageSetCurItem(		winItem(handle,PLAYMP3FILEOPS_SELECT_ID),	0);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsCloseWin
* Description    : playMp3FileOpsCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3FileOpsCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsWinChildClose
* Description    : playMp3FileOpsWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3FileOpsWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsKeyMsgOk
* Description    : playMp3FileOpsKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;	
		//}
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYMP3FILEOPS_SELECT_ID));
		if(item == MP3_FILE_OPS_PREDIR)
		{
			if(taskPlayMp3ParentDirOpen() < 0)
			{
				uiOpenWindow(&tipsWindow,0,2,"[FAIL] ROOT DIR",2);
			}else
			{
				if(mp3_dec_sta() >= MP3_DEC_START)
					task_playMp3_op.playfirstIndex = task_playMp3_op.openListTotal;	//当前音乐播放完继续播放打开的列表
				else
					task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
				uiWinDestroy(&handle);
			}
		}else if(item == MP3_FILE_OPS_PLAY)
		{
			if(mp3_dec_sta() >= MP3_DEC_START && 
			   task_playMp3_op.playCurDirIndex == task_playMp3_op.opendirindex && 
			   cur_file_item == SysCtrl.file_index)//当前歌曲正在播放
			{
				if(mp3_dac_sta() == MP3_DAC_PAUSE) //暂停则恢复播放
				{
					mp3_dac_resume();
				}
			}else
			{
				if(mp3_dec_sta() >= MP3_DEC_START)
					mp3_api_stop();
				task_playMp3_op.playfirstIndex = cur_file_item;
				taskPlayMp3MainStart(cur_file_item);
			}
			if(mp3_dec_sta() >= MP3_DEC_START)
				uiOpenWindow(&playMp3SubWindow, 0, 0);	
			else
				uiWinDestroy(&handle);
		}else if(item == MP3_FILE_OPS_DELETE)
		{
			if(mp3_dec_sta() >= MP3_DEC_START && 
			   task_playMp3_op.playCurDirIndex == task_playMp3_op.opendirindex && 
			   cur_file_item == SysCtrl.file_index)//当前歌曲正在播放
			{
				mp3_api_stop();
			}
			deg_Printf("delete : %s.",cur_file_name);
			if(f_unlink(cur_file_name)==FR_OK)
			{
				deg_Printf("->ok\n");
				if(task_playMp3_op.playCurDirIndex == task_playMp3_op.opendirindex)
				{
					filelist_delFileByIndex(SysCtrl.mp3_list,cur_file_item);
					task_playMp3_op.openListTotal = filelist_api_CountGet(SysCtrl.mp3_list);
					if(SysCtrl.file_index >= task_playMp3_op.openListTotal && task_playMp3_op.openListTotal > 0)
						SysCtrl.file_index = task_playMp3_op.openListTotal -1;
						
				}
				task_com_sdc_freesize_check();
				uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_SUCCESS,2);
			}
			else
			{
				deg_Printf("->fail\n");
				uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_FAILED,2);
			}			
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsKeyMsgUp
* Description    : playMp3FileOpsKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}
		uiItemManagePreItem(winItem(handle,PLAYMP3FILEOPS_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsKeyMsgDown
* Description    : playMp3FileOpsKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}		
		uiItemManageNextItem(winItem(handle,PLAYMP3FILEOPS_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsKeyMsgMode
* Description    : playMp3FileOpsKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(mp3_dec_sta() < MP3_DEC_START)
		{
			//task_com_keysound_play();	
			//PLAYMP3MAIN_WAIT_KEYSOUND_END;				
			app_taskChange();
		}else
		{
			uiOpenWindow(&playMp3SubWindow, 0, 0);
		}		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsKeyMsgMenu
* Description    : playMp3FileOpsKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);	
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3FileOpsSysMsgSD
* Description    : playMp3FileOpsSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3FileOpsSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) 
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			mp3_api_stop();
		}
		uiOpenWindow(&noFileWindow, 0, 1, "no mp3 file");
	}
	return 0;
}



ALIGNED(4) msgDealInfor playMp3FileOpsMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playMp3FileOpsOpenWin},
	{SYS_CLOSE_WINDOW,		playMp3FileOpsCloseWin},
	{SYS_CHILE_COLSE,		playMp3FileOpsWinChildClose},
	{KEY_EVENT_OK,			playMp3FileOpsKeyMsgOk},
	{KEY_EVENT_UP,			playMp3FileOpsKeyMsgUp},
	{KEY_EVENT_DOWN,		playMp3FileOpsKeyMsgDown},
	{KEY_EVENT_MODE,		playMp3FileOpsKeyMsgMode},
	{KEY_EVENT_MENU,		playMp3FileOpsKeyMsgMenu},
	{SYS_EVENT_SDC,			playMp3FileOpsSysMsgSD},

	{EVENT_MAX,NULL},
};

WINDOW(playMp3FileOpsWindow,playMp3FileOpsMsgDeal,playMp3FileOpsWin)

#endif
