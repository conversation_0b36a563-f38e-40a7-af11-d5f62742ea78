/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_DIALOG_H
#define UI_WIN_DIALOG_H

/*******************************************************************************
* Function Name  : uiDialogCreate
* Description    : uiDialogCreate: first win must not be widget
* Input          : widgetCreateInfor* pWidgets,uiWinCB cb,winHandle parent
* Output         : none
* Return         : none
*******************************************************************************/
winHandle uiDialogCreate(widgetCreateInfor* pWidgets,uiWinCB cb,winHandle parent, void* winInfor);
/*******************************************************************************
* Function Name  : uiDialogItem
* Description    : uiDialogItem: get widget with item id
* Input          : winHandle hDialog,u16 id
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiDialogItem(winHandle hDialog,u16 id);
#endif
