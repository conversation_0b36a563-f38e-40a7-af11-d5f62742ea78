/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"

#if MEDIA_IMAGE_DECODE_EN
/*******************************************************************************
* Function Name  : jpg_decode
* Description    : jpg decode
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeSubCheck(JPG_DEC_ARG *arg)
{
	if(arg->src_type == MEDIA_SRC_FS)
	{
		u32 offset;
		if(fs_seek(arg->fd, arg->jpgsize - 12, 0) < 0)
		{
			return -1;
		}
		if(fs_read(arg->fd, arg->jpgbuf, 12) < 0)
		{
			return -2;
		}
		if((LDB_WORD(&arg->jpgbuf[0]) == 0xFFFE) &&
		   (LDB_WORD(&arg->jpgbuf[2]) == 0x000A) &&
		   (LDB_DWORD(&arg->jpgbuf[8]) == 0x55AA55AA))
		{
			offset = LDB_DWORD(&arg->jpgbuf[4]);
			if(offset < arg->jpgsize)
			{
				fs_seek(arg->fd, offset, 0);
				arg->jpgsize = arg->jpgsize - offset;
				deg_Printf("IMG SUB check:%x\n", offset);
				return 0;
			}
		}
		fs_seek(arg->fd, 0, 0);

	}
	deg_Printf("IMG NO SUB\n");
	return -1;
}
/*******************************************************************************
* Function Name  : jpg_decode
* Description    : jpg decode
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeStart(JPG_DEC_ARG *arg)
{
	int ret = 0;
	u32 packet_decode = 0;
	u32 packet_size;
	u32 packet_end;
	u32 packet_size_t;
	u32 sub_img_flag = 0;
	int fd;
	int (*f_read)(int addr,void *buffer,int size) = NULL;
	if(arg == NULL || arg->type != MEDIA_FILE_JPG)
		return -1;
	u32 mem_rem = hal_sysMemRemain();
	//mem_rem = 10*1024; //TEST
	if(mem_rem < 4096)
	{
		return -1;
	}
	if(arg->src_type == MEDIA_SRC_FS)
	{
		if(arg->fd < 0)
			return -1;
		arg->jpgsize = fs_size(arg->fd);
		fd = arg->fd;
		f_read = fs_read;

	}else if(arg->src_type == MEDIA_SRC_NVFS)
	{
		fd = nv_open(arg->fd);
		if(fd < 0)
			return -1;
		arg->jpgsize = nv_size(arg->fd);
		f_read = nv_read;

	}else if(arg->src_type == MEDIA_SRC_RAM)
	{

	}else
	{
		deg_Printf("jpg dec err src_type\n");
	}
	if(f_read)
	{
		if(arg->jpgsize > mem_rem)
		{
			packet_decode = 1;
			packet_end  = 0;
			packet_size = mem_rem;
			//deg_Printf("jpg dec packet:%d, %d\n", packet_size,arg->jpgsize );
		}else
		{
			packet_size = arg->jpgsize;
		}
		arg->jpgbuf = (u8*)hal_sysMemMalloc(packet_size);
		if(arg->jpgbuf)
		{

			if(arg->src_type == MEDIA_SRC_FS)
			{
				if(imageDecodeSubCheck(arg) >= 0)
				{
					sub_img_flag = 1;
					if(packet_size > arg->jpgsize)
					{
						packet_size = arg->jpgsize;
						packet_decode = 0;
					}

				}
			}

			if(f_read(fd,arg->jpgbuf,packet_size) < 0)
			{
				ret = -2;
				goto END_JPG_DEC;
			}
			if(arg->src_type == MEDIA_SRC_NVFS)
			{
				fd += packet_size;
			}
		}

	}
	if(arg->jpgbuf == NULL || arg->jpgsize == 0)
	{
		deg_Printf("jpg dec buf err\n");
		return -1;
	}
	if(hal_mjpHeaderParse(arg->jpgbuf)<0)
	{
		ret = -3;
		//deg_Printf("headparse fail\n");
		goto END_JPG_DEC;
    }
	hal_mjpDecodeGetResolution(&arg->src_width,&arg->src_height);
	if(arg->src_width > 4032 || arg->src_height  >  4032)
	{
		ret = -15;
		//deg_Printf("headparse fail\n");
		goto END_JPG_DEC;
    }
	if(arg->dst_width > arg->src_width || arg->dst_height  >  arg->src_height)
	{
		if(arg->p_lcd_buffer)
		{
			arg->dst_width = hx330x_min(arg->src_width, arg->dst_width);
			arg->dst_height = hx330x_min(arg->src_width, arg->dst_height);
			hal_lcdVideoFrameFlush(arg->p_lcd_buffer,arg->p_lcd_buffer->posX,arg->p_lcd_buffer->posY,arg->dst_width,arg->dst_height,arg->p_lcd_buffer->destw,arg->p_lcd_buffer->desth);
			arg->yout 	= arg->p_lcd_buffer->y_addr;
			arg->uvout 	= arg->p_lcd_buffer->uv_addr;
		}else
		{
			ret = -16;
			//deg_Printf("headparse fail\n");
			goto END_JPG_DEC;
		}
	}
	if(sub_img_flag == 0 && packet_decode == 0 && arg->src_height > (arg->dst_height*4))
	{
		arg->step = 2;
	}else
	{
		arg->step = 1;
	}
	if(arg->step > 1)
	{
		arg->step_dst_width 	= (arg->dst_width * 2)&~0x1f;
		arg->step_dst_height	= (arg->dst_height * 2);
		if(arg->step_dst_width > 1280)
			arg->step_dst_width = 1280;
		if(arg->step_dst_height > 720)
			arg->step_dst_height = 720;
		//deg_Printf("step [%d,%d]\n", arg->step_dst_width, arg->step_dst_height);
		arg->step_yout			= (u8*)hal_sysMemMalloc(arg->step_dst_width*arg->step_dst_height*3/2);
		if(arg->step_yout == NULL)
		{
			goto JPG_DEC_ONCE;
		}
		arg->step_uvout = arg->step_yout + arg->step_dst_width*arg->step_dst_height;
		if(hal_mjpegDecodePicture_noisr(arg->jpgbuf,(u8*)arg->step_yout,(u8*)arg->step_uvout,arg->step_dst_width,arg->step_dst_height) == false)
		{
			ret = -5;
			goto END_JPG_DEC;
		}
		if(hx330x_mjpB_Decode_check() == false)
		{
			ret = -6;
			goto END_JPG_DEC;
		}
		hal_mjpDecodeStop();
		hx330x_mjpA_EncodeInit(1,0);
		hx330x_mjpA_Encode_inlinebuf_init((u32)arg->step_yout,(u32)arg->step_uvout);
		hx330x_mjpA_EncodeSizeSet(arg->step_dst_width,arg->step_dst_height, arg->step_dst_width,arg->step_dst_height);
		hx330x_mjpA_EncodeQuilitySet(JPEG_Q_75);
		hx330x_mjpA_EncodeBufferSet((u32)arg->jpgbuf,(u32)(arg->jpgbuf+arg->jpgsize));
		hx330x_mjpA_EncodeInfoSet(0);
		hx330x_mjpA_Encode_manual_on();
		if(hx330x_mjpA_Encode_check() == false)
		{
			ret = -7;
			goto END_JPG_DEC;
		}
		if(hal_mjpHeaderParse(arg->jpgbuf)<0)
		{
			ret = -8;
			deg_Printf("headparse fail\n");
			goto END_JPG_DEC;
		}
	}
JPG_DEC_ONCE:
	if(packet_decode)
	{
		
		if(hal_mjpegDecodePicture_packet(arg->jpgbuf,arg->jpgbuf + packet_size, arg->yout,arg->uvout,arg->dst_width,arg->dst_height) == false)
		{
			ret = -11;
			goto END_JPG_DEC;
		}
		packet_size_t = 0;
		while(1)
		{
			hal_wdtClear();
			packet_size_t += packet_size;
			ret = hx330x_mjpB_DecodePacket_check();
			if(ret == 0)
			{
				break;
			}else if(ret > 0) //packet end
			{
				if(packet_end)
				{
					ret = -14;
					goto END_JPG_DEC;
				}
				if(arg->jpgsize <= (packet_size_t + packet_size) )
				{
					packet_size = arg->jpgsize - packet_size_t;
					packet_end  = 1;
				}
				if(f_read(fd,arg->jpgbuf,packet_size) < 0)
				{
					ret = -12;
					goto END_JPG_DEC;
				}
				if(arg->src_type == MEDIA_SRC_NVFS)
				{
					fd += packet_size;
				}
				if(packet_end)
				{
					hx330x_mjpB_DecodeInputResume((u32)arg->jpgbuf,0);
				}else
				{
					hx330x_mjpB_DecodeInputResume((u32)arg->jpgbuf,((u32)arg->jpgbuf + packet_size)|(1 << 29));
				}
				
				hx330x_mjpB_Decode_InResume();

			}else
			{
				ret = -13;
				goto END_JPG_DEC;
			}
		}
	}else
	{
		if(hal_mjpegDecodePicture_noisr(arg->jpgbuf,arg->yout,arg->uvout,arg->dst_width,arg->dst_height) == false)
		{
			ret = -9;
			goto END_JPG_DEC;
		}
		if(hx330x_mjpB_Decode_check() == false)
		{
			ret = -10;
			goto END_JPG_DEC;
		}
	}

	hal_mjpDecodeStop();
	hal_mjpDecodeSetResolution(arg->src_width,arg->src_height);
	if(sub_img_flag)
	{
		if(fs_seek(arg->fd, 0, 0) < 0)
		{
			ret = -12;
			goto END_JPG_DEC;
		}
		if(f_read(fd,arg->jpgbuf,packet_size) < 0)
		{
			ret = -13;
			goto END_JPG_DEC;
		}
		if(hal_mjpHeaderParse(arg->jpgbuf)<0)
		{
			ret = -14;
			//deg_Printf("headparse fail\n");
			goto END_JPG_DEC;
		}
		hal_mjpDecodeGetResolution(&arg->src_width,&arg->src_height);

	}
	
END_JPG_DEC:
	if(arg->src_type != MEDIA_SRC_RAM)
		hal_sysMemFree((void *)arg->jpgbuf);
	if(arg->step_yout)
		hal_sysMemFree((void *)arg->step_yout);
	if(ret < 0)
		deg_Printf("JPG DEC fail:%d\n",ret);
	return ret;
}
/*******************************************************************************
* Function Name  : imageDecodeSpiStart
* Description    : imageDecodeSpiStart
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeSpiStart(JPG_DEC_ARG *arg)
{
	int ret = 0;
	u32 size;
	deg_Printf("imageDecodeSpiStart\n");
	if(arg == NULL || arg->type != MEDIA_FILE_JPG || arg->src_type != MEDIA_SRC_NVJPG || arg->fd < 0)
	{
		deg_Printf("imageDecodeSpiStart 11111\n");
		return -1;
	}
		
	if(arg->jpgsize == 0)
		arg->jpgsize = nv_jpgfile_size();
	arg->jpgbuf = (u8*)hal_sysMemMalloc(arg->jpgsize);
	if(arg->jpgbuf == NULL || arg->jpgsize == 0)
	{
		deg_Printf("jpg dec buf err\n");
		return -1;
	}
	deg_Printf("arg->jpgbuf:%x,%x\n", arg->jpgbuf, arg->jpgsize);
	if(nv_jpgfile_read (arg->jpgbuf,arg->jpgsize, &size)!= NV_OK || size != arg->jpgsize)
	{
		ret = -2;
		//deg_Printf("headparse fail\n");
		goto END_JPG_DEC;
	}


	if(hal_mjpHeaderParse(arg->jpgbuf)<0)
	{
		ret = -3;
		//deg_Printf("headparse fail\n");
		goto END_JPG_DEC;
    }
	hal_mjpDecodeGetResolution(&arg->src_width,&arg->src_height);

	if(arg->p_lcd_buffer &&(arg->dst_width > arg->src_width || arg->dst_height  >  arg->src_height))
	{
		arg->dst_width = hx330x_min(arg->src_width, arg->dst_width);
		arg->dst_height = hx330x_min(arg->src_width, arg->dst_height);
		hal_lcdVideoFrameFlush(arg->p_lcd_buffer,arg->p_lcd_buffer->posX,arg->p_lcd_buffer->posY,arg->dst_width,arg->dst_height,arg->p_lcd_buffer->destw,arg->p_lcd_buffer->desth);
		arg->yout 	= arg->p_lcd_buffer->y_addr;
		arg->uvout 	= arg->p_lcd_buffer->uv_addr;
	}
	if(hal_mjpegDecodePicture_noisr(arg->jpgbuf,arg->yout,arg->uvout,arg->dst_width,arg->dst_height) == false)
	{
		ret = -9;
		goto END_JPG_DEC;
	}
	if(hx330x_mjpB_Decode_check() == false)
	{
		ret = -10;
		goto END_JPG_DEC;
	}

	hal_mjpDecodeStop();
	hal_mjpDecodeSetResolution(arg->src_width,arg->src_height);
END_JPG_DEC:
	hal_sysMemFree((void *)arg->jpgbuf);
	arg->jpgbuf = NULL;
	if(ret < 0)
		deg_Printf("JPG DEC fail:%d\n",ret);
	return ret;
}
/*******************************************************************************
* Function Name  : imageDecodeGetResolution
* Description	 : image decode get jpeg resolution
* Input 		 : INT16U *width,INT16U *height
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageDecodeGetResolution(INT16U *width,INT16U *height)
{
	hal_mjpDecodeGetResolution(width,height);

	return 0;
}
/*******************************************************************************
* Function Name  : imageDecodeDirect
* Description    : imageDecodeDirect : should after hal_mjpHeaderParse 
* Input          : JPG_DEC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int imageDecodeDirect(u16 dst_width, u16 dst_height, u8* src_jpg_buf, u8* dst_start_addr)
{
	u16 src_width, src_height;
	u16 decode_width, decode_height,decode_stride;
	u32 decode_y_size;
	u8* encode_buf = NULL;
	int ret = 0;
	if(hal_mjpHeaderParse(src_jpg_buf)<0)
	{
		ret = -1;
		goto DECODE_FAIL;
	}
	hal_mjpDecodeGetResolution(&src_width,&src_height);
	deg_Printf("[IMAGE DECODE] res %d - %d\n",src_width,src_height);
	if(dst_width <= src_width && dst_height <= src_height) //only decode will be ok
	{
		decode_width  	= dst_width;
		decode_height 	= dst_height;
		encode_buf		= NULL;
		
	}else  //need to decode -> encode(scale up) -> decode  
	{
		decode_width	= hx330x_min(src_width, dst_width);
		decode_height   = hx330x_min(src_height, dst_height);
		encode_buf		= (u8*)hal_sysMemMalloc(200*1024); //100K
		if(encode_buf == NULL)
		{
			ret = -2;
			goto DECODE_FAIL;
		}
	}
	decode_stride = (decode_width + 0x1f)&~0x1f;
	decode_y_size = decode_stride*decode_height;
	if(encode_buf)
	{
		if(hal_mjpegDecodePicture_noisr((u8*)src_jpg_buf,(u8*)dst_start_addr,(u8*)(dst_start_addr + decode_y_size),decode_width,decode_height) == false)
		{
			ret = -3;
			goto DECODE_FAIL;
		}
		if(hx330x_mjpB_Decode_check() == false)
		{
			ret = -4;
			goto DECODE_FAIL;
		}	
		hal_mjpDecodeStop();
		hx330x_mjpA_EncodeInit(1,0);
		hx330x_mjpA_Encode_inlinebuf_init((u32)dst_start_addr,(u32)dst_start_addr + decode_y_size);
		hx330x_mjpA_EncodeSizeSet(decode_width,decode_height, dst_width,dst_height);
		hx330x_mjpA_EncodeQuilitySet(JPEG_Q_40);
		hx330x_mjpA_EncodeBufferSet((u32)encode_buf,(u32)(encode_buf + 200*1024));
		hx330x_mjpA_EncodeInfoSet(0);
		hx330x_mjpA_Encode_manual_on();
		if(hx330x_mjpA_Encode_check() == false)
		{
			ret = -5;
			goto DECODE_FAIL;
		}
		if(hal_mjpHeaderParse(encode_buf)<0)
		{
			ret = -6;
			goto DECODE_FAIL;
		}
		src_jpg_buf = encode_buf;
		decode_y_size = ((dst_width + 0x1f)&~0x1f)*dst_height;
	}
	if(hal_mjpegDecodePicture_noisr((u8*)src_jpg_buf,(u8*)dst_start_addr,(u8*)(dst_start_addr + decode_y_size),dst_width,dst_height) == false)
	{
		ret = -7;
		goto DECODE_FAIL;
	}
	if(hx330x_mjpB_Decode_check() == false)
	{
		ret = -8;
		goto DECODE_FAIL;
	}	
	hal_mjpDecodeStop();
DECODE_FAIL:
	if(encode_buf)
		hal_sysMemFree(encode_buf);
	if(ret < 0)
		deg_Printf("JPG DEC fail:%d\n",ret);
	return ret;

}
#endif







