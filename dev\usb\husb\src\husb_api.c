/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"


ALIGNED(4) HUSB_HANDLE  husb_ctl[HUSB_CH_NUM];

/*******************************************************************************
* Function Name  : husb_api_handle_get
* Description    : husb_api_handle_get
* Input          : None
* Output         : None
* Return         : HUSB_HANDLE *
*******************************************************************************/
HUSB_HANDLE* husb_api_handle_get(u8 ch)
{
#if HUSB_CH_NUM == 2
	if(ch == USB20_CH)
		return &husb_ctl[0];
	else if(ch == USB11_CH)
		return &husb_ctl[1]; 
	else
		return NULL;
#elif HUSB_CH_NUM == 1
	if((ch == USB20_CH) ||(ch == USB11_CH))
		return &husb_ctl[0];
	else
		return NULL;
#else
	return NULL;
#endif
	
}
/*******************************************************************************
* Function Name  : husb_api_u20_connect
* Description    : husb_api_u20_connect
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_api_u20_connect(void)
{
	deg_Printf("[ HUSB20 INSERT]\n");
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB20_CH);
	memset((u8*)pHusb_handle, 0, sizeof(HUSB_HANDLE));
	pHusb_handle->usbsta.device_sta  = USB_INSERT;
	pHusb_handle->usbsta.ch 		 = USB20_CH;
	pHusb_handle->usbsta.phcdtrb     = (HUSB_HCDTRB *)husb_enum_hcdtrb;
	hx330x_usb20_host_speed_connect();
	husb_api_ep0_kick(pHusb_handle);
}
static void husb_api_u11_connect(void)
{
	deg_Printf("[ HUSB11 INSERT]\n");
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB11_CH);
	memset((u8*)pHusb_handle, 0, sizeof(HUSB_HANDLE));
	pHusb_handle->usbsta.device_sta = USB_INSERT;
	pHusb_handle->usbsta.ch 		 = USB11_CH;
	pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)husb_enum_hcdtrb;
	hx330x_usb11_host_speed_connect();
	husb_api_ep0_kick(pHusb_handle);
}
/*******************************************************************************
* Function Name  : husb_api_u20_remove
* Description    : husb_api_u20_remove
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_u20_remove(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB20_CH);
	
	hx330x_intEnable(IRQ_USB20,0);
	hx330x_usb20_CallbackRegister(USB_IRQ_HANDLER,NULL);
	deg_Printf("husb_uvc_detech\n");
	husb_uvc_detech(pHusb_handle);
	//XSFR_USB20_CON0 = 0;
	hx330x_usb20_uinit();
	deg_Printf("[ HUSB20 remove]\n");
}
/*******************************************************************************
* Function Name  : husb_api_u11_remove
* Description    : husb_api_u11_remove
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_u11_remove(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB11_CH);
	hx330x_intEnable(IRQ_USB11,0);
	husb_uvc_detech(pHusb_handle);
	hx330x_usb11_uinit();
	//husb_api_handle_reg(NULL);
	debg("[ HUSB11 remove]\n");	
}
/*******************************************************************************
* Function Name  : husb_api_u20_reset
* Description    : husb_api_u20_reset
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_api_u20_reset(void)
{
	static u32 enum20_retry = 0;
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB20_CH);
	deg_Printf("[ HUSB20 Reset]\n");
	if(pHusb_handle->usbsta.device_sta & (USB_MSC_ATECH|USB_UVC_ATECH))
	{
		enum20_retry = 0;
		husb_api_u20_remove();
	}else
	{
		
		enum20_retry++;
		if(enum20_retry > 5)
		{
			enum20_retry = 0;
			husb_api_u20_remove();
		}else{
			husb_api_u20_connect();
		}
	}
}
static void husb_api_u11_reset(void)
{
	static u32 enum11_retry = 0;
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB11_CH);
	deg_Printf("[ HUSB11 Reset]\n");
	if(pHusb_handle->usbsta.device_sta & (USB_MSC_ATECH|USB_UVC_ATECH))
	{
		enum11_retry = 0;
		husb_api_u11_remove();
	}else
	{
		
		enum11_retry++;
		if(enum11_retry > 5)
		{
			enum11_retry = 0;
			husb_api_u11_remove();
		}else{
			husb_api_u11_connect();
		}
	}
}
/*******************************************************************************
* Function Name  : husb_api_u20_ep0isr
* Description    : husb_api_u20_ep0isr
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_api_u20_ep0isr(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB20_CH);
	if(husb_api_ep0_process(pHusb_handle) == false)
	{
		husb_api_u20_remove();
	}
}
static void husb_api_u11_ep0isr(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB11_CH);
	if(husb_api_ep0_process(pHusb_handle) == false)
	{
		husb_api_u11_remove();
	}
}


/*******************************************************************************
* Function Name  : husb_api_u20_fun_reg
* Description    : husb_api_u20_fun_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_api_u20_fun_reg(void)
{
	hx330x_usb20_CallbackRegister(USB_IRQ_INSERT,	husb_api_u20_connect);
	hx330x_usb20_CallbackRegister(USB_IRQ_REMOVE,	husb_api_u20_remove);
	hx330x_usb20_CallbackRegister(USB_IRQ_RESET,	husb_api_u20_reset);
	hx330x_usb20_CallbackRegister(USB_IRQ_EP0,		husb_api_u20_ep0isr);
	hx330x_usb20_CallbackRegister(USB_IRQ_HANDLER,	hx330x_usb20_hostIRQHanlder);
}
static void husb_api_u11_fun_reg(void)
{
	hx330x_usb11_CallbackRegister(USB11_IRQ_INSERT,	husb_api_u11_connect);
	hx330x_usb11_CallbackRegister(USB11_IRQ_REMOVE,	husb_api_u11_remove);
	hx330x_usb11_CallbackRegister(USB11_IRQ_RESET,	husb_api_u11_reset);
	hx330x_usb11_CallbackRegister(USB11_IRQ_EP0,	husb_api_u11_ep0isr);
	hx330x_usb11_CallbackRegister(USB11_IRQ_HANDLER,hx330x_usb11_hostIRQHanlder);
	//hx330x_usb11_Func_Call(USB11_IRQ_HANDLER);
}
/*******************************************************************************
* Function Name  : husb_api_u20_init
* Description    : husb_api_u20_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_api_u20_init(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB20_CH);
	pHusb_handle->usbsta.device_sta  = USB_INIT;
	hx330x_intEnable(IRQ_USB20,0);
	hx330x_sysCpuMsDelay(100);
	hx330x_usb20_host_init();
	//XSFR_USB20_PHY_REG3 |= BIT(2);	//disconnect digital dis
	hx330x_usb20_host_reset();
	husb20_ep0_cfg();
	husb_api_u20_fun_reg();
	hx330x_intEnable(IRQ_USB20,1);

}

static void husb_api_u11_init(void)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(USB11_CH);
	pHusb_handle->usbsta.device_sta  = USB_INIT;
	hx330x_intEnable(IRQ_USB11,0);
	hx330x_sysCpuMsDelay(100);
	hx330x_usb11_host_init();
	hx330x_usb11_host_reset();
	husb11_ep0_cfg();
	husb_api_u11_fun_reg();
	hx330x_intEnable(IRQ_USB11,1);	
}
/*******************************************************************************
* Function Name  : husb_api_init
* Description    : husb_api_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_init(u8 usb_ch)
{
	if(usb_ch == USB20_CH)
		husb_api_u20_init();
	else if(usb_ch == USB11_CH)
		husb_api_u11_init();
}
/*******************************************************************************
* Function Name  : husb_api_check
* Description    : husb_api_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
UNUSED static u8 husb_api_check(u8 ch)
{
	if(ch == USB20_CH)
	{
		if(hx330x_usb20_host_check() == true)
			return USB20_CH;
	}else if(ch == USB11_CH)
	{
		if(hx330x_usb11_host_check() == true)
			return USB11_CH;
	}
	return USBNONE_CH;

}
/*******************************************************************************
* Function Name  : husb_api_msc_try_tran
* Description    : husb_api_msc_try_tran
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 husb_api_devicesta(u8 ch)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(ch);
	if(pHusb_handle == NULL)
	{
		return USB_NONE;
	}
	return pHusb_handle->usbsta.device_sta;
}
/*******************************************************************************
* Function Name  : husb_api_msc_try_tran
* Description    : husb_api_msc_try_tran
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_msc_try_tran(u8 ch)
{
	HUSB_HANDLE* pHusb_handle = husb_api_handle_get(ch);
	if(pHusb_handle != NULL)
	{
		return enum_mass_dev(pHusb_handle);
	}
	return false;
}
#define POWER_IO_SOFTSTART			0
/*******************************************************************************
* Function Name  : dev_husb_io_power_set
* Description    : dev_husb_io_power_set
* Input          : u8 poweron: 1 : power on, 0: power off
* Output         : None
* Return         : None
*******************************************************************************/
void dev_husb_io_power_set(u8 poweron)
{
	if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE)
	{
		if(poweron)
		{
			if(hardware_setup.usb_host_pwr_io == IO1_CH_PA7)
			{
				hal_gpioWrite(GPIO_PA,	GPIO_PIN7, 0);
				hal_gpioInit(GPIO_PA,	GPIO_PIN7,	GPIO_OUTPUT,	GPIO_PULL_FLOATING);
			}else
			{
				hal_gpioWrite(GPIO_PD,	GPIO_PIN1, 0);
				hal_gpioInit(GPIO_PD,	GPIO_PIN1,	GPIO_OUTPUT,	GPIO_PULL_FLOATING);  //配置为高压IO					
			}
		}else
		{
			if(hardware_setup.usb_host_pwr_io == IO1_CH_PA7)
			{
				hal_gpioWrite(GPIO_PA,	GPIO_PIN7, 0);
				hal_gpioInit_io1d1(GPIO_PA,	GPIO_PIN7,	GPIO_OUTPUT,	GPIO_PULL_FLOATING);  //配置为高压IO
			}		
			else
			{
				hal_gpioWrite(GPIO_PD,	GPIO_PIN1, 0);
				hal_gpioInit_io1d1(GPIO_PD,	GPIO_PIN1,	GPIO_OUTPUT,	GPIO_PULL_FLOATING);  //配置为高压IO
			}	
		}
	}

}

/*******************************************************************************
* Function Name  : dev_husb_init
* Description    : dev_husb_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_husb_init(void)
{
	if(hardware_setup.usb_host_en)
	{	
		if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE)
		{
			dev_husb_io_power_set(0);
			hal_io1d1_softstart(hardware_setup.usb_host_pwr_io,1); //DEFAULT IO1D soft slow pd, PD200K DISABLE	
		}
		if(hardware_setup.usb_host_dect_ctrl)	
		{
			if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE) //PWER OFF, DET INPUT PULLUP, IF USB INSERT, DET READ HIGH; ELSE READ LOW
			{
				hal_gpioInit(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin,	GPIO_INPUT, GPIO_PULL_UP);
			}else //PWR ALWAYS ON, DET PIN INPUT pulldown, if usb insert, DET read low; else DET read high
			{
				hal_gpioInit(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin,	GPIO_INPUT,GPIO_PULL_DOWN);
			}
			
		}	
		return 0;
	}else
	{
		return -1;
	}

}
/*******************************************************************************
* Function Name  : dev_husb_ioctrl
* Description    : dev_husb_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_husb_ioctrl(INT32U op,INT32U para)
{
	if(hardware_setup.usb_host_en)
	{
		static u32 husb_pwr_sta = 0; //pwr需要控制时，默认是关电的

		if(op == DEV_HUSB_DET_CHECK)	//usb insert check
		{
			if(hardware_setup.usb_host_dect_ctrl)
			{
				if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE)
				{
					if(husb_pwr_sta) //PWR  ON, DET PIN INPUT pulldown, if usb insert, DET read low; else DET read high
					{
						*((INT32U *)para) = !hal_gpioRead(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin);
					}else //PWER OFF, DET INPUT PULLUP, IF USB INSERT, DET READ HIGH; ELSE READ LOW
					{
						*((INT32U *)para) = hal_gpioRead(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin);
					}
				}else //PWR ALWAYS ON, DET PIN INPUT pulldown, if usb insert, DET read low; else DET read high
				{
					*((INT32U *)para) = !hal_gpioRead(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin);	
				}

			}else
			{
				*((INT32U *)para) = 1;// use soft detect, always det
			}
		}else if(op == DEV_HUSB_POWER_CTRL)	//usb power on/off, 1:power on, 3: power on faster, 0: power off
		{
			
			//static u32 husb_pwr_time = XOSTimeGet();
			if(para == husb_pwr_sta)
				return 0;
			husb_pwr_sta = para;
			if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE)
			{

				if(para == HUSB_POWER_ON) //PWR  ON, DET PIN INPUT pulldown, if usb insert, DET read low; else DET read high
				{
				#if (POWER_IO_SOFTSTART == 0)
					dev_husb_io_power_set(1); //power on
				#else
					hal_io1d1_softstart_clr(0);//OUTPUT LOW , USB POWER ON
				#endif
				}else
				{
				#if (POWER_IO_SOFTSTART == 0)
					dev_husb_io_power_set(0); //power off
				#else				
					hal_io1d1_softstart_clr(1);//1 :enable softstart function
				#endif
				}	
				if(hardware_setup.usb_host_dect_ctrl)
				{
					//PWR  ON, DET PIN INPUT pulldown
					//PWER OFF, DET INPUT PULLUP, 
					hal_gpioInit(hardware_setup.usb_host_dect_ch, hardware_setup.usb_host_dect_pin,	GPIO_INPUT, (husb_pwr_sta) ? GPIO_PULL_DOWN : GPIO_PULL_UP);
				}
			}
		}else if (op == DEV_HUSB_TYPE_CHECK)
		{
			if(hardware_setup.usb_host_ch == USBAUTO_CH)
			{
				int ret = husb_api_check(USB20_CH);
				if(ret != USB20_CH)
				{
					return husb_api_check(USB11_CH);
				}
			}else 
			{
				return husb_api_check(hardware_setup.usb_host_ch);
			}
		}else if (op == DEV_HUSB_INIT)
		{
			husb_api_init(para);
		}
		return 0;		
	}else
	{
		return -1;
	}

}

