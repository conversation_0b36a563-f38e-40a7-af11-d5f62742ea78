/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : menuProcDateTime
* Description    : menuProcDateTime
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcDateTime(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&dateTimeWindow,0,0);
}
/*******************************************************************************
* Function Name  : menuProcFormat
* Description    : menuProcFormat
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcFormat(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&formatWindow,0,0);
}
/*******************************************************************************
* Function Name  : menuProcDefault
* Description    : menuProcDefault
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void menuProcDefault(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&defaultWindow,0,0);
}
/*******************************************************************************
* Function Name  : menuProcVersion
* Description    : menuProcVersion
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
#if UI_SHOW_SMALL_PANEL
void menuProcVersion(winHandle handle,u32 parameNum,u32* parame)
{
	uiOpenWindow(&versionWindow,0,0);
}
#endif


