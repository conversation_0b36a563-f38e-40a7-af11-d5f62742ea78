/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_R61509V


#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0x00),CMD(0x00),<PERSON><PERSON>(0x00),<PERSON><PERSON>(0x00),
    <PERSON><PERSON>(100),

    <PERSON><PERSON>(0x04),C<PERSON>(0x00),DAT(0x6a),DAT(0x00),
    CMD(0x00),CMD(0x08),DAT(0x08),DAT(0x08),
    DLY(200),

    CMD(0x03),CMD(0x00),DAT(0x05),DAT(0x04),
    CMD(0x03),CMD(0x01),DAT(0x16),DAT(0x1c),
    CMD(0x03),CMD(0x02),DAT(0x05),DAT(0x01),
    CMD(0x03),CMD(0x03),DAT(0x11),DAT(0x04),
    CMD(0x03),CMD(0x04),DAT(0x11),DAT(0x30),
    CMD(0x03),CMD(0x05),DAT(0x04),DAT(0x11),
    CMD(0x03),CMD(0x06),DAT(0x11),DAT(0x05),
    CMD(0x03),CMD(0x07),DAT(0x1c),DAT(0x06),
    CMD(0x03),CMD(0x08),DAT(0x04),DAT(0x05),
    CMD(0x03),CMD(0x09),DAT(0x13),DAT(0x30),
    DLY(20),

    CMD(0x00),CMD(0x10),DAT(0x00),DAT(0x17),
    DLY(15),
    CMD(0x01),CMD(0x00),DAT(0x03),DAT(0x30),
    CMD(0x01),CMD(0x01),DAT(0x02),DAT(0x37), // 2,37
    CMD(0x01),CMD(0x02),DAT(0xf9),DAT(0xB0),
    CMD(0x01),CMD(0x03),DAT(0x0c),DAT(0x00),
    DLY(15),
    CMD(0x00),CMD(0x11),DAT(0x01),DAT(0x01),
    CMD(0x00),CMD(0x12),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x13),DAT(0x00),DAT(0x01),

    DLY(120),
    CMD(0x02),CMD(0x80),DAT(0xc2),DAT(0x00),


    DLY(15),

    CMD(0x00),CMD(0x01),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x02),DAT(0x01),DAT(0x00),
    CMD(0x00),CMD(0x03),DAT(0x10),DAT(0xA0),
    CMD(0x00),CMD(0x09),DAT(0x00),DAT(0x01),
    CMD(0x00),CMD(0x0C),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x90),DAT(0x80),DAT(0x08),
    CMD(0x00),CMD(0x0F),DAT(0x00),DAT(0x00),
    DLY(20),
    CMD(0x02),CMD(0x10),DAT(0x00),DAT(0x00),
    CMD(0x02),CMD(0x11),DAT(0x00),DAT(0xef),
    CMD(0x02),CMD(0x12),DAT(0x00),DAT(0x00),
    CMD(0x02),CMD(0x13),DAT(0x01),DAT(0x8f),
    DLY(20),
    CMD(0x05),CMD(0x00),DAT(0x00),DAT(0x00),
    CMD(0x05),CMD(0x01),DAT(0x00),DAT(0x00),
    CMD(0x05),CMD(0x02),DAT(0x00),DAT(0x5f),
    DLY(20),
    CMD(0x04),CMD(0x01),DAT(0x00),DAT(0x01),
    CMD(0x04),CMD(0x04),DAT(0x00),DAT(0x00),
    DLY(15),
    CMD(0x00),CMD(0x07),DAT(0x01),DAT(0x00),
    DLY(100),

    CMD(0x02),CMD(0x02),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_R61509V",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(240*400*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 400,

    .video_w  		= 400,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif







