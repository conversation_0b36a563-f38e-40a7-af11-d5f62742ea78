/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_SPI_ILI9341

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0xCB),
    DAT(0x39),
    DAT(0x2C),
    DAT(0x00),
    DAT(0x34),
    DAT(0x02),

    CMD(0xCF),
    DAT(0x00),
    DAT(0XC1),
    DAT(0X30),

    CMD(0xE8),
    DAT(0x85),
    DAT(0x00),
    DAT(0x78),

    CMD(0xEA),
    DAT(0x00),
    DAT(0x00),

    CMD(0xED),
    DAT(0x64),
    DAT(0x03),
    DAT(0X12),
    DAT(0X81),

    CMD(0xF7),
    DAT(0x20),

    CMD(0xC0),    //Power control
    DAT(0x23),   //VRH[5:0]

    CMD(0xC1),    //Power control
    DAT(0x10),   //SAP[2:0];BT[3:0]

    CMD(0xC5),    //VCM control
    DAT(0x3e), //对比度调节
    DAT(0x28),

    CMD(0xC7),    //VCM control2
    DAT(0x86),  //--

    CMD(0x36),    // Memory Access Control
    DAT(0x48), //C8	   //48 68竖屏//28 E8 横屏

    CMD(0x3A),
    DAT(0x55),

    CMD(0xB1),
    DAT(0x00),
    DAT(0x18),

    CMD(0xB6),    // Display Function Control
    DAT(0x08),
    DAT(0x82),
    DAT(0x27),

    CMD(0xF2),    // 3Gamma Function Disable
    DAT(0x00),

    CMD(0x26),    //Gamma curve selected
    DAT(0x01),

    CMD(0xE0),    //Set Gamma
    DAT(0x0F),
    DAT(0x31),
    DAT(0x2B),
    DAT(0x0C),
    DAT(0x0E),
    DAT(0x08),
    DAT(0x4E),
    DAT(0xF1),
    DAT(0x37),
    DAT(0x07),
    DAT(0x10),
    DAT(0x03),
    DAT(0x0E),
    DAT(0x09),
    DAT(0x00),

    CMD(0XE1),    //Set Gamma
    DAT(0x00),
    DAT(0x0E),
    DAT(0x14),
    DAT(0x03),
    DAT(0x11),
    DAT(0x07),
    DAT(0x31),
    DAT(0xC1),
    DAT(0x48),
    DAT(0x08),
    DAT(0x0F),
    DAT(0x0C),
    DAT(0x31),
    DAT(0x36),
    DAT(0x0F),

    CMD(0x11),    //Exit Sleep
    DLY(120),

    CMD(0x29),    //Display on
    CMD(0x2c),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name           = "SPI_ILI9341",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_90,
    .te_mode        = LCD_MCU_TE_DISABLE,

    .io_data_pin    = LCD_DPIN_EN_ONLY_D0,

    .pclk_div       = LCD_PCLK_DIV(48000000),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    LCD_SPI_DEFAULT(8),

    .data_mode = LCD_DATA_MODE0_SPI_8BIT_RGB565,

    .screen_w       = 240,
    .screen_h       = 320,

    .video_w        = 320,
    .video_h        = 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index   = 8,

    .gamma_index    = {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()


#endif








