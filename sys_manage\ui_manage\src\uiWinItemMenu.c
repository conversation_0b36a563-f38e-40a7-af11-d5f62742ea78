/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiItemMenuProc
* Description    : uiItemMenuProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemMenuProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiWinObj *pWin;
	uiResInfo *infor;
	uiItemMenuObj *pItemMenu;
	if(uiWidgetProc(msg))
		return;
	hWin		= msg->curWin;
	pItemMenu	= (uiItemMenuObj*)uiHandleToPtr(hWin);
	pWin		= &(pItemMenu->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("menuItem win create\n");
			return;
		case MSG_WIN_PAINT:
			if(pItemMenu->select)
			{
				if(pItemMenu->selectColor != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenu->selectColor);
				if(pItemMenu->imageSelect.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenu->imageSelect);
			}
			else
			{
				if(pItemMenu->color != INVALID_COLOR)
					uiWinDrawRect((uiRect*)(msg->para.p),pItemMenu->color);	
				if(pItemMenu->image.id != INVALID_RES_ID)
					uiWinDrawIcon(&pWin->rect,(uiRect*)(msg->para.p),&pItemMenu->image);
			}
			return;
		case MSG_WIN_INVALID_RESID:
			uiWinSetResid(pItemMenu->hImage,INVALID_RES_ID);
			uiWinSetResid(pItemMenu->hStr,INVALID_RES_ID);
			return;
		case MSG_WIN_CHANGE_RESID:
			if(RES_ID_IS_ICON(msg->para.v))
				uiWinSetResid(pItemMenu->hImage,msg->para.v);
			else
				uiWinSetResid(pItemMenu->hStr,msg->para.v);
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenu->imageSelect.id	   = infor->image;
			pItemMenu->imageSelect.bgColor = infor->color;
			pItemMenu->selectColor 		   = infor->color;	
			uiWinSendMsg(pItemMenu->hStr,msg);
			uiWinSendMsg(pItemMenu->hImage,msg);
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenu->image.id	     = infor->image;
			pItemMenu->image.bgColor = infor->color;
			pItemMenu->color 		 = infor->color;	
			uiWinSendMsg(pItemMenu->hStr,msg);
			{
				uiResInfo tempInfor;

				memcpy(&tempInfor,msg->para.p,sizeof(uiResInfo));
				tempInfor.fontColor = INVALID_COLOR;
				uiWinSetUnselectInfor(pItemMenu->hImage,&tempInfor);
			}
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			uiWinSendMsg(pItemMenu->hStr,msg);
			return;
		case MSG_WIN_SELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenu->imageSelect.id	   = infor->image;
			pItemMenu->imageSelect.bgColor = infor->color;
			pItemMenu->selectColor 		   = infor->color;
			return;
		case MSG_WIN_UNSELECT_INFOR:
			infor = (uiResInfo*)(msg->para.p);
			pItemMenu->image.id	     = infor->image;
			pItemMenu->image.bgColor = infor->color;
			pItemMenu->color 		 = infor->color;	
			return;
		case MSG_WIN_UNSELECT:
			if(pItemMenu->select == 0)
				return;
			pItemMenu->select = 0;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenu->hStr,msg);
			uiWinSendMsg(pItemMenu->hImage,msg);
			return;
		case MSG_WIN_SELECT:
			if(pItemMenu->select)
				return;
			pItemMenu->select = 1;
			if(uiWinIsVisible(hWin))
				uiWinParentRedraw(hWin);
			uiWinSendMsg(pItemMenu->hStr,msg);
			uiWinSendMsg(pItemMenu->hImage,msg);
			return;
		case MSG_WIN_VISIBLE_SET:
			if(msg->para.v)
			{
				uiWinSetVisible(pItemMenu->hImage,1);
				uiWinSetVisible(pItemMenu->hStr,1);
			}
			else
			{
				uiWinSetVisible(pItemMenu->hImage,0);
				uiWinSetVisible(pItemMenu->hStr,0);
			}
			break;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiItemCreateItemMenu
* Description    : uiItemCreateItemMenu
* Input          : s16 x0,s16 y0,u16 width,u16 height
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateItemMenu(s16 x0,s16 y0,u16 width,u16 height,u16 style)
{
	winHandle 		hItemMenu;
	uiItemMenuObj  *pItemMenu;
	hItemMenu = uiWinCreate(x0,y0,width,height,INVALID_HANDLE,uiItemMenuProc,sizeof(uiItemMenuObj),WIN_WIDGET|WIN_NOT_ZOOM);
	if(hItemMenu!=INVALID_HANDLE)
	{
		
		pItemMenu = (uiItemMenuObj*)uiHandleToPtr(hItemMenu);
		pItemMenu->image.id 				= INVALID_RES_ID;
		pItemMenu->image.iconAlign			= ALIGNMENT_CENTER;
		pItemMenu->image.bgColor			= INVALID_COLOR;
		pItemMenu->image.iconColor			= INVALID_COLOR;
		pItemMenu->image.visible			= 1;
		pItemMenu->imageSelect.id 			= INVALID_RES_ID;
		pItemMenu->imageSelect.iconAlign 	= ALIGNMENT_CENTER;
		pItemMenu->imageSelect.bgColor		= INVALID_COLOR;
		pItemMenu->imageSelect.iconColor	= INVALID_COLOR;
		pItemMenu->imageSelect.visible		= 1;
		pItemMenu->select					= 0;
		pItemMenu->color					= INVALID_COLOR;
		pItemMenu->selectColor				= INVALID_COLOR;
		pItemMenu->hImage = uiImageIconCreateDirect( x0,	    y0, height,		  height,INVALID_HANDLE, WIN_NOT_ZOOM,INVALID_WIDGET_ID);
		pItemMenu->hStr	  = uiStringIconCreateDirect(x0+height, y0, width-height, height,INVALID_HANDLE, WIN_NOT_ZOOM,INVALID_WIDGET_ID);		
		uiWidgetSetId(hItemMenu,INVALID_WIDGET_ID);
		uiWinSetbgColor(hItemMenu, INVALID_COLOR);
	}
	return hItemMenu;
}
