
                                              // ( A  R  G  B)
#define R_ID_PALETTE_Grayish_White       0xEB // (FF EE EE EE)
#define R_ID_LINE                        0xEC // (FF CC CC CC)
#define R_ID_GREY_W                      0xED // (FF CB CB CB)
#define R_ID_PALETTE_SET_LONG            0xEE // (FF CB 8C 2C)
#define R_ID_PALETTE_SET_BOTTOM          0xEF // (FF 3B 3B AA)
#define R_ID_PALETTE_SET_TOP             0xF0 // (FF 19 19 5F)
#define R_ID_PALETTE_DoderBlue           0xF1 // (FF AA AA AA)
#define R_ID_PALETTE_DarkGreen           0xF2 // (FF 02 29 59)
#define R_ID_PALETTE_LightGreen          0xF3 // (FF 60 C0 C0)
#define R_ID_PALETTE_Yellow              0xF4 // (FF EB AC 14)
#define R_ID_PALETTE_Blue                0xF5 // (FF 00 00 FF)
#define R_ID_PALETTE_Green               0xF6 // (FF 00 FF 00)
#define R_ID_PALETTE_Red                 0xF7 // (FF FF 00 00)
#define R_ID_PALETTE_DimGray             0xF8 // (FF 30 30 30)
#define R_ID_PALETTE_DarkGray            0xF9 // (FF 50 50 50)
#define R_ID_PALETTE_Gray                0xFA // (FF 75 75 75)
#define R_ID_PALETTE_TransBlack          0xFB // (80 00 00 00)
#define R_ID_PALETTE_White               0xFC // (FF FF FF FF)
#define R_ID_PALETTE_Black               0xFD // (FF 00 00 00)
#define R_ID_PALETTE_Transparent         0xFE // (00 00 00 00)
#define R_ID_PALETTE_Error               0xFF // (00 00 00 00)

