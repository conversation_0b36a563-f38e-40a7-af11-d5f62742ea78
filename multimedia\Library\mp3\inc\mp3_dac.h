/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_DAC_H
       #define  MP3_DAC_H
	   
typedef enum{
	MP3_DAC_STOP = 0,
	MP3_DAC_PAUSE,
	MP3_DAC_START,
	MP3_DAC_RESUME,
	//MP3_DAC_WAITSTOP,
}MP3_DAC_STA;	   
typedef struct MP3_DAC_CTL_S 
{	
	//u32  prepause;
	u32  dec_len;
	u32  dac_len;	
	u32  dac_volume;
	//u8	*decCurBuf;
	u8  *dacCurFrame;
	u8  *dacNextFrame; 
	u32  dac_fcnt; //dac play frame cnt
	u32  dec_fcnt; //dac play frame cnt
	u32  dec_frame_len;
	u32  dec_samplerate;
	Stream_Head_T mp3;
	Stream_Node_T mp3Node[MP3_PCM_BUFFER_NUM];
}MP3_DAC_CTL_T;

/*******************************************************************************
* Function Name  : mp3_dac_init
* Description    : mp3_dac_init: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_init(void);
/*******************************************************************************
* Function Name  : mp3_dac_volume_cfg
* Description    : mp3_dac_volume_cfg: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_volume_cfg(u32 volume);
/*******************************************************************************
* Function Name  : mp3_dac_stream_malloc
* Description    : mp3_dac_stream_malloc: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_dac_stream_malloc(void);
/*******************************************************************************
* Function Name  : mp3_dac_stream_in_preprocess
* Description    : mp3_dac_stream_in_preprocess: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_stream_in_preprocess(void *buf, u32 len, u32 ch);
/*******************************************************************************
* Function Name  : mp3_dac_stream_in_preprocess
* Description    : mp3_dac_stream_in_preprocess: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_stream_in(void);
/*******************************************************************************
* Function Name  : mp3_dac_stream_in_end
* Description    : mp3_dac_stream_in_end: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_stream_in_end(void);
/*******************************************************************************
* Function Name  : mp3_dac_stop
* Description    : mp3_dac_stop: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_stop(void);
/*******************************************************************************
* Function Name  : mp3_dac_stop
* Description    : mp3_dac_stop: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_pause(void);
/*******************************************************************************
* Function Name  : mp3_dac_resume
* Description    : mp3_dac_resume: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_resume(void);
/*******************************************************************************
* Function Name  : mp3_dac_stop
* Description    : mp3_dac_stop: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_dac_start(void);
/*******************************************************************************
* Function Name  : mp3_dac_isr
* Description    : mp3_dac_isr: 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_dac_isr(int flag);
#endif
