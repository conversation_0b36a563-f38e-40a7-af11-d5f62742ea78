/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TIP_STRING_ID=0,
	TIP_STRING2_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor tipsWin[] =
{
	createFrameWin(					Rx(70),	Ry(60), Rw(180), Rh(120),	R_ID_GREY_W,	WIN_ABS_POS),
	createStringIcon(TIP_STRING_ID,	Rx(0),	<PERSON>y(0), 	Rw(180), Rh(120),	" ",					ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	widgetEnd(),
};



