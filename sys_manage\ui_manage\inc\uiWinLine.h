/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_LINE_H
#define UI_WIN_LINE_H

typedef struct
{
	uiWidgetObj 	widget;
	LINE_DRAW_T		line;
}uiLineObj;

/*******************************************************************************
* Function Name  : uiLineCreate
* Description    : uiLineCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiLineCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
