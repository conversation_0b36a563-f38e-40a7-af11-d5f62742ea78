/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef LED_PWM_API_H
#define LED_PWM_API_H
/*******************************************************************************
* Function Name  : dev_key_init
* Description    : dev_key_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_init(void);
/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_ioctrl(u32 op, u32 para);


#endif
