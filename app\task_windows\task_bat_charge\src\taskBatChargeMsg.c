/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskBatChargeWin.c"
//R_ID_IMAGE_BAT_MAIN


static int batChargeKeyMsgOK(winHandle handle,u32 parameNum,u32* parame)
{
	static u32 count;
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_CONTINUE)
	{


		
	}	
	return 0;
}





/*******************************************************************************
* Function Name  : batChargeKeyMsgOk
* Description    : batChargeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeKeyMsgCommon(winHandle handle,u32 parameNum,u32* parame)
{
	
	//if(hardware_setup.battery_pos != ADC_CH_MVOUT && SysCtrl.dev_stat_battery >= (BATTERY_STAT_MAX-1) )
	//{
	//	batChargeOp.bat_ui_show_time = XOSTimeGet();
	//}	
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgUSB
* Description    : playAudioSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) //DC拔出时关机
	{
		//app_taskStart(TASK_POWER_OFF,0);
		//app_uninit();
	}
	//if(hardware_setup.battery_pos != ADC_CH_MVOUT && SysCtrl.dev_stat_battery >= (BATTERY_STAT_MAX-1) )
	//{
	//	batChargeOp.bat_ui_show_time = XOSTimeGet();
	//}		
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgBattery
* Description    : playAudioSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	int ret, temp;

	//电池充满时处理：
	//if(SysCtrl.dev_stat_battery  == (BATTERY_STAT_MAX-1))

	//deg_Printf("hx330x_gpioDataGet=%d\r\n",hx330x_gpioDataGet(USER_CHARGE_DET_CH,USER_CHARGE_DET_PIN));
	if(hx330x_gpioDataGet(GPIO_PE,GPIO_PIN1))
	{
		batChargeOp.sub_cur_w = batChargeOp.sub_w;
		taskBatChargeBarShow();
		uiWinSetVisible(winItem(handle,BAT_CHARGE_STR_ID),1);
		//uiWinSetResid(winItem(handle,BAT_CHARGE_STR_ID),RAM_ID_MAKE("100%"));
		batChargeOp.bat_ui_show_time = XOSTimeGet();
	}else
	{
		uiWinSetVisible(winItem(handle,BAT_CHARGE_STR_ID),0);
	}
	dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
	if(temp == 0)//wxn--231031-为了解决特定情况下，插拔数据线充电，有时拔了数据线后，还会一直显示充电图标
		app_uninit();
	//deg_Printf("batChargeSysMsgBattery:%d\n",SysCtrl.dev_stat_battery);
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgTimeUpdate
* Description    : playAudioSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeSysMsgTimeUpdate(winHandle handle,uint32 parameNum,uint32* parame)
{
	static u8 first_into = 1;
	if(first_into){
		first_into = 0;
		hal_gpioInit(GPIO_PE, GPIO_PIN1,GPIO_INPUT,GPIO_PULL_FLOATING);
	}

	batChargeOp.sub_cur_w += batChargeOp.sub_step_w;
	taskBatChargeBarShow();
	if(batChargeOp.sub_cur_w >= batChargeOp.sub_w)
	{
		if(hx330x_gpioDataGet(GPIO_PE,GPIO_PIN1))//(SysCtrl.dev_stat_battery  != BATTERY_STAT_MAX)
		{
			batChargeOp.sub_cur_w = ((u32)SysCtrl.dev_stat_battery * batChargeOp.sub_w)/(BATTERY_STAT_MAX-1);
		}else
		{
			batChargeOp.sub_cur_w = batChargeOp.sub_w/2;
		}
		
	}
	batChargeSysMsgBattery(handle,parameNum,parame );
	return 0;
}
/*******************************************************************************
* Function Name  : batChargeOpenWin
* Description    : batChargeOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	uiRect rect;
	uiWinGetPos(winItem(handle,BAT_CHARGE_RECT_ID),&rect);
	taskBatChargeBarShowInit(&rect);
	if(1)//(SysCtrl.dev_stat_battery  != BATTERY_STAT_MAX)
	{
		batChargeOp.sub_cur_w = ((u32)SysCtrl.dev_stat_battery * batChargeOp.sub_w)/(BATTERY_STAT_MAX-1);
	}else{
		batChargeOp.sub_cur_w = batChargeOp.sub_w/2;
	}
	
	batChargeSysMsgBattery(handle,parameNum,parame );
	taskBatChargeBarShow();
	return 0;
}
/*******************************************************************************
* Function Name  : batChargeCloseWin
* Description    : batChargeCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	app_logo_show(0,1,1);
	hal_lcdUiEnable(UI_LAYER0,1);
	deg_Printf("[WIN]batChargeCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : batChargeWinChildClose
* Description    : batChargeWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	batChargeOp.sub_cur_w = ((u32)SysCtrl.dev_stat_battery * batChargeOp.sub_w)/(BATTERY_STAT_MAX-1);
	batChargeSysMsgBattery(handle,parameNum,parame );
	taskBatChargeBarShow();
	
	
	return 0;
}

/*******************************************************************************
* Function Name  : batChargeKeyMsgPowerOff
* Description    : batChargeKeyMsgPowerOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int batChargeKeyMsgPowerOff(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskSdUpdateProcess();
		app_taskStart(TASK_RECORD_PHOTO,0);		
		task_com_led_on();		
	}
	return 0;
}



ALIGNED(4) msgDealInfor batChargeMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		batChargeOpenWin},
	{SYS_CLOSE_WINDOW,		batChargeCloseWin},
	{SYS_CHILE_COLSE,		batChargeWinChildClose},
	{KEY_EVENT_POWEROFF,    batChargeKeyMsgPowerOff},
	{KEY_EVENT_OK,			batChargeKeyMsgOK},
	{KEY_EVENT_UP,			batChargeKeyMsgCommon},
	{KEY_EVENT_DOWN,		batChargeKeyMsgCommon},

	{SYS_EVENT_USBDEV,		batChargeSysMsgUSB},
//	{SYS_EVENT_BAT,			batChargeSysMsgBattery},
	{SYS_EVENT_TIME_UPDATE,	batChargeSysMsgTimeUpdate},
	{EVENT_MAX,NULL},
};

WINDOW(batChargeWindow,batChargeMsgDeal,batChargeWin)


