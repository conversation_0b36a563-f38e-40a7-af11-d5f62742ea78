/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TIP1_STRING_ID=0,
	TIP1_TIPS_ID,
	TIP1_STRING2_ID
};
UNUSED ALIGNED(4) const widgetCreateInfor tips1Win[] =
{
	createFrameWin(					Rx(40),	Ry(50), Rw(260),Rh(140),R_ID_GREY_W,		WIN_ABS_POS),
	//createStringIcon(TIP1_TIPS_ID,	Rx(0),	<PERSON>y(0), 	<PERSON>w(260),<PERSON>h(45),	R_ID_STR_SET_PROMT,	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TIP1_STRING_ID,Rx(0),	Ry(45), Rw(260),Rh(45),	" ",				ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TIP1_STRING2_ID,Rx(0),	Ry(65), Rw(260),Rh(45),	" ",				ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	widgetEnd(),
};



