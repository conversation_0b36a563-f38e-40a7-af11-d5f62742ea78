/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef HAL_SPI1_H
#define HAL_SPI1_H

/*******************************************************************************
* Function Name  : hal_spi1DmaCallback
* Description	 : spi initial
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
bool hal_spi1DmaDoneCheck(void);
/*******************************************************************************
* Function Name  : hal_spi1Init
* Description	 : spi initial
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spi1Init(void);
/*******************************************************************************
* Function Name  : hal_spi1SendByte
* Description	 : spi1 send one byte
* Input 		 : u8 byte : data
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spi1SendByte(u8 u8Data);
/*******************************************************************************
* Function Name  : hal_spi1RecvByte
* Description	 : spi1 recv one byte
* Input 		 : none
* Output		 : None
* Return		 : u8 :
*******************************************************************************/
u8 hal_spi1RecvByte(void);
/*******************************************************************************
* Function Name  : hal_sp1SendDmaKick
* Description	 : spi1 send DMA
* Input 		 : u8 *pDataBuf: send buf
*                  u32 u32DataLen: send len
* Output		 : None
* Return		 : none
*******************************************************************************/
bool hal_spi1SendDmaKick(u8 *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hal_sp1SendDma
* Description	 : spi1 send DMA: no use isr, wait send done
* Input 		 : u8 *pDataBuf: send buf
*                  u32 u32DataLen: send len
* Output		 : None
* Return		 : none
*******************************************************************************/
bool hal_spi1SendDma(u8 *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hal_spi1RecvDmaKick
* Description	 : spi1 recieve DMA kick
* Input 		 : u8 *pDataBuf: send buf
*                  u32 u32DataLen: send len
* Output		 : None
* Return		 : none
*******************************************************************************/
bool hal_spi1RecvDmaKick(u8 *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hal_spi1RecvDma
* Description	 : spi1 recieve DMA: no use isr, wait send done
* Input 		 : u8 *pDataBuf: send buf
*                  u32 u32DataLen: send len
* Output		 : None
* Return		 : none
*******************************************************************************/
bool hal_spi1RecvDma(u8 *pDataBuf,u32 u32DataLen);
/*******************************************************************************
* Function Name  : hal_spi1Init
* Description	 : spi initial
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spi1_test(void);



#endif

