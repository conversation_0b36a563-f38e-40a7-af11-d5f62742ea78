/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_ENUM_H_
#define USB_HOST_ENUM_H_
/*******************************************************************************
* Function Name  : husb20_ep0_cfg
* Description    : husb20_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb20_ep0_cfg(void);

/*******************************************************************************
* Function Name  : husb11_ep0_cfg
* Description    : husb11_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb11_ep0_cfg(void);

/*******************************************************************************
* Function Name  : usensor_resolution_select
* Description    : usensor_resolution_select
* Input          : HUSB_HANDLE *pHusb_handle
*                  u8 format:UVC_FORMAT_MJP/UVC_FORMAT_YUV
				   u8 frame_switch: 0 - default frame，1 - 切換frame
* Output         : None
* Return         : true：选择分辨率成功，保存分辨率信息到pHusb_handle->usbsta.usensor_res
*******************************************************************************/
bool usensor_resolution_select(void *handle, u8 format,u8 frame_switch);
/*******************************************************************************
* Function Name  : husb_api_ep0_kick
* Description    : husb_api_ep0_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_kick(void *handle);

/*******************************************************************************
* Function Name  : husb_api_ep0_process
* Description    : husb_api_ep0_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_ep0_process(void *handle);

/*******************************************************************************
* Function Name  : husb_api_ep0_asterncheck_kick
* Description    : husb_api_ep0_asterncheck_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_asterncheck_kick(void* handle);


#endif /* USB_HOST_ENUM_H_ */
