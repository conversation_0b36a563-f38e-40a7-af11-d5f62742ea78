/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
ALIGNED(4) BAT_CHARGE_OP_T batChargeOp;


/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskBatChargeBarShowUinit(void)
{
	if(batChargeOp.bar_win_show == 0)
	{
		return;
	}
	if(batChargeOp.main_yaddr)
		hal_sysMemFree(batChargeOp.main_yaddr);
	if(batChargeOp.sub_yaddr)
		hal_sysMemFree(batChargeOp.sub_yaddr);	
	batChargeOp.main_yaddr = NULL;
	batChargeOp.sub_yaddr  = NULL;
	batChargeOp.bar_win_show = 0;
}
/*******************************************************************************
* Function Name  : taskMainOpen
* Description    : taskMainOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskBatChargeBarShowInit(uiRect *rect)
{
	int res = 0;
	batChargeOp.sub_x = (rect->x0 + 1)&~1;
	batChargeOp.sub_y = (rect->y0 + 1)&~1;
	batChargeOp.sub_w = (rect->x1 - rect->x0 + 1 + 0x1f)&~0x1f;
	batChargeOp.sub_h = (rect->y1 - rect->y0 + 1 + 1)&~1;

	//deg_Printf("taskBatChargeBarShowInit: sub:[%d,%d,%d,%d]\n", batChargeOp.sub_x, batChargeOp.sub_y,batChargeOp.sub_w , batChargeOp.sub_h);
	batChargeOp.bar_win_show = 1;
	hal_lcdGetVideoPos(&batChargeOp.x, &batChargeOp.y);
	hal_lcdGetVideoResolution(&batChargeOp.ratio_w,&batChargeOp.ratio_h);
	hal_lcdGetVideoResolution(&batChargeOp.dest_w,&batChargeOp.dest_h);
	batChargeOp.main_stride = (batChargeOp.ratio_w + 0x1f)&~0x1f;
	batChargeOp.main_yaddr = hal_sysMemMalloc(batChargeOp.main_stride * batChargeOp.ratio_h *3/2);
	batChargeOp.sub_yaddr = hal_sysMemMalloc(batChargeOp.sub_w * batChargeOp.sub_h *3/2);
	if(batChargeOp.main_yaddr == NULL || batChargeOp.sub_yaddr == NULL)
	{
		res = -1;
		goto OUT;
	}
	batChargeOp.main_uvaddr = batChargeOp.main_yaddr + batChargeOp.main_stride * batChargeOp.ratio_h;
	batChargeOp.sub_uvaddr  = batChargeOp.sub_yaddr + batChargeOp.sub_w * batChargeOp.sub_h;
	res = res_image_decode(R_ID_IMAGE_BAT_MAIN, batChargeOp.main_yaddr, batChargeOp.ratio_w, batChargeOp.ratio_h);
	if(res < 0)
	{
		res = -2;
		goto OUT;
	}
	hx330x_sysDcacheInvalid((u32)batChargeOp.main_yaddr,batChargeOp.main_stride * batChargeOp.ratio_h *3/2);
	res = res_image_decode(R_ID_IMAGE_BAT_SUB, batChargeOp.sub_yaddr, batChargeOp.sub_w, batChargeOp.sub_h);
	if(res < 0)
	{
		res = -3;
		goto OUT;
	}
	hx330x_sysDcacheInvalid((u32)batChargeOp.sub_yaddr,batChargeOp.sub_w * batChargeOp.sub_h *3/2);
OUT:
	if(res < 0)
	{
		deg_Printf("taskBatChargeBarShowInit fail:%d\n", res);
		taskBatChargeBarShowUinit();
	}	
}

/*******************************************************************************
* Function Name  : batChargeKeyMsgOk
* Description    : batChargeKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
void taskBatChargeBarShow(void)
{
	if(batChargeOp.bar_win_show == 0)
	{
		return;
	}
	u32 i;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	do {
		hal_wdtClear();
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer == NULL);
	hal_lcdVideoFrameFlush(p_lcd_buffer, batChargeOp.x, batChargeOp.y, batChargeOp.ratio_w, batChargeOp.ratio_h, batChargeOp.dest_w, batChargeOp.dest_h);
	hx330x_sysDcacheInvalid((u32)p_lcd_buffer->y_addr,p_lcd_buffer->buf_size);
	if(batChargeOp.sub_cur_w > batChargeOp.sub_w)
	{
		batChargeOp.sub_cur_w = batChargeOp.sub_w;
	}
	batChargeOp.sub_cur_w = (batChargeOp.sub_cur_w + 1)&~1;
	for(i = 0; i < batChargeOp.ratio_h; i++)
	{
		hal_wdtClear();
		if(i < batChargeOp.sub_y || i >= (batChargeOp.sub_y + batChargeOp.sub_h))
		{
			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + batChargeOp.main_stride * i, (void*)batChargeOp.main_yaddr + batChargeOp.main_stride * i, batChargeOp.ratio_w);
			if((i&1) == 0)
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + batChargeOp.main_stride * i/2, (void*)batChargeOp.main_uvaddr + batChargeOp.main_stride * i/2, batChargeOp.ratio_w);
		}else
		{
			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + batChargeOp.main_stride * i, (void*)batChargeOp.main_yaddr + batChargeOp.main_stride * i, batChargeOp.sub_x);
			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + batChargeOp.main_stride * i + batChargeOp.sub_x, (void*)batChargeOp.sub_yaddr + batChargeOp.sub_w * (i - batChargeOp.sub_y),  batChargeOp.sub_cur_w);
			hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr + batChargeOp.main_stride * i + batChargeOp.sub_x + batChargeOp.sub_cur_w, 
											(void *)batChargeOp.main_yaddr + batChargeOp.main_stride * i + batChargeOp.sub_x + batChargeOp.sub_cur_w, 
													batChargeOp.ratio_w - batChargeOp.sub_x - batChargeOp.sub_cur_w);
			if((i&1) == 0)
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + batChargeOp.main_stride * i/2, (void*)batChargeOp.main_uvaddr + batChargeOp.main_stride * i/2, batChargeOp.sub_x);
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + batChargeOp.main_stride * i/2 + batChargeOp.sub_x, (void*)batChargeOp.sub_uvaddr + batChargeOp.sub_w * (i - batChargeOp.sub_y)/2,  batChargeOp.sub_cur_w);
				hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->uv_addr + batChargeOp.main_stride * i/2 + batChargeOp.sub_x + batChargeOp.sub_cur_w, 
												(void *)batChargeOp.main_uvaddr + batChargeOp.main_stride * i/2 + batChargeOp.sub_x + batChargeOp.sub_cur_w, 
														batChargeOp.ratio_w - batChargeOp.sub_x - batChargeOp.sub_cur_w);
			}
				
		}
	}
	hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
	hal_lcdVideoSetFrame((lcdshow_frame_t *)p_lcd_buffer);
	hal_lcdVideoSetFrameWait((lcdshow_frame_t *)p_lcd_buffer);
}
/*******************************************************************************
* Function Name  : taskBatChargeOpen
* Description    : taskBatChargeOpen
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskBatChargeOpen(uint32 arg)
{
	task_com_usb_dev_out(1);
	dusb_api_Uninit();
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);
	batChargeOp.bat_ui_show_time = XOSTimeGet();
	
	batChargeOp.bat_lcd_on      = 1;
	batChargeOp.bar_win_show	= 0;
	batChargeOp.sub_step_w		= 4;
	uiOpenWindow(&batChargeWindow, 0, 0);	
	
}
/*******************************************************************************
* Function Name  : taskBatChargeClose
* Description    : taskBatChargeClose
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskBatChargeClose(uint32 arg)
{
	taskBatChargeBarShowUinit();
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	task_com_usb_dev_out(0);
	task_com_auto_poweroff(1);
}
/*******************************************************************************
* Function Name  : taskBatChargeService
* Description    : taskBatChargeService
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskBatChargeService(uint32 arg)
{
	if(batChargeOp.bat_lcd_on)
	{
		hx330x_lcdShowWaitDone();
		XOSTimeDly(50);
		task_com_lcdbk_set(1); // back light on
		batChargeOp.bat_lcd_on = 0;
	}
	
	//电池选择RTC ADC时，无法确定什么时候充满
	//if(hardware_setup.battery_pos == ADC_CH_MVOUT || SysCtrl.dev_stat_battery  < (BATTERY_STAT_MAX-1)) //刷新熄屏
	if(1)//X86有硬件IO口识别是否充满电，所以这里置1
	{
		task_com_sreen_check(SREEN_RESET_AUTOOFF);
		task_com_auto_poweroff(1);
		if(batChargeOp.bat_ui_show_time + 100 < XOSTimeGet())
		{
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
			batChargeOp.bat_ui_show_time = XOSTimeGet();
		}		
	}
	//if(hardware_setup.battery_pos != ADC_CH_MVOUT && SysCtrl.dev_stat_battery >= (BATTERY_STAT_MAX-1) )
	//{
	//	if(batChargeOp.bat_ui_show_time + 200 < XOSTimeGet())
	//	{
	//		task_com_sreen_check(SREEN_SET_OFF);
	//		batChargeOp.bat_ui_show_time = XOSTimeGet();
	//	}
	//}

	
}

ALIGNED(4) sysTask_T taskBatCharge =
{
	"Bat Charge",
	0,
	taskBatChargeOpen,
	taskBatChargeClose,
	taskBatChargeService,
};
