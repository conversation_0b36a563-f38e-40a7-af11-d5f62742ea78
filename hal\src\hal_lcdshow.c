/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

/*******************************************************************************
* Function Name  : hal_lcdSetCsiCrop
* Description    : set csi LDMA crop
* Input          : u16 crop_sx,u16 crop_ex,u16 crop_sy,u16 crop_ey
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetCsiCrop(u16 crop_sx,u16 crop_sy,u16 crop_ex,u16 crop_ey)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    lcd_show_ctrl.crop_sx = crop_sx;
    lcd_show_ctrl.crop_sy = crop_sy;
    lcd_show_ctrl.crop_ex = crop_ex;
    lcd_show_ctrl.crop_ey = crop_ey;
    lcd_show_ctrl.lcd_scaler_process = 1;
    HAL_CRITICAL_EXIT();
    hal_lcdSetWINAB(LCDWIN_A,-1,-1,-1,-1,-1,-1);
    hal_lcdSetWINAB(LCDWIN_B,-1,-1,-1,-1,-1,-1);
}
/*******************************************************************************
* Function Name  : hal_lcdGetCsiCrop
* Description    : get csi LDMA crop region
* Input          : None
* Output         : u16 crop_sx,u16 crop_ex,u16 crop_sy,u16 crop_ey
* Return         : None
*******************************************************************************/
void hal_lcdGetCsiCrop(u16 *crop_sx,u16 *crop_sy,u16 *crop_ex,u16 *crop_ey)
{
	if(crop_sx)
    	*crop_sx = lcd_show_ctrl.crop_sx &~1;
	if(crop_sy)
    	*crop_sy = lcd_show_ctrl.crop_sy &~1;
	if(crop_sy)
		*crop_ex = lcd_show_ctrl.crop_ex &~1;
	if(crop_ey)
		*crop_ey = lcd_show_ctrl.crop_ey &~1;
}
/*******************************************************************************
* Function Name  : hal_lcdSetPlayScalerCheckDone
* Description    : 
* Input          : None
* Output         : None
* Return         : int 0: done
                    <0: busy
*******************************************************************************/
int hal_lcdSetPlayScalerCheckDone(void)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    int res = (lcd_show_ctrl.play_scaler_process) ? -1 : 0;
    HAL_CRITICAL_EXIT();
    return res;
}

/*******************************************************************************
* Function Name  : hal_lcdSetPlayScaler
* Description    : set csi LDMA crop
* Input          : u8 type: PLAY_SCALER_STAT_KICK, updata without hal_lcdVideoSetFrame
                            PLAY_SCALER_STAT_KICKBUF, updata after hal_lcdVideoSetFrame
                   u16 play_sx,u16 play_sy,u16 play_ex,u16 play_ey
* Output         : None
* Return         : None
*******************************************************************************/
int hal_lcdSetPlayScaler(u8 type, u16 play_sx,u16 play_sy,u16 play_ex,u16 play_ey)
{
    if(lcd_show_ctrl.play_scaler_process != PLAY_SCALER_STAT_NONE)
    {
        return -1;
    }
    //deg_Printf("hal_lcdSetPlayScaler in[%x][%d,%d,%d,%d]\n",type, play_sx,play_sy,
    //    play_ex, play_ey);
    if(type == PLAY_SCALER_STAT_KICK || type == PLAY_SCALER_STAT_KICKBUF)
    {
        HAL_CRITICAL_INIT();
        HAL_CRITICAL_ENTER();
        if(lcd_show_ctrl.video_need_rotate)
        {
            lcd_show_ctrl.play_sx = play_sy&~1;
            lcd_show_ctrl.play_sy = play_sx&~1;
            lcd_show_ctrl.play_ex = play_ey&~1;
            lcd_show_ctrl.play_ey = play_ex&~1;  
        }else
        {
            lcd_show_ctrl.play_sx = play_sx&~1;
            lcd_show_ctrl.play_sy = play_sy&~1;
            lcd_show_ctrl.play_ex = play_ex&~1;
            lcd_show_ctrl.play_ey = play_ey&~1;            
        }
        lcd_show_ctrl.play_scaler_process = type;
        //deg_Printf("hal_lcdSetPlayScaler[%x][%d,%d,%d,%d]\n",lcd_show_ctrl.play_scaler_process, lcd_show_ctrl.play_sx,lcd_show_ctrl.play_sy,
        //lcd_show_ctrl.play_ex, lcd_show_ctrl.play_ey);
        HAL_CRITICAL_EXIT();
        return 0;
    }
    return -2;
}

/*******************************************************************************
* Function Name  : hal_lcdVideoSetRotate
* Description    : hardware layer ,set lcd video rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
* Output         : None
* Return         :
*******************************************************************************/
//ROTATE      |  0    |   180  |   90  |  270  |    H_M   |  V_M
//---------------------------------------------------------------
//         0  |  0    |   180  |   90  |  270  |    H_M   |  V_M
//         90 |  90   |   270  |  180  |    0  |   90+H_M |  90+V_M
//        180 |  180  |     0  |  270  |   90  |    V_M   |  H_M
//        270 |  270  |    90  |    0  |  180  |   90+V_M |  90+H_M
//        H_M |  H_M  |   V_M  | 90+H_M|90+V_M |     0    |  180
//        V_M |  V_M  |   H_M  | 90+V_M|90+H_M |    180   |  0
//----------------------------------------------------------------
s32 hal_lcdVideoSetRotate(u8 rotate)
{

    u8 target = lcd_show_ctrl.p_lcddev->scan_mode + rotate;
    u8 rotate_mode;
    u8 mirror_mode = LCD_DISPLAY_ROTATE_0;
    switch(target & (LCD_DISPLAY_ROTATE_MASK|LCD_DISPLAY_MIRROR_MASK))
    {
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_180; break;
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_V_MIRROR) :
        case (LCD_DISPLAY_H_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_0;   break;
        default: mirror_mode = target & LCD_DISPLAY_MIRROR_MASK;
    }
    rotate_mode = (target & LCD_DISPLAY_ROTATE_MASK);
    if((rotate_mode >= LCD_DISPLAY_ROTATE_180 ) && (mirror_mode != LCD_DISPLAY_ROTATE_0))
    {
        rotate_mode -= LCD_DISPLAY_ROTATE_180;
        if(mirror_mode == LCD_DISPLAY_V_MIRROR)
        {
            mirror_mode = LCD_DISPLAY_H_MIRROR;
        }else
        {
            mirror_mode = LCD_DISPLAY_V_MIRROR;
        }
    }
    deg_Printf("[LCD SET ROTATE] rotate:%x, mirror:%x\n",rotate_mode, mirror_mode );
    if( mirror_mode != LCD_DISPLAY_MIRROR_NONE && rotate_mode != LCD_DISPLAY_ROTATE_0 &&  rotate_mode!= LCD_DISPLAY_ROTATE_90)
    {
        return -1;
    }
    lcd_show_ctrl.video_scan_mode = rotate_mode + mirror_mode;
    if(lcd_show_ctrl.video_need_rotate != (lcd_show_ctrl.video_scan_mode & LCD_DISPLAY_ROTATE_EN))
    {
        lcd_show_ctrl.video_need_rotate = (lcd_show_ctrl.video_scan_mode & LCD_DISPLAY_ROTATE_EN);
        lcd_show_ctrl.video_w     = lcd_show_ctrl.p_lcddev->video_h;
        lcd_show_ctrl.video_h     = lcd_show_ctrl.p_lcddev->video_w;
        lcd_show_ctrl.video_scaler_w     = lcd_show_ctrl.p_lcddev->video_scaler_h;
        lcd_show_ctrl.video_scaler_h     = lcd_show_ctrl.p_lcddev->video_scaler_w;
        //lcd_show_ctrl.ui_w     = lcd_show_ctrl.p_lcddev->ui_h;
        //lcd_show_ctrl.ui_h     = lcd_show_ctrl.p_lcddev->ui_w;
    }
    deg_Printf("[LCD VIDEO ROTATE]%x\n", lcd_show_ctrl.video_scan_mode);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdVideoSetRotate
* Description    : hardware layer ,set lcd video rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
* Output         : None
* Return         :
*******************************************************************************/
void hal_lcdVideoSetRotate180(u8 rotate_180)
{
    static u8 scan_mode_save = 0xff;
    if(scan_mode_save == 0xff)
    {
        scan_mode_save = lcd_show_ctrl.video_scan_mode;
    }
    
    
    u8 scan_mode;
    if(rotate_180)
    {
        u8 rotate_mode,mirror_mode;
        scan_mode = scan_mode_save + LCD_DISPLAY_ROTATE_180;
        rotate_mode = (scan_mode & LCD_DISPLAY_ROTATE_MASK);
        mirror_mode = scan_mode & LCD_DISPLAY_MIRROR_MASK;
        if((rotate_mode >= LCD_DISPLAY_ROTATE_180 ) && (mirror_mode != LCD_DISPLAY_ROTATE_0))
        {
            rotate_mode -= LCD_DISPLAY_ROTATE_180;
            if(mirror_mode == LCD_DISPLAY_V_MIRROR)
            {
                mirror_mode = LCD_DISPLAY_H_MIRROR;
            }else
            {
                mirror_mode = LCD_DISPLAY_V_MIRROR;
            }
        }  
        scan_mode = rotate_mode|mirror_mode;
    }else
    {
        scan_mode = scan_mode_save;
    }
    deg_Printf("Rotate180:%x,%x\n", scan_mode_save, scan_mode);
    HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
    lcd_show_ctrl.video_scan_mode = scan_mode;
    HAL_CRITICAL_EXIT();
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdUiSetRotate
* Description    : hardware layer ,set lcd ui rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdUiSetRotate(u8 rotate)
{
    u8 src_mode = lcd_show_ctrl.p_lcddev->scan_mode & LCD_DISPLAY_ROTATE_MASK;
    rotate = rotate & LCD_DISPLAY_ROTATE_MASK;
    u8 target_mode = (src_mode + rotate) & LCD_DISPLAY_ROTATE_MASK;

    lcd_show_ctrl.ui_scan_mode = target_mode;
    if(lcd_show_ctrl.ui_need_rotate != (lcd_show_ctrl.ui_scan_mode & LCD_DISPLAY_ROTATE_EN))
    {
        lcd_show_ctrl.ui_w     = lcd_show_ctrl.p_lcddev->ui_h;
        lcd_show_ctrl.ui_h     = lcd_show_ctrl.p_lcddev->ui_w;
        lcd_show_ctrl.ui_x     = lcd_show_ctrl.p_lcddev->ui_y;
        lcd_show_ctrl.ui_y     = lcd_show_ctrl.p_lcddev->ui_x;
    }
    lcd_show_ctrl.ui_need_rotate = (lcd_show_ctrl.ui_scan_mode & LCD_DISPLAY_ROTATE_EN);
    deg_Printf("[LCD UI][%x,%x,%x,%x]\n", lcd_show_ctrl.ui_w,lcd_show_ctrl.ui_h,lcd_show_ctrl.ui_x,lcd_show_ctrl.ui_y);
    deg_Printf("[LCD UI ROTATE]%x\n", lcd_show_ctrl.ui_scan_mode);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdRatioResCal
* Description    : calculate resolution of ratio
* Input          : u8 ratio_w,u8 ratio_h
                   u16 *width : width
                   u16 *height : height
* Output         : none
* Return         :0
*******************************************************************************/
static void hal_lcdRatioResCal(u8 ratio_w,u8 ratio_h,u16 *width,u16 *height)
{
    int ratw,rath;
 	u16 tar_width, tar_height;
    if(width == NULL || height == NULL)
		return ;

	ratw = *width/ratio_w;
	rath = *height/ratio_h;
	if(ratw == rath)
		return;

	tar_height = ratw*ratio_h; //120*3 = 360
	if(tar_height > *height)
	{
		tar_width = rath*ratio_w; //91*4 = 364
		if(tar_width < *width)
			*width = tar_width;
	}
	else
		*height = tar_height;
    return;
}

/*******************************************************************************
* Function Name  : hal_lcdSetRatio
* Description    : hal_lcdSetRatio
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : None
* Return         :
*******************************************************************************/
void hal_lcdSetRatio(u16 ratio)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    u8 ratio_w = LCD_RATIO_GET_W(ratio);
    u8 ratio_h = LCD_RATIO_GET_H(ratio);
    lcd_show_ctrl.ratio_w = lcd_show_ctrl.video_w;
    lcd_show_ctrl.ratio_h = lcd_show_ctrl.video_h;
    lcd_show_ctrl.ratio_dest_w = lcd_show_ctrl.video_scaler_w;
    lcd_show_ctrl.ratio_dest_h = lcd_show_ctrl.video_scaler_h;
    lcd_show_ctrl.ratio_x  = lcd_show_ctrl.video_x;
    lcd_show_ctrl.ratio_y  = lcd_show_ctrl.video_y;
    if(ratio_w && ratio_h )
    {
        hal_lcdRatioResCal(ratio_w,ratio_h, &lcd_show_ctrl.ratio_dest_w, &lcd_show_ctrl.ratio_dest_h);
        if(lcd_show_ctrl.ratio_dest_w < lcd_show_ctrl.ratio_w )
        {
           lcd_show_ctrl.ratio_w =  lcd_show_ctrl.ratio_dest_w;
        }
        if(lcd_show_ctrl.ratio_dest_h < lcd_show_ctrl.ratio_h )
        {
           lcd_show_ctrl.ratio_h =  lcd_show_ctrl.ratio_dest_h;
        }
        lcd_show_ctrl.ratio_x +=  (lcd_show_ctrl.video_scaler_w - lcd_show_ctrl.ratio_dest_w)/2;
        lcd_show_ctrl.ratio_y +=  (lcd_show_ctrl.video_scaler_h - lcd_show_ctrl.ratio_dest_h)/2;
    }
    deg_Printf("[LCD ratio][%d:%d] pos[%d,%d], res[%d,%d], dest[%d,%d]\n",
    ratio_w, ratio_h, lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
    lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h, lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
    HAL_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : hal_lcdVideoNeedRotateGet
* Description    : hardware layer ,get lcd video need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdVideoNeedRotateGet(void)
{
    return (bool) lcd_show_ctrl.video_need_rotate;
}
/*******************************************************************************
* Function Name  : hal_lcdUiNeedRotateGet
* Description    : hardware layer, get lcd ui need rotate or not
* Input          : u8 rotate : rotate degree
* Output         : None
* Return         :
*******************************************************************************/
bool hal_lcdUiNeedRotateGet(void)
{
    return (bool) lcd_show_ctrl.ui_need_rotate;
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV(lcdshow_frame_t * buffer,u8 y,u8 u,u8 v)
{
    if(buffer)
    {
        u32 y_size  = buffer->ratio_h * buffer->stride;
        u32 uv_size = y_size / 2;
        memset(buffer->y_addr,y,y_size);
        hx330x_sysDcacheWback((u32)buffer->y_addr,y_size);
        memset(buffer->uv_addr,u,uv_size);
        hx330x_sysDcacheWback((u32)buffer->uv_addr,uv_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV_2(u8 * buffer,u32 buf_size,u8 y,u8 uv)
{
    if(buffer)
    {
		u32 y_size = buf_size*2/3;
        memset(buffer,y,y_size);
		memset(buffer+y_size,uv,y_size/2);
        hx330x_sysDcacheWback((u32)buffer,buf_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetWINAB
* Description    : set lcd video channles
* Input          : u8 src:video channle source,enum {LCDWIN_B,LCDWIN_A}
*                  u8 layer:video channle layer,enum {LCDWIN_TOP_LAYER,LCDWIN_BOT_LAYER}
*                  u16 x:if x == -1,means don't change this parameter
*                  u16 y:
*                  u16 w:
*                  u16 h:
*                  u8 win_en:channle enable,enum {WINAB_EN,WINAB_DIS},if win_en == -1,means don't change
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWINAB(u8 src,u8 layer,
                  u16 x,u16 y,u16 w,u16 h,
                  u8 win_en)
{

    lcd_win_t * p_lcd_vch = lcd_show_ctrl.lcdwins[src]->bak;
    //deg_Printf("[%d:%d] pos[%d,%d], res[%d,%d]\n", src, layer, x, y,w, h);
    if(p_lcd_vch)
    {
        p_lcd_vch->config_enable = 0;
        if(x != 0xffff)    p_lcd_vch->x = x & ~7;
        if(y != 0xffff)    p_lcd_vch->y = y & ~1;
        if(w != 0xffff)    p_lcd_vch->w = w;
        if(h != 0xffff)    p_lcd_vch->h = h;
        if(layer != 0xff)  p_lcd_vch->layer = layer;
        if(win_en != 0xff) p_lcd_vch->status = win_en;
        p_lcd_vch->config_enable = 1;
    }


}
/*******************************************************************************
* Function Name  : hal_lcdWinEnablePreSet
* Description    : prepare set lcd win enable/disbale,
*                  take effect when next csi frame done
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdWinEnablePreSet(u8 enable)
{
    lcd_show_ctrl.winAB_EN = enable;
}
/*******************************************************************************
* Function Name  : hal_lcdSetWinEnable
* Description    : set lcd win enable/disbale,take effect immediately
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWinEnable(u8 enable)
{
    lcd_show_ctrl.winAB_EN = enable;
    hx330x_lcdWinABEnable(enable);
}
/*******************************************************************************
* Function Name  : lcd_struct_get
* Description    : lcd_struct_get
* Input          :
* Output         : lcddev_t * p_lcd_struct
* Return         : none
*******************************************************************************/
lcddev_t * lcd_struct_get(void)
{
	return lcd_show_ctrl.p_lcddev;
}
/*******************************************************************************
* Function Name  : hal_lcdLCMPowerOff
* Description    : lcd module power off sequence
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdLCMPowerOff(void)
{
    if(lcd_show_ctrl.p_lcddev && lcd_show_ctrl.p_lcddev->uninit_table)
    {
        if(lcd_show_ctrl.p_lcddev->lcd_bus_type == LCD_BUS_MCU)
        {
            hx330x_lcdEnable(0);
            hx330x_lcdTeMode(0, 0);
            hx330x_lcdEnable(1);
        }
        lcd_initTab_config(lcd_show_ctrl.p_lcddev->uninit_table);
    }
}

/*******************************************************************************
 * WINAB PROCESS有三种
 *  WINAB_PROCESS_TYPE_DECWINSRC_ENCSRC：UVC 同时解码到 WIN和SRC，然后用SRC的YUV进行ENC,经历以下STEP：
 * (1) WINAB_PROCESS_STEP_END:
 * -读UVC FRAME，成功
 *      -如果不需要ENC，则进入 WINAB_PROCESS_STEP_0_DECWIN ，只需要DEC到WIN,  KICK LCD DMA
 *      -如果需要ENC，则进入 WINAB_PROCESS_STEP_0_DECSRC ，同时DEC到WIN和SRC, KICK LCD DMA
 * -读UVC FRAME，失败， WINAB_PROCESS_STEP_END, KICK LCD DMA
 * (2) WINAB_PROCESS_STEP_0_DECWIN：等待DEC WIN解码完成，结束。
 * - 解码成功：LB_READY1， FREE UVC FRAME，WINAB_PROCESS_STEP_END
 * - 解码失败：FREE UVC FRAME（ERROR），WINAB_PROCESS_STEP_END
 * (3) WINAB_PROCESS_STEP_0_DECSRC: 等待DEC SRC解码完成
 * - 解码成功：LB_READY1， FREE UVC FRAME，判断外部是否停止ENC,
 *      - 没有停止 ENC: kick enc SRC (不需要配linebuf)
 *      - 停止 ENC : WINAB_PROCESS_STEP_END
 * - 解码失败：FREE UVC FRAME（ERROR）， WINAB_PROCESS_STEP_END
 * (4) ENC_DONE: WINAB_PROCESS_STEP_END
 * *******************************************************************************/

/*******************************************************************************
* Function Name  : hal_lcd_getJpgIdleBuf
* Description    : hal_lcd_getJpgIdleBuf
* Input          : none
* Output         : None
* Return         : bool: true: no kic lcd dma, false: kick lcd dma
*******************************************************************************/
static void hal_lcd_win_dec_kick(void)
{
    //lcd_show_ctrl.jpg_dcd_buf = NULL;
	if(husb_api_usensor_tran_sta())
    {
        //deg_Printf("E");
        if(lcd_show_ctrl.winAB_process_step != WINAB_PROCESS_STEP_END)
        {
            return ;
        }
        //deg_Printf("F");
        lcd_show_ctrl.jpg_dcd_buf = husb_api_usensor_frame_read(&lcd_show_ctrl.jpg_dcd_len);
        if(lcd_show_ctrl.jpg_dcd_buf && lcd_show_ctrl.jpg_dcd_len)
		{
            lcd_show_ctrl.jpg_frame_sta = 1;
            lcd_show_ctrl.jpg_dcd_len += 64;
			hx330x_mjpB_reset();
            if(hx330x_mjpB_Encode_StartFunc_Check() == false)
            {
                hal_mjpB_DecodeODMA1En(0);
                lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_0_DECWIN;
                lcd_show_ctrl.lcd_dma_sta = 0;

            }else
            {
                if(hal_mjpDecodeIsYUV422())
                {
                    hal_mjpB_DecodeODMA1En(1);
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_0_DECSRC;
                    lcd_show_ctrl.lcd_dma_sta = 0; //will kick lcd dma
                }else
                {
                    hal_mjpB_DecodeODMA1En(0);
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_1_DECWIN;
                    lcd_show_ctrl.lcd_dma_sta = 1;
                }



            }
            lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
            u8 * p_y = &lcd_show_ctrl.win_using->y_addr[p_WINB->x +
                       (p_WINB->y * lcd_show_ctrl.win_using->stride)];
            u8 * p_uv= &lcd_show_ctrl.win_using->uv_addr[p_WINB->x +
                       (p_WINB->y / 2 * lcd_show_ctrl.win_using->stride)];
            //deg_Printf("JPG:[%x][%d,%d][%d,%d,%d]\n",p_uv-p_y,p_WINB->x, p_WINB->y,  p_WINB->w,p_WINB->h,lcd_show_ctrl.win_using->stride);
            hal_mjpDecodeOneFrame_Fast(lcd_show_ctrl.jpg_dcd_buf,lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len,
                                        p_y,p_uv,
                                        p_WINB->w,p_WINB->h,
                                        lcd_show_ctrl.win_using->stride);
            //如果JPG没有DRI，可能导致JPG丢包但解码仍然成功，所以指定JPIEND地址，会产生DC TIME OUT 中断
            //XSFR_JPEG1_IN_EADDR = ((u32)lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len + 7)&~0x07;
            //if(lcd_show_ctrl.winAB_process_step == WINAB_PROCESS_STEP_0_DECSRC)
            //{
            //     XSFR_JPEG1_INTCON   = (XSFR_JPEG1_INTCON &~(1<<6))|(1<<2); //only enable yuv422 src img decode int
            //}
            //deg_Printf("G");
        }else{
            lcd_show_ctrl.jpg_frame_sta = 0;
            lcd_show_ctrl.lcd_dma_sta = 0;
			lcd_show_ctrl.jpg_dcd_buf = NULL;
        }
        //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);

	}else
	{
		lcd_show_ctrl.jpg_dcd_buf = NULL;
	}
}
/*******************************************************************************
* Function Name  : hal_lcd_wintotal_dec_kick
* Description    : hal_lcd_wintotal_dec_kick
* Input          : none
* Output         : None
* Return         : bool: true: no kic lcd dma, false: kick lcd dma
*******************************************************************************/
static bool hal_lcd_wintotal_dec_kick(void)
{
	if(lcd_show_ctrl.jpg_dcd_buf)
	{
        u32 linebuf_y, linebuf_uv;
        u16 sensor_w, sensor_h;
	    hal_mjpB_LineBuf_get(&linebuf_y, &linebuf_uv);
        husb_api_usensor_res_get(&sensor_w, &sensor_h);
        hx330x_lcdWinABEnable(0);
        lcd_show_ctrl.win_reset_en = 1;
        hx330x_mjpB_reset();
		hal_mjpDecodeOneFrame_Fast(lcd_show_ctrl.jpg_dcd_buf,lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len,
                                    (u8*)linebuf_y,(u8*)linebuf_uv,
                                         sensor_w,sensor_h,
                                         (sensor_w + 0x1f)&~0x1f);
		//如果JPG没有DRI，可能导致JPG丢包但解码仍然成功，所以指定JPIEND地址，会产生DC TIME OUT 中断
		//XSFR_JPEG1_IN_EADDR = ((u32)lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len + 7)&~0x07;
        return true;

	}
    return false;
}
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_decwin_done(int flag)
{
    u8 kick_enc = 0;
    u8 usensor_frame_free  = 0;
    u8 dec_sta = 0;
    u8 kick_lcddma = 0;
    if(!lcd_show_ctrl.win_using) return;

    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);


	if(flag & (BIT(1)|BIT(8)|BIT(5)|BIT(12)|BIT(13)))//error,不管处于哪个step，都要结束，同时释放usensor_frame
    {
        //deg_Printf("err:%x\n", flag);
        goto LCD_DECWIN_FAIL;
	}else if(flag & (BIT(2)|BIT(6)))
    {
        //if(!(flag & BIT(1)))
        //{
        //    deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        //    deg_Printf("err:%x\n", flag);
        //    goto LCD_DECWIN_FAIL;
        //}else  //decode win done
        {
            if(flag & BIT(6))
            {
                switch(lcd_show_ctrl.winAB_process_step)
                {
                    case WINAB_PROCESS_STEP_0_DECWIN:   //不用enc，直接结束
                        usensor_frame_free = 1;
                        lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        break;
                    case WINAB_PROCESS_STEP_0_DECSRC:   //要等decode src done再启动encode
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        //usensor_frame_free = 1;
                        break;
                    case WINAB_PROCESS_STEP_1_DECWIN:   //再启动一次DEC
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        if(hx330x_mjpB_Encode_StartFunc_Check() == false || hal_lcd_wintotal_dec_kick() == false)
                        {
                            //deg_Printf("wintotal_dec_kick fail\n");
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                            kick_lcddma = 1;
                            usensor_frame_free = 1;
                        }else
                        {
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_1_DECWINTOTAL;
                        }
                        break;
                    case WINAB_PROCESS_STEP_1_DECWINTOTAL: //配置buf，启动ENC，释放 uvc frame, 重配win并kick lcd dma,注意需要在ENC DONE释放jpg_using
                        if(lcd_show_ctrl.win_reset_en == 1)
                        {
                            hx330x_lcdWinABEnable(1);
                            lcd_show_ctrl.win_reset_en = 0;
                        }
                        kick_lcddma = 1;
                        usensor_frame_free = 1;
                        if(hx330x_mjpB_Encode_StartFunc_Check() == true)
                        {
                            kick_enc = 1;
                        }else
                        {
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                        }
                        break;
                }

            }
            if(lcd_show_ctrl.winAB_process_step == WINAB_PROCESS_STEP_0_DECSRC) //启动src encode，linebuf固定
            {
                lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                usensor_frame_free = 1;
                if((flag & BIT(2)) && (hx330x_mjpB_Encode_StartFunc_Check() == true))
                {
                    kick_enc = 1;
                }else
                {
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                }
            }
            goto LCD_DECWIN_OK;

        }    
    }else
    {
        deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        deg_Printf("err:%x\n", flag);      
    }
LCD_DECWIN_FAIL:
    usensor_frame_free = 1;
	dec_sta = 1;
    kick_lcddma = 1;
    if(lcd_show_ctrl.win_reset_en == 1)
    {
        hx330x_lcdWinABEnable(1);
        lcd_show_ctrl.win_reset_en = 0;
    }else
    {
        hx330x_lcdWinReset(WIN_SRC_B);
    }
    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
    //deg_Printf("err:%x\n", flag);
    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
LCD_DECWIN_OK:
    
    //deg_Printf("1[%d]\n",lcd_show_ctrl.winAB_process_step);
    if(usensor_frame_free && lcd_show_ctrl.jpg_frame_sta)
    {
        husb_api_usensor_dcdown(dec_sta);
        lcd_show_ctrl.jpg_frame_sta = 0;
        //deg_Printf("A");
    }
    if(kick_lcddma && lcd_show_ctrl.lcd_dma_sta)
    {
        hx330x_csiLCDDmaKick();
        lcd_show_ctrl.lcd_dma_sta = 0;
    //    deg_Printf("B");
    }
    if(kick_enc)
    {
		hx330x_mjpB_Encode_StartFunc_call(); //call hal_mjpB_manualstart
        //deg_Printf("C");
    }

}
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_encwin_done(void)
{
    if(!lcd_show_ctrl.win_using) return;
    if(lcd_show_ctrl.win_reset_en == 1)
    {
        hx330x_lcdWinABEnable(1);
        lcd_show_ctrl.win_reset_en = 0;
    }
    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
    if(lcd_show_ctrl.jpg_frame_sta && lcd_show_ctrl.jpg_dcd_buf)
    {
        husb_api_usensor_dcdown(0);
        lcd_show_ctrl.jpg_frame_sta = 0;
    }
    if( lcd_show_ctrl.lcd_dma_sta)
    {
        hx330x_csiLCDDmaKick();
        lcd_show_ctrl.lcd_dma_sta = 0;
    //    deg_Printf("B");
    }
    //deg_Printf("P");
}
/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndCallback
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/

void hal_CSI_lcdFrameEndCallback(void)
{
    lcdshow_frame_t * ready = lcd_show_ctrl.win_using;
    lcdshow_frame_t * next = NULL;
	#if 1//FUNC_AF_ENABLE
    u8 af_flag;
    #endif
    if(!ready)
    {
        //never run here
        deg_Printf("[LCD Err] csi using null\n");
        return;
    }

    //debgchar('-');
    hal_lcdWinUpdata();

    lcd_win_t * p_WINA = lcd_show_ctrl.lcdwins[LCDWIN_A];
    lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
    lcd_win_t * p_WIN_top = lcd_show_ctrl.lcdwins[LCDWIN_TOP_LAYER];

    u8 not_skip = 1;
    lcd_show_ctrl.lcd_dma_sta = 0;
    lcd_show_ctrl.win_reset_en = 0;
    //config lcdwinAB,必须在csi没有DMA，且JPGB也没有DMA的情况下修改lcdwinAB配置
    if(p_WIN_top->config_enable && (p_WIN_top->status == WINAB_EN))
    {
        //hx330x_csiLCDDmaEnable(0);
        hx330x_lcdWinABConfig(p_WIN_top->src,
                         p_WIN_top->x,p_WIN_top->y,
                         p_WIN_top->w,p_WIN_top->h,
                         lcd_show_ctrl.winAB_EN);
        hx330x_lcdVideoSetScaleLine(p_WIN_top->y + p_WIN_top->h - 1,1);
        //deg_Printf("[WIN%d]%x, %x, %x,%x,%x\n",p_WIN_top->src, XSFR_LCDPIP_CON,XSFR_LCDPIP_POS,XSFR_LCDPIP_BSIZE, XSFR_LCDC_SHOW_CTRL );
        //deg_Printf("pip:%d, [%d,%d],[%d,%d]%d\n",p_WIN_top->src,
        //                 p_WIN_top->x,p_WIN_top->y,
        //                 p_WIN_top->w,p_WIN_top->h, lcd_show_ctrl.winAB_EN);
        //deg_Printf("JPG:[%d,%d][%d,%d,%d]\n",p_WINB->x, p_WINB->y,
        //p_WINB->w,p_WINB->h,lcd_show_ctrl.win_using->stride);
        ///hx330x_csiLCDDmaEnable(1);
    }
    //config linescaler
    if(p_WINA->config_enable)
    {
        not_skip = 0;//skip this frame

		hx330x_csiLCDScaler(p_WINA->w,p_WINA->h,
                             lcd_show_ctrl.crop_sx,lcd_show_ctrl.crop_sy,lcd_show_ctrl.crop_ex,lcd_show_ctrl.crop_ey,
							ready->stride);
    }

    ready->win_sta |= LB_READY0;
    u8  buf_ready = LB_READY0;
    if (p_WINB->status == WINAB_EN)
    {
        buf_ready |= LB_READY1;
    }
    if(hardware_setup.lcd_first_drop)
    {
        hardware_setup.lcd_first_drop--;
        not_skip = 0;
    }
    if(((ready->win_sta & buf_ready) == buf_ready) && not_skip)
    {
        next = hal_lcdVideoIdleFrameMalloc();
        if(next)
        {
            //update display buffer
			if(hx330x_MJPA_EncodeLcdPre_Func_Check())
            {
                hx330x_MJPA_EncodeLcdPre_Func_call((u32)ready->y_addr, (u32)ready->uv_addr);
            }   
            hal_lcdVideoSetFrame(ready);
           	#if 1//FUNC_AF_ENABLE
            af_flag= task_common_get_af_state();
            if(AF_FLAG_GETTING_FIRST_BUFFER == af_flag || AF_FLAG_GETTING_SECOND_BUFFER == af_flag){// getting buffer
                if(task_common_get_af_handler_buffer()){
                    hx330x_word_memcpy(task_common_get_af_handler_buffer(), 
                                            ready->y_addr+(ready->stride*(ready->h-60)/2), 
                                            320*60);
                    if(AF_FLAG_GETTING_FIRST_BUFFER == af_flag)
                        task_common_set_af_state(AF_FLAG_GOT_FIRST_BUFFER);// get buffer done
                    else if(AF_FLAG_GETTING_SECOND_BUFFER == af_flag)
                        task_common_set_af_state(AF_FLAG_GOT_SECOND_BUFFER);// get buffer done
                }
            }
            #endif
            //config next frame
			u16 csi_w,csi_h;
			//hal_SensorResolutionGet(&csi_w,&csi_h);
            hal_SensorRatioResolutionGet(&csi_w,&csi_h);
            lcd_show_ctrl.ratio_w = hx330x_min(csi_w,lcd_show_ctrl.ratio_w);
		    lcd_show_ctrl.ratio_h = hx330x_min(csi_h,lcd_show_ctrl.ratio_h);
            //deg_Printf("next:pos[%d,%d],src[%d,%d],dst[%d,%d]\n",lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
            //                       lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
            //                       lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            hal_lcdVideoFrameFlush(next,
                                   lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
                                   lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
                                   lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * next->stride);
            u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * next->stride);

            hx330x_csiLCDFrameSet((u32)&next->y_addr[csi_y_ofs],
                                  (u32)&next->uv_addr[csi_uv_ofs]);
            //updata using
            lcd_show_ctrl.win_using = next;
        }
    }

    if(!next)
    {
        u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * ready->stride);
        u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * ready->stride);
        hx330x_csiLCDFrameSet((u32)&ready->y_addr[csi_y_ofs],
                              (u32)&ready->uv_addr[csi_uv_ofs]);
        ready->win_sta = LB_IDLE;
    }
    p_WINA->config_enable =
    p_WINB->config_enable = 0;

    if((p_WINB->status == WINAB_EN) &&
        (hal_BackRecDecodeStatusCheck()) &&
        hx330x_csiLCDScalerDoneCheck())
    {
        hal_lcd_win_dec_kick();
    }
    if(lcd_show_ctrl.lcd_dma_sta == 0)
        hx330x_csiLCDDmaKick();

    if(not_skip && next)
    {
        lcd_show_ctrl.lcd_scaler_process = 0;
    }
}
/*******************************************************************************
* Function Name  : hal_lcd_pause_set
* Description    : hal_lcd_pause_set
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_pause_set(u8 en)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    lcd_show_ctrl.lcd_pause_en = en;
    HAL_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : hal_lcd_pause_sta_get
* Description    : hal_lcd_pause_sta_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u8 hal_lcd_pause_sta_get(void)
{
    u8 sta;
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    sta = lcd_show_ctrl.lcd_pause_en;
    HAL_CRITICAL_EXIT();
    return sta;
}
/*******************************************************************************
* Function Name  : hal_lcdSetGamma
* Description    :
* Input          : lcddev_t *p_lcddev : lcd op node
* Output         : none
* Return         : none
*******************************************************************************/
extern int _lcd_res_lma;
extern int _lcd_res_size;
#define LCD_RES_FLASH_ADDR(x)       ((u32)(x) + ((u32)&_lcd_res_lma))
extern u32 lcd_gamma[12][128];
extern u8 lcd_contra_tab[13][256];
void hal_lcdSetGamma(lcddev_t * desc)
{
	const u32 contra_len = sizeof(lcd_contra_tab[0]);
	const u32 gamma_len = sizeof(lcd_gamma[0]);

    u32 i = 0;
	u8 * buffer = hal_sysMemMalloc(contra_len + 3 * gamma_len);
	u8 * contra = buffer;
	u8 * gamma = contra + contra_len;

    // load contra tab
    if (desc->contra_index < ARRAY_NUM(lcd_contra_tab))
    {
        hal_spiFlashRead(LCD_RES_FLASH_ADDR(&lcd_contra_tab) + desc->contra_index * contra_len,
                        (u32)contra,
                        contra_len);
    }

    // load RGB gamma tab
    for (i = 0; i < 3; i++)
    {
        if (desc->gamma_index[i] < ARRAY_NUM(lcd_gamma))
        {
            hal_spiFlashRead(LCD_RES_FLASH_ADDR(&lcd_gamma) + desc->gamma_index[i] * gamma_len,
                            (u32)(gamma + i * gamma_len),
                            gamma_len);
        }
    }
	for (i = 0; i < gamma_len * 3; i++)
	{
		s32 temp;
		temp = gamma[i];
		temp = temp + (-(s32)desc->brightness) * (temp + 1) / 256;
		temp = hx330x_clip(temp, 0, 255);
		gamma[i] = contra[temp];
	}

    hx330x_lcdVideoSetGAMA((u32)&gamma[0], (u32)&gamma[gamma_len], (u32)&gamma[2 * gamma_len]);
    hal_sysMemFree(buffer);
}
