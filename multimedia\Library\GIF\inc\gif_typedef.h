/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  GIF_TYPEDEF_H
#define  GIF_TYPEDEF_H

#define  GIF_SIGNATURE_TAB				0x464947//"GIF"
#define  GIF_VERSION0_TAB				0x613738//"87a"
#define  GIF_VERSION1_TAB				0x613938//"89a"

#define  GIF_IMAGE_SEPARATOR_FLAG		0x2c //','
#define  GIF_EXINTRODUCER_FLAG			0x21 //'!'
#define  GIF_EXINTRODUCER_ENDFLAG		0x00 //
#define  GIF_GRAPCRLLABEL_FLAG			0xf9 //''
#define  GIF_PLAINTEXTLABEL_FLAG		0x01 
#define  GIF_COMMENTLABEL_FLAG			0xfe
#define  GIF_EXTENSIONLABEL_FLAG		0xff
#define  GIF_TRAILER_FLAG				0x3B //';'

//GIF文件头
typedef struct GIFHEADER_S
{
    BYTE bySignature[3];
    BYTE byVersion[3];
} __attribute__((packed)) GIFHEADER;
//逻辑屏幕描述块
typedef struct GIFSCRDESC_S
{
    WORD wWidth;  //屏的宽度
    WORD wHeight; //屏的高度
    struct GlobalFlag_S
    {
        BYTE PalBits     : 3; //全局彩色表大小:每个像素的位数, 它用来计算全局彩色表(Global Color Table)中包含的字节数, 在全局彩色表标志(Global Color Table Flag)域G＝0时就不需要计算，G＝1时就要计算彩色表的大小
        BYTE SortFlag    : 1; //彩色表排序标志:如果S＝0，表示没有重要性排序；如果S＝1表示最重要的颜色排在前。这样做的目的是辅助颜色数比较少的解码器能够选择最好的颜色子集，在这种情况下解码器就可选择彩色表中开始段的彩色来显示图像
        BYTE ColorRes    : 3; //彩色分辨率: 用来表示原始图像可用的每种基色的位数(实际值减1),这个位数表示整个调色板的大小，而不是这幅图像使用的实际的颜色数。例如，如果该域的值CR＝3，说明原始图像可用每个基色有4位的调色板来生成彩色图像
        BYTE GlobalPal   : 1; //全局彩色表标志, 1: 有一个全局彩色表(Global Color Table); 背景颜色索引(BackgroundColor Index)域中的值就用作背景颜色的索引
    }GlobalFlag;
    BYTE byBackground; //背景颜色索引(BackgroundColor Index)，它是彩色表的一个索引值，用来指定背景颜色。如果全局彩色表标志(Global Color Table Flag)域G＝0，这个域的值也设置为
    BYTE byAspect;//像素宽高比(PixelAspect Ratio)域中的值是一个因数，是计算原始图像像素的宽高比的一个近似值。如果该域的值范围为1～255,  Aspect Ratio = (Pixel AspectRatio + 15) / 64
				  //像素宽高比(PixelAspect Ratio)定义成像素的宽度与高度之比，比值的范围在4:1～1:4之间，其增量为1/64。
} __attribute__((packed)) GIFSCRDESC;

//(可选)全局彩色表(Global Color Table):GlobalPal = 1, index max = 彩色表的表项数目等于2^(n +1), n = PalBits
//BYTE:red intensity 000
//BYTE:green intensity 000
//BYTE:blue intensity 000
//.............
//BYTE:red intensity 255
//BYTE:green intensity 255
//BYTE:blue intensity 255

//图形描述块：每一幅图像都由一个图像描述块(Image Descriptor)、可有可无的局部彩色表(Local Color Table)和图像数据组成
typedef struct GIFIMAGE_S
{
	//BYTE flag; //Image Separator: 图像分隔符(Image Separator)用来标识图像描述块的开始，该域包含固定的值：0x2C //','
   	WORD wLeft;
   	WORD wTop;
   	WORD wWidth;
   	WORD wDepth;
   	struct LocalFlag_S
	{
        BYTE PalBits     : 3;  //局部彩色表大小(Size of Local Color Table)域的值用来计算局部彩色表(Global Color Table)中包含的字节数。
        BYTE Reserved    : 2;
        BYTE SortFlag    : 1;  //彩色表排序标志(Sort Flag)域的含义与全局彩色表(GlobalColor Table)中(Sort Flag)域的含义相同
        BYTE Interlace 	 : 1; //交插显示标志(Interlace Flag)域I用来表示该图像是不是交插图像(Interlaced Images)
							  
        BYTE LocalPal    : 1; //用来说明是否有局部彩色表存在: 1:表示有一个局部彩色表(Local Color Table)将紧跟在这个图像描述块(ImageDescriptor)之后
							  //                           0:表示图像描述块(Image Descriptor)后面没有局部彩色表(Local Color Table)，该图像要使用全局彩色表(GlobalColor Table)
	}LocalFlag;
}__attribute__((packed)) GIFIMAGE;


//局部彩色表(LocalColor Table):与全局彩色表(Global Color Table)格式相同， LocalPal = 1，index max = 彩色表的表项数目等于2^(n +1), n = PalBits 

//表式压缩图像数据：GIF图像采用了LZW算法对实际的图像数据进行压缩，对LZW编码器输出的代码采用可变长度码VLC(variable-length-code)，不是用位数高度的代码来表示输出，而且代表码字的位数是可变的
//BYTE: LZW最小代码长度(LZW Minimum Code Size), LZW最小代码长度域的值用来确定图像数据中LZW代码使用的初始位数
//Image Data: 图像数据(Image Data)由数据子块(Data Sub-blocks)序列组成 : BLOCK SIZE + DATA VALUES

//图像控制扩展块: 图形控制扩展块(GraphicControl Extension)包含处理图形描绘块时要使用的参数, GIF_VERSION1_TAB
typedef struct GIFCONTROL_S
{
	BYTE ExtensionIntroducer; //GIF_EXINTRODUCER_FLAG 固定
	BYTE GraphicControlLabel; //GIF_GRAPCRLLABEL_FLAG 固定
   	BYTE byBlockSize;		  //用来说明该扩展块所包含字节数，该字节数是从这个块大小(Block Size)域之后到块结束符之间的字节数
   	struct Flag_S
    {
        BYTE Transparency     : 1; //透明(TransparencyFlag)表示是否给出透明索引(transparency index)
        BYTE UserInput        : 1; //用户输入标志(UserInput Flag)域表示在继续处理之前是否需要用户输入响应。在延时时间(Delay Time)和用户输入标志(UserInput Flag)都设置为1的情况下，继续处理的开始时间取决于用户响应输入在前还是延时时间结束在前
        BYTE DisposalMethod   : 3; //0: 没有指定要做任何处理；1： 不处理，图形留在原处；2：显示图形的区域必须要恢复成背景颜色；3：恢复成以前显示的图形
        BYTE Reserved         : 3;
    }Flag;
   	WORD wDelayTime; //延时时间(DelayTime)用来指定在图形显示之后继续处理数据流之前的等待时间，一百分之一秒为单位
   	BYTE byTransparencyIndex; //当且仅当透明标志位设置为1时，透明索引(Transparency Index)用来指示处理程序是否要修改显示设备上的相应象点。当且仅当透明标志位设置为1时，就要修改。
   	BYTE byTerminator; //块结束符(BlockTerminator)表示该图形控制扩展块(Graphic Control Extension)结束，它是由一个字节组成的数据块，该域的值是一个固定的值：0x00，因此称为零长度数据子块(zero-lengthData Sub-block)。
} __attribute__((packed))  GIFCONTROL;


//无格式文本扩展块:无格式文本扩展块(PlainText Extension)包含文本数据和描绘文本所须的参数。文本数据用7位的ASCII字符编码并以图形形式显示, GIF_VERSION1_TAB
typedef struct GIFPLAINTEXT_S
{
	//BYTE: GIF_EXINTRODUCER_FLAG
	//BYTE: GIF_PLAINTEXTLABEL_FLAG
	BYTE byBlockSize; 		//byBlockSize用来指定该图像扩充块的长度，其取值固定为13
	WORD wTextGridLeft; 	//wTextGridLeft用来指定文字显示方格相对于逻辑屏幕左上角的X坐标（以像素为单位）
	WORD wTextGridTop;  	//wTextGridTop用来指定文字显示方格相对于逻辑屏幕左上角的Y坐标
	WORD wTextGridWidth; 	//wTextGridWidth用来指定文字显示方格的宽度
	WORD wTextGridDepth;	//wTextGridDepth用来指定文字显示方格的高度
	BYTE byCharCellWidth;  	//byCharCellWidth用来指定字符的宽度
	BYTE byCharCellDepth;	//byCharCellDepth用来指定字符的高度
	BYTE byForeColorIndex;	//byForeColorIndex用来指定字符的前景色
	BYTE byBackColorIndex;	//byBackColorIndex用来指定字符的背景色
}__attribute__((packed)) GIFPLAINTEXT;

//注释扩展块: 注释扩展块(CommentExtension)域的内容用来说明图形、作者或者其他任何非图形数据和控制信息的文本信息, GIF_VERSION1_TAB
//BYTE: GIF_EXINTRODUCER_FLAG
//BYTE: GIF_COMMENTLABEL_FLAG
//N BYTES: 其中的注释数据是序列数据子块(Data Sub-blocks)，每块最多255个字节，最少1个字节
//BYTE: GIF_EXINTRODUCER_ENDFLAG

//应用程序扩展块: 应用扩展块(ApplicationExtension)包含制作该图像文件的应用程序的相关信息, GIF_VERSION1_TAB
typedef struct GIFAPPLICATION_S
{
	//BYTE: GIF_EXINTRODUCER_FLAG
	//BYTE: GIF_EXTENSIONLABEL_FLAG
    BYTE byBlockSize; 		//byBlockSize用来指定该应用程序扩充块的长度，其取值固定为12
    BYTE byIdentifier[8];   //byIdentifier用来指定应用程序名称
    BYTE byAuthentication[3];//byAuthentication用来指定应用程序的识别码
	//APP DATA
	//BYTE: GIF_EXINTRODUCER_ENDFLAG
}__attribute__((packed)) GIFAPPLICATION;



//GIF文件结束块: 结束块(GIFTrailer)表示GIF文件的结尾，它包含一个固定的数值：0x3B
//BYTE: GIF_TRAILER_FLAG
#endif
