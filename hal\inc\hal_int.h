/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_INT_H
#define  HAL_INT_H


/*******************************************************************************
* Function Name  : hal_intEnable(u8 irqNum,u8 en)
* Description    : int enable set
* Input          : u8 irqNum : irq index,in IRQ_IDX_E
                   u8 en     : 1->enable,0->disable
* Output         : none
* Return         : none
*******************************************************************************/
#define hal_intEnable          hx330x_intEnable


/*******************************************************************************
* Function Name  : hx330x_int_priority(u8 irqNum,u8 level)
* Description    : int enable set
* Input          : u8 irqNum : irq index,in IRQ_IDX_E
                   u8 level     : 1->high priority,0->low priority
* Output         : none
* Return         : none
*******************************************************************************/
#define hal_int_priority  		hx330x_int_priority

/*******************************************************************************
* Function Name  : hal_intInit
* Description    : int initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_intInit(void);
/*******************************************************************************
* Function Name  : 
* Description    : usage:
*                   void foo(void)
*                   {
*                       HAL_CRITICAL_INIT();
* 
*                       HAL_CRITICAL_ENTER();
*                       // do something
* 
*                       HAL_CRITICAL_EXIT();
*                   }
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
#define HAL_CRITICAL_INIT()     hx330x_intCriticalInit()

#define HAL_CRITICAL_ENTER()    hx330x_intCriticalEnter()

#define HAL_CRITICAL_EXIT()     hx330x_intCriticalExit()

#define HAL_HE_CRITICAL_INIT()  hx330x_intHECriticalInit()

#define HAL_HE_CRITICAL_ENTER() hx330x_intHECriticalEnter()

#define HAL_HE_CRITICAL_EXIT()  hx330x_intHECriticalExit()



#endif


