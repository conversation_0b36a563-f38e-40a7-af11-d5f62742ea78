/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"




/****************************************************************************************
 * UVC_JPG:
 * NULL_FRAME: malloc->NEW_FRAME
 * NEW_FRAME:  frame end -->RDY_FRAME
 * RDY_FRAME:  (1)malloc, not more NULL_FRAME, RDY_FRAME->NEW_FRAME
 *             (2)kick to MJP DECODE,  RDY_FRAME->DCD_FRAME
 * DCD_FRAME:  (1)decode done OK, DCD_FRAME->BAK_FRAME
 *             (2)decode done err, DCD_FRAME->NULL_FRAME
 * BAK_FRAME:  (1)when kick to MJP DECODE, not more RDY_FRAME,  BAK_FRAME->DCD_FRAME
 *             (2)decode done OK, last BAK_FRAME->NULL_FRAME
 ****************************************************************************************
 * NORMAL:NULL_FRAME-> NEW_FRAME->RDY_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 * USENSOR FPS FAST: RDY_FRAME-> NEW_FRAME->RDY_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 * USENSOR FPS SLOW: BAK_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 ****************************************************************************************
 * UVC_YUV:
 * NULL_FRAME: malloc->NEW_FRAME
 * NEW_FRAME:  (1)size match -->RDY_FRAME
 * 			   (2)size not match -->NULL_FRAME
 * RDY_FRAME:  (1)malloc, not more NULL_FRAME, RDY_FRAME->NEW_FRAME
 *             (2)kick to CSI,  RDY_FRAME->DCD_FRAME
 * DCD_FRAME:  (1)CSI USE DONE, DCD_FRAME->BAK_FRAME
 * BAK_FRAME:  (1)when kick to CSI, not more RDY_FRAME,  BAK_FRAME->DCD_FRAME
 *             (2)CSI USE  OK, last BAK_FRAME->NULL_FRAME
 ****************************************************************************************
 * NORMAL:NULL_FRAME-> NEW_FRAME->RDY_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 * USENSOR FPS FAST: RDY_FRAME-> NEW_FRAME->RDY_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 * USENSOR FPS SLOW: BAK_FRAME->DCD_FRAME->BAK_FRAME->NULL_FRAME
 ****************************************************************************************/

/*******************************************************************************
* Function Name  : husb_uvc_cache_uinit
* Description    : husb_uvc_cache_uinit
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_uvc_cache_uinit(void *handle)
{
	u32 i;
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	hx330x_bytes_memset((u8*)&pHusb_handle->usbsta.uvc_fstack, 0 , sizeof(pHusb_handle->usbsta.uvc_fstack));
	for(i = 0; i < UVC_CACHE_NUM; i++)
	{
		if(pHusb_handle->usbsta.uvc_cache[i].buff)
			hal_sysMemFree((void*)pHusb_handle->usbsta.uvc_cache[i].buff);
		pHusb_handle->usbsta.uvc_cache[i].sta = NULL_FRAME;
		pHusb_handle->usbsta.uvc_cache[i].buff = NULL;
		pHusb_handle->usbsta.uvc_cache[i].next = NULL;
	}
	pHusb_handle->usbsta.uvc_fill_cache = NULL;
	pHusb_handle->usbsta.uvc_dcd_cache 	= NULL;
	pHusb_handle->usbsta.uvc_bak_cache 	= NULL;

}
/*******************************************************************************
* Function Name  : husb_uvc_cache_init
* Description    : husb_uvc_cache_init
* Input          : None
* Output         : None
* Return         : None
* NOTICE         : 动态申请buf，录像时需要考虑预留空间
*******************************************************************************/
static bool husb_uvc_cache_init(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	u32 cache_size;
	u8 * buff;
	if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
	{
		cache_size = _UVC_JPG_CACHE_SIZE;
	}else
	{
		cache_size = pHusb_handle->usbsta.usensor_res.wMaxVideoFrameBufSzie;
	}
	husb_uvc_cache_uinit(handle);
	u32 i;
	for(i = 0; i<UVC_CACHE_NUM;i++)
	{
		buff = hal_sysMemMalloc(cache_size);
		if(buff == NULL)
		{
			deg_Printf("HUSB UVC malloc buf fail:%d\n",cache_size);
			husb_uvc_cache_uinit(handle);
			return false;
		}
		hx330x_sysDcacheInvalid((u32)buff, cache_size);
		//deg_Printf("[cache %d]%x\n", i, buff);
		pHusb_handle->usbsta.uvc_cache[i].buff = buff;
		pHusb_handle->usbsta.uvc_cache[i].size = 0;
		pHusb_handle->usbsta.uvc_cache[i].max_size = cache_size;
		pHusb_handle->usbsta.uvc_cache[i].next = (i == (UVC_CACHE_NUM-1)) ? &pHusb_handle->usbsta.uvc_cache[0]: &pHusb_handle->usbsta.uvc_cache[i+1];
	}
	return true;
}
/*******************************************************************************
* Function Name  : husb_uvc_cache_uinit
* Description    : husb_uvc_cache_uinit
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static u8 *huvc_cache_fill_malloc(void *handle)
{
	u32 i;
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();

	UVC_CACHE * last_fill_cache = pHusb_handle->usbsta.uvc_fill_cache;
	pHusb_handle->usbsta.uvc_fill_cache = NULL;
	//deg_Printf("M0[%d] 1[%d] 2 [%d]\n", pHusb_handle->usbsta.uvc_cache[0].sta,
	//								pHusb_handle->usbsta.uvc_cache[1].sta,
	//								pHusb_handle->usbsta.uvc_cache[2].sta);
	for(i = 0;i<UVC_CACHE_NUM;i++)
	{
		if(pHusb_handle->usbsta.uvc_cache[i].sta == NULL_FRAME)
		{
			pHusb_handle->usbsta.uvc_fill_cache =  &pHusb_handle->usbsta.uvc_cache[i];
			break;
		}
	}
	if(pHusb_handle->usbsta.uvc_fill_cache == NULL)
	{
		if(last_fill_cache == NULL)
			last_fill_cache = &pHusb_handle->usbsta.uvc_cache[UVC_CACHE_NUM-1];
		for(i = 0;i<UVC_CACHE_NUM;i++)
		{
			if(last_fill_cache->next->sta == RDY_FRAME)
			{
				pHusb_handle->usbsta.uvc_fill_cache = last_fill_cache->next;
				break;
			}
			last_fill_cache = last_fill_cache->next;
		}
	}

	if(pHusb_handle->usbsta.uvc_fill_cache == NULL)
	{
		deg_Printf("huvc_cache_fill_malloc err\n");
		HAL_HE_CRITICAL_EXIT() ;
		return NULL;
		//exception_trigger();while(1);
	}else
	{
		pHusb_handle->usbsta.uvc_fill_cache->sta  = NEW_FRAME;
		pHusb_handle->usbsta.uvc_fill_cache->size = 0;
		//deg_Printf("NEW [%d] [%d] [%d] [%d]\n", pHusb_handle->usbsta.uvc_cache[0].sta,
		//							pHusb_handle->usbsta.uvc_cache[1].sta,
		//							pHusb_handle->usbsta.uvc_cache[2].sta,
		//							pHusb_handle->usbsta.uvc_cache[3].sta);
		//deg_Printf("[NEW: %d, %x]\n", i, pHusb_handle->usbsta.uvc_fill_cache->buff);
		//buf = pHusb_handle->usbsta.uvc_fill_cache->buff;
		HAL_HE_CRITICAL_EXIT() ;
		return pHusb_handle->usbsta.uvc_fill_cache->buff;
	}
	//HAL_CRITICAL_EXIT() ;
	//return buf;
}
/*******************************************************************************
* Function Name  : huvc_cache_fill_down
* Description    : huvc_cache_fill_down
* Input          : u8 err: 1 usb recieve err
* Output         : None
* Return         : u8 sta: RDY_FRAME/NULL_FRAME
*******************************************************************************/
static void huvc_cache_fill_down(void *handle, u8 err)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle->usbsta.uvc_fstack.jbuf == NULL)
	{
		return;
	}
	if((pHusb_handle->usbsta.uvc_fill_cache == NULL))
	{
		//deg_Printf("HUVC ERR:<fill cahe null>\n");
		return;
		//exception_trigger();while(1);
	}
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();
	if(!err)
	{
		if(pHusb_handle->usbsta.uvc_fill_cache->buff != (pHusb_handle->usbsta.uvc_fstack.jbuf - pHusb_handle->usbsta.uvc_fstack.jlen))
		{
			deg_Printf("HUVC ERR:<fill buff not match>%d \n", pHusb_handle->usbsta.uvc_fstack.jlen );
			err = 1;
			//exception_trigger();while(1);
		}
		if(pHusb_handle->usbsta.uvc_fstack.jlen == 0)
		{
			deg_Printf("HUVC ERR: jlen 0  \n");
			err = 1;
		}
		if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_YUV)
		{
			if(pHusb_handle->usbsta.uvc_fstack.jlen != pHusb_handle->usbsta.uvc_fill_cache->max_size)
			{
				err = 1;
			}
		}
	}
	//HAL_CRITICAL_INIT();
	//HAL_CRITICAL_ENTER();
	//deg_Printf("[RDY:%x,%d]\n",pHusb_handle->usbsta.uvc_fill_cache->buff, err);
	if(!err)
	{
		pHusb_handle->usbsta.uvc_fill_cache->sta  = RDY_FRAME;
		pHusb_handle->usbsta.uvc_fill_cache->size = pHusb_handle->usbsta.uvc_fstack.jlen;
		//deg_Printf("[NEW->RDY]\n");
	}else{
		pHusb_handle->usbsta.uvc_fill_cache->sta  = NULL_FRAME;
	}
	pHusb_handle->usbsta.uvc_fill_cache = NULL;
	HAL_HE_CRITICAL_EXIT() ;
	//deg_Printf("RDY [%d] [%d]  [%d]\n", pHusb_handle->usbsta.uvc_cache[0].sta,
	//								pHusb_handle->usbsta.uvc_cache[1].sta,
	//								pHusb_handle->usbsta.uvc_cache[2].sta);
}

/*******************************************************************************
* Function Name  : huvc_cache_dcd_get
* Description    : huvc_cache_dcd_get
* Input          : void *handle, u8** buf, u32 *len
* Output         : None
* Return         : u8 sta: RDY_FRAME/NULL_FRAME
*******************************************************************************/
static bool huvc_cache_dcd_get(void *handle, u8** buf, u32 *len)
{
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	UVC_CACHE * last_dcd_cache = pHusb_handle->usbsta.uvc_dcd_cache;
	pHusb_handle->usbsta.uvc_dcd_cache = NULL;
	u8 i;
	if(last_dcd_cache == NULL)
		last_dcd_cache = &pHusb_handle->usbsta.uvc_cache[UVC_CACHE_NUM-1];
	for(i = 0;i<UVC_CACHE_NUM;i++)
	{
		if(last_dcd_cache->next->sta == RDY_FRAME)
		{
			pHusb_handle->usbsta.uvc_dcd_cache = last_dcd_cache->next;
			break;
		}
		last_dcd_cache = last_dcd_cache->next;
	}
	if(pHusb_handle->usbsta.uvc_dcd_cache == NULL)
	{
		pHusb_handle->usbsta.uvc_dcd_cache = pHusb_handle->usbsta.uvc_bak_cache;
	}
	if(pHusb_handle->usbsta.uvc_dcd_cache == NULL)
	{
		HAL_HE_CRITICAL_EXIT() ;
		return false;
	}else
	{

		pHusb_handle->usbsta.uvc_dcd_cache->sta  = DCD_FRAME;
		*buf = pHusb_handle->usbsta.uvc_dcd_cache->buff;
		*len  = pHusb_handle->usbsta.uvc_dcd_cache->size;
		//deg_Printf("DCD [%d] [%d] [%d]\n", pHusb_handle->usbsta.uvc_cache[0].sta,
		//							pHusb_handle->usbsta.uvc_cache[1].sta,
		//							pHusb_handle->usbsta.uvc_cache[2].sta);
		//deg_Printf("[DCD:%d, %x]\n",i, *buf);
		HAL_HE_CRITICAL_EXIT() ;
		return true;
	}
}
/*******************************************************************************
* Function Name  : huvc_cache_dcd_down
* Description    : huvc_cache_dcd_down
* Input          : u8 err: 1 usb recieve err
* Output         : None
* Return         : u8 sta: RDY_FRAME/NULL_FRAME
*******************************************************************************/
void huvc_cache_dcd_down(void *handle, u8 err)
{
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle->usbsta.uvc_dcd_cache == NULL)
	{
		HAL_HE_CRITICAL_EXIT() ;
		return;
	}

	//deg_Printf("[BAK:%x]\n",pHusb_handle->usbsta.uvc_dcd_cache->buff);
	if(err)
	{
		pHusb_handle->usbsta.uvc_dcd_cache->sta = NULL_FRAME;
		//pHusb_handle->usbsta.uvc_bak_cache      = pHusb_handle->usbsta.uvc_bak_cache;
	}else //if not err , BAK_FRAME->NULL_FRAME, DCD_FRAME -> BAK_FRAME,
	{
		if(pHusb_handle->usbsta.uvc_bak_cache)
			pHusb_handle->usbsta.uvc_bak_cache->sta = NULL_FRAME; //释放上一个bak
		pHusb_handle->usbsta.uvc_bak_cache 			= pHusb_handle->usbsta.uvc_dcd_cache; //dcd->BAK
		pHusb_handle->usbsta.uvc_bak_cache->sta 	= BAK_FRAME;
		//deg_Printf("[DCD->BAK]\n");
	}
	pHusb_handle->usbsta.uvc_dcd_cache = NULL;

	//deg_Printf("DOWN [%d] [%d] [%d]\n", pHusb_handle->usbsta.uvc_cache[0].sta,
	//								pHusb_handle->usbsta.uvc_cache[1].sta,
	//								pHusb_handle->usbsta.uvc_cache[2].sta);
	HAL_HE_CRITICAL_EXIT() ;
}

/*******************************************************************************
* Function Name  : husb20_isopayload_recieve
* Description    : husb20_isopayload_recieve
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static u8 *husb20_isopayload_recieve(u32 *tgl,u32 * rlen)
{
	u8* buf  = NULL;
	u32 index_temp = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS	= HUSB20_RXEP_UVC;
	if(rlen)
		*rlen = 0;
	while(1)
	{
		u32 rxcsr1 = XSFR_USB20_SIE_EPRX_CTRL0;
		if(rxcsr1 & HUSB_EPRX_ERROR)
		{
			XSFR_USB20_SIE_EPRX_CTRL0 = 0;
			deg_Printf("HUSB20 UVC RX err\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_SENTSTALL)
		{
			deg_Printf("HUSB20 UVC RX stall\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_RXPKTRDY)
		{
			*rlen = (XSFR_USB20_SIE_EPXRXCNTH<<8)+XSFR_USB20_SIE_EPXRXCNTL;
			if(*rlen)
			{
				buf = (u8 *)((&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_UVC*2-2]);
				//deg_Printf("rlen:%x\n",*rlen);
			}
			break;
		}else
		{
			deg_Printf("rxcsr1:%x\n",rxcsr1);
			break;
		}
	}
	if(buf)
	{
		*tgl ^= 1;
		(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_UVC*2-2] = (u32)_USB20_UVC_FIFO_[*tgl];

	}
	hx330x_mcpy1_sdram2gram_nocache_waitdone();
	XSFR_USB20_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO|HUSB_EPRX_REQPKT;
	XSFR_USB20_SIE_EPS   = index_temp;
	return buf;
}

/*******************************************************************************
* Function Name  : husb20_isopayload_recieve
* Description    : husb20_isopayload_recieve
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static u8 *husb11_isopayload_recieve(u32 *tgl,u32 * rlen)
{
	u8* buf  = NULL;
	u32 index_temp = XSFR_USB11_SIE_EPS;
	XSFR_USB11_SIE_EPS	= HUSB11_RXEP_UVC;
	if(rlen)
		*rlen = 0;
	while(1)
	{
		u32 rxcsr1 = XSFR_USB11_SIE_EPRX_CTRL0;
		if(rxcsr1 & HUSB_EPRX_ERROR)
		{
			XSFR_USB11_SIE_EPRX_CTRL0 = 0;
			deg_Printf("HUSB11 UVC RX err\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_SENTSTALL)
		{
			deg_Printf("HUSB11 UVC RX stall\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_RXPKTRDY)
		{
			*rlen = (XSFR_USB11_SIE_EPXRXCNTH<<8)+XSFR_USB11_SIE_EP0RXCNTL;
			if(*rlen)
			{
				buf = (u8 *)((&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_UVC*2-2]);
			}
			break;
		}else
		{
			deg_Printf("rxcsr1:%x\n",rxcsr1);
			break;
		}
	}
	if(buf)
	{
		*tgl ^= 1;
		(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_UVC*2-2] = (u32)_USB11_UVC_FIFO_[*tgl];

	}
	hx330x_mcpy1_sdram2gram_nocache_waitdone();
	XSFR_USB11_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO|HUSB_EPRX_REQPKT;
	XSFR_USB11_SIE_EPS  = index_temp;
	return buf;
}
/*******************************************************************************
* Function Name  : husb_uvc_eprx_isr
* Description    : husb_uvc_eprx_isr
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool husb_uvc_eprx_isr(void *handle)
{
	#define NEXT_TGL  ((payload[1] & BIT(0)) ^ 1)
	#define CURT_TGL  (payload[1] & BIT(0))
	#define _UVC_HEADER_	12
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	u8 *payload = NULL; //= _uvc_payld_[0];
	u32 paylen = 0;
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		payload = husb20_isopayload_recieve(&(pHusb_handle->usbsta.uvc_fifo_tgl),&paylen);
	}else if(pHusb_handle->usbsta.ch == USB11_CH)
	{
		payload = husb11_isopayload_recieve(&(pHusb_handle->usbsta.uvc_fifo_tgl),&paylen);
	}
	if(payload == NULL)
	{
		return true;
	}
	if(pHusb_handle->usbsta.uvc_fstack.usta == UVC_ASYN)
	{
		pHusb_handle->usbsta.uvc_fstack.jbuf = NULL;
		pHusb_handle->usbsta.uvc_fstack.jlen = 0;
		pHusb_handle->usbsta.uvc_fstack.usta = UVC_SYNF;
		pHusb_handle->usbsta.uvc_fstack.utgl = NEXT_TGL;
	}
	hx330x_sysDcacheFlush((u32)payload,32);
	if(payload[0] ^ _UVC_HEADER_){
		huvc_cache_fill_down(pHusb_handle,1);
		pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
		//deg_Printf("HUVC HEADER ERR\n");
		return true;
	}
	if(payload[1] & (BIT(6) | BIT(5)))
	{
		huvc_cache_fill_down(pHusb_handle,1);
		pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
		//deg_Printf("HUVC BAD FRAME\n");
		return true;
	}

	if(pHusb_handle->usbsta.uvc_fstack.usta == UVC_SYNF)
	{
		if(pHusb_handle->usbsta.uvc_fstack.utgl == CURT_TGL)
		{

			if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
			{
				if(paylen > _UVC_HEADER_)
				{
					if((payload[_UVC_HEADER_]!= 0xff) || (payload[_UVC_HEADER_+1]!= 0xd8))
					{
						//deg_Printf("HUVC:1ST JPG Header err:%x,%x\n",payload[_UVC_HEADER_],payload[_UVC_HEADER_+1]);
						pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
						return true;
					}
				}
			}
			//deg_Printf("X");
			if(pHusb_handle->usbsta.uvc_drop_frame == 0)
			{
				pHusb_handle->usbsta.uvc_fstack.jbuf = huvc_cache_fill_malloc(pHusb_handle);
				if(pHusb_handle->usbsta.uvc_fstack.jbuf == NULL)
				{
					pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
					return true;
				}	
			}else
			{
				pHusb_handle->usbsta.uvc_fstack.jbuf = NULL;
			}

			pHusb_handle->usbsta.uvc_fstack.jlen = 0;
			pHusb_handle->usbsta.uvc_fstack.usta = UVC_SYNC;

		}
	}
	if(pHusb_handle->usbsta.uvc_fstack.usta == UVC_SYNC)
	{
		if(pHusb_handle->usbsta.uvc_fstack.utgl != CURT_TGL)
		{
			//hx330x_mcpy1_sdram2gram_nocache_waitdone();
			//deg_Printf("+");
			pHusb_handle->usbsta.uvc_fstack.jUcnt++;
			if(pHusb_handle->usbsta.uvc_drop_frame == 0)
			{
				if(pHusb_handle->usbsta.uvc_fstack.jlen <= pHusb_handle->usbsta.uvc_fill_cache->max_size)
					huvc_cache_fill_down(pHusb_handle,0);
				else
				{
					deg_Printf("HUVC: Frame overflow:%d,%d\n",pHusb_handle->usbsta.uvc_fstack.jlen,pHusb_handle->usbsta.uvc_fill_cache->max_size);
					huvc_cache_fill_down(pHusb_handle,1);
				}

				//if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
					//hx330x_sysDcacheInvalid((u32)pHusb_handle->usbsta.uvc_fstack.jbuf, pHusb_handle->usbsta.uvc_fstack.jlen);
			}else
			{
				pHusb_handle->usbsta.uvc_drop_frame--;
				huvc_cache_fill_down(pHusb_handle,1);
			}
			if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
			{
				if(paylen > _UVC_HEADER_)
				{
					if((payload[_UVC_HEADER_]!= 0xff) || (payload[_UVC_HEADER_+1]!= 0xd8))
					{
						//deg_Printf("HUVC:NST JPG Header err:%x,%x\n",payload[_UVC_HEADER_],payload[_UVC_HEADER_+1]);
						pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
						return true;
					}
				}
			}
			if(pHusb_handle->usbsta.uvc_drop_frame == 0)
				pHusb_handle->usbsta.uvc_fstack.jbuf = huvc_cache_fill_malloc(pHusb_handle);
			else
				pHusb_handle->usbsta.uvc_fstack.jbuf = NULL;
			pHusb_handle->usbsta.uvc_fstack.jlen = 0;
			pHusb_handle->usbsta.uvc_fstack.usta = UVC_SYNC;
			pHusb_handle->usbsta.uvc_fstack.utgl = CURT_TGL;

		}
	}
	if((pHusb_handle->usbsta.uvc_fstack.usta == UVC_SYNC) && (pHusb_handle->usbsta.uvc_fstack.jbuf)&&(paylen > _UVC_HEADER_))
	{
#if 0
		if((pHusb_handle->usbsta.uvc_fstack.jlen + paylen - _UVC_HEADER_) > pHusb_handle->usbsta.uvc_fill_cache->max_size)
		{
			huvc_cache_fill_down(pHusb_handle,1);
			pHusb_handle->usbsta.uvc_fstack.usta = UVC_ASYN;
			deg_Printf("HUVC: Frame overflow:\n");
		}else
		{

			//hx330x_mcpy1_sdram2gram_nocache(pHusb_handle->usbsta.uvc_fstack.jbuf, &payload[_UVC_HEADER_] , paylen- _UVC_HEADER_);
			hx330x_mcpy1_sdram2gram_nocache_kick(pHusb_handle->usbsta.uvc_fstack.jbuf, &payload[_UVC_HEADER_] , paylen- _UVC_HEADER_);
			//hx330x_mcpy1_sdram2gram(pHusb_handle->usbsta.uvc_fstack.jbuf, &payload[_UVC_HEADER_] , paylen- _UVC_HEADER_);
			pHusb_handle->usbsta.uvc_fstack.jlen += paylen - _UVC_HEADER_;
			pHusb_handle->usbsta.uvc_fstack.jbuf += paylen - _UVC_HEADER_;
		}
#else
		if((pHusb_handle->usbsta.uvc_fstack.jlen + paylen - _UVC_HEADER_) <= pHusb_handle->usbsta.uvc_fill_cache->max_size)
		{
			hx330x_mcpy1_sdram2gram_nocache_kick(pHusb_handle->usbsta.uvc_fstack.jbuf, &payload[_UVC_HEADER_] , paylen- _UVC_HEADER_);	
			pHusb_handle->usbsta.uvc_fstack.jbuf += paylen - _UVC_HEADER_;
		}
		pHusb_handle->usbsta.uvc_fstack.jlen += paylen - _UVC_HEADER_;
		
#endif

	}
	//if(pHusb_handle->usbsta.uvc_fstack.jlen > pHusb_handle->usbsta.uvc_fill_cache->max_size)
	//{
	//	deg_Printf("HUVC:FRAME OVERFLOW:%d\n",pHusb_handle->usbsta.uvc_fstack.jlen);
	//	return false;
	//}
	return true;
}
/*******************************************************************************
* Function Name  : husb_uvc20_eprx_isr
* Description    : husb_uvc20_eprx_isr
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_uvc20_eprx_isr(void)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)husb_api_handle_get(USB20_CH);
	if(pHusb_handle)
	{
		//deg_Printf("*");
		if(!husb_uvc_eprx_isr(pHusb_handle))
		{
			husb_api_u20_remove();
		}
	}
}
/*******************************************************************************
* Function Name  : husb_uvc20_eprx_isr
* Description    : husb_uvc20_eprx_isr
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_uvc11_eprx_isr(void)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)husb_api_handle_get(USB11_CH);
	if(pHusb_handle)
	{
		if(!husb_uvc_eprx_isr(pHusb_handle))
		{
			husb_api_u11_remove();
		}
	}
}
/*******************************************************************************
* Function Name  : husb_uvc_frame_read
* Description    : husb_uvc_frame_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_uvc_frame_read(void *handle,u8**p, u32 *len)
{
	u8 *pbuf;
	u32 plen;
	//HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(!huvc_cache_dcd_get(handle, &pbuf, &plen))
	{
		return false;
	}
	if(plen == 0)
	{
		deg_Printf("frame len = 0\n");
		huvc_cache_dcd_down(handle, 1);
		return false;
	}
	//if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
	//{
		//hx330x_sysDcacheFlush((u32)pbuf,16);
		//if((pbuf[0] != 0xff) || (pbuf[1] != 0xd8))
		//{
		//	debg("uj: <header err>:%x,%x\n",pbuf,plen);
			//usb_usensor_frame_free();
		//	huvc_cache_dcd_down(handle, 1);
		//	return false;
		//}
	//}
	//deg_Printf("pbuf:%x\n",(u32)pbuf);
	*p = pbuf;
	*len = plen;
	return true;

}
UVC_CACHE * husb_uvc_dcd_cache_get(void *handle)
{
    HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	return pHusb_handle->usbsta.uvc_dcd_cache;
}
/*******************************************************************************
* Function Name  : husb_uvc_atech_codec
* Description    : husb_uvc_atech_codec
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_uvc_atech_codec(void *handle)
{
	u8 *p;
	u32 len;
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(husb_uvc_frame_read(handle, &p, &len))
	{
		//deg_Printf("p:%x\n",(u32)p);
		if(pHusb_handle->usbsta.usensor_res.type == UVC_FORMAT_YUV)
		{
			if(len != pHusb_handle->usbsta.usensor_res.wMaxVideoFrameBufSzie)
			{
				huvc_cache_dcd_down(pHusb_handle,1);
				return false;
			}
			//hx330x_csiInputAddrSet((u32)p);
			//csi_src = 1; //sdram input
		}else
		{
			if(hal_mjpDecodeParse((u8 *)p,pHusb_handle->usbsta.usensor_res.width,pHusb_handle->usbsta.usensor_res.height) < 0){
				deg_Printf("usensor: <1st mjp decode fail>\n");
				huvc_cache_dcd_down(pHusb_handle,1);
				return false;
			}
			huvc_cache_dcd_down(pHusb_handle,0);
			//csi_src = 0; //csi colorbar
		}
		debg("usensor:<1st frame ok>\n");
		//hal_csi_input_switch(csi_src, (u32)p);
		return true;
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_uvc_init
* Description    : husb_uvc_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_uvc_link_register(void *handle, bool (*callback)(void *))
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	pHusb_handle->usbsta.usensor_atech_link = callback;
}
/*******************************************************************************
* Function Name  : husb_uvc_init
* Description    : husb_uvc_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_init(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle->uvc.ctyp != CC_VIDEO)
		return ;
	if(!husb_uvc_cache_init(pHusb_handle))
		return ;
	pHusb_handle->usbsta.device_sta 	|= USB_UVC_ATECH;
	pHusb_handle->usbsta.uvc_drop_frame = USENSOR_FIRSTCONNECT_DROP_FRAME;
	pHusb_handle->usbsta.uvc_fifo_tgl 	= 0;

	//memset((u8*)&(pHusb_handle->usbsta.uvc_fstack),0,sizeof(UVC_FSTACK));

	husb_uvc_link_register(pHusb_handle,husb_uvc_atech_codec);
	hal_mjpB_usb_resolution_set(pHusb_handle->usbsta.usensor_res.width,pHusb_handle->usbsta.usensor_res.height);
	husb_api_handle_reg(pHusb_handle);
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		deg_Printf("HUSB20 UVC INIT.\n");
		u32 index_temp	= XSFR_USB20_SIE_EPS;
		//XSFR_USB20_SIE_EPS 	= HUSB20_TXEP_UVC;
		//XSFR_USB20_SIE_EPTX_CTRL0	= HUSB_EPTX_CLRDATATOG|HUSB_EPTX_FLUSHFIFO;
		//XSFR_USB20_SIE_EPTX_CTRL1	= 0;
		//XSFR_USB20_SIE_TXPKGMAXL  	= (u8)(1024 & 0xff);//0xe4;
		//XSFR_USB20_SIE_TXPKGMAXH  	= (u8)(1024 >> 8) | BIT(4)| BIT(3);//0x1b;

		XSFR_USB20_SIE_EPS		= HUSB20_RXEP_UVC;
		XSFR_USB20_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB20_SIE_EPRX_CTRL1	= 0;
		XSFR_USB20_SIE_RXPKGMAXL	= (u8)(1024 & 0xff);
		XSFR_USB20_SIE_RXPKGMAXH	= (u8)(1024 >> 8) | BIT(4)| BIT(3);
		XSFR_USB20_SIE_RXTYPE 	= (TT_ISOCHRONOUS|(pHusb_handle->uvc.strm_ep&0x7f));//SET BULK AND ENDPIONT

		//(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_UVC*2-2]  = (u32)_USB20_MSC_TXFIFO_;
		(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_UVC*2-2]  = (u32)_USB20_UVC_FIFO_; //pHusb_handle->usbsta.uvc_fifo_tgl = 0;
		XSFR_USB20_SDR_DMA_EN &= ~BIT(HUSB20_RXEP_UVC);  //使用sram通道
		hx330x_usb20_eprx_register(1, HUSB20_RXEP_UVC,husb_uvc20_eprx_isr);
		XSFR_USB20_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		//deg_Printf("XSFR_USB20_SIE_EPRX_CTRL0:%x\n",XSFR_USB20_SIE_EPRX_CTRL0);
		//deg_Printf("XSFR_USB20_SIE_EPRX_CTRL1:%x\n",XSFR_USB20_SIE_EPRX_CTRL1);
		//deg_Printf("XSFR_USB20_SIE_RXPKGMAXL:%x\n",XSFR_USB20_SIE_RXPKGMAXL);
		//deg_Printf("XSFR_USB20_SIE_RXPKGMAXH:%x\n",XSFR_USB20_SIE_RXPKGMAXH);
		//deg_Printf("XSFR_USB20_EP1_RXADDR:%x\n",XSFR_USB20_EP1_RXADDR);
		XSFR_USB20_SIE_EPS		= index_temp;
	}else
	{
		deg_Printf("HUSB11 UVC INIT.\n");
		u32 index_temp	= XSFR_USB11_SIE_EPS;
		//XSFR_USB11_SIE_EPS 	= HUSB11_TXEP_UVC;
		//XSFR_USB11_SIE_EPTX_CTRL0	= HUSB_EPTX_CLRDATATOG|HUSB_EPTX_FLUSHFIFO;
		//XSFR_USB11_SIE_EPTX_CTRL1	= 0;
		//XSFR_USB11_SIE_TXPKGMAX	= 250;
		//XSFR_USB11_SIE_TXTYPE 	= ( TT_ISOCHRONOUS | pHusb_handle->msc.epout);
		XSFR_USB11_SIE_EPS		= HUSB11_RXEP_UVC;
		XSFR_USB11_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB11_SIE_EPRX_CTRL1	= 0;
		XSFR_USB11_SIE_RXPKGMAX	= 250;
		XSFR_USB11_SIE_RXTYPE 	= (TT_ISOCHRONOUS|(pHusb_handle->uvc.strm_ep&0x7f));//SET BULK AND ENDPIONT
		//(&XSFR_USB11_EP1_TXADDR)[HUSB11_TXEP_UVC*2-2]  = (u32)_USB11_MSC_TXFIFO_;
		(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_UVC*2-2]  = (u32)_USB11_UVC_FIFO_;
		hx330x_usb11_host_eprx_register(1, HUSB11_RXEP_UVC,husb_uvc11_eprx_isr);
		XSFR_USB11_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		XSFR_USB11_SIE_EPS		= index_temp;
	}
	return;
}
/*******************************************************************************
* Function Name  : husb_uvc_linking
* Description    : husb_uvc_linking
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_linking(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
		if((pHusb_handle->usbsta.device_sta & USB_UVC_ATECH)&&(pHusb_handle->usbsta.usensor_atech_link))
		{
			if((*pHusb_handle->usbsta.usensor_atech_link)(pHusb_handle) == true)
			{
				HAL_HE_CRITICAL_INIT();
				HAL_HE_CRITICAL_ENTER();
				pHusb_handle->usbsta.device_sta |= USB_UVC_TRAN;
				husb_uvc_link_register(pHusb_handle,NULL);
				HAL_HE_CRITICAL_EXIT();
				deg_Printf("HUVC: prelink to LCD OK\n");


			}
		}
	}
}
/*******************************************************************************
* Function Name  : husb_uvc_relink_register
* Description    : husb_uvc_relink_register
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_relink_register(void* handle)
{
	//HUSB_HANDLE* pHusb_handle = husb_api_handle_get(ch);
	if(handle)
		husb_uvc_link_register(handle,husb_uvc_atech_codec);
}
/*******************************************************************************
* Function Name  : husb_uvc_detech
* Description    : husb_uvc_detech
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_detech(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	//if(pHusb_handle->usbsta.device_sta & USB_UVC_ATECH)
	{
		hal_mjpB_Enc_Stop();
		hal_mjpB_usb_resolution_set(0,0);
		hx330x_mcpy1_sdram2gram_nocache_waitdone();
		pHusb_handle->usbsta.device_sta = USB_NONE;

		husb_uvc_link_register(pHusb_handle,NULL);
		husb_uvc_cache_uinit(pHusb_handle);
	}
}

