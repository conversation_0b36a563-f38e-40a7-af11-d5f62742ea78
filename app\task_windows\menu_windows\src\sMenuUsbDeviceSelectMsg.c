/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuUsbDeviceSelectWin.c"
ALIGNED(4) const u32 usb_id_tab[] = {
	
	R_ID_STR_SET_USBMASS,
	R_ID_STR_SET_USBCAM,
	R_ID_STR_SET_VIDEO,
	

};

/*******************************************************************************
* Function Name  : getUsbDeviceSelectResInfor
* Description    : getUsbDeviceSelectResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static uint32 getUsbDeviceSelectResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image = INVALID_RES_ID;
	if(str)
		*str   = usb_id_tab[item];

	return 0;

}
/*******************************************************************************
* Function Name  : UsbDeviceSelectKeyMsgOk
* Description    : UsbDeviceSelectKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(usbDeviceOp.usb_process_flag)
			return 0;
		item = uiItemManageGetCurrentItem(winItem(handle,USBDEVICESELECT_SELECT_ID));
		u32 usb_mode;

		if(item == 0)
		{
			res_image_show(R_ID_IMAGE_USB_MODE);
			usb_mode = USB_DEVTYPE_MSC;
			while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY);
			dusb_api_Init(usb_mode);	
			usbDeviceOp.usb_process_flag = 1;
		}else if(item == 1)
		{
			// VIDEO_ARG_T arg1;
			// arg1.avi_arg.width	= 1280;
			// arg1.avi_arg.height	= 720;
			// videoRecordInit(&arg1); // enable csi&mjpeg 
			// hal_csiEnable(1);
			// app_lcdCsiVideoShowStart();
			// app_lcdShowWinModeCfg(LCDSHOW_ONLYWINA);
			
			res_image_show(R_ID_IMAGE_PCCAM_MODE);
			//hal_lcdUiEnable(UI_LAYER0,0);
			usb_mode = USB_DEVTYPE_COMBINE;
			while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY);
			dusb_api_Init(usb_mode);
			usbDeviceOp.usb_process_flag = 2;
		}else
		{
			app_taskStart(TASK_RECORD_PHOTO,0);
		}

	}else if(keyState == KEY_CONTINUE)
	{
		app_taskStart(TASK_POWER_OFF,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectKeyMsgUp
* Description    : UsbDeviceSelectKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(usbDeviceOp.usb_process_flag)
			return 0;		
		uiItemManagePreItem(winItem(handle,USBDEVICESELECT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectKeyMsgDown
* Description    : UsbDeviceSelectKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(usbDeviceOp.usb_process_flag)
			return 0;		
		 uiItemManageNextItem(winItem(handle,USBDEVICESELECT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectKeyMsgMenu
* Description    : UsbDeviceSelectKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(usbDeviceOp.usb_process_flag)
			return 0;	
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectKeyMsgMode
* Description    : UsbDeviceSelectKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		//uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectOpenWin
* Description    : UsbDeviceSelectOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]UsbDeviceSelectOpenWin\n");
/*	uiItemManageSetRowSum(winItem(handle,USBDEVICESELECT_SELECT_ID),1,Rh(32));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,USBDEVICESELECT_SELECT_ID),0,2,Rw(50), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,USBDEVICESELECT_SELECT_ID),0,2,Rw(100),Rw(6));
#endif */
	uiItemManageSetItemHeight(winItem(handle,USBDEVICESELECT_SELECT_ID),Rh(35));
	uiItemManageCreateItem( 	winItem(handle,USBDEVICESELECT_SELECT_ID),uiItemCreateMenuOption,getUsbDeviceSelectResInfor,3);

	uiItemManageSetCharInfor(	winItem(handle,USBDEVICESELECT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,USBDEVICESELECT_SELECT_ID),R_ID_PALETTE_Yellow);
	uiItemManageSetUnselectColor(winItem(handle,USBDEVICESELECT_SELECT_ID),R_ID_PALETTE_Black);

	uiItemManageSetCurItem(winItem(handle,USBDEVICESELECT_SELECT_ID),0);
	
	
	
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectCloseWin
* Description    : UsbDeviceSelectCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]UsbDeviceSelectCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectWinChildClose
* Description    : UsbDeviceSelectWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]UsbDeviceSelectWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectTouchWin
* Description    : UsbDeviceSelectTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("UsbDeviceSelectTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == USBDEVICESELECT_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : UsbDeviceSelectTouchSlideOff
* Description    : UsbDeviceSelectTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int UsbDeviceSelectTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

static int UsbDeviceSelectKeyMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	u32 usbdev_state = USBDEV_STAT_MAX;
	if(parameNum == 1)
		usbdev_state = parame[0];
	if(usbdev_state >= USBDEV_STAT_MAX)
		return 0;	
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	if(usbdev_state == USBDEV_STAT_NULL)  
	{
		app_taskStart(TASK_RECORD_PHOTO,0);
	}

	return 0;
}

ALIGNED(4) msgDealInfor UsbDeviceSelectMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	UsbDeviceSelectOpenWin},
	{SYS_CLOSE_WINDOW,	UsbDeviceSelectCloseWin},
	{SYS_CHILE_COLSE,	UsbDeviceSelectWinChildClose},
	{SYS_TOUCH_WINDOW,  UsbDeviceSelectTouchWin},
	{SYS_TOUCH_SLIDE_OFF,UsbDeviceSelectTouchSlideOff},
	{KEY_EVENT_OK,		UsbDeviceSelectKeyMsgOk},
	{KEY_EVENT_UP,		UsbDeviceSelectKeyMsgUp},
	{KEY_EVENT_DOWN,	UsbDeviceSelectKeyMsgDown},
	{KEY_EVENT_MENU,	UsbDeviceSelectKeyMsgMenu},
	{KEY_EVENT_MODE,	UsbDeviceSelectKeyMsgMode},

	{SYS_EVENT_USBDEV,	UsbDeviceSelectKeyMsgUSB},
	{EVENT_MAX,NULL},
};

WINDOW(UsbDeviceSelectWindow,UsbDeviceSelectMsgDeal,UsbDeviceSelectWin)


