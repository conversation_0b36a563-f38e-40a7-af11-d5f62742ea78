/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_MIPI_H
	#define  HX330X_MIPI_H

typedef struct {
	u32 lanes;				//mipi lane
	u32 raw_bit;			//10/8: RAW10/RAW8
	u32 dphy_pll;
	u32 csi_pclk;
	u32 tclk_settle;		//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
	u32 tclk_miss;			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
	u32 tclk_prepare;		//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
	u32 ths_settle;		//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
	u32 ths_skip;			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
	u32 ths_dtermen;		//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
	u32 hsa;				//HSA_TIME			= hsa*(1/csi_pclk)
	u32 hbp;				//HBP_TIME			= hbp*(1/csi_pclk)
	u32 hsd;				//HSD_TIME			= hsd*(1/csi_pclk)
	u32 hlines;
	u32 vsa_lines;
	u32 vbp_lines;
	u32 vfp_lines;
	u32 vactive_lines;
} MIPI_Adapt;



/*******************************************************************************
* Function Name  : hx330x_mipiClkCfg
* Description    : mipi master clk cfg  
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_mipiClkCfg(u32 clk);
/*******************************************************************************
* Function Name  : hx330x_MipiCSIUinit
* Description    : hx330x_MipiCSIUinit
* Input          :
* Output         :
* Return         :
*******************************************************************************/
void hx330x_MipiCSIUinit(void);
/*******************************************************************************
* Function Name  : hx330x_MipiCSIInit
* Description    : csi irq handler
* Input          : None
* Output         : None
* Return         : csi frame cnt
*******************************************************************************/
void hx330x_MipiCSIInit(MIPI_Adapt *pMipi_adapt);

#endif
