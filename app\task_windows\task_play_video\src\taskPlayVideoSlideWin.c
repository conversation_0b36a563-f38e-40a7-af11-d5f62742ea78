/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYVIDEOSLIDE_TOUCH_ID,
	PLAYVIDEOSLIDE_FILE_NAME_ID,
	PLAYVIDEOSLIDE_FILE_INDEX_ID,

	PLAYVIDEOSLIDE_MAX_ID
};


UNUSED ALIGNED(4) const widgetCreateInfor playVideoSlideWin[] =
{
	createFrameWin( 								Rx(0),   <PERSON>y(0),   Rw(320), <PERSON>h(240), R_ID_PALETTE_Transparent, WIN_ABS_POS),
	createStringIcon(PLAYVIDEOSLIDE_FILE_NAME_ID,   Rx(5),   Ry(0),   Rw(160), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createStringIcon(PLAYVIDEOSLIDE_FILE_INDEX_ID,  Rx(170), Ry(0),   Rw(120), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createRect(PLAYVIDEOSLIDE_TOUCH_ID,				Rx(0),   Ry(25),   Rw(320),Rh(215), INVALID_COLOR),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playVideoSlideFileNameShow
* Description    : playVideoSlideFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/	
static void playVideoSlideFileNameShow(winHandle handle)
{
	static char fileindexstr[] = {"0000/0000"};
	char* name = filelist_GetFileFullNameByIndex(SysCtrl.avi_list,SysCtrl.file_index,NULL);
	
	if(name)
	{
		hx330x_str_cpy(SysCtrl.file_fullname,name);
	}
	else
	{
		SysCtrl.file_fullname[0] = 0;
	}
	uiWinSetResid(winItem(handle,PLAYVIDEOSLIDE_FILE_NAME_ID),RAM_ID_MAKE(SysCtrl.file_fullname));

	hx330x_num2str(fileindexstr, SysCtrl.file_index + 1, 4);
	fileindexstr[4] = '/';
	hx330x_num2str(fileindexstr + 5, filelist_api_CountGet(SysCtrl.avi_list), 4);
	if(SysCtrl.file_index >= 0)
		uiWinSetResid(winItem(handle,PLAYVIDEOSLIDE_FILE_INDEX_ID),RAM_ID_MAKE(fileindexstr));
	//deg_Printf("SHOW<%s> [%d, %d]\n", name, SysCtrl.file_index,filelist_api_CountGet(SysCtrl.avi_list));
}


