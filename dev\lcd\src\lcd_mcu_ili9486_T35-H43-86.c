﻿/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ILI9486_T35_H43_86

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()

    //************* Start Initial Sequence **********//

CMD(0xF1), DAT(0x36), DAT(0x04), DAT(0x00), DAT(0x3C), DAT(0x0F), DAT(0x8F),
CMD(0xF2), DAT(0x18), DAT(0xA3), DAT(0x12), DAT(0x02), DAT(0xB2), DAT(0x12), DAT(0xFF), DAT(0x10), DAT(0x00),
CMD(0xF8), DAT(0x21), DAT(0x04),
CMD(0xF9), DAT(0x00), DAT(0x08),

CMD(0xE0), DAT(0x0F), DAT(0x2A), DAT(0x20), DAT(0x08), DAT(0x0C), DAT(0x07), DAT(0x4B), DAT(0x98), DAT(0x39), DAT(0x07), DAT(0x13), DAT(0x04), DAT(0x09), DAT(0x09), DAT(0x00),
CMD(0XE1), DAT(0x0F), DAT(0x36), DAT(0x36), DAT(0x0B), DAT(0x0C), DAT(0x08), DAT(0x46), DAT(0x76), DAT(0x35), DAT(0x08), DAT(0x13), DAT(0x07), DAT(0x1F), DAT(0x15), DAT(0x00),

CMD(0xC0), DAT(0x0A), DAT(0x0A),
CMD(0xC1), DAT(0x44),
CMD(0xC2), DAT(0x22),
CMD(0xC5), DAT(0x00), DAT(0x27),

// Frame rate
CMD(0xB1), DAT(0xA0), DAT(0x11),
CMD(0xB4), DAT(0x02),//02,03
// blank
// CMD(0xB5), DAT(0x08), DAT(0x08), DAT(0x0A), DAT(0x04),
CMD(0xB6), DAT(0x00), DAT(0x42), DAT(0x3B),

CMD(0x35), DAT(0x00),
CMD(0x36), DAT(0x08),
CMD(0x3A), DAT(0x55),

CMD(0x44), DAT(0x00), DAT(0x00),

CMD(0x11),
DLY(120),
CMD(0x29),
CMD(0x2A), DAT(0x00), DAT(0x00), DAT(0x01), DAT(0x3F),
CMD(0x2B), DAT(0x00), DAT(0x00), DAT(0x01), DAT(0xDF),
CMD(0x2C),

LCD_INIT_TAB_END()


LCD_DESC_BEGIN()
    .name           = "MCU_iLi9486_T35-H43-86",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_90,
    .te_mode        = LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div       = LCD_PCLK_DIV(320*480*2*60),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w       = 320,
    .screen_h       = 480,

    .video_w        = 480,
    .video_h        = 320,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -12,

    .saturation     = LCD_SATURATION_130,

    .contra_index   = 6,

    .gamma_index    = {4, 4, 4},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























