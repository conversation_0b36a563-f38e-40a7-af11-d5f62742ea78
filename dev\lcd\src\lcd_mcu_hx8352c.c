/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_HX8352C

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
#if 1
    //===hx8352c====
    CMD(0xE5),DAT(0x10),
    <PERSON><PERSON>(0xE7),DAT(0x10),
    CMD(0xE8),DAT(0x48),
    CMD(0xEC),DAT(0x09),
    CMD(0xED),DAT(0x6C),

    // Power on Setting

    CMD(0x23),DAT(0x00),    // 0x6f
    DLY(10),
    CMD(0x24),DAT(0x40),    // vcomh 0x57
    DLY(10),
    CMD(0x25),DAT(0x64),    // vcomL 0x64
    DLY(10),

    CMD(0xE2),DAT(0x18),
    CMD(0x1B),DAT(0x15),    // 0x15
    CMD(0x01),DAT(0x00),
    CMD(0x1C),DAT(0x03),    //0x03

    // Power on sequence
    CMD(0x18),DAT(0x0c),
    CMD(0x19),DAT(0x01),    //0x01

    DLY(50),
    CMD(0x1F),DAT(0x8c),
    CMD(0x1F),DAT(0x84),
    DLY(50),
    CMD(0x1F),DAT(0x94),
    DLY(50),
    CMD(0x1F),DAT(0xd4),    //  0xd4
    DLY(50),

    // Gamma Setting
    CMD(0x40),DAT(0x00),
    CMD(0x41),DAT(0x2B),
    CMD(0x42),DAT(0x29),
    CMD(0x43),DAT(0x3E),
    CMD(0x44),DAT(0x3D),
    CMD(0x45),DAT(0x3F),
    CMD(0x46),DAT(0x24),
    CMD(0x47),DAT(0x74),
    CMD(0x48),DAT(0x08),
    CMD(0x49),DAT(0x06),
    CMD(0x4A),DAT(0x07),
    CMD(0x4B),DAT(0x0D),
    CMD(0x4C),DAT(0x17),
    CMD(0x50),DAT(0x00),
    CMD(0x51),DAT(0x02),
    CMD(0x52),DAT(0x01),
    CMD(0x53),DAT(0x16),

    CMD(0x54),DAT(0x14),
    CMD(0x55),DAT(0x3F),
    CMD(0x56),DAT(0x0B),
    CMD(0x57),DAT(0x5B),
    CMD(0x58),DAT(0x08),
    CMD(0x59),DAT(0x12),
    CMD(0x5A),DAT(0x18),
    CMD(0x5B),DAT(0x19),
    CMD(0x5C),DAT(0x17),
    CMD(0x5D),DAT(0xFF),



    // Display ON Setting
    //CMD(0x31),DAT(0x00), //RGB I/F
    CMD(0x16),DAT(0x48),// Mx,My,MV 240*400
    CMD(0x17),DAT(0x55),
    CMD(0x18),DAT(0x0c), // 77

    CMD(0x60),DAT(0x08),//TE
    CMD(0x70),DAT(0x00),
    CMD(0x71),DAT(0x08),
    CMD(0x84),DAT(0x00),
    CMD(0x85),DAT(0x08),
    /*CMD(0x29),DAT(0x00),
    CMD(0x2a),DAT(0x00),
    CMD(0x2b),DAT(0x09),*/

    CMD(0x28),DAT(0x20),
    DLY(40),
    CMD(0x28),DAT(0x38),
    DLY(80),// Waiting 2 frames al least
    CMD(0x28),DAT(0x3C),
    DLY(40),

    //CMD(0x29),DAT(0xff),
    //CMD(0x2a),DAT(0x11),
    //CMD(0x2b),DAT(0x88),
    //CMD(0x2c),DAT(0x88),


    /*
    CMD(0x2b),DAT(0x02),
    CMD(0x2c),DAT(0x02),
    */

    CMD(0x02),DAT(0x00),
    CMD(0x03),DAT(0x00),
    CMD(0x04),DAT(0x00),
    CMD(0x05),DAT(0xeF),

    CMD(0x06),DAT(0x00),
    CMD(0x07),DAT(0x00),
    CMD(0x08),DAT(0x01),
    CMD(0x09),DAT(0x8F),

    CMD(0x22),
#else
    CMD(0x1A),DAT(0x02), //BT
    CMD(0x1B),DAT(0x88), //VRH
    //****VCOM offset**///
    CMD(0x23),DAT(0x00), //SEL_VCM
    CMD(0x24),DAT(0x5F), //VCM
    CMD(0x25),DAT(0x15), //VDV
    CMD(0x2D),DAT(0x03), //NOW[2:0]=011
    //Power on Setting
    CMD(0x18),DAT(0x02), //Frame rate 72Hz
    CMD(0x19),DAT(0x01), //OSC_EN='1'),DAT( start Osc
    CMD(0x01),DAT(0x00), //DP_STB='0'),DAT( out deep sleep
    CMD(0x1F),DAT(0x88), //STB=0
    DLY(5),
    CMD(0x1F),DAT(0x80), //DK=0
    DLY(5),
    CMD(0x1F),DAT(0x90), //PON=1
    DLY(5),
    CMD(0x1F),DAT(0xD0), //VCOMG=1
    DLY(5),
    //262k/65k color selection
    CMD(0x16),DAT(0x48),
    CMD(0x17),DAT(0x05), //default 0x06 262k color // 0x05 65k color
    CMD(0x18),DAT(0x02), //Frame rate 72Hz
    //SET PANEL
    //CMD(0x36),DAT(0x03), //REV_P),DAT( SM_P),DAT( GS_P),DAT( BGR_P),DAT( SS_P
    CMD(0x29),DAT(0x31), //400 lines
    CMD(0x70),DAT(0x00),
    CMD(0x71),DAT(0x12), //RTN
    //Gamma 2.2 Setting
    CMD(0x40),DAT(0x00),
    CMD(0x41),DAT(0x45),
    CMD(0x42),DAT(0x45),
    CMD(0x43),DAT(0x04),
    CMD(0x44),DAT(0x00),
    CMD(0x45),DAT(0x08),
    CMD(0x46),DAT(0x23),
    CMD(0x47),DAT(0x23),
    CMD(0x48),DAT(0x77),
    CMD(0x49),DAT(0x40),
    CMD(0x4A),DAT(0x04),
    CMD(0x4B),DAT(0x00),
    CMD(0x4C),DAT(0x88),
    CMD(0x4D),DAT(0x88),
    CMD(0x4E),DAT(0x88),

    //Display ON Setting
    CMD(0x28),DAT(0x38), //GON=1),DAT( DTE=1),DAT( D=10
    DLY(40),
    CMD(0x28),DAT(0x3C), //GON=1),DAT( DTE=1),DAT( D=11
    //Set GRAM Area
    CMD(0x02),DAT(0x00),
    CMD(0x03),DAT(0x00), //Column Start
    CMD(0x04),DAT(0x01),
    CMD(0x05),DAT(0x8F), //Column End
    CMD(0x06),DAT(0x00),
    CMD(0x07),DAT(0x00), //Row Start
    CMD(0x08),DAT(0x00),
    CMD(0x09),DAT(0xeF), //Row End

    CMD(0x22), //Start GRAM write
#endif
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name           = "MCU_HX8352C",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_270,
    .te_mode        = LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div       = LCD_PCLK_DIV(240*400*2*47),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w       = 240,
    .screen_h       = 400,

    .video_w        = 400,
    .video_h        = 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -12,

    .saturation     = LCD_SATURATION_130,

    .contra_index   = 6,

    .gamma_index    = {4, 4, 4},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























