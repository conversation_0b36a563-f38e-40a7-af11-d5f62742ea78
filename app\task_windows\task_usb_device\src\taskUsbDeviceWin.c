/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
typedef enum
{
	USBDEV_SEL_ID0 = 0,
	USBDEV_UNSEL_ID0,
	USBDEV_STR_ID0,
	
	USBDEV_SEL_ID1,
	USBDEV_UNSEL_ID1,
	USBDEV_STR_ID1,
	
	USBDEV_SEL_ID2,
	USBDEV_UNSEL_ID2,
	USBDEV_STR_ID2,
	
	USBDEV_SEL_ID3,
	USBDEV_UNSEL_ID3,
	USBDEV_STR_ID3,
}USBDEV_ID;
ALIGNED(4) static USBDEV_ID usbDeviceIDnum[] =
{
	USBDEV_SEL_ID0,
	USBDEV_SEL_ID1,
	USBDEV_SEL_ID2,
	USBDEV_SEL_ID3,
};
ALIGNED(4) static u32 usbDeviceStrIDTab[] =
{
	R_ID_STR_SET_USBMASS,
	R_ID_STR_SET_USBCAM,
	R_ID_STR_SET_VIDEO,
	R_ID_STR_SET_SETTING,
};

#define USBDEV_ID_MAX			(sizeof(usbDeviceIDnum)/sizeof(usbDeviceIDnum[0]))
#define USBDEV_SEL_ID(num)		usbDeviceIDnum[num%USBDEV_ID_MAX]
#define USBDEV_UNSEL_ID(num)	(USBDEV_SEL_ID(num)+1)
#define USBDEV_STRING_ID(num)	(USBDEV_SEL_ID(num)+2)
#define USBDEV_PAGESTARTID(num)	((num/USBDEV_ID_MAX)*USBDEV_ID_MAX)
#define USBDEV_TOUCHITEM(num)	((num)/3)



UNUSED ALIGNED(4) const widgetCreateInfor usbDeviceWin[] =
{
	createFrameWin(							Rx(0),   Ry(0),   Rw(320), Rh(240),	R_ID_PALETTE_Transparent,		WIN_ABS_POS),
	
	createRect(USBDEV_SEL_ID0,     			Rx(0),   Ry(0),   Rw(80),  Rh(240),	R_ID_PALETTE_Transparent),
	createRect(USBDEV_UNSEL_ID0,     		Rx(10),  Ry(10),  Rw(60),  Rh(220),	R_ID_PALETTE_Transparent),
	createStringIcon(USBDEV_STR_ID0, 		Rx(0),   Ry(180), Rw(80),  Rh(60),  R_ID_STR_SET_USBMASS,  	ALIGNMENT_CENTER, R_ID_PALETTE_White, DEFAULT_FONT),
	
	createRect(USBDEV_SEL_ID1,     			Rx(80),  Ry(0),   Rw(80),  Rh(240),	R_ID_PALETTE_Transparent),
	createRect(USBDEV_UNSEL_ID1,     		Rx(90),  Ry(10),  Rw(60),  Rh(220),	R_ID_PALETTE_Transparent),
	createStringIcon(USBDEV_STR_ID1, 		Rx(80),  Ry(180), Rw(80),  Rh(60),  R_ID_STR_SET_USBCAM,  	ALIGNMENT_CENTER, R_ID_PALETTE_White, DEFAULT_FONT),	

	createRect(USBDEV_SEL_ID2,     			Rx(160), Ry(0),   Rw(80),  Rh(240),	R_ID_PALETTE_Transparent),
	createRect(USBDEV_UNSEL_ID2,     		Rx(170), Ry(10),  Rw(60),  Rh(220),	R_ID_PALETTE_Transparent),
	createStringIcon(USBDEV_STR_ID2, 		Rx(160), Ry(180), Rw(80),  Rh(60),  R_ID_STR_SET_VIDEO,  	ALIGNMENT_CENTER, R_ID_PALETTE_White, DEFAULT_FONT),	

	createRect(USBDEV_SEL_ID3,     			Rx(240), Ry(0),   Rw(80),  Rh(240),	R_ID_PALETTE_Transparent),
	createRect(USBDEV_UNSEL_ID3,     		Rx(250), Ry(10),  Rw(60),  Rh(220),	R_ID_PALETTE_Transparent),
	createStringIcon(USBDEV_STR_ID3, 		Rx(240), Ry(180), Rw(80),  Rh(60),  R_ID_STR_SET_SETTING,  ALIGNMENT_CENTER, R_ID_PALETTE_White, DEFAULT_FONT),	
	
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playVideoThumbnallSelect
* Description    : playVideoThumbnallSelect
* Input          : winHandle handle,u32 num,u32 select
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void usbDeviceSelect(winHandle handle,u32 num,u32 select)
{
	u32 strid = USBDEV_STRING_ID(num);
	uiWinSetResid(winItem(handle,strid),usbDeviceStrIDTab[num]);
	if(select)
	{
		uiWinSetStrInfor(winItem(handle,strid), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_Yellow);
	}
	else
	{
		uiWinSetStrInfor(winItem(handle,strid), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_White);
	}
	
	uiWinUpdateResId(winItem(handle,strid));
}


