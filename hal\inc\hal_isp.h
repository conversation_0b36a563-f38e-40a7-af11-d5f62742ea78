/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_ISP_H
    #define HAL_ISP_H
/*******************************************************************************
* Function Name  : hal_isp_filter_cfg
* Description    : hal_isp_filter_cfg
* Input          : filter_type: SENSOR_FILTER_TYPE
* Output         : none
* Return         : NONE
*******************************************************************************/
void hal_isp_filter_cfg(u8 filter_type);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_isp_init(void);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_SensorRegister(SENSOR_API_T *pSensor_api);
/*******************************************************************************
* Function Name  : hal_SensorRegister
* Description    : sensor api register
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
SENSOR_API_T* hal_SensorApiGet(void);
/*******************************************************************************
* Function Name  : hal_SensorResolutionGet
* Description    : hal layer .get csi resolution
* Input          : u16 *width : width
                      u16 *height : height
* Output         : None
* Return         : int 0 : success
                      int -1: fail
*******************************************************************************/
int hal_SensorResolutionGet(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_sensor_fps_adpt
* Description    : hal_sensor_fps_adpt
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_sensor_fps_adpt(u32 frequecy_mode,u32 frame_rate);
/*******************************************************************************
* Function Name  : hal_isp_process
* Description    : hal layer .isp auto adjust process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_isp_process(void);
/*******************************************************************************
* Function Name  : hal_sensor_awb_scene_set
* Description    : awb scene mode set
* Input          : u32 awb_scene_mod: 0 - auto mode, 1~5 : en mode
* Output         : none                                            
* Return         : none
*******************************************************************************/
void hal_sensor_awb_scene_set(u32 awb_scene_mod);

/*******************************************************************************
* Function Name  : hal_sensor_EV_set
* Description    : sensor EV setting
* Input          : u32 ev_mode: cur level
                                exp_adapt.ev_mode = 1, base VDE bright_oft(0~255);
								exp_adapt.ev_mode = 1, base AE EXP auto 	   
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_EV_set(u32 ev_mode);
/*******************************************************************************
* Function Name  : hal_isp_br_get
* Description    : get light level from sensor
* Input          : none
* Output         : none                                            
* Return         : level value(0~255)  0: max dark  255: max light
*******************************************************************************/
u8 hal_isp_br_get(void);
/*******************************************************************************
* Function Name  : hal_ispService
* Description    : hal_ispService
* Input          : none
* Output         : none                                            
* Return         : NONE
*******************************************************************************/
void hal_ispService(void);
/*******************************************************************************
* Function Name  : hal_SensorRotate
* Description    : hal_SensorRotate
* Input          : rotate       : for fp_rotate(rotate)
                   colrarray    : see CSI_POLARITY_E
                                : 0xff = skip
* Output         : none                                            
* Return         : NONE
*******************************************************************************/
void hal_SensorRotate(u32 rotate,u32 colrarray);
/*******************************************************************************
* Function Name  : hal_isplog_cnt
* Description    : 
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
u32 hal_isplog_cnt(void);
/*******************************************************************************
* Function Name  : hal_isplog_cnt
* Description    : 
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
u32 hal_isp_cur_yloga(void);
/*******************************************************************************
* Function Name  : hal_sensor_ISO_set
* Description    : sensor ISO SET, _AE_POS_配置为_ISP_AUTO_, 需要注册 fp_exp_gain_wr函数，配置 gain_max_iso表
* Input          : iso_mode: 0 - ISO AUTO,1 - ISO100, 2 - ISO200, 3 - ISO400, 
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_ISO_set(u32 iso_mode);
/*******************************************************************************
* Function Name  : hal_sensor_sharp_set
* Description    : sensor sharp SET, _EE_POS_配置为_ISP_AUTO_或 _ISP_EN_时有效，需要配置sharp_class_adj
* Input          : sharp_mode: 0 - 锐利度低, 1 - 锐利度中, 2 - 锐利度高
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_sharp_set(u32 sharp_mode);
/*******************************************************************************
* Function Name  : hal_sensor_beauty_set
* Description    : sensor beauty SET, _EE_POS_配置为_ISP_AUTO_或 _ISP_EN_时有效,需要配置sensor的ee_sharp_slope_beauty
* Input          : beauty_mode: 0 - 美颜关，1 - 美颜开
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_beauty_set(u32 beauty_mode);
/*******************************************************************************
* Function Name  : hal_sensor_wdr_set
* Description    : sensor wdr SET, 需配置vde_adapt.wdr_bright_k
* Input          : u32 on
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_wdr_set(u32 on);
/*******************************************************************************
* Function Name  : hal_sensor_scene_mode_set
* Description    : sensor scene mode SET, hgrm_adapt_portrait 和 hgrm_adapt_scene
* Input          : u32 scene_mode: 0: 自动，1： 人像模式，2：风景模式
* Output         : none
* Return         : none
*******************************************************************************/
void hal_sensor_scene_mode_set(u32 scene_mode);

#endif
