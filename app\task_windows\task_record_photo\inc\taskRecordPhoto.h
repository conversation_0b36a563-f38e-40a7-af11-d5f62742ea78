/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_RECORD_PHOTO_H
#define  __TASK_RECORD_PHOTO_H
typedef struct{
	u32 winState;
	u32 jpgsize;
	u8* jpgbuf;
	u32 wintype; //
	int step;
    u32 file_remain;
	
    u8  add_mode; 
    u8  capture_mode;
    u8  capture_time;
    u8  capture_cnt;
    u8  capture_kick;

    u8  upkeystate;
    u8  downkeystate;
}taskRecordPhotoOp;
EXTERN_WINDOW(recordPhotoWindow);
extern sysTask_T taskRecordPhoto;
extern taskRecordPhotoOp recordPhotoOp;
/*******************************************************************************
* Function Name  : app_taskRecordPhoto_callback
* Description    : APP LAYER: app_taskRecordPhoto
* Input          : INT32U channel,INT32U cmd,INT32U para
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int app_taskRecordPhoto_callback(INT32U channel,INT32U cmd,INT32U para);
/*******************************************************************************
* Function Name  : taskRecordPhotoProcess
* Description    : take a photo by user config
* Input          : none
* Output         : none                                            
* Return         : int <0 fail
*******************************************************************************/
int taskRecordPhotoProcess(void);




/*******************************************************************************
* Function Name  : filelist_remain
* Description    : take a photo by user config
* Input          : none
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void taskRecordPhotoRemainCal(void);

#endif
