/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  API_MULTIMEDIA_H
#define  API_MULTIMEDIA_H


#include "AVI/inc/avi_api.h"
#include "WAV/inc/wav_api.h"
#include "JPG/inc/jpg_api.h"
#include "mp3/inc/mp3_api.h"
#include "GIF/inc/gif_api.h"
#define  JPG_EXIF_EN				1

#define  AVI_STANDARD_EN			1
#define  AVI_OPENDML_EN				1
#define  WAV_PCM_EN					1
#define  WAV_ALAWPCM_EN				0
#define  WAV_ULAWPCM_EN				0
#define  WAV_ADPCM_EN				0
#define  MP3_EN						0
#define  GIF_EN						1




#define MEDIA_HANDLE_NUM		    3	//AVI A, AVI B, reserve
//MEDIA_TYPE
#define	MEDIA_AVI_STD 		 		(0<<1)
#define	MEDIA_AVI_ODML				(1<<1)
#define	MEDIA_WAV					(2<<1)
#define	MEDIA_MP3					(3<<1)
#define	MEDIA_TYPE_MAX				(4<<1)
#define	MEDIA_TYPE_RAW				0xff



typedef enum{
	MEDIA_FILE_AVI = 0,
	MEDIA_FILE_WAV,
	MEDIA_FILE_MP3,
	MEDIA_FILE_JPG,
	MEDIA_FILE_BMP,
	MEDIA_FILE_RAW,
}MEDIA_FILE_TYPE;
typedef enum{
	MEDIA_ENCODE = 0,
	MEDIA_DECODE = 1,
}MEDIA_DIR;
typedef enum{
	MEDIA_OK 		= 0,
	MEDIA_ERR_CH 	= -1,
	MEDIA_ERR_TYPE	= -2,
	MEDIA_ERR_FUNC	= -3,
}MEDIA_STATUS;
typedef enum  // source type
{
	MEDIA_SRC_FS=0,   // file system
	MEDIA_SRC_NVFS,   // spi flash
	MEDIA_SRC_RAM,    // sdram
	MEDIA_SRC_NVJPG,
	MEDIA_SRC_MAX
}MEDIA_SRC_TYPE;
typedef enum  // source type
{
	MEDIA_CACHE_ERR_NONE = 0,
	MEDIA_CACHE_ERR_END	 = -1,
	MEDIA_CACHE_ERR_READ = -2,
	MEDIA_CACHE_ERR_SRCTYPE = -3,
}CACHE_OP_ERR;

typedef struct Cache_OP_S
{
	INT8U 	*buf;
	INT32U 	len;
	INT32U 	winstart;
	INT32U 	winend;
	INT32U  curoffset;
	INT32U  minoffset;
	INT32U	maxoffset;
	INT32U	src_type;   //MEDIA_SRC_TYPE
	int     fd;
	u32     base_addr;  //for MEDIA_SRC_NVFS 
	u32     size;		//for MEDIA_SRC_NVFS 
}Cache_OP_T;


typedef struct MEDIA_FUNC_S
{
	int (*init)(void *handle);
	int (*uninit)(void *handle);
	int (*start)(void *handle, void *arg);
	int	(*end)(void *handle);
	int	(*service)(void *handle);
	int	(*addjunk)(void *handle);
	int (*encodeframe)(void *handle, void *buf, int len, u8 type);
	int (*decodeframe)(void *handle, void *buf, int *offset, int *len,u32 type);
	int (*gettime)(void *handle, int *totaltime, int *curtime);
	int (*getsta)(void *handle);
	int (*decodefast)(void *handle, int deta);
	int (*getArg)(void *handle, void *arg);
}MEDIA_FUNC_T;
typedef struct MEDIA_API_HANDLE_S
{
	INT8U 	ch;
	INT8U 	type;
	INT8S 	state;
	INT8S 	status;

	void*	opt;
	int  	(*write)(int fd,const void *buff,UINT len);
	int 	(*read)(int fd,void *buff, UINT len);
	FSIZE_t 	(*seek)(int fd,FSIZE_t offset,INT32U opt);
	int     (*merge)(int fd1,int fd2);
	int 	(*bound)(int fd1,int fd2);
	MEDIA_FUNC_T*	hanle_func;
}MEDIA_API_HANDLE_T;


extern const MEDIA_FUNC_T avi_std_enc_func;
extern const MEDIA_FUNC_T avi_odml_enc_func;
extern const MEDIA_FUNC_T avi_dec_func;
extern const MEDIA_FUNC_T wav_enc_func;
extern const MEDIA_FUNC_T wav_dec_func;
/*******************************************************************************
* Function Name  : api_multimedia_cache_init
* Description	 : api_multimedia_cache_init
* Input 		 : Cache_OP_T *cache
				   void *buffer
				   INT32U len
				   INT32U offset
				   INT32U maxoffset
* Output		 : none
* Return		 : none
*******************************************************************************/
int api_multimedia_cache_init(Cache_OP_T *cache, void *buffer,INT32U len,INT32U offset, INT32U maxoffset);
/*******************************************************************************
* Function Name  : api_multimedia_cachePreRead
* Description	 : api_multimedia_cachePreRead
* Input 		 : Cache_OP_T *cache
				   INT32S offset
				   INT32U len
* Output		 : none
* Return		 : cache buf
*******************************************************************************/
int api_multimedia_cachePreRead(Cache_OP_T *cache,INT32U *addr,INT32S offset,INT32U len);
/*******************************************************************************
* Function Name  : api_multimedia_cacheRead
* Description	 : api_multimedia_cache read
* Input 		 : Cache_OP_T *cache
				   INT32S offset
				   INT32U len
* Output		 : none
* Return		 : cache buf
*******************************************************************************/
int api_multimedia_cacheRead(Cache_OP_T *cache,INT32U *addr,INT32S offset,INT32U len);
/*******************************************************************************
* Function Name  : api_multimedia_init
* Description	 : multimedia hanle register
* Input 		 : u8 dir: 	MEDIA_ENCODE , MEDIA_DECODE
* 				   u8 type: MEDIA_TYPE
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_init(u8 dir, u8 type);
/*******************************************************************************
* Function Name  : api_multimedia_uninit
* Description	 : multimedia hanle uninit
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_uninit(int ch);

/*******************************************************************************
* Function Name  : api_multimedia_start
* Description	 : multimedia hanle start
* Input 		 : int ch    : multimedia handle ch
*                  void *arg : cfg para
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_start(int ch, void *arg);

/*******************************************************************************
* Function Name  : api_multimedia_end
* Description	 : multimedia hanle end
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_end(int ch);

/*******************************************************************************
* Function Name  : api_multimedia_service
* Description	 : multimedia hanle service
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_service(int ch);
/*******************************************************************************
* Function Name  : api_multimedia_addjunk
* Description	 : multimedia hanle add junk to align
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_addjunk(int ch);

/*******************************************************************************
* Function Name  : api_multimedia_encodeframe
* Description	 : multimedia hanle encode one frame service
* Input 		 : int ch      : multimedia handle ch
* 				   void *buf   : encode input buf
				   int len     : data len per bytes
                   u8 type     : encode type
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_encodeframe(int ch, void *buf, int len, u8 type);

/*******************************************************************************
* Function Name  : api_multimedia_decodeframe
* Description	 : multimedia hanle encode one frame service
* Input 		 : int ch      : multimedia handle ch
* 				   void *buf   : decode output buf
				   int *offset : decode frame file offset
                   int *len    : decode frame len
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_decodeframe(int ch, void *buf, int *offset, int *len,u32 type);

/*******************************************************************************
* Function Name  : api_multimedia_gettime
* Description	 : multimedia hanle get time
* Input 		 : int ch         : multimedia handle ch
* 				   int *totaltime : totaltime in ms
				   int *curtime   : curtime in ms
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_gettime(int ch, int *totaltime, int *curtime);

/*******************************************************************************
* Function Name  : api_multimedia_getsta
* Description	 : multimedia hanle get cur sta
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_getsta(int ch);

/*******************************************************************************
* Function Name  : api_multimedia_decodefast
* Description	 : multimedia hanle decode step set
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_decodefast(int ch, int delta);
/*******************************************************************************
* Function Name  : api_multimedia_getArg
* Description	 : multimedia hanle get arg
* Input 		 : int ch: multimedia handle ch
* Output		 : none
* Return		 : int 0 success
*******************************************************************************/
int api_multimedia_getArg(int ch, void *arg);


#endif
