/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_DVP_VGA_GC0308 > 0
//【配置MCLK】
//#warning "此处修改GC0308 MCLK！！！"
#define MCLK		        			24000000L  //HZ单位

#define IMAGE_V             			1   //0: 正像， 1： 倒像18

//【配置IO电压】
//#warning "此处修改SP1409 VDDSENSOR CORE！！！"
#define	SENR_CORE	        			VDDSEN_3_00V//snesor core triming 1.8~3.3V , 配置为0默认3.0V
//#warning "此处修改SP1409 VDDSENSOR IO"
#define	SENR_VDD	        			VDDSENIO_3_00V//sensor VDD IO triming 1.8~3.3V, 配置为0默认3.0V

//【配置sensor格式】
//定义sensor数据格式，支持RAW或YUV422输入
#define SENSOR_WIDTH					640
#define SENSOR_HEIGHT					480
#define SENSOR_HSYNC					1    //1: 高电平有效, 0: 低电平有效
#define SENSOR_VSYNC					1    //1: 高电平有效, 0: 低电平有效

#define SENSOR_DATAFOMAT    			CSI_TYPE_YUV422//CSI_TYPE_YUV422//CSI_TYPE_RAW10



#if  (SENSOR_DATAFOMAT == CSI_TYPE_YUV422)
#define H_LEN      						(SENSOR_WIDTH*2  + 720)//314//((106*3+46))//行消隐pclk数 ,配置值为(DEFAULT_HBLANK-46)  //MJP 960P至少需要20us
#define V_LEN      						(SENSOR_HEIGHT + 18) //场消隐数行数
#define SENSOR_COLOR_ARRAY  			CSI_PRIORITY_Y0CBY1CR//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
															//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB
#else

#define H_LEN      						(SENSOR_WIDTH  + 314)//314//((106*3+46))//行消隐pclk数 ,配置值为(DEFAULT_HBLANK-46)  //MJP 960P至少需要20us
#define V_LEN      						(SENSOR_HEIGHT + 18) //场消隐数行数
#define SENSOR_COLOR_ARRAY  			CSI_PRIORITY_RGGB//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
															//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB
#endif



//定义sensor iic相关参数
#define SENSOR_W_CMD        			0x42
#define SENSOR_R_CMD        			0x43
#define SENSOR_ID           			0x9b
#define SENSOR_ADDR_NUM     			0x01
#define SENSOR_DATA_NUM     			0x01
#define SENSOR_ID_REG       			0x0000

//PCLK 调节
#define PCLK_DIG_FIR	    			0     //PCLK 数字滤波： 0: disable filter, 1: 2 step filter, 2: 3 step filter, 3: disable pclk output	
#define PCLK_ANA_FIR					0     //PCLK 模拟滤波： 0x0~0x07: diable, 0x08~0x0f: enable 1~8 steps filter	
#define PCLK_TUNE_VALUE					0     //PCLK 相位调节 0x00~0x0f: delay 1~15 steps;  0x10~0x1f: output inver and delay 1~15 steps
#define PCLK_INV_EN						0	  //PCLK 反向使能	

SENSOR_INIT_SECTION const u8 GC0308InitTable[]=
{
    //sensor inittab 初始化表
    0xfe,0x80,  //soft rest and select page0------------------------------------------------------
    0xfe,0x00,	//soft reset release
    0xd2,0x10,	//BIT[7] AEC_EN, BIT[5] color Y mode, BIT[4] skin weight mode, BIT[3] weight drop mode, BIT[2] color select 1:0x20, 0:0x10; BIT[1:0] color max gain select
    0x22,0x54,	//BIT[6] auto_DNDD_en, BIT[5] auto_EE_en, BIT[4] auto_SA_en, BIT[2] ABS_enable, BIT[1] AWB_enable
	#if  (SENSOR_DATAFOMAT == CSI_DATA_FORMAT_YUV422)
	0x5a,0x40,//0x56,  //0x56 AWB_R_gain
    0x5b,0x40,  //0x40 AWB_G_gain
    0x5c,0x40,//0x4a,  //0x4a AWB_B_gain
    0x22,0x57,	//BIT[6] auto_DNDD_en, BIT[5] auto_EE_en, BIT[4] auto_SA_en, BIT[2] ABS_enable, BIT[1] AWB_enable
	#endif


    //0x01,(u32)HBLANK_BASE & 0xff,//0x6a//0xde //HB[7:0]
    //0x02,(u32)VBLANK_BASE & 0xff,  //0x0c //VB[7:0]
    //0x0f,(((u32)VBLANK_BASE >> 8)<<4)|((u32)HBLANK_BASE >> 8), //{0x0f,0x00}, //VB/HB [11:8]
    //0x0f,0x11,
    0xe2,0x00,	//[3:0] Anti_flicker_step[11:8]
    0xe3,0x96,	//Anti_flicker_step[7:0]
    0xe4,0x01,  //exp_level_1 high
    0xe5,0x00,  //exp_level_1 low 0xc2,//0x8f
    0xe6,0x01,  //exp_level_2 high
    0xe7,0xc2,  //exp_level_2 low
    0xe8,0x02,  //exp_level_3 high
    0xe9,0x58,  //exp_level_3 low
    0xea,0x02,  //exp_level_4 high
    0xeb,0x58,  //exp_level_4 low//0x95
    0xec,0x00,//0x20,//BIT[5:4] max exp levels by AEC; BIT[3:0] min exp level high 4bits
    //0xed,0x04, //min exp level low 8bits
    //0x01,0xfc,//0xde
    //0x02,0x24,
    //0x0f,0x00, //{0x0f,0x00},
    //0xe2,0x00,
    //0xe3,0x83,
    //0xe4,0x02,//exp level
    //0xe5,0x7a,//0x8f
    //0xe6,0x02,
    //0xe7,0x7a,
    //0xe8,0x02,
    //0xe9,0x7a,
    //0xea,0x03,
    //0xeb,0x77,//0x95
    0x03,0x01, //BIT[3:0]: Exposure[11:8] ,use line processing time as the unit
    0x04,0x00,//0x2c,//BIT[7:0]: Exposure[7:0] ,use line processing time as the unit
#if  SENSOR_DATAFOMAT == CSI_DATA_FORMAT_YUV422
    0x05,0x00,  //window size and position
    0x06,0x00,  //row start [10] -- 0x05[1:0]+0x06[7:0]
    0x07,0x00,
    0x08,0x00,  //col start [10] -- 0x07[1:0]+0x08[7:0]
    0x09,0x01,
    0x0a,0xe8,  //window height[9] -- 0x09[0] + 0x0a[7:0], default 488
    0x0b,0x02,
    0x0c,0x88,  //window width[10] -- 0x0b[1:0] + 0x0c[7:0], default 648
#elif SENSOR_DATAFOMAT == CSI_DATA_FORMAT_RAW
#if 0//SENSOR_OUT_648
    0x05,0x00,  //window size and position
    0x06,0x00,  //row start [10] = 4
    0x07,0x00,
    0x08,0x00,  //col start [10] = 4
    0x09,0x01,
    0x0a,0xe8,  //(0xe0-2),//window height[9] = 0x1e0 = 480
    0x0b,0x02,
    0x0c,0x88,  //window width[10] = 0x280 = 640
#else
    0x05,0x00,  //window size and position
    0x06,0x04,  //row start [10] = 4
    0x07,0x00,
    0x08,0x04,  //col start [10] = 4
    0x09,0x01,
    0x0a,0xe0,  //(0xe0-2),//window height[9] = 0x1e0 = 480
    0x0b,0x02,
    0x0c,0x80,  //window width[10] = 0x280 = 640
#endif
#endif
    0x0d,0x02,	 //vs_st[7:0]: num of row time , frame start to first HSYNC valid
    0x0e,0x02,	 //vs_et[7:0]: num of row time , last HSYNC valid to frame end...   VB>(vs_st+vs_et)
    //0x0f,0x00,	 //[7:4] VB[11:8], [3:0] HB[11:8]
    0x10,0x22,	 //Rsh_width:  [7:4] restg_width, [3:0] sh_width
    0x11,0xfd,	 //Tsp_width:  [7:2] tx_width, [1:0] space width x2
    0x12,0x2a,   //Sh_delay: number of delays after hsync finish and after pipeline
    0x13,0x00,	 //Row_tail_width: [3:0] generate more hsync for special application
#if IMAGE_V == 1
    0x14,0x10,	 //0',180'  BIT[7] hsync always on, [1] upside down, [0] mirror
     //#warning "- GC0308 倒像"
#else
    0x14,0x13,	 //0',180'  BIT[7] hsync always on, [1] upside down, [0] mirror
   // #warning  "- GC0308 正像"
#endif
    0x15,0x0a,	 //[7:6] output_mode: 00 VGA, 01 even skip, 10 CIF, 11 MTD, [5:4] restg_mode, [3:2]
    0x16,0x05,	 //[4] capture ad_data_edge 1 positive 0 nagative, [3:0] num of A/D pipe stages
    0x17,0x01,	 //[5] coltest_en , [4] ad_test_enable, [3] tx_allow, [2] black sun correction enable, [1:0] black sun control 00- 480mv, 01 - 500mv(default), 10 - 600mv, 11 - 700mv
    0x18,0x44,
    0x19,0x44,
    0x1a,0x1e,
    0x1b,0x00,
    0x1c,0xc1,//current
    0x1d,0x08,
    0x1e,0x60,
    0x1f,0x3f,//0x16 PAD_drv [5:4] sync_drv 4/8/12/16, [3:2] data_drv 2/4/8/10, [1:0] pclk_drv 2/4/8/10
#if  SENSOR_DATAFOMAT == CSI_DATA_FORMAT_YUV422
    0x20,0xff,	//[7] BKS_enable, [6] gamma_enable, [5] CC_enable, [4] Edge Enhance, [3] Interpolation enable, [2] noise removal enable, [1] defect removal enable, [0] lens-shading correction enable
    0x21,0xf8,  //[6] blue edge en, [5] skin correction enable, [4] Cb Cr Hue enable, [3] Y_as_en, [2] Auto_gray_en, [1] Y_gamma_en, [0] HSP_en
    0x22,0x57,	//[6] auto_DNDD_en, [5] auto_EE_en, [4] auto_SA_en, [2] ABS_enable, [1] AWB_enable
    0x24,0xa2,//0xa0 pt [7] ISP high 8 or low 8, [6] BCR_Y_C, [5] avg neighbor chroma,
              //[4:0] 2 - YCbYCr, 19 - LSC_OUT_MODE
#elif SENSOR_DATAFOMAT == CSI_DATA_FORMAT_RAW
    0x20,0xff,//0x00,
    0x21,0xf8,//0x00,
    0x22,0x54,//0x00,
    0x24,0xb9, //LSC OUT
    //0xd2,0x00,
    //0x29,0x08,
    //0x28,0x00, //MCLK DIV
#endif
    0x25,0x0f, //[3] output data en, [2] output pclk en, [1] output hsync en, [0] output vsync en
    0x26,0x03, //[1] hsync high valid, [0] vysnc high valid
    0x2f,0x01, //[0] update gain mode: 1: update gains, 0: update exposure
    0x30,0xf7,//BLK
    0x31,0x50,
    0x32,0x00,
    0x39,0x04,
    0x3a,0x18,
    0x3b,0x20,
    0x3c,0x00,
    0x3d,0x00,
    0x3e,0x00,
    0x3f,0x00,
    0x50,0x10,//PREGAIN
    0x51,0x40,
    0x52,0x40,
    0x53,0x82,
    0x54,0x80,
    0x55,0x80,
    0x56,0x82,
    0x8b,0x40,//LSC
    0x8c,0x40,
    0x8d,0x40,
    0x8e,0x2e,
    0x8f,0x2e,
    0x90,0x2e,
    0x91,0x3c,
    0x92,0x50,
    0x5d,0x12,
    0x5e,0x1a,
    0x5f,0x24,
    0x60,0x07,//DNDD 篓篓拢陇??
    0x61,0x15,
    0x62,0x08,
    0x64,0x03,
    0x66,0xe8,
    0x67,0x86,
    0x68,0xa2,
    0x69,0x18,//ASDE
    0x6a,0x0f,
    0x6b,0x00,
    0x6c,0x5f,
    0x6d,0x8f,
    0x6e,0x55,
    0x6f,0x38,
    0x70,0x15,
    0x71,0x33,
    0x72,0xdc,//0xdc INTPEE edge
    0x73,0x80,//0x80
    0x74,0x02,
    0x75,0x3f,
    0x76,0x02,
    0x77,0x36,
    0x78,0x88,
    0x79,0x81,
    0x7a,0x81,
    0x7b,0x22,
    0x7c,0xff,
    0x93,0x48,//CC
    0x94,0x00,
    0x95,0x05,
    0x96,0xe8,
    0x97,0x40,
    0x98,0xf0,
    0xb1,0x38,//YCP
    0xb2,0x38,
    0xbd,0x38,
    0xbe,0x36,
    0xd0,0xc9,//AEC
    0xd1,0x10,
    0xd3,0x80,//0x80
    0xd5,0xf2,
    0xd6,0x16,
    0xdb,0x92,
    0xdc,0xa5,
    0xdf,0x23,
    0xd9,0x00,
    0xda,0x00,
    0xe0,0x09,
    0xec,0x20,
    0xed,0x04,
    0xee,0xa0,
    0xef,0x40,
    0x80,0x03,
    0x80,0x03,
    0x9F,0x10,
    0xA0,0x20,
    0xA1,0x38,
    0xA2,0x4E,
    0xA3,0x63,
    0xA4,0x76,
    0xA5,0x87,
    0xA6,0xA2,
    0xA7,0xB8,
    0xA8,0xCA,
    0xA9,0xD8,
    0xAA,0xE3,
    0xAB,0xEB,
    0xAC,0xF0,
    0xAD,0xF8,
    0xAE,0xFD,
    0xAF,0xFF,
    0xc0,0x00,
    0xc1,0x10,
    0xc2,0x1C,
    0xc3,0x30,
    0xc4,0x43,
    0xc5,0x54,
    0xc6,0x65,
    0xc7,0x75,
    0xc8,0x93,
    0xc9,0xB0,
    0xca,0xCB,
    0xcb,0xE6,
    0xcc,0xFF,
    0xf0,0x02,//ABS
    0xf1,0x01,
    0xf2,0x01,
    0xf3,0x30,
    0xf9,0x9f,//measure window
    0xfa,0x78,
    0xfe,0x01,//select page1---------------------------------------------------------------
    0x00,0xf5,
    0x02,0x1a,
    0x0a,0xa0,
    0x0b,0x60,
    0x0c,0x08,
    0x0e,0x4c,
    0x0f,0x39,
    0x11,0x3f,
    0x12,0x72,
    0x13,0x13,
    0x14,0x42,
    0x15,0x43,
    0x16,0xc2,
    0x17,0xa8,
    0x18,0x18,
    0x19,0x40,
    0x1a,0xd0,
    0x1b,0xf5,
    0x70,0x40,
    0x71,0x58,
    0x72,0x30,
    0x73,0x48,
    0x74,0x20,
    0x75,0x60,
    0x77,0x20,
    0x78,0x32,
    0x30,0x03,
    0x31,0x40,
    0x32,0xe0,
    0x33,0xe0,
    0x34,0xe0,
    0x35,0xb0,
    0x36,0xc0,
    0x37,0xc0,
    0x38,0x04,
    0x39,0x09,
    0x3a,0x12,
    0x3b,0x1C,
    0x3c,0x28,
    0x3d,0x31,
    0x3e,0x44,
    0x3f,0x57,
    0x40,0x6C,
    0x41,0x81,
    0x42,0x94,
    0x43,0xA7,
    0x44,0xB8,
    0x45,0xD6,
    0x46,0xEE,
    0x47,0x0d,
    0xfe,0x00,
#if  SENSOR_DATAFOMAT == CSI_DATA_FORMAT_YUV422
    0xd2,0x90,	 //[7] AEC EN, [4] skin weight mode
#elif SENSOR_DATAFOMAT == CSI_DATA_FORMAT_RAW
    0xd2,0x10,
#endif
    0xfe,0x00,
    0x10,0x26,
    0x11,0x0d,
    0x1a,0x2a,
    0x1c,0x49,
    0x1d,0x9a,
    0x1e,0x61,
    0x3a,0x20,
    0x50,0x14,
    0x53,0x80,
    0x56,0x80,
    0x8b,0x20,
    0x8c,0x20,
    0x8d,0x20,
    0x8e,0x14,
    0x8f,0x10,
    0x90,0x14,
    0x94,0x02,
    0x95,0x07,
    0x96,0xe0,
    0xb1,0x40,
    0xb2,0x40,
    0xb3,0x40,
    0xb6,0xe0,
    0xd0,0xcb,
    0xd3,0x48,
    0xf2,0x02,
    0xf7,0x12,
    0xf8,0x0a,
    0xfe,0x01,
    0x02,0x20,
    0x04,0x10,
    0x05,0x08,
    0x06,0x20,
    0x08,0x0a,
    0x0e,0x44,
    0x0f,0x32,
    0x10,0x41,
    0x11,0x37,
    0x12,0x22,
    0x13,0x19,
    0x14,0x44,
    0x15,0x44,
    0x19,0x50,
    0x1a,0xd8,
    0x32,0x10,
    0x35,0x00,
    0x36,0x80,
    0x37,0x00,
    0xfe,0x00,//select page 0


#if  SENSOR_DATAFOMAT == CSI_DATA_FORMAT_YUV422
    0x28,0x00, //MCLK DIV

    #define VS_ST_BASE  (0)	   //1045+ VS_ST_BASE*1716
    #define VS_ET_BASE  (0)   //31+ VS_ET_BASE *1716
    0x0d, VS_ST_BASE&0xff,	 //vs_st[7:0]: num of row time , frame start to first HSYNC valid
    0x0e, VS_ET_BASE&0xff,	 //vs_et[7:0]: num of row time , last HSYNC valid to frame end...   VB>(vs_st+vs_et)
    //0x0f,0x00,	 //[7:4] VB[11:8], [3:0] HB[11:8]
	#define HB_CAL	((H_LEN - SENSOR_WIDTH*2-108)/2)
	#define VB_CAL	(V_LEN - SENSOR_HEIGHT - 8)	

    0x01, HB_CAL&0xff,//hbank L
    0x02, VB_CAL&0xff,//0x10,//vbank L
    0x0f, (((VB_CAL>>8)&0x0f)<<4)|((HB_CAL>>8)&0x0f), //vbank,hbank H
#elif SENSOR_DATAFOMAT == CSI_DATA_FORMAT_RAW
    0x28,0x00, //MCLK DIV

    #define VS_ST_BASE  (0)	   //1045+ VS_ST_BASE*1716
    #define VS_ET_BASE  (0)   //31+ VS_ET_BASE *1716
    0x0d, VS_ST_BASE&0xff,	 //vs_st[7:0]: num of row time , frame start to first HSYNC valid
    0x0e, VS_ET_BASE&0xff,	 //vs_et[7:0]: num of row time , last HSYNC valid to frame end...   VB>(vs_st+vs_et)
    //0x0f,0x00,	 //[7:4] VB[11:8], [3:0] HB[11:8]
	#define HB_CAL	(H_LEN - SENSOR_WIDTH  - 46)
	#define VB_CAL	(V_LEN - SENSOR_HEIGHT)	
    0x01, HB_CAL&0xff,//hbank L
    0x02, VB_CAL&0xff,//0x10,//vbank L
    0x0f, ((VB_CAL&0x0f)<<4)|((HB_CAL>>8)&0x0f), //vbank,hbank H
    //0x01, 0x80,//hbank L
    //0x02, 0x08,//0x10,//vbank L
    //0x0f, 0x00, //vbank,hbank H
#endif
    0xfe, 0x00,
    //0x2e,0x01,	   //[0] test image type1, [1] test image type2
	
	SENSOR_TAB_END
};
UNUSED static void GC0308_rotate(u32 r)
{
	unsigned char buf[2];
	buf[0] = 0x14;		//0',180'
	buf[1] = 0x10|(r<<0);
	//	sensor_iic_enable();
	//	sensor_iic_info();
		sensor_iic_write(buf);
	// sensor_iic_disable();

}

UNUSED static void GC0308_hvblank(u32 h,u32 v)
{
	u32 i;
	const u8 t[][2] = {
		{0x01,h & 0xff},//0x6a//0xde //HB[7:0]
		{0x02,v & 0xff},  //0x0c //VB[7:0]
		{0x0f,((v >> 8)<<4)|(h >> 8)} //{0x0f,0x00}, //VB/HB [11:8]
	};
//	sensor_iic_enable();
//	sensor_iic_info();	
	for(i=0;i<3;i++)
		sensor_iic_write((u8 *)&t[i][0]);
// sensor_iic_disable();

}

SENSOR_OP_SECTION const Sensor_Adpt_T  gc0308_adpt = 
{
	.typ 				= CSI_TYPE_YUV422| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= MCLK,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= MCLK,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = SENSOR_WIDTH, 			//sensor input width
	.senPixelh          = SENSOR_HEIGHT,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = SENSOR_WIDTH,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = SENSOR_HEIGHT,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= SENSOR_WIDTH,				//csi input width
	.pixelh				= SENSOR_HEIGHT,				//csi input height
	.hsyn 				= SENSOR_HSYNC,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= SENSOR_VSYNC,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= SENSOR_COLOR_ARRAY,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB

	.sensorCore			= SYS_VOL_V2_8,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V2_5,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56	

	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},

	.hvb_adapt = {
		.pclk			= 42000000,			//csi pclk input
		.v_len			= 1120,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 15,				//sensor fps set
#else
		.fps			= 15,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_DIS_  <<_BLC_POS_ | _ISP_DIS_  <<_LSC_POS_  | _ISP_DIS_<<_DDC_POS_   | _ISP_DIS_<<_AWB_POS_  \
					|_ISP_DIS_  <<_CCM_POS_ | _ISP_DIS_<<_AE_POS_   | _ISP_DIS_<<_DGAIN_POS_ | _ISP_DIS_<<_YGAMA_POS_ \
					| _ISP_DIS_<<_RGB_GAMA_POS_ | _ISP_DIS_<<_CH_POS_\
					|_ISP_DIS_<<_VDE_POS_ | _ISP_DIS_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    |_ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= 0,					//BLC red adjust //signed 10bit
		.blkl_gr	= 0,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= 0,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= 0,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 191,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 191,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 485, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xc0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			178,178,177,175,173,170,167,164,160,156,152,148,144,140,136,129,123,117,113,108,104,100, 94, 90, 87, 81, 78, 78, 76, 76, 76, 73, //bgain_out_high
			178,176,174,171,168,164,160,153,149,146,141,139,134,130,126,121,112,106,102, 96, 92, 88, 86, 83, 81, 77, 75, 74, 73, 72, 73, 74, //bgain_in_high
			178,169,161,150,146,143,138,136,131,125,119,112,113,111,102, 99, 91, 88, 85, 83, 82, 81, 78, 77, 74, 72, 71, 71, 71, 70, 71, 74, //bgain_in_low  
			175,154,143,138,136,130,126,119,116,113,108,106,106, 99, 92, 90, 85, 81, 79, 77, 75, 74, 72, 71, 70, 68, 67, 67, 66, 67, 69, 71 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000,  
			0x00,	0x00,	0x100   
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {40,50,60,70,75,80,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*4,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 80,
			.ae_win_x1			= 160,
			.ae_win_x2			= 480,
			.ae_win_x3			= 560,
			.ae_win_y0			= 60,
			.ae_win_y1			= 120,
			.ae_win_y2			= 360,
			.ae_win_y3			= 420,
			.weight_0_7			= 0x44111111,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x114f4114,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x11111444,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},		
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {5,5,5,5,5,5,5,5},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 14,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 14,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.sat		= {4,8,12,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 6,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {60,64,68,78,84,88,88,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 6,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {2,2,2,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {24,24,24,24,24,24,24,24}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,12,12,12,12,12,13,13,14,14,15,15,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {5,6,7,8,9,10,12,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 6,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (640/4)*1,	
		.win_h_end		= (640/4)*3,
		.win_v_start	= (480/4)*1,
		.win_v_end		= (480/4)*3,
	}, 

	.p_fun_adapt = {
		.fp_rotate		= NULL,//GC0308_rotate,
		.fp_hvblank		= NULL,//GC0308_hvblank,
		.fp_exp_gain_wr	= NULL
	},

};
SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T gc0308_init =
{
	.sensor_struct_addr   	= (u32 *)&gc0308_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)GC0308InitTable,     
	.sensor_init_tab_size 	= sizeof(GC0308InitTable),
	.lsc_tab_adr 			= (u32 *)NULL,     
	.lsc_tab_size 			= 0, 
	.sensor_name	  		= "GC0308_VGA",
	.w_cmd            		= 0x42,                   
	.r_cmd            		= 0x43,                   
	.addr_num         		= 0x01,                   
	.data_num         		= 0x01,   
	.id               		= 0x9b, 
	.id_reg           		= 0x00, 
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_RESET_CTRL_EN = 1 有效
	.reset_en				= 0, //目前不使用
	.reset_valid			= 0, //0:低电平reset， 1: 高电平reset
	//USER_HARDWARE_CFG_xxx_H 中 CMOS_SENSOR_PWDN_CTRL_EN = 1 有效
	.pwdn_en				= 0, //根据需要来配置
	.pwdn_valid				= 1, //pwdn_en = 1时有效，0:低电平 power down， 1: 高电平power down； pwdn_en = 1时 默认高电平POWER DOWN
};


#endif

