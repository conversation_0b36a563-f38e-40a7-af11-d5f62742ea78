/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ST7789V

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)



LCD_INIT_TAB_BEGIN()
#if (LCD_7789_CUSTOMER_SELECT ==LCD7789_QX)//庆显
    CMD(0x11), //Sleep Out
    DLY(120),  //Delay 120ms

    //------------------------------display and color format setting--------------------------------//
    CMD(0x36), //Memory Data Access Control
    DAT(0x00),
    CMD(0x3A), //Interface Pixel Format
    DAT(0x05),
    CMD(0x21), //Display Inversion On
    //--------------------------------ST7789V Frame rate setting----------------------------------//
    CMD(0xB2), // Porch Setting
    DAT(0x0C),//DAT(0x05),// DAT(0x0C),
    DAT(0x0C),//DAT(0x05),// DAT(0x0C),
    DAT(0x00),// DAT(0x00),
    DAT(0x33),// DAT(0x33),
    DAT(0x33),// DAT(0x33),
    CMD(0xB7),// Gate Control
    DAT(0x35),// DAT(0x35),

    //---------------------------------ST7789V Power setting--------------------------------------//
    CMD(0xBB), //VCOM Setting
    DAT(0x34), //VCOM //1.4V //DAT(0x1C),0.8V
    CMD(0xC0), //LCM Control
    DAT(0x2C),
    CMD(0xC2), //VDV and VRH Command Enable
    DAT(0x01),


    CMD(0xC3), //VRH Set
    DAT(0x13), //VAP(GVDD) 4.5V DAT(0x0B), 4.1V
    CMD(0xC4), //VDV Set.
    DAT(0x20),
    CMD(0xC6), //Frame Rate Control in Normal Mode
    DAT(0x10),//DAT(0x0F),//60FPS //DAT(0x10), 58FPS
    CMD(0xD0), //Power Control 1
    DAT(0xA4),
    DAT(0xA1),
    //CMD(0xD6), //??
    //DAT(0xA1),
    //--------------------------------ST7789V gamma setting---------------------------------------//
CMD(0xE0),
DAT(0xF0),
DAT(0x06),
DAT(0x0E),
DAT(0x08),
DAT(0x09),
DAT(0x17),
DAT(0x37),
DAT(0x54),
DAT(0x4C),
DAT(0x39),
DAT(0x14),
DAT(0x12),
DAT(0x2F),
DAT(0x34),

CMD(0xE1),
DAT(0xF0),
DAT(0x0F),
DAT(0x14),
DAT(0x0C),
DAT(0x0A),
DAT(0x04),
DAT(0x37),
DAT(0x43),
DAT(0x4C),
DAT(0x36),
DAT(0x12),
DAT(0x14),
DAT(0x2D),
DAT(0x36),


    CMD(0x35), //Tearing Effect Line On
    DAT(0x00),
    //CMD(0x44),  //Set Tear Scanline
    //DAT(0x01),
    //DAT(0x00),
    //CMD(0x2B),   //Row Address Set
    //DAT(0x00),
    //DAT(0x00),
    //DAT(0x01),
    //DAT(0x3F),
	
    //CMD(0x2A),   //Column Address Set
    //DAT(0x00),
    //DAT(0x00),
    //DAT(0x00),
    //DAT(0xEF),

    CMD(0x29), //Display on

    CMD(0x2C),
#elif (LCD_7789_CUSTOMER_SELECT ==LCD7789_CH||LCD_7789_CUSTOMER_SELECT == LCD7789_RX)//畅航
 CMD(0x11),     
 
 DLY(120),                
 
 CMD(0xB2),     
 DAT(0x0C),   
 DAT(0x0C),   
 DAT(0x00),   
 DAT(0x33),   
 DAT(0x33),  
 
 CMD(0x35),     
 DAT(0x00),  
 
 CMD(0x36),     
 DAT(0x00),   
 
 CMD(0x3A),     
 DAT(0x05),     
 
 CMD(0xB7),     
 DAT(0x35),   
 
 CMD(0xBB),     
 DAT(0x34),   
 
 CMD(0xC0),     
 DAT(0x2C),   
 
 CMD(0xC2),     
 DAT(0x01),   
 
 CMD(0xC3),     
 DAT(0x13),//4.5V   
 
 CMD(0xC4),     
 DAT(0x20),    
 
 CMD(0xC6),     
 DAT(0x0F),

    CMD(0xD0),     
    DAT(0xA7),   
    DAT(0xA1),   
    DLY(3),  
 
 CMD(0xD0),     
 DAT(0xA4),   
 DAT(0xA1),   
 
 CMD(0xD6),     
 DAT(0xA0), 
 
 CMD(0xE0),
 DAT(0xD0),
 DAT(0x0A),
 DAT(0x10),
 DAT(0x0C),
 DAT(0x0C),
 DAT(0x18),
 DAT(0x35),
 DAT(0x43),
 DAT(0x4D),
 DAT(0x39),
 DAT(0x13),
 DAT(0x13),
 DAT(0x2D),
 DAT(0x34),
 
 CMD(0xE1),
 DAT(0xD0),
 DAT(0x05),
 DAT(0x0B),
 DAT(0x06),
 DAT(0x05),
 DAT(0x02),
 DAT(0x35),
 DAT(0x43),
 DAT(0x4D),
 DAT(0x16),
 DAT(0x15),
 DAT(0x15),
 DAT(0x2E),
 DAT(0x32),
 
 CMD(0x21),  
 
 CMD(0x29),     
 
 CMD(0x2C), 
 #elif (LCD_7789_CUSTOMER_SELECT ==LCD7789_YX)//钰显
 CMD(0x11),     

DLY(120),                

CMD(0xB2),     
DAT(0x0C),   
DAT(0x0C),   
DAT(0x00),   
DAT(0x33),   
DAT(0x33),  

CMD(0x35),     
DAT(0x00),  

CMD(0x36),     
DAT(0x00),   

CMD(0x3A),     
DAT(0x05),     

CMD(0xB7),     
DAT(0x35),   

CMD(0xBB),     
DAT(0x34),   

CMD(0xC0),     
DAT(0x2C),   

CMD(0xC2),     
DAT(0x01),   

CMD(0xC3),     
DAT(0x13),//4.5V   

CMD(0xC4),     
DAT(0x20),    

CMD(0xC6),     
DAT(0x0F),   

CMD(0xD0),     
DAT(0xA4),   
DAT(0xA1),   

CMD(0xD6),     
DAT(0xA1), 

CMD(0xE0),
DAT(0xD0),
DAT(0x0A),
DAT(0x10),
DAT(0x0C),
DAT(0x0C),
DAT(0x18),
DAT(0x35),
DAT(0x43),
DAT(0x4D),
DAT(0x39),
DAT(0x13),
DAT(0x13),
DAT(0x2D),
DAT(0x34),

CMD(0xE1),
DAT(0xD0),
DAT(0x05),
DAT(0x0B),
DAT(0x06),
DAT(0x05),
DAT(0x02),
DAT(0x35),
DAT(0x43),
DAT(0x4D),
DAT(0x16),
DAT(0x15),
DAT(0x15),
DAT(0x2E),
DAT(0x32),

CMD(0x21),  

CMD(0x29),     

CMD(0x2C), 
 #else//JPY
CMD(0x11),     

DLY(120),                

CMD(0xB2),     
DAT(0x0C),   
DAT(0x0C),   
DAT(0x00),   
DAT(0x33),   
DAT(0x33),  

CMD(0x35),     
DAT(0x00),  

CMD(0x36),     
DAT(0x00),   

CMD(0x3A),     
DAT(0x05),     

CMD(0xB7),     
DAT(0x06),   

CMD(0xBB),     
DAT(0x20),   

CMD(0xC0),     
DAT(0x2C),   

CMD(0xC2),     
DAT(0x01),   

CMD(0xC3),     
DAT(0x0F),     

CMD(0xC6),     
DAT(0x0F),   

CMD(0xD0),     
DAT(0xA7),  

CMD(0xD0),     
DAT(0xA4),   
DAT(0xA1),   

CMD(0xD6),     
DAT(0xA1), 

CMD(0xE0),
DAT(0xF0),
DAT(0x06),
DAT(0x0E),
DAT(0x08),
DAT(0x09),
DAT(0x17),
DAT(0x37),
DAT(0x54),
DAT(0x4C),
DAT(0x39),
DAT(0x14),
DAT(0x12),
DAT(0x2F),
DAT(0x34),

CMD(0xE1),
DAT(0xF0),
DAT(0x0F),
DAT(0x14),
DAT(0x0C),
DAT(0x0A),
DAT(0x04),
DAT(0x37),
DAT(0x43),
DAT(0x4C),
DAT(0x36),
DAT(0x12),
DAT(0x14),
DAT(0x2D),
DAT(0x36),

CMD(0x21),  

CMD(0x29),     

CMD(0x2C), 

#endif

LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()
    .name 			= "MCU_st7789v",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    #if 0//JPY
    .contrast   	= LCD_CONTRAST_100,

    .brightness 	= 0,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 4,

    .gamma_index 	= {4, 4, 4},//JPY
	

    .asawtooth_index = {5, 5},
    #elif (LCD_7789_CUSTOMER_SELECT ==LCD7789_CH)||(LCD_7789_CUSTOMER_SELECT == LCD7789_RX)//CH
    .contrast   	= LCD_CONTRAST_100,

    .brightness 	= 255,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},
	

    .asawtooth_index = {5, 5},	    

#elif (LCD_7789_CUSTOMER_SELECT ==LCD7789_QX)//QX

    .contrast   	= LCD_SATURATION_100,

    .brightness 	= -10,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 4,

    .gamma_index 	= {4, 5, 4},
	

    .asawtooth_index = {5, 5},
#elif (LCD_7789_CUSTOMER_SELECT ==LCD7789_YX)//钰显
    .contrast   	= LCD_CONTRAST_100,

    .brightness 	= 0,

    .saturation 	= LCD_SATURATION_100,

    .contra_index 	= 4,

    .gamma_index 	= {5, 5, 5},
	

    .asawtooth_index = {5, 5},

#endif
    .lcd_ccm         = LCD_CCM_DEFAULT,




    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()



#endif


