/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  APP_API_H
    #define  APP_API_H
//hal layer
#include "../../../hal/inc/hal.h"

#include "app_typedef.h"

#include "../../user_config/inc/user_config_api.h"

#include "../../resource/user_res.h"
#include "../../task_windows/task_api.h"





extern System_Ctrl_T SysCtrl;

/*******************************************************************************
* Function Name  : app_lcdVideoShowScaler_cfg
* Description    : set lcd show crop scaler(from csi img)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdVideoShowScaler_cfg(int step);
/*******************************************************************************
* Function Name  : app_lcdPlayShowScaler_cfg
* Description    : set lcd play scaler(from playback)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
				   u8 type: PLAY_SCALER_STAT_KICK, updata without hal_lcdVideoSetFrame
                            PLAY_SCALER_STAT_KICKBUF, updata after hal_lcdVideoSetFrame
				   u8 use_ratio: 1: scaler ref by ratio
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdPlayShowScaler_cfg(int step, u8 type, u8 use_ratio);
/*******************************************************************************
* Function Name  : app_lcdVideoShowRatio_cfg
* Description    : user cfg display ratio ,only effect to video show
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : none
* Return         :0
*******************************************************************************/
void app_lcdVideoShowRatio_cfg(u16 ratio);
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStart
* Description    : lcd video show start for showing csi, will reset scaler
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdCsiVideoShowStart(void);
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop 
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdCsiVideoShowStop(void);

/*******************************************************************************
* Function Name  : app_lcdVideoIdleFrameGet
* Description    : lcd video show idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdVideoIdleFrameGet(void);

/*******************************************************************************
* Function Name  : app_lcdUiShowInit
* Description    : lcd ui show init
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdUiShowInit(void);
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop 
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdUiShowUinit(void);

/*******************************************************************************
* Function Name  : app_lcdUiDrawIdleFrameGet
* Description    : lcd ui draw idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdUiDrawIdleFrameGet(void);
/*******************************************************************************
* Function Name  : app_lcdShowWinModeCfg
* Description    : config lcdshow WINAB mode
* Input          : INT8U mode : mode index
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdShowWinModeCfg(INT8U mode);


/*******************************************************************************
* Function Name  : app_logo_show
* Description    : app_logo_show: show image & start music
* Input          : INT8U music_en : music play or not
				   INT8U wait : wait music end or not
				   INT8U stat : power on or power off.1->power on,other->power off
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int app_logo_show(INT8U music_en,INT8U wait,INT8U stat);
/*******************************************************************************
* Function Name  : app_version_get
* Description    : get system version
* Input          : none
* Output         : none                                            
* Return         : char *
*******************************************************************************/
void app_version_get(void);
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void app_draw_init(void);
/*******************************************************************************
* Function Name  : app_init
* Description    : app_init
* Input          : none
* Output         : none                                            
* Return         :0
*******************************************************************************/
int app_init(void);
/*******************************************************************************
* Function Name  : app_uninit
* Description    : app_uninit
* Input          : none
* Output         : none                                            
* Return         :0
*******************************************************************************/
int app_uninit(void);
/*******************************************************************************
* Function Name  :  app_systemService
* Description    : system event service for event and key get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void app_systemService(void);
/*******************************************************************************
* Function Name  :  app_sendDrawUIMsg
* Description    : system event service for event and key get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void app_sendDrawUIMsg(void);
/*******************************************************************************
* Function Name  : uidraw_Service
* Description    : uidraw_Service
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void app_draw_Service(u8 force);

/*******************************************************************************
* Function Name  : app_Cmos_Sensor_Switch
* Description    : app_Cmos_Sensor_Switch: video record should stop
* Input          : 
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_Cmos_Sensor_Switch(void);
/*******************************************************************************
* Function Name  : app_lcdVideoShowRotate_cfg
* Description    : user cfg display video rotate
* Input          : rotate_180: 1: add 180,  0: not add 180
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdVideoShowRotate180(void);
/*******************************************************************************
* Function Name  : app_sensor_filter_ctrl
* Description    : app_sensor_filter_ctrl
* Input          : u32 stat: SENSOR_FILTER_CHANGE_NONE, SENSOR_FILTER_CHANGE_NEXT, SENSOR_FILTER_CHANGE_PREV
* Output         : None
* Return         : None
*******************************************************************************/
void app_sensor_filter_ctrl(u32 stat);
#endif

