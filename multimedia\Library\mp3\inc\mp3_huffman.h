/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_HUFFMAN_H
       #define  MP3_HUFFMAN_H



typedef union MP3_HUFFQUAD_S {
	struct {
		unsigned short final  :  1;
		unsigned short bits   :  3;
		unsigned short offset : 12;
	} ptr;
	struct {
		unsigned short final  :  1;
		unsigned short hlen   :  3;
		unsigned short v      :  1;
		unsigned short w      :  1;
		unsigned short x      :  1;
		unsigned short y      :  1;
	} value;
	unsigned short final    :  1;
}MP3_HUFFQUAD_T;

typedef union MP3_HUFFPAIR_S {
	struct {
		unsigned short final  :  1;
		unsigned short bits   :  3;
		unsigned short offset : 12;
	} ptr;
	struct {
		unsigned short final  :  1;
		unsigned short hlen   :  3;
		unsigned short x      :  4;
		unsigned short y      :  4;
	} value;
	unsigned short final    :  1;
}MP3_HUFFPAIR_T;

typedef struct MP3_HUFFTABLE_S {
	const MP3_HUFFPAIR_T *table;
	unsigned short linbits;
	unsigned short startbits;
}MP3_HUFFTABLE_T;

extern MP3_HUFFQUAD_T const *const mp3_huff_quad_table[2];

extern MP3_HUFFTABLE_T const mp3_huff_pair_table[32];
#endif
