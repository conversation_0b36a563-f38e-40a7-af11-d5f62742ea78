/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : uiWinCircularProgress.c
* Author             :
* Version            : v1
* Date               : 12/01/2024
* Description        : Circular Progress Bar Component Implementation
***************************************************************************/
#include "../../../hal/inc/hal.h"
#include "../inc/uiWinCircularProgress.h"

/*******************************************************************************
* Function Name  : uiCircularProgressDrawArc
* Description    : 绘制圆弧
* Input          : uiRect* rect, u16 centerX, u16 centerY, u16 radius,
*                  float startAngle, float endAngle, u8 thickness, uiColor color
* Output         : none
* Return         : none
*******************************************************************************/
static void uiCircularProgressDrawArc(uiRect* rect, u16 centerX, u16 centerY, u16 radius,
                                     float startAngle, float endAngle, u8 thickness, uiColor color)
{
	float angle;
	float angleStep = 1.0f; // 1度步进
	u16 x, y;
	u8 t;

	// 绘制圆弧，通过多个同心圆实现厚度
	for(t = 0; t < thickness; t++)
	{
		u16 currentRadius = radius - t;
		if(currentRadius == 0) break;

		for(angle = startAngle; angle <= endAngle; angle += angleStep)
		{
			// 使用系统提供的三角函数计算（返回值*100）
			s16 cosVal = (s16)(currentRadius * hx330x_cos((s32)angle) / 100);
			s16 sinVal = (s16)(currentRadius * hx330x_sin((s32)angle) / 100);
			x = centerX + cosVal;
			y = centerY + sinVal;

			// 边界检查
			if(x >= rect->x0 && x <= rect->x1 && y >= rect->y0 && y <= rect->y1)
			{
				uiWinDrawPoint(rect, x, y, 1, color);
			}
		}
	}
}

/*******************************************************************************
* Function Name  : uiCircularProgressDrawText
* Description    : 绘制中心文字
* Input          : uiRect* rect, u32 rate, charFont font, uiColor color
* Output         : none
* Return         : none
*******************************************************************************/
static void uiCircularProgressDrawText(uiRect* rect, u32 rate, charFont font, uiColor color)
{
	char textBuffer[8];
	u16 centerX = (rect->x0 + rect->x1) / 2;
	u16 centerY = (rect->y0 + rect->y1) / 2;
	STRING_DRAW_T stringDraw;

	// 格式化百分比文字
	hx330x_num2str(textBuffer, rate, 0);
	hx330x_str_cat(textBuffer, "%");

	// 设置字符串绘制参数
	stringDraw.id = RAM_ID_MAKE(textBuffer);
	stringDraw.font = font;
	stringDraw.strAlign = ALIGNMENT_CENTER;
	stringDraw.fontColor = color;
	stringDraw.bgColor = INVALID_COLOR;
	stringDraw.rimColor = INVALID_COLOR;
	stringDraw.visible = 1;

	// 计算文字位置（居中）
	uiRect textRect;
	textRect.x0 = centerX - 20;
	textRect.x1 = centerX + 20;
	textRect.y0 = centerY - 8;
	textRect.y1 = centerY + 8;

	uiWinDrawString(&textRect, &textRect, &stringDraw);
}

/*******************************************************************************
* Function Name  : uiCircularProgressProc
* Description    : 圆形进度条消息处理函数
* Input          : uiWinMsg* msg
* Output         : none
* Return         : none
*******************************************************************************/
static void uiCircularProgressProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiCircularProgressObj* pCircularProgress;
	uiWinObj* pWin;
	u16 centerX, centerY, radius;
	float progressAngle;

	if(uiWidgetProc(msg))
		return;

	hWin = msg->curWin;
	pCircularProgress = (uiCircularProgressObj*)uiHandleToPtr(hWin);
	pWin = &(pCircularProgress->widget.win);

	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;

		case MSG_WIN_PAINT:
			// 计算圆心和半径
			centerX = (pWin->rect.x0 + pWin->rect.x1) / 2;
			centerY = (pWin->rect.y0 + pWin->rect.y1) / 2;
			radius = hx330x_min((pWin->rect.x1 - pWin->rect.x0),
			                   (pWin->rect.y1 - pWin->rect.y0)) / 2 - pCircularProgress->thickness;

			// 清除背景
			uiWinDrawRect(&(pWin->rect), pWin->bgColor);

			// 绘制背景圆环
			uiCircularProgressDrawArc(&(pWin->rect), centerX, centerY, radius,
			                         0, 360, pCircularProgress->thickness, pCircularProgress->bgColor);

			// 计算进度角度
			progressAngle = (float)pCircularProgress->rate * 360.0f / 100.0f;

			// 绘制进度弧线
			if(pCircularProgress->rate > 0)
			{
				float startAngle = pCircularProgress->startAngle;
				float endAngle;

				if(pCircularProgress->direction == CIRCULAR_PROGRESS_CLOCKWISE)
					endAngle = startAngle + progressAngle;
				else
					endAngle = startAngle - progressAngle;

				uiCircularProgressDrawArc(&(pWin->rect), centerX, centerY, radius,
				                         startAngle, endAngle, pCircularProgress->thickness,
				                         pCircularProgress->fgColor);
			}

			// 绘制中心文字
			if(pCircularProgress->showText)
			{
				uiCircularProgressDrawText(&(pWin->rect), pCircularProgress->rate,
				                          pCircularProgress->textFont, pCircularProgress->textColor);
			}
			return;

		case MSG_WIN_PROGRESS_RATE:
			{
				u32 newRate = msg->para.v;
				if(newRate > 100)
					newRate = 100;
				if(pCircularProgress->rate == newRate)
					return;
				pCircularProgress->rate = newRate;
				uiWinUpdateInvalid(hWin);
			}
			return;

		case MSG_WIN_CHANGE_FG_COLOR:
			pCircularProgress->fgColor = msg->para.v;
			uiWinUpdateInvalid(hWin);
			return;

		case MSG_WIN_TOUCH:
			break;

		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin = pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle = hWin;
			((touchInfor *)(msg->para.p))->touchID = pCircularProgress->widget.id;
			((touchInfor *)(msg->para.p))->touchItem = 0;
			return;

		default:
			break;
	}
	uiWinDefaultProc(msg);
}

/*******************************************************************************
* Function Name  : uiCircularProgressCreateDirect
* Description    : 直接创建圆形进度条
* Input          : s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id
* Output         : none
* Return         : winHandle
*******************************************************************************/
winHandle uiCircularProgressCreateDirect(s16 x0, s16 y0, u16 width, u16 height,
                                         winHandle parent, u16 style, u16 id)
{
	winHandle hCircularProgress;
	uiCircularProgressObj *pCircularProgress;

	hCircularProgress = uiWinCreate(x0, y0, width, height, parent, uiCircularProgressProc,
	                               sizeof(uiCircularProgressObj), WIN_WIDGET|style);
	uiWidgetSetId(hCircularProgress, id);

	if(hCircularProgress != INVALID_HANDLE)
	{
		pCircularProgress = (uiCircularProgressObj*)uiHandleToPtr(hCircularProgress);
		pCircularProgress->rate = 0;
		pCircularProgress->fgColor = R_ID_PALETTE_Blue;
		pCircularProgress->bgColor = R_ID_PALETTE_DarkGray;
		pCircularProgress->thickness = 8;
		pCircularProgress->startAngle = 270; // 从顶部开始
		pCircularProgress->direction = CIRCULAR_PROGRESS_CLOCKWISE;
		pCircularProgress->showText = 1;
		pCircularProgress->textFont = DEFAULT_FONT;
		pCircularProgress->textColor = R_ID_PALETTE_White;
	}
	return hCircularProgress;
}

/*******************************************************************************
* Function Name  : uiCircularProgressCreate
* Description    : 通过配置结构创建圆形进度条
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none
* Return         : winHandle
*******************************************************************************/
winHandle uiCircularProgressCreate(widgetCreateInfor* infor, winHandle parent, uiWinCB cb)
{
	winHandle hCircularProgress;
	uiCircularProgressObj* pCircularProgress;

	hCircularProgress = uiWinCreate(infor->x0, infor->y0, infor->width, infor->height,
	                               parent, uiCircularProgressProc, sizeof(uiCircularProgressObj),
	                               WIN_WIDGET|infor->style);

	if(hCircularProgress != INVALID_HANDLE)
	{
		pCircularProgress = (uiCircularProgressObj*)uiHandleToPtr(hCircularProgress);
		pCircularProgress->rate = 0;
		pCircularProgress->fgColor = infor->fontColor;
		pCircularProgress->bgColor = infor->bgColor;
		pCircularProgress->thickness = 8;
		pCircularProgress->startAngle = 270;
		pCircularProgress->direction = CIRCULAR_PROGRESS_CLOCKWISE;
		pCircularProgress->showText = 1;
		pCircularProgress->textFont = infor->font;
		pCircularProgress->textColor = infor->fontColorS;

		uiWidgetSetId(hCircularProgress, infor->id);
		uiWinSetbgColor(hCircularProgress, infor->bgColor);
	}
	return hCircularProgress;
}

/*******************************************************************************
* Function Name  : uiCircularProgressSetThickness
* Description    : 设置圆环厚度
* Input          : winHandle hWin, u8 thickness
* Output         : none
* Return         : none
*******************************************************************************/
void uiCircularProgressSetThickness(winHandle hWin, u8 thickness)
{
	uiCircularProgressObj* pCircularProgress;
	if(hWin == INVALID_HANDLE)
		return;

	pCircularProgress = (uiCircularProgressObj*)uiHandleToPtr(hWin);
	if(pCircularProgress->thickness != thickness)
	{
		pCircularProgress->thickness = thickness;
		uiWinUpdateInvalid(hWin);
	}
}

/*******************************************************************************
* Function Name  : uiCircularProgressSetDirection
* Description    : 设置绘制方向
* Input          : winHandle hWin, u8 direction
* Output         : none
* Return         : none
*******************************************************************************/
void uiCircularProgressSetDirection(winHandle hWin, u8 direction)
{
	uiCircularProgressObj* pCircularProgress;
	if(hWin == INVALID_HANDLE)
		return;

	pCircularProgress = (uiCircularProgressObj*)uiHandleToPtr(hWin);
	if(pCircularProgress->direction != direction)
	{
		pCircularProgress->direction = direction;
		uiWinUpdateInvalid(hWin);
	}
}
