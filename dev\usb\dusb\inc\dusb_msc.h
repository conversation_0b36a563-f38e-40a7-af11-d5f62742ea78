/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_MSC_H_
#define USB_MSC_H_

#define SCSI_CBW_SIG                    						0x43425355UL
#define SCSI_CSW_SIG                    						0x53425355UL

// UFI COMMAND DEFINE
#define UFI_TEST_UNIT_READY                                        	0x00
#define UFI_REWIND                                                 	0x01
#define UFI_REZERO_UNIT                                            	0x01
#define UFI_REQUEST_SENSE                                          	0x03
#define UFI_FORMAT                                                 	0x04
#define UFI_FORMAT_UNIT                                            	0x04
#define UFI_READ_BLOCK_LIMITS                                      	0x05
#define UFI_INITIALIZE_ELEMENT_STATUS                              	0x07
#define UFI_REASSIGN_BLOCKS                                        	0x07
#define UFI_GET_MESSAGE_06                                         	0x08
#define UFI_READ_06                                                	0x08
#define UFI_RECEIVE                                                	0x08
#define UFI_PRINT                                                  	0x0A
#define UFI_SEND_MESSAGE_06                                       	0x0A
#define UFI_SEND_06                                               	0x0A
#define UFI_WRITE_06                                              	0x0A
#define UFI_SEEK_06                                               	0x0B
#define UFI_SLEW_AND_PRINT                                         	0x0B
#define UFI_READ_REVERSE                                           	0x0F
#define UFI_SYNCHRONIZE_BUFFER                                     	0x10
#define UFI_WRITE_FILEMARKS                                        	0x10
#define UFI_SPACE                                                  	0x11
#define UFI_INQUIRY                                                	0x12
#define UFI_VERIFY_06                                             	0x13
#define UFI_RECOVER_BUFFERED_DATA                                  	0x14
#define UFI_MODE_SELECT_06                                        	0x15
#define UFI_RESERVE                                                	0x16
#define UFI_RESERVE_UNIT                                           	0x16
#define UFI_RELEASE                                                	0x17
#define UFI_RELEASE_UNIT                                           	0x17
#define UFI_COPY                                                   	0x18
#define UFI_ERASE                                                  	0x19
#define UFI_MODE_SENSE_06                                         	0x1A
#define UFI_LOAD_UNLOAD                                            	0x1B
#define UFI_SCAN                                                   	0x1B
#define UFI_STOP_PRINT                                             	0x1B
#define UFI_STOP_START_UNIT                                        	0x1B
//+=============================================================================+
//|           D - DIRECT ACCESS DEVICE                       Device Column Key  |
//|           .T - SEQUENTIAL ACCESS DEVICE                  M = Mandatory      |
//|           . L - PRINTER DEVICE                           O = Optional       |
//|           .  P - PROCESSOR DEVICE                        V = Vendor Specific|
//|           .  .W - WRITE ONCE READ MULTIPLE DEVICE        R = Reserved       |
//|           .  . R - READ ONLY (CD-ROM) DEVICE                                |
//|           .  .  S - SCANNER DEVICE                                          |
//|           .  .  .O - OPTICAL MEMORY DEVICE                                  |
//|           .  .  . M - MEDIA CHANGER DEVICE                                  |
//|           .  .  .  C - COMMUNICATION DEVICE                                 |
//|           .  .  .  .                                                        |
//|        OP DTLPWRSOMC Description                                            |
//|----------+----------+-------------------------------------------------------|
//|        1C OOOOOOOOOO RECEIVE DIAGNOSTIC RESULTS                             |
#define UFI_RECEIVE_DIAGNOSTIC_RESULTS                             	0x1C
#define UFI_SEND_DIAGNOSTIC                                        	0x1D
#define UFI_PREVENT_ALLOW_MEDIUM_REMOVAL                           	0x1E
#define UFI_READ_FORMAT_CAPACITY                                   	0x23
#define UFI_SET_WINDOW                                             	0x24
#define UFI_GET_WINDOW                                             	0x25
#define UFI_READ_CAPACITY                                          	0x25
#define UFI_READ_CD_ROM_CAPACITY                                   	0x25
#define UFI_SCSI_CAPACITY_SECTOR_COUNT_MSB  						2
#define UFI_SCSI_CAPACITY_SECTOR_COUNT_LSB  						3
#define UFI_SCSI_CAPACITY_SECTOR_SIZE_MSB  							6
#define UFI_SCSI_CAPACITY_SECTOR_SIZE_LSB  							7
#define UFI_GET_MESSAGE_10                                        	0x28
#define UFI_READ_10                                               	0x28
#define UFI_READ_GENERATION                                        	0x29
#define UFI_SEND_MESSAGE_10                                       	0x2A
#define UFI_SEND_10                                               	0x2A
#define UFI_WRITE_10                                              	0x2A
#define UFI_LOCATE                                                 	0x2B
#define UFI_POSITION_TO_ELEMENT                                    	0x2B
#define UFI_SEEK_10                                               	0x2B
#define UFI_ERASE_10                                              	0x2C
#define UFI_READ_UPDATED_BLOCK                                     	0x2D
#define UFI_WRITE_AND_VERIFY_10                                   	0x2E
#define UFI_VERIFY_10                                             	0x2F
#define UFI_SEARCH_DATA_HIGH_10                                   	0x30
#define UFI_OBJECT_POSITION                                        	0x31
#define UFI_SEARCH_DATA_EQUAL_10                                  	0x31
#define UFI_SEARCH_DATA_LOW_10                                    	0x32
#define UFI_SET_LIMITS_10                                         	0x33
#define UFI_GET_DATA_BUFFER_STATUS                                 	0x34
#define UFI_PRE_FETCH                                              	0x34
#define UFI_READ_POSITION                                          	0x34
#define UFI_SYNCHRONIZE_CACHE                                      	0x35
#define UFI_LOCK_UNLOCK_CACHE                                      	0x36
#define UFI_READ_DEFECT_DATA_10                                   	0x37
#define UFI_MEDIUM_SCAN                                           	0x38
#define UFI_COMPARE                                               	0x39
#define UFI_COPY_AND_VERIFY                                       	0x3A
#define UFI_WRITE_BUFFER                                          	0x3B
#define UFI_READ_BUFFER                                           	0x3C
#define UFI_UPDATE_BLOCK                                          	0x3D
#define UFI_READ_LONG                                             	0x3E
#define UFI_WRITE_LONG                                            	0x3F


#define UFI_CHANGE_DEFINITION                                      	0x40
#define UFI_WRITE_SAME                                             	0x41
#define UFI_READ_SUB_CHANNEL                                       	0x42
#define UFI_READ_TOC                                               	0x43
#define UFI_READ_HEADER                                            	0x44
#define UFI_PLAY_AUDIO_10                                         	0x45
#define UFI_PLAY_AUDIO_MSF                                         	0x47
#define UFI_PLAY_AUDIO_TRACK_INDEX                                 	0x48
#define UFI_PLAY_TRACK_RELATIVE_10                                	0x49
#define UFI_PAUSE_RESUME                                           	0x4B
#define UFI_LOG_SELECT                                             	0x4C
#define UFI_LOG_SENSE                                              	0x4D
#define UFI_MODE_SELECT_10                                        	0x55
#define UFI_MODE_SENSE_10                                         	0x5A

#define UFI_MOVE_MEDIUM                                            	0xA5
#define UFI_PLAY_AUDIO_12                                         	0xA5
#define UFI_EXCHANGE_MEDIUM                                        	0xA6
#define UFI_GET_MESSAGE_12                                        	0xA8
#define UFI_READ_12                                               	0xA8
#define UFI_PLAY_TRACK_RELATIVE_12                                	0xA9
#define UFI_SEND_MESSAGE_12                                       	0xAA
#define UFI_WRITE_12                                              	0xAA
#define UFI_ERASE_12                                              	0xAC
#define UFI_WRITE_AND_VERIFY_12                                   	0xAE
#define UFI_VERIFY_12                                             	0xAF
#define UFI_SEARCH_DATA_HIGH_12                                   	0xB0
#define UFI_SEARCH_DATA_EQUAL_12                                  	0xB1
#define UFI_SEARCH_DATA_LOW_12                                    	0xB2
#define UFI_SET_LIMITS_12                                         	0xB3
#define UFI_REQUEST_VOLUME_ELEMENT_ADDRESS                         	0xB5
#define UFI_SEND_VOLUME_TAG                                        	0xB6
#define UFI_READ_DEFECT_DATA_12                                   	0xB7
#define UFI_READ_ELEMENT_STATUS                                   	0xB8

#define UFI_USER_UFMODE												0xCC
#define UFI_USER_RETURNMASK											0xCD
#define UFI_USER_UPDATERTC											0xCE

/* Sense Key Definition */
#define SENSE_KEY_NO_SENSE										0x00
#define SENSE_KEY_RECOVERED_ERROR								0x01
#define SENSE_KEY_NOT_READY										0x02
#define SENSE_KEY_MEDIUM_ERROR									0x03
#define SENSE_KEY_HARDWARE_ERROR								0x04
#define SENSE_KEY_ILLEGAL_REQUEST								0x05
#define SENSE_KEY_UNIT_ATTENTION								0x06
#define SENSE_KEY_DATA_PROTECT									0x07
#define SENSE_KEY_BLANK_CHECK									0x08
#define SENSE_KEY_VENDOR_SPECIFIC								0x09
#define SENSE_KEY_ABORTED_COMMAND								0x0B
#define SENSE_KEY_VOLUME_OVERFLOW								0x0D
#define SENSE_KEY_MIS_COMPARE									0x0E


/* Additional Sense code definition*/
#define ASC_NO_ADDITIONAL_SENSE_INFORMATION						0x00
#define ASC_RECOVERED_DATA_WITH_RETRIES							0x17
#define ASC_RECOVERED_DATA_WITH_ECC								0x18
#define ASC_MEDIUM_PRESENT										0x3A
#define ASC_LOGICAL_DRIVE_NOT_READY_BEING_READY					0x04
#define ASC_LOGICAL_DRIVE_NOT_READY_FMT_IN_PRGS					0x04
#define ASC_NO_REFERENCE_POSITION_FOUND							0x06
#define ASC_NO_SEEK_COMPLETE									0x02
#define ASC_WRITE_FAULT											0x03
#define ASC_ID_CRC_ERROR										0x10
#define ASC_UNRECOVERED_READ_ERROR								0x11
#define ASC_ADDRESS_MARK_NOT_FOUND_FOR_ID_FIELD					0x12
#define ASC_RECORDED_ENTITY_NOT_FOUND							0x14
#define ASC_INCOMPATIBLE_MEDIUM_INSTALLED						0x30
#define ASC_CANNOT_READ_MEDIUM_INCOMPATIBLE_FMT					0x30
#define ASC_CANNOT_READ_MEDIUM_UNKNOWN_FORMAT					0x30
#define ASC_FORMAT_COMMAND_FAILED								0x31
#define ASC_INVALID_COMMAND_OPERATION_CODE						0x20
#define ASC_LOGICAL_BLOCK_ADDRESS_OUT_OF_RANGE					0x21
#define ASC_INVALID_FIELD_IN_COMMAND_PACKET						0x24
#define ASC_LOGICAL_UNIT_NOT_SUPPORTED							0x25
#define ASC_INVALID_FIELD_IN_PARAMETER_LIST						0x26
#define ASC_MEDIUM_REMOVAL_PREVENTED							0x53
#define ASC_NOT_READY_TO_READY_TRANSIT_MDI_CHNG					0x28
#define ASC_POWER_ON_OR_BUS_DEVICE_RESET						0x29
#define ASC_WRITE_PROTECTED_MEDIA								0x27
#define ASC_OVERLAPPED_COMMAND_ATTEMPTED						0x4E

/* Definition of additional sense code qualifier*/
/* Additional Sense code definition */
#define ASCQ_NO_ADDITIONAL_SENSE_INFORMATION					0x00
#define ASCQ_RECOVERED_DATA_WITH_RETRIES						0x01
#define ASCQ_RECOVERED_DATA_WITH_ECC							0x00
#define ASCQ_MEDIUM_PRESENT										0x00
#define ASCQ_LOGICAL_DRIVE_NOT_READY_BEING_READY				0x01
#define ASCQ_LOGICAL_DRIVE_NOT_READY_FMT_IN_PRGS				0x04
#define ASCQ_NO_REFERENCE_POSITION_FOUND						0x00
#define ASCQ_NO_SEEK_COMPLETE									0x00
#define ASCQ_WRITE_FAULT										0x00
#define ASCQ_ID_CRC_ERROR										0x00
#define ASCQ_UNRECOVERED_READ_ERROR								0x00
#define ASCQ_ADDRESS_MARK_NOT_FOUND_FOR_ID_FIELD				0x00
#define ASCQ_RECORDED_ENTITY_NOT_FOUND							0x00
#define ASCQ_INCOMPATIBLE_MEDIUM_INSTALLED						0x00
#define ASCQ_CANNOT_READ_MEDIUM_INCOMPATIBLE_FMT				0x02
#define ASCQ_CANNOT_READ_MEDIUM_UNKNOWN_FORMAT					0x01
#define ASCQ_FORMAT_COMMAND_FAILED								0x01
#define ASCQ_INVALID_COMMAND_OPERATION_CODE						0x00
#define ASCQ_LOGICAL_BLOCK_ADDRESS_OUT_OF_RANGE					0x00
#define ASCQ_INVALID_FIELD_IN_COMMAND_PACKET					0x00
#define ASCQ_LOGICAL_UNIT_NOT_SUPPORTED							0x00
#define ASCQ_INVALID_FIELD_IN_PARAMETER_LIST					0x00
#define ASCQ_MEDIUM_REMOVAL_PREVENTED							0x02
#define ASCQ_NOT_READY_TO_READY_TRANSIT_MDI_CHNG				0x00
#define ASCQ_POWER_ON_OR_BUS_DEVICE_RESET						0x00
#define ASCQ_WRITE_PROTECTED_MEDIA								0x00
#define ASCQ_OVERLAPPED_COMMAND_ATTEMPTED						0x00

#define		NO_SENSE					0
#define		INVALID_FIELD_IN_COMMAND	1
#define		NEW_MEDIUM_ARRIVEL			2
#define		WRITE_PROTECTED				3
//#define		PASSWORD_ERROR				4
//#define		USERNAME_ERROR				5
//#define		USER_PROHIBITTED			6
#define		MEDIUM_NOT_PRESENT			4  //7
#define		DATA_PHASE_ERROR			5  //8


/*******************************************************************************
* Function Name  : msc_epx_cfg
* Description    : msc_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void msc_epx_cfg(void);


/*******************************************************************************
* Function Name  : dusb_WriteToMem
* Description    : dusb_WriteToMem
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_WriteToMem(u8 *buf, u32 len);
/*******************************************************************************
* Function Name  : dusb_ReadFromMem
* Description    : dusb_ReadFromMem
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ReadFromMem(u8 *buf, u32 len);
/*******************************************************************************
* Function Name  : rbc_mem_read
* Description    : rbc_mem_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void rbc_mem_read(void);
/*******************************************************************************
* Function Name  : rbc_mem_write
* Description    : rbc_mem_write
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/        
void rbc_mem_write(void);
/*******************************************************************************
* Function Name  : rbc_mem_rxfunc
* Description    : rbc_mem_rxfunc
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/    
void rbc_mem_rxfunc(void);
/*******************************************************************************
* Function Name  : sdk_returnmask
* Description    : sdk_returnmask
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
void sdk_returnmask(void);
/*******************************************************************************
* Function Name  : cbw_updatartc
* Description    : cbw_updatartc
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
void cbw_updatartc(void);
/*******************************************************************************
* Function Name  : mscCmd_ufmod
* Description    : mscCmd_ufmod
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
void mscCmd_ufmod(void);
/*******************************************************************************
* Function Name  : mscCmd_ufmod
* Description    : mscCmd_ufmod
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool sent_csw(void);
/*******************************************************************************
* Function Name  : mscCmd_Read
* Description    : mscCmd_Read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool mscCmd_Read(void);
/*******************************************************************************
* Function Name  : mscCmd_Write
* Description    : mscCmd_Write
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool mscCmd_Write(void);
/*******************************************************************************
* Function Name  : scsi_cmd_analysis
* Description    : scsi_cmd_analysis
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool scsi_cmd_analysis(void);
/*******************************************************************************
* Function Name  : get_cbw
* Description    : get_cbw
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool get_cbw(void);
/*******************************************************************************
* Function Name  : scsi_cmd_analysis
* Description    : scsi_cmd_analysis
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool rbc_rec_pkg(void);
/*******************************************************************************
* Function Name  : rbc_process
* Description    : rbc_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool rbc_process(void);

#endif /* USB_DEVICE_MASS_H_ */
