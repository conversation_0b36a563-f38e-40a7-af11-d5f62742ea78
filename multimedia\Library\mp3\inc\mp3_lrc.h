/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_LRC_H
       #define  MP3_LRC_H
#define LRC_ITEM_NUM	256

typedef enum{
	MP3_LRC_NONE = 0,
	MP3_LRC_SHOW,
}MP3_LRC_STA;
typedef struct LrcLink_s{
	u16  pos_s;
	u16  len;//0: means not next link
	int  time;
	struct LrcLink_s *prev;
	struct LrcLink_s *next;
}LrcLink_t;
typedef struct LRC_ITEM_S{
	u16 pos_s;
	u16 len;
}LRC_ITEM_T;
typedef struct LRC_DEC_CTL_S{
	u8* lrc_buf;
	u32 lrc_size;
	LRC_ITEM_T al_item; //唱片集
	LRC_ITEM_T ti_item; //歌曲名
	LRC_ITEM_T ar_item; //歌手
	LRC_ITEM_T au_item; //作曲家
	LRC_ITEM_T by_item; //lrc文件创建者
	int time_offset;	//ms
	LrcLink_t *lrc_Link;
	LrcLink_t *lrc_Link_first;
	LrcLink_t lrc_pool[LRC_ITEM_NUM];
}LRC_DEC_CTL_T;
/*******************************************************************************
* Function Name  : mp3_lrc_decode
* Description    : mp3_lrc_decode: 
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_lrc_start(int fd);
/*******************************************************************************
* Function Name  : mp3_lrc_decode
* Description    : mp3_lrc_decode: 
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
void mp3_lrc_stop(void);
/*******************************************************************************
* Function Name  : mp3_lrc_al
* Description    : mp3_lrc_al: 唱片集
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_al(void);
/*******************************************************************************
* Function Name  : mp3_lrc_ti
* Description    : mp3_lrc_ti: //歌曲名
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_ti(void);
/*******************************************************************************
* Function Name  : mp3_lrc_ar
* Description    : mp3_lrc_ar: //歌手
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_ar(void);
/*******************************************************************************
* Function Name  : mp3_lrc_au
* Description    : mp3_lrc_au: //作曲家
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_au(void);
/*******************************************************************************
* Function Name  : mp3_lrc_by
* Description    : mp3_lrc_by: //lrc文件创建者
* Input          : u8 *buf
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_by(void);
/*******************************************************************************
* Function Name  : mp3_lrc_link_match
* Description    : mp3_lrc_link_match: use playtime to forward cur lrc_link
* Input          : int playtime
* Output         : none
* Return         : none
*******************************************************************************/
//MP3_TEXT_SECTION
int mp3_lrc_link_match(int playtime);
/*******************************************************************************
* Function Name  : mp3_lrc_string_get
* Description    : mp3_lrc_string_get: 
* Input          : int offset: 0: cur lrc_link, <0: prev lrc_link, >0: next lrc_link
* Output         : none
* Return         : NULL: not exist lrc string
*******************************************************************************/
//MP3_TEXT_SECTION
u8* mp3_lrc_string_get(int offset);
#endif
