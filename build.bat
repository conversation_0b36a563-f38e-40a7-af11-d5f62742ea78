@echo off
REM HX330x SDK Build Script for VSCode
echo HX330x SDK Build Script
echo ======================

REM Set paths
set TOOLCHAIN_PATH=E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\bin
set MAKE_PATH=C:\Program Files (x86)\GnuWin32\bin\make.exe
set PATH=%TOOLCHAIN_PATH%;%PATH%

REM Parse arguments
set BUILD_ACTION=build
if "%1"=="clean" set BUILD_ACTION=clean
if "%1"=="rebuild" set BUILD_ACTION=rebuild

echo Action: %BUILD_ACTION%
echo.

REM Check toolchain
if not exist "%TOOLCHAIN_PATH%\or1k-unknown-elf-gcc.exe" (
    echo [ERROR] Toolchain not found at: %TOOLCHAIN_PATH%
    exit /b 1
)
echo [OK] Toolchain found

REM Check project file
if not exist "app\hx330x_sdk.cbp" (
    echo [ERROR] Code::Blocks project not found: app\hx330x_sdk.cbp
    exit /b 1
)
echo [OK] Project file found

REM Execute build action
if "%BUILD_ACTION%"=="clean" goto :clean
if "%BUILD_ACTION%"=="rebuild" goto :rebuild
goto :build

:clean
echo Cleaning build files...
if exist "app\bin\Debug" rmdir /s /q "app\bin\Debug"
if exist "app\obj\Debug" rmdir /s /q "app\obj\Debug"
echo Clean completed.
goto :end

:rebuild
echo Rebuilding (clean + build)...
call :clean
goto :build

:build
echo Building project...

REM Try different build methods
REM Method 1: Use Code::Blocks command line
where codeblocks >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [Method 1] Using Code::Blocks command line...
    cd app
    codeblocks --build hx330x_sdk.cbp
    cd ..
    goto :check_result
)

REM Method 2: Use make if available
if exist "%MAKE_PATH%" (
    echo [Method 2] Using make...
    "%MAKE_PATH%" -C app -f ../build.mk
    goto :check_result
)

REM Method 3: Show manual instructions
echo [ERROR] No suitable build tool found!
echo.
echo Please install one of the following:
echo 1. Code::Blocks (add to PATH)
echo 2. Make tool
echo.
echo Or open app\hx330x_sdk.cbp in Code::Blocks IDE
exit /b 1

:check_result
if %ERRORLEVEL% EQU 0 (
    echo.
    echo [SUCCESS] Build completed!
    echo Output: app\bin\Debug\hx330x_sdk.elf
    if exist "app\bin\Debug\hx330x_sdk.elf" (
        echo [OK] Binary file created
    ) else (
        echo [WARNING] Binary file not found
    )
) else (
    echo.
    echo [ERROR] Build failed with error code: %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

:end
