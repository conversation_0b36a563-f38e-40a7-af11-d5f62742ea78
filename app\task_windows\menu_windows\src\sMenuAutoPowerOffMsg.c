/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuAutoPowerOffWin.c"

typedef struct{
	u32 strid;
	u32 value;
}AutoPowerOffCtl;

ALIGNED(4) const AutoPowerOffCtl autoPowerOffTab[] = {
	{R_ID_STR_COM_OFF, 1},
	{R_ID_STR_TIM_5MIN, 2},
	{R_ID_STR_TIM_10MIN, 3},
};

ALIGNED(4) u32 autoPowerOff_config;
ALIGNED(4) u32 autoPowerOff_enable = 0;  // 添加自动关机使能标志位

/*******************************************************************************
* Function Name  : getAutoPowerOffResInfor
* Description    : getAutoPowerOffResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getAutoPowerOffResInfor(u32 item,u32* image,u32* str)
{
	if(item < ARRAY_NUM(autoPowerOffTab))
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = autoPowerOffTab[item].strid;
	}else
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = INVALID_RES_ID;	
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffKeyMsgOk
* Description    : autoPowerOffKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;

	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,AUTO_POWER_OFF_SELECT_ID));
		if(item < ARRAY_NUM(autoPowerOffTab))
		{
			autoPowerOff_config = autoPowerOffTab[item].strid;
			user_config_set(CONFIG_ID_AUTOOFF, autoPowerOffTab[item].strid);
			user_config_cfgSys(CONFIG_ID_AUTOOFF);
			user_config_save();	
			
			// 设置自动关机使能标志位
			if(autoPowerOffTab[item].strid == R_ID_STR_COM_OFF)
				autoPowerOff_enable = 0;
			else
				autoPowerOff_enable = 1;
		}	
		uiWinDestroy(&handle);			
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffKeyMsgUp
* Description    : autoPowerOffKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,AUTO_POWER_OFF_SELECT_ID));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffKeyMsgDown
* Description    : autoPowerOffKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
		
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,AUTO_POWER_OFF_SELECT_ID));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffKeyMsgMenu
* Description    : autoPowerOffKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffKeyMsgMode
* Description    : autoPowerOffKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffOpenWin
* Description    : autoPowerOffOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]autoPowerOffOpenWin\n");
	
	// 使用平均间距函数设置选项高度
	uiItemManageSetHeightAvgGap(winItem(handle,AUTO_POWER_OFF_SELECT_ID),Rh(34));
	
	uiItemManageCreateItem(		winItem(handle,AUTO_POWER_OFF_SELECT_ID),uiItemCreateMenuOption,getAutoPowerOffResInfor,ARRAY_NUM(autoPowerOffTab));
	uiItemManageSetCharInfor(	winItem(handle,AUTO_POWER_OFF_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,AUTO_POWER_OFF_SELECT_ID),R_ID_LINE);
	uiItemManageSetUnselectColor(winItem(handle,AUTO_POWER_OFF_SELECT_ID),R_ID_PALETTE_White);

	autoPowerOff_config = user_config_get(CONFIG_ID_AUTOOFF);
	
	// 初始化自动关机标志位状态
	if(autoPowerOff_config == R_ID_STR_COM_OFF)
		autoPowerOff_enable = 0;
	else
		autoPowerOff_enable = 1;
		
	u32 itemNum = 0;
	while(itemNum < ARRAY_NUM(autoPowerOffTab))
	{
		if(autoPowerOffTab[itemNum].strid == autoPowerOff_config)
			break;
		itemNum++;
	}
	if(itemNum >= ARRAY_NUM(autoPowerOffTab))
		itemNum = 0;
	uiItemManageSetCurItem(		winItem(handle,AUTO_POWER_OFF_SELECT_ID),itemNum);
    uiWinSetResid(winItem(handle,AUTO_POWER_OFF_TITLE_ID),R_ID_STR_SET_AUTOOFF);	
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffCloseWin
* Description    : autoPowerOffCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]autoPowerOffCloseWin\n");
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffWinChildClose
* Description    : autoPowerOffWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]autoPowerOffWinChildClose\n");
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffTouchWin
* Description    : autoPowerOffTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		return 0;
	}
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == AUTO_POWER_OFF_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}

/*******************************************************************************
* Function Name  : autoPowerOffTouchSlideOff
* Description    : autoPowerOffTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int autoPowerOffTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;
	if(parame[0] == TP_DIR_UP)
		uiItemManageNextPage(winItem(handle,AUTO_POWER_OFF_SELECT_ID));
	else if(parame[0] == TP_DIR_DOWN)
		uiItemManagePrePage(winItem(handle,AUTO_POWER_OFF_SELECT_ID));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	return 0;
}

ALIGNED(4) msgDealInfor autoPowerOffMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	autoPowerOffOpenWin},
	{SYS_CLOSE_WINDOW,	autoPowerOffCloseWin},
	{SYS_CHILE_COLSE,	autoPowerOffWinChildClose},
	{SYS_TOUCH_WINDOW,  autoPowerOffTouchWin},
	{SYS_TOUCH_SLIDE_OFF,autoPowerOffTouchSlideOff},
	{KEY_EVENT_OK,		autoPowerOffKeyMsgOk},
	{KEY_EVENT_UP,		autoPowerOffKeyMsgUp},
	{KEY_EVENT_DOWN,	autoPowerOffKeyMsgDown},
	{KEY_EVENT_MENU,	autoPowerOffKeyMsgMenu},
	{KEY_EVENT_MODE,	autoPowerOffKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(autoPowerOffWindow,autoPowerOffMsgDeal,autoPowerOffWin) 