/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_GIF_API_H
#define RES_GIF_API_H

/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_gif_show_start(INT32U idx, INT32U uidisable);
/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
int res_gif_show_process(void);
/*******************************************************************************
* Function Name  : res_gif_show
* Description    : res_gif_show
* Input          : INT16U idx resource id
				   INT32U uidisable : 1: no show ui 
* Output         : none
* Return         : int 0;
*******************************************************************************/
void res_gif_show_stop(void);

#endif
