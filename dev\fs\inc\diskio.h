/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : diskio.c
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : Low level disk I/O module skeleton for FatFs
***************************************************************************/

#ifndef _DISKIO_H
#define _DISKIO_H

#ifdef __cplusplus
extern "C" {
#endif

#include "fs_typedef.h"

/* Disk Status Bits (DSTATUS) */
typedef enum{
	DISK_STA_OK			= 0,			/* Drive OK */
	DISK_STA_NOINIT		= (1 << 0),		/* Drive not initialized */
	DISK_STA_NODISK		= (1 << 1),		/* No medium in the drive */
	DISK_STA_PROTECT	= (1 << 2),		/* Write protected */
	DISK_STA_ERR		= (1 << 3), 	/* R/W Error */
	DISK_STA_NOTRDY		= (1 << 4),		/* Not Ready */
	DISK_STA_PARERR		= (1 << 5),		/* Invalid Parameter */
	
}DISK_STATUS;
/* Status of Disk Functions : DISK_STATUS */
typedef BYTE			DSTATUS;

/*******************************************************************************
* Function Name  : get_fattime
* Description    : get_fattime
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
DWORD get_fattime(void);
/*******************************************************************************
* Function Name  : disk_status
* Description    : get a drive status
* Input          : BYTE pdrv :Physical drive nmuber to identify the drive
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_status(BYTE pdrv);
/*******************************************************************************
* Function Name  : disk_initialize
* Description    : Inidialize a Drive 
* Input          : BYTE pdrv :Physical drive nmuber to identify the drive
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_initialize(BYTE pdrv);
/*******************************************************************************
* Function Name  : disk_read
* Description    : Read Sector(s)   
* Input          : BYTE pdrv  	: Physical drive nmuber to identify the drive 
				   BYTE *buff 	: Data buffer to store read data 
				   DWORD sector : Start sector in LBA 
				   UINT count	: Number of sectors to read 
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_read(BYTE pdrv,BYTE *buff,DWORD sector,UINT count);
/*******************************************************************************
* Function Name  : disk_write
* Description    : Write Sector(s)  
* Input          : BYTE pdrv  	: Physical drive nmuber to identify the drive 
				   const BYTE *buff : Data to be written 
				   DWORD sector : Start sector in LBA 
				   UINT count	: Number of sectors to write 
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_write(BYTE pdrv,const BYTE *buff,DWORD sector,UINT count);
/*******************************************************************************
* Function Name  : disk_ioctl
* Description    : Miscellaneous Functions
* Input          : BYTE pdrv  	: Physical drive nmuber (0..) 
				   BYTE cmd 	: Control code 
				   void *buff 	: Buffer to send/receive control data
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_ioctl (BYTE pdrv,BYTE cmd,void *buff);

/* Command code for disk_ioctrl fucntion */

/* Generic command (Used by FatFs) */
#define CTRL_SYNC			0	/* Complete pending write process (needed at _FS_READONLY == 0) */
#define GET_SECTOR_COUNT	1	/* Get media size (needed at _USE_MKFS == 1) */
#define GET_SECTOR_SIZE		2	/* Get sector size (needed at _MAX_SS != _MIN_SS) */
#define GET_BLOCK_SIZE		3	/* Get erase block size (needed at _USE_MKFS == 1) */
#define CTRL_TRIM			4	/* Inform device that the data on the block of sectors is no longer used (needed at _USE_TRIM == 1) */
#define CTRL_ERASE_SECTOR	5	/* Force erased a block of sectors (for only _USE_ERASE) */

/* Generic command (Not used by FatFs) */
#define CTRL_POWER			5	/* Get/Set power status */
#define CTRL_LOCK			6	/* Lock/Unlock media removal */
#define CTRL_EJECT			7	/* Eject media */
#define CTRL_FORMAT			8	/* Create physical format on the media */

/* MMC/SDC specific ioctl command */
#define MMC_GET_TYPE		10	/* Get card type */
#define MMC_GET_CSD			11	/* Get CSD */
#define MMC_GET_CID			12	/* Get CID */
#define MMC_GET_OCR			13	/* Get OCR */
#define MMC_GET_SDSTAT		14	/* Get SD status */
#define ISDIO_READ			55	/* Read data form SD iSDIO register */
#define ISDIO_WRITE			56	/* Write data to SD iSDIO register */
#define ISDIO_MRITE			57	/* Masked write data to SD iSDIO register */

/* ATA/CF specific ioctl command */
#define ATA_GET_REV			20	/* Get F/W revision */
#define ATA_GET_MODEL		21	/* Get model name */
#define ATA_GET_SN			22	/* Get serial number */




#ifdef __cplusplus
}
#endif

#endif
