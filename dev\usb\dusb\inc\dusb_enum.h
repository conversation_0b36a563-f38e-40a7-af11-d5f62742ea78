/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_DEVICE_ENUM_H_
#define USB_DEVICE_ENUM_H_

#define DMSC_INTFS							4
#define DUVC_CTL_INTFS						0
#define DUVC_STRM_INTFS						1
#define DUAC_CTL_INTFS 						2
#define DUAC_STRM_INTFS						3

//UVC TERMINAL UNIT ID
#define UVC_INPUT_ID						0x01
#define UVC_PROCESS_ID						0x02
#define UVC_OUTPUT_ID						0x03
#define UAC_INPUT_ID						0x04
#define UAC_FEATURE_ID						0x05
#define UAC_OUTPUT_ID						0x06


#define bcdUSB20                       		0x0200
#define bcdUSB11                       		0x0110
#define bcdUSB10                       		0x0100
#define bcdDEVICE                      		0x0100
#define bcdUVCDEVICE                  		0x0301

#define SELF_POWER                     		1
#define REMOTE_WAKEUP                  		0

#define UVC_PICSTA_PUSH						2
#define UVC_PICSTA_POP						1
#define UVC_PICSTA_NULL						0




////////////////////////////////////////////////////////////////////////////////
// EP0 STATE
#define EP0_IDLE_STATE                		0x00
#define EP0_TX_STATE            			0x01
#define EP0_RX_STATE            			0x02
#define EP0_END_STATE           			0x03
////////////////////////////////////////////////////////////////////////////////
// USB STATE
#define USB_DEFAULT_STATE       			0x01
#define USB_ADDRESS_STATE       			0x02
#define USB_CONFIG_STATE        			0x03



//String Index
#define ID_LANGUAGE                    	 	0x00
#define ID_MANUFACTURE                  	0x01   	
#define ID_PRODUCT                      	0x02	
#define ID_SERIAL			            	0x03
#define ID_PRODUCT1                      	0x04	
#define ID_PRODUCT2                      	0x05


#define UVC_5FPS                        	0x001E8480 //1s/100ns/5
#define UVC_15FPS                       	0x000A2C2B //1s/100ns/15
#define UVC_20FPS                       	0x0007A120 //1s/100ns/20
#define UVC_25FPS                       	0x00061A80 //1s/100ns/25
#define UVC_30FPS                       	0x00051615 //1s/100ns/30

#define UVC_FREQ_S_24M                      0x016E3600
#define UVC_FREQ_S_30M						0x01C9C380

#define UVC_FPS_MIN							UVC_5FPS
#define UVC_FPS_MAX							UVC_30FPS
#define UVC_FPS_DEFAULT						UVC_30FPS
#define UVC_FREQ_S							UVC_FREQ_S_24M //24M
				



/*******************************************************************************
* Function Name  : usb_stall_ep
* Description    : usb_stall_ep
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_stall_ep(u8 epnum);



/*******************************************************************************
* Function Name  : usb_ep0_tx
* Description    : usb_ep0_tx
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool dusb_ep0_tx(u8* txbuf, u8 len);

/*******************************************************************************
* Function Name  : usb_ep0_receive
* Description    : usb_ep0_receive
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_recieve_set(bool (*callback)(u8 * rxbuf));



/*******************************************************************************
* Function Name  : dusb_ep0_process
* Description    : dusb_ep0_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_process(void);

/*******************************************************************************
* Function Name  : enum_epx_cfg
* Description    : enum_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ep0_cfg(void);

/*******************************************************************************
* Function Name  : enum_epx_cfg
* Description    : enum_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_cfg_reg(u8 type);


#endif /* USB_DEVICE_ENUM_H_ */
