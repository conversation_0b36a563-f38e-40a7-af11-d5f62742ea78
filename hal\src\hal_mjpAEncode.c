/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

typedef struct MJPEG_ENC_OP_S
{
	u8 type;
	u8 timestamp;
	u8 manual_pkg_start;
	u8 manual_kick_noisr;
	u8 encode_lcd_mode;
	u8 encode_crop_scaler;
//#if HAL_CFG_MJPEG_QULITY_AUTO>0
	s8 qulity;
    u8 q_cnt;
	u8 q_auto;
	s8 q_dir;
	u32 q_csize;
//#endif
	u32 jpg_encode_type;

	u32 mjp_flg;

	u16 csi_width;
	u16 csi_height;
	u16 csi_ratio_width;
	u16 csi_ratio_height;
    u16 crop_sx,crop_sy, crop_ex, crop_ey;//for csi -> mjpg crop
    u16 crop_width;//for csi -> mjpg crop
    u16 crop_height;//for csi -> mjpg crop
    u16 mjpeg_width;
	u16 mjpeg_height;
	u16 lcd_width;
	u16 lcd_height;
	u16 line_width;
	u16 line_height;

	u32 jfcnt;
    u32 ybuffer;
	u32 uvbuffer;
	u32 mjpbuf;
	u32 mjpsize;

	u32 curBuffer;
	u32 curLen;
	u32 curLen_temp;
	Stream_Head_T vids;
	Stream_Node_T mjpegNode[MJPEG_ITEM_NUM];
}MJPA_ENC_OP_T;


ALIGNED(4) static MJPA_ENC_OP_T  mjpAEncCtrl;


#if HAL_CFG_MJPEG_QULITY_AUTO>0
ALIGNED(4) const u8 mjpegQualityTable[MJPEG_Q_MAX] = {JPEG_Q_27,JPEG_Q_28,JPEG_Q_31,JPEG_Q_33,JPEG_Q_36,JPEG_Q_40,JPEG_Q_42,JPEG_Q_50,JPEG_Q_62,JPEG_Q_75,JPEG_Q_81};
#endif

/*******************************************************************************
* Function Name  : hal_mjpA_Sizecalculate
* Description    : hal layer .mjpeg output jpeg size
* Input          : u8 quailty : quality
				   u16 width : output width
				   u16 height: output height
* Output         : None
* Return         : int : size
*******************************************************************************/
u32 hal_mjpA_Sizecalculate(u8 quailty,u16 width,u16 height)
{
#if HAL_CFG_MJPEG_QULITY_AUTO > 0
	if(height <= 720)
		return HAL_CFG_MJPEG_720_SIZE_MAX;
	else
		return HAL_CFG_MJPEG_1080_SIZE_MAX;
#else
	INT32U quli;
	switch(quailty)
	{
		case JPEG_Q_27 : quli = 40;break;
		case JPEG_Q_28 : quli = 45;break;
		case JPEG_Q_31 : quli = 50;break;
		case JPEG_Q_33 : quli = 65;break;
		case JPEG_Q_36 : quli = 70;break;
		case JPEG_Q_AUTO :
		case JPEG_Q_40 : quli = 80;break;
		case JPEG_Q_42 : quli = 80;break;
		case JPEG_Q_50 : quli = 90;break;
		case JPEG_Q_62 : quli = 100;break;
		case JPEG_Q_75 : quli = 110;break;
		case JPEG_Q_81 : quli = 120;break;
		default : quli = 100;break;
	}
//    quli+=20;
	return (((width*height/100)*quli)/10); // byte
#endif
}
/*******************************************************************************
* Function Name  : hal_mjpA_QualityAjust
* Description    : hal layer .mjpeg quality ajust
* Input          : u32 len : current mjpeg size
* Output         : None
* Return         : NONE
*******************************************************************************/
static void hal_mjpA_QualityAjust(u32 len)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0
	if(mjpAEncCtrl.q_auto!=JPEG_Q_AUTO)
		return ;

	if(mjpAEncCtrl.q_dir == 0)  //dir： (0) - default, (1) - Q decrease, (-1) - Q increase
	{
		int mjpeg_throld = mjpAEncCtrl.q_csize*15/100;  // 40%
		if(len > (mjpAEncCtrl.q_csize+mjpeg_throld))
		{
			mjpAEncCtrl.q_dir = 1;
			mjpAEncCtrl.q_cnt = 0;
			//deg_Printf("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize+mjpeg_throld);
		}
		else if(len < (mjpAEncCtrl.q_csize-mjpeg_throld))
		{
			mjpAEncCtrl.q_dir = -1;
			mjpAEncCtrl.q_cnt = 0;
			//deg_Printf("HAL : [MJPEG]<INFO> size auto check %x,%x\n",len,mjpAEncCtrl.q_csize-mjpeg_throld);
		}
	}
	else if(mjpAEncCtrl.q_dir > 0)
	{
		if(len < mjpAEncCtrl.q_csize)
		{
			mjpAEncCtrl.q_dir = 0;
			mjpAEncCtrl.q_cnt = 0;
		}
		else
			mjpAEncCtrl.q_cnt++;
	}
	else if(mjpAEncCtrl.q_dir < 0)
	{
		if(len > mjpAEncCtrl.q_csize)
		{
			mjpAEncCtrl.q_dir = 0;
			mjpAEncCtrl.q_cnt = 0;
		}
		else
			mjpAEncCtrl.q_cnt++;
	}

	if(mjpAEncCtrl.q_cnt>=10)
	{
		mjpAEncCtrl.qulity-= mjpAEncCtrl.q_dir;
		if(mjpAEncCtrl.qulity < 0)
			mjpAEncCtrl.qulity = 0;
		else if(mjpAEncCtrl.qulity >= MJPEG_Q_MAX)
			mjpAEncCtrl.qulity = MJPEG_Q_MAX-1;


		hx330x_mjpA_EncodeQuilitySet(mjpegQualityTable[mjpAEncCtrl.qulity]);

		//deg_Printf("HAL : [MJPEG]<INFO> quality auto check[%d] ->%d\n",mjpAEncCtrl.q_dir,mjpAEncCtrl.qulity);
		mjpAEncCtrl.q_dir = 0;
		mjpAEncCtrl.q_cnt = 0;
	}
#endif
}
/*******************************************************************************
* Function Name  : hal_mjpA_QualityCheck
* Description    : hal layer .mjpeg quality check
* Input          : u8 quality : user set quality
* Output         : None
* Return         : u8
*******************************************************************************/
static u8 hal_mjpA_QualityCheck(u8 quality)
{
#if HAL_CFG_MJPEG_QULITY_AUTO>0
    mjpAEncCtrl.q_auto = quality;

    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_40;
	int i;
	for(i = 0;i < MJPEG_Q_MAX;i++)
	{
		if(mjpegQualityTable[i] == quality)
			break;
	}
	if(i >= MJPEG_Q_MAX)
		i = MJPEG_Q_MAX-1;
	mjpAEncCtrl.qulity = i;
	mjpAEncCtrl.q_cnt = 0;
	mjpAEncCtrl.q_dir = 0;
    mjpAEncCtrl.q_csize = hal_mjpA_Sizecalculate(quality,mjpAEncCtrl.mjpeg_width,mjpAEncCtrl.mjpeg_height);
	mjpAEncCtrl.q_csize = mjpAEncCtrl.q_csize*55/100;
#endif
    if(quality == JPEG_Q_AUTO)
		quality = JPEG_Q_40;
	deg_Printf("video record Q = %x\n",quality);
	return quality;
}

/*******************************************************************************
* Function Name  : hal_mjpA_EncState
* Description    : hal layer .mjpA encode state check
* Input          : u8 flag:
* Output         : None
* Return         : bool true : encode success
*******************************************************************************/
static bool hal_mjpA_EncState(int flag)
{
	if(flag & BIT(MJPEG_IRQ_OUTPAUSE)){//ODMAPAUSE
		if(!(flag & BIT(MJPEG_IRQ_FRAMEEND))){
			//deg_Printf("mallo j frame size err\n");
			return false;
		}
	}
	if((flag & BIT(MJPEG_IRQ_OUTERR))){
		return false;
	}
	return true;
}

/*******************************************************************************
* Function Name  : hal_jA_fcnt_mnt
* Description    : hal layer .mjpA encode frame debg
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_jA_fcnt_mnt(void)
{
	u32 temp = mjpAEncCtrl.jfcnt;
	deg_Printf("JA_fcnt:%d\n",mjpAEncCtrl.jfcnt);
	mjpAEncCtrl.jfcnt = 0;
	return temp;
}
/*******************************************************************************
* Function Name  : hal_mjpA_Start
* Description    : hal layer .mjpeg start.for sync to frame,this function will be callback by csi frame end
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpA_Start(void)
{
	hx330x_mjpA_EncodeEnable(1);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,NULL); // only need execute once
}
/*******************************************************************************
* Function Name  : hal_mjpA_Restart
* Description    : hal layer .mjpeg restart
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpA_Restart(void)
{
	hx330x_mjpA_EncodeEnable(0);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,hal_mjpA_Start);
}


/*******************************************************************************
* Function Name  : hal_mjpA_EncodeInit
* Description    : hal layer .mjpeg initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeInit(void)
{
	hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
    //mjpAEncCtrl.type = 0;
	//mjpAEncCtrl.uvbuffer = 0;//(u32)halSysMemMJPEGBuffer.uv_buffer;
	//mjpAEncCtrl.ybuffer  = 0;//(u32)halSysMemMJPEGBuffer.y_buffer;
	//mjpAEncCtrl.mjpbuf   = 0;//&halSysMemMJPEGBuffer.mjpeg_buffer;	
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_SensorRatioResolutionGet(&mjpAEncCtrl.csi_ratio_width,&mjpAEncCtrl.csi_ratio_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	hx330x_MJPA_EncodeLcdPreRegister(NULL);
	hx330x_MJPA_EncodeLcdBackRegister(NULL);
	hal_mjp_enle_init();
	hal_mjp_step_init();
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
}
/*******************************************************************************
* Function Name  : hal_mjpA_EncodeUninit
* Description    : hal layer .mjpeg uninitial 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_EncodeUninit(void)
{
	//if(mjpAEncCtrl.mjpbuf == 0)
	//	return;
	hx330x_mjpA_EncodeEnable(0);
	hal_mjp_step_unit();
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_mjpA_EncodeISRRegister(NULL);
	hx330x_MJPA_EncodeLcdPreRegister(NULL);
	hx330x_MJPA_EncodeLcdBackRegister(NULL);
	hal_mjpA_MemUninit();
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
}
/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_LineBuf_MenInit(u8 type,u16 width,u16 height)
{
    u32 linebufsize;
	mjpAEncCtrl.line_width = hx330x_min(width,  mjpAEncCtrl.csi_ratio_width);
 
	mjpAEncCtrl.line_height = hx330x_min(height, mjpAEncCtrl.csi_ratio_height);

	mjpAEncCtrl.line_width = (mjpAEncCtrl.line_width + 0x1f)&~0x1f;
	
	if((mjpAEncCtrl.jpg_encode_type == ENC_MODE_NORMAL) || type  == MJPEG_TYPE_VIDEO || type == MJPEG_TYPE_UVC)
	{
		if(height < 1440)//mjpegEncCtrl.csi_height)
			mjpAEncCtrl.line_height = hx330x_min(192, mjpAEncCtrl.line_height);
		else
			mjpAEncCtrl.line_height = hx330x_min(320, mjpAEncCtrl.line_height);//120*(2<<(height/mjpegEncCtrl.csi_height-1));
	}
	

	deg_Printf("MJPA LINE W:%d,H:%d\n",mjpAEncCtrl.line_width,mjpAEncCtrl.line_height);
    linebufsize = mjpAEncCtrl.line_width*mjpAEncCtrl.line_height*3/2;

	if(mjpAEncCtrl.ybuffer == 0)
	{
	    if(linebufsize <= 64*1024L)
            mjpAEncCtrl.ybuffer = (u32)&__line_start;//
        else
            mjpAEncCtrl.ybuffer = (u32)hal_sysMemMalloc(linebufsize);
		if(mjpAEncCtrl.ybuffer == 0)
		{
			return false;
		}
		mjpAEncCtrl.uvbuffer = mjpAEncCtrl.ybuffer+mjpAEncCtrl.line_height*mjpAEncCtrl.line_width;
	}
	return true;
}

/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_buf_MenInit(u8 type)
{
	u32 size;
	if(mjpAEncCtrl.ybuffer == 0)
		return false;
	if(mjpAEncCtrl.mjpbuf == 0)
	{
		size = hal_sysMemRemain() - 16*1024; //预留给创建文件用buf

		if(hardware_setup.usb_host_en)
		{
			if(mjpAEncCtrl.line_width <= 640) //VGA时才支持双路
			{
				if(!husb_api_usensor_tran_sta())
					size -= _UVC_JPG_CACHE_SIZE*3;  //预留给HUSB_UVC_CACHE
			}
		} 


		size &=~0x1ff;
		//size = 1200*1024L;
		mjpAEncCtrl.mjpbuf = (u32)hal_sysMemMalloc(size);
		if(mjpAEncCtrl.mjpbuf == 0)
		{
			return false;
		}
		mjpAEncCtrl.mjpsize = size;
		deg_Printf("HAL :<INFO> mjpA addr = 0x%x,size = %dMB%dKB\n",mjpAEncCtrl.mjpbuf,size>>20,(size>>10)&1023);
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_linebufUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_linebufUninit(void)
{
	if(mjpAEncCtrl.ybuffer >= 0x2000000)
		hal_sysMemFree((void *)mjpAEncCtrl.ybuffer);
	mjpAEncCtrl.ybuffer = 0;
	mjpAEncCtrl.uvbuffer = 0;

}
/*******************************************************************************
* Function Name  : hal_mjpA_MemUninit
* Description    : hal layer .mjpeg memory uninitial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_MemUninit(void)
{
	hal_mjp_enle_unit();
	if(mjpAEncCtrl.mjpbuf)
		hal_sysMemFree((void *)mjpAEncCtrl.mjpbuf);
	mjpAEncCtrl.mjpbuf = 0;
	hal_mjpA_linebufUninit();
}
/*******************************************************************************
* Function Name  : hal_mjpA_frameDoneCheck
* Description    : check crop state after current frame encode end
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpA_frameDoneCheck(void)
{
	u16 crop_sx, crop_sy, crop_ex, crop_ey;
	hal_lcdGetCsiCrop(&crop_sx, &crop_sy, &crop_ex, &crop_ey);
	if(mjpAEncCtrl.crop_sx == crop_sx && mjpAEncCtrl.crop_sy == crop_sy 
	   &&mjpAEncCtrl.crop_ex == crop_ex && mjpAEncCtrl.crop_ey == crop_ey)
	{
		return;
	}
	mjpAEncCtrl.crop_sx 	= crop_sx;
	mjpAEncCtrl.crop_sy 	= crop_sy;
	mjpAEncCtrl.crop_ex 	= crop_ex;
	mjpAEncCtrl.crop_ey 	= crop_ey;
	mjpAEncCtrl.crop_width  = mjpAEncCtrl.crop_ex - mjpAEncCtrl.crop_sx;
	mjpAEncCtrl.crop_height = mjpAEncCtrl.crop_ey - mjpAEncCtrl.crop_sy;
	hal_mjpA_Restart();
	if(false == hx330x_csiMJPEGCrop(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,
									mjpAEncCtrl.crop_sx,mjpAEncCtrl.crop_sy,
									mjpAEncCtrl.crop_ex,mjpAEncCtrl.crop_ey))
	{
		deg_Printf("mjpA Encode Frame done: I2J CROP fail\n");
		return false;
	}
	u32 line_size = (u32)mjpAEncCtrl.line_height * (u32)mjpAEncCtrl.line_width;
	u32 line_width = (mjpAEncCtrl.crop_width + 0x1f)&~0x1f;
	u32 line_height = line_size/line_width;
	hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,line_height,line_width);
	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.crop_width,mjpAEncCtrl.crop_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);

}
/*******************************************************************************
* Function Name  : hal_mjpA_AutoEncodeCallback
* Description    : hal layer .mjpA auto mode encode callback function
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpA_AutoEncodeCallback(int flag)
{
	u32 addr,len;
	u32 malloc_len;
	u32 mjp_sync;
	u32 mjp_sync_next;

	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		mjpAEncCtrl.jfcnt++;
		mjp_sync      = hal_auadc_stamp_out();
		mjp_sync_next = hal_auadc_stamp_next();
		len = hx330x_mjpA_EncodeLoadAddrGet()- mjpAEncCtrl.curBuffer;
		len = (len+0x1ff)&(~0x1ff);
		if(hal_mjpA_EncState(flag))
		{
			hal_streamIn(&mjpAEncCtrl.vids,len,mjp_sync,mjp_sync_next);
			if(mjpAEncCtrl.type == MJPEG_TYPE_PHOTO)
			{
				//deg_Printf("end\n");
				hx330x_mjpA_EncodeEnable(0);
				return;
			}
			malloc_len =(len > _JPGA_SIZE_MIN_DEF_)? (len*2) : (_JPGA_SIZE_MIN_DEF_*2);
		}
		else
		{
			//deg_Printf("?");
			hal_streamIn(&mjpAEncCtrl.vids,0,mjp_sync,mjp_sync_next);
			hal_mjpA_Restart();
			malloc_len = (mjpAEncCtrl.curLen > (_JPGA_SIZE_MIN_DEF_))? (mjpAEncCtrl.curLen*2) : (_JPGA_SIZE_MIN_DEF_*2);
		}
		addr = hal_streamMalloc(&mjpAEncCtrl.vids,malloc_len);
		//addr = hal_streamMalloc(&mjpAEncCtrl.vids,(len*3/2));
		if(addr)
		{
			//deg_Printf("M");
			mjpAEncCtrl.curBuffer = addr;
			mjpAEncCtrl.curLen    = malloc_len;
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		}else
		{
			deg_Printf("D");
			//deg_Printf("[A:%d,%d,%d]\n",mjpAEncCtrl.vids.head_size,mjpAEncCtrl.mjpsize,malloc_len);
			hal_streamMallocDrop(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
		}
	#if HAL_CFG_MJPEG_QULITY_AUTO >0
		if(mjpAEncCtrl.q_auto != JPEG_Q_AUTO){
			if(mjpAEncCtrl.mjpeg_height <= 720){
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);
			}else{
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_1080_SIZE_MIN,HAL_CFG_MJPEG_1080_SIZE_MAX);
			}
		}
		else
		{
			hal_mjpA_QualityAjust(len);
		}
	#endif
	}
	hx330x_mjpA_Flag_Clr(flag);
}

/*******************************************************************************
* Function Name  : hal_mjpAEncodeVideoDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodeVideoDoneManual(int flag)
{
	u32 len;
	u32 mjp_sync;
	u32 mjp_sync_next;
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		len = hx330x_mjpA_EncodeLoadAddrGet()- mjpAEncCtrl.curBuffer;
		len = (len+0x1ff)&(~0x1ff);	
		mjp_sync      = hal_auadc_stamp_out();
		mjp_sync_next = hal_auadc_stamp_next();
		if(hal_mjpA_EncState(flag))
		{
			mjpAEncCtrl.jfcnt++;
			hal_streamIn(&mjpAEncCtrl.vids,len,mjp_sync,mjp_sync_next);
		}
		else
		{
			hal_streamIn(&mjpAEncCtrl.vids,0,mjp_sync,mjp_sync_next);
		}	
		mjpAEncCtrl.curBuffer = (u32)0;
		mjpAEncCtrl.curLen    = (len > _JPGA_SIZE_MIN_DEF_)? (len*2) : (_JPGA_SIZE_MIN_DEF_*2);	
	#if HAL_CFG_MJPEG_QULITY_AUTO >0
		if(mjpAEncCtrl.q_auto != JPEG_Q_AUTO){
			if(mjpAEncCtrl.mjpeg_height <= 720){
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);
			}else{
				hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_1080_SIZE_MIN,HAL_CFG_MJPEG_1080_SIZE_MAX);
			}
		}
		else
		{
			hal_mjpA_QualityAjust(len);
		}
	#endif	
		hx330x_mjpA_Flag_Clr(flag);				
		
	}else{
		deg_Printf("ERR:%x\n",flag);
		hx330x_mjpA_Flag_Clr(flag);
	}

}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodePhotoDoneManual(int flag)
{
	u32 len;
	u32 sync_flag = 0;
	//u32 mjp_sync;
	//u32 mjp_sync_next;
	//deg_Printf("flag:%x\n",flag);
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		len = hx330x_mjpA_EncodeLoadAddrGet()- mjpAEncCtrl.curBuffer;
		if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_LLPKG)
		{
			
			len = (len+3)&(~3);
			int res;
			if(hal_mjpA_EncState(flag) == false)
			{
				hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
				
				hx330x_mjpA_EncodeEnable(0);
				deg_Printf("encode llpkg fail 1\n");
			}else
			{
				//size += len;
				res = hal_mjp_enle_manual_done(len);
				if(res  < 0)
				{
					deg_Printf("encode llpkg fail :%d\n",res);
					hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
				}else if(res > 0)
				{
					if(mjpAEncCtrl.manual_pkg_start)
					{
						sync_flag |= JPG_PKG_START;
						mjpAEncCtrl.manual_pkg_start = 0;
					}
					if(res == 2)
					{
						sync_flag |= JPG_PKG_END;
					}else
					{
						mjpAEncCtrl.manual_kick_noisr = 1;
					}	
					
					hx330x_sysDcacheFlush(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
					if(hal_mjp_enle_buf_mdf() < 0)
					{
						hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
					}else
					{
						//hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,mjp_sync,0);	
						hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,sync_flag,0);	
					}	
					//if(mjp_sync & JPG_PKG_END)
					//{
					//	//deg_Printf("size:%d\n", size);
					//	//size = 0;
					//}		
					
				}
			}	
		}else if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_PKG)
		{
			if(mjpAEncCtrl.manual_pkg_start)
			{
				sync_flag |= JPG_PKG_START;

				mjpAEncCtrl.manual_pkg_start = 0;
				//deg_Printf("S");
			}
			if(hal_mjpA_EncState(flag)) // frame done
			{
				//deg_Printf("E");
				len = (len+0x1ff)&(~0x1ff);

				hal_streamIn(&mjpAEncCtrl.vids,len,sync_flag|JPG_PKG_END,0); // sync as frame done flag
				hx330x_mjpA_EncodeEnable(0);
				return;

			}
			//PKG PAUSE
			hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,sync_flag,0);
			mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
			if(mjpAEncCtrl.curBuffer)
			{
				//deg_Printf("A");
				hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
				//kick
				hx330x_mjpA_Flag_Clr(flag);
				
			}else
			{
				//deg_Printf("B");
				hx330x_intEnable(IRQ_JPGA,0);
				mjpAEncCtrl.mjp_flg = flag;
				mjpAEncCtrl.manual_kick_noisr = 1;
			}
		}else
		{
			len += 12;
			if(hal_mjpA_EncState(flag))
			{
				//mjpAEncCtrl.jfcnt++;
				hal_streamIn(&mjpAEncCtrl.vids,len,0,0);
				hx330x_mjpA_EncodeEnable(0);
			}
			else
			{
				hal_streamIn(&mjpAEncCtrl.vids,0,0,0);
				if(mjpAEncCtrl.encode_lcd_mode == 0)
					hx330x_csiMJPEGDmaEnable(1);
			}	
			mjpAEncCtrl.curBuffer = (u32)0;		
		#if HAL_CFG_MJPEG_QULITY_AUTO >0
			if(mjpAEncCtrl.q_auto != JPEG_Q_AUTO){
				if(mjpAEncCtrl.mjpeg_height <= 720){
					hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_720_SIZE_MIN,HAL_CFG_MJPEG_720_SIZE_MAX);
				}else{
					hx330x_mjpA_EncodeQadj(len,HAL_CFG_MJPEG_1080_SIZE_MIN,HAL_CFG_MJPEG_1080_SIZE_MAX);
				}
			}
			else
			{
				hal_mjpA_QualityAjust(len);
			}
		#endif	
			hx330x_mjpA_Flag_Clr(flag);				
		}
	}else{
		deg_Printf("ERR:%x\n",flag);
		hx330x_mjpA_Flag_Clr(flag);
	}
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeManualResume
* Description    : mjp encode pkg mode resume
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodeManualResume(void)
{
	
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	volatile u8 kick = mjpAEncCtrl.manual_kick_noisr;
	HAL_CRITICAL_EXIT();
	if(kick == 0)
	{
		return 0;
	}
	deg_Printf("hal_mjpAEncodeManualResume\n");
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		//deg_Printf("C");
		mjpAEncCtrl.manual_kick_noisr = 0;
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_Flag_Clr(mjpAEncCtrl.mjp_flg);
		hx330x_intEnable(IRQ_JPGA,1);
		return 0;
	}
	return -1;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeLLPKG
* Description    : mjp encode llppkg mode resume
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeLLPKG(void)
{
	HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	volatile u8 kick = mjpAEncCtrl.manual_kick_noisr;
	HAL_CRITICAL_EXIT();
	if(kick == 0)
	{
		return 0;
	}
	//deg_Printf("hal_mjpAEncodePhotoResumeLLPKG\n");
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		
		mjpAEncCtrl.manual_kick_noisr = 0;
		hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
		//deg_Printf("END\n");
		return 0;
	}
	
	return -1;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodePhotoResumeRam
* Description    : mjp resume encode(no step mode) for sub or ram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpAEncodePhotoResumeRam(void)
{
	if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_PKG)
	{
		mjpAEncCtrl.curLen *= 2;
	}
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		mjpAEncCtrl.jpg_encode_type 			= ENC_MODE_NORMAL;
		if(mjpAEncCtrl.encode_lcd_mode == 0)
		{
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height, mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height);
			hal_jpg_watermarkStart(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,mjpAEncCtrl.timestamp);		
		}else{
			u16 tar_w = hx330x_max(mjpAEncCtrl.lcd_width, mjpAEncCtrl.csi_width);
			u16 tar_h = hx330x_max(mjpAEncCtrl.lcd_height, mjpAEncCtrl.csi_height);
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, tar_w,tar_h);
			hal_jpg_watermarkStart(tar_w,tar_h,mjpAEncCtrl.timestamp);				
		}

		hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_EncodeDriModeSet(0);
		hx330x_mjpA_Encode_manual_on();
		hx330x_intEnable(IRQ_JPGA,1);
		return 0;
	}
	return -1;
}


/*******************************************************************************
* Function Name  : hal_mjpA_step_buf_MenInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool hal_mjpA_step_buf_MenInit(void)
{
	u32 size;
	if(mjpAEncCtrl.mjpbuf == 0)
	{
		size = hal_sysMemRemain() - 16*1024; //预留给创建文件用buf
		size &=~0x1ff;
		//size = 1200*1024L;
		mjpAEncCtrl.mjpbuf = (u32)hal_sysMemMalloc(size);
		if(mjpAEncCtrl.mjpbuf == 0)
		{
			return false;
		}
		mjpAEncCtrl.mjpsize = size;
		deg_Printf("HAL :<INFO> mjpA addr = 0x%x,size = %dMB%dKB\n",mjpAEncCtrl.mjpbuf,size>>20,(size>>10)&1023);
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodeStepDoneManual(int flag)
{
	u32 len;
	u32 len_align;
	u32 sync_flag = 0;
	u8  step;
	//u32 mjp_sync;
	//u32 mjp_sync_next;
	//deg_Printf("hal_mjpAEncodeStepDoneManual flag:%x\n",flag);
#if 1
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		len = hx330x_mjpA_EncodeLoadAddrGet()- mjpAEncCtrl.curBuffer;
		len_align = ((len+0x3f)&~0x3f) ;
		if(mjpAEncCtrl.manual_pkg_start)
		{
			sync_flag |= JPG_PKG_START;

			mjpAEncCtrl.manual_pkg_start = 0;
			//deg_Printf("-----S\n");
		}
		step = hal_mjp_step_get();
		if(hal_mjpA_EncState(flag)) // frame done
		{
			//deg_Printf("--------E\n");
			
			
			if(step == ENC_STEP_SUB)
			{
				hx330x_mjpA_EncodeEnable(0);
				if(hal_mjp_stepsub_done(mjpAEncCtrl.curBuffer, &len) < 0)
				{
					hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
					return;
				}
				hal_streamIn(&mjpAEncCtrl.vids,len,0,0); // sync as frame done flag
			}else{
				if(hal_step_is_elne_mode())
				{
					int res = hal_mjp_step_elne_done(len);
					//deg_Printf("elne done:%d\n", res);
					if(res < 0)
					{
						deg_Printf("encode step elne fail :%d\n",res);
						hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
					}else if(res > 0)
					{
						if(res == 1)
						{
							hal_mjpstep_manual_kick();	
						}else if(res == 2)
						{
							sync_flag |= JPG_PKG_END;
						}
						hx330x_sysDcacheFlush(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
						if(hal_mjp_step_elne_buf_mdf() < 0)
						{
							//deg_Printf("elne_buf mdf 11111111111\n");
							hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
							hx330x_mjpA_EncodeEnable(0);
						}else
						{
							//deg_Printf("elne_buf mdf 2222222222\n");
							//hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,mjp_sync,0);	
							hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,sync_flag,0);	
						}	
					}
				}else{
					if(hal_mjp_step_done(mjpAEncCtrl.curBuffer, &len) < 0)
					{
						hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
						return;
					}
					if(step == ENC_STEP_END)
					{
						
						len = (len + 0x1ff)&(~0x1ff);
						hal_streamIn(&mjpAEncCtrl.vids,len,sync_flag|JPG_PKG_END,0); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
						
					}else
					{
						hal_streamIn(&mjpAEncCtrl.vids,len_align,sync_flag,len_align - len); // sync as frame done flag
						hx330x_mjpA_EncodeEnable(0);
						hal_mjpstep_manual_kick();
					}
				}

			}

			//hx330x_mjpA_Encode_manual_stop();
			//hx330x_intEnable(IRQ_JPGA,0); // diable jpeg irq
			return;
		}else if(step == ENC_STEP_SUB)
		{
			deg_Printf("[MJP STEP SUB] encode done err\n");
			hx330x_mjpA_EncodeEnable(0);
			hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
			return;
		}else if(hal_step_is_elne_mode())
		{
			deg_Printf("[MJP STEP ELNE] encode done err\n");
			hx330x_mjpA_EncodeEnable(0);
			hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
			return;	
		}
		//deg_Printf("--------P\n");
		//PKG PAUSE
		len = mjpAEncCtrl.curLen;
		if(hal_mjp_step_done(mjpAEncCtrl.curBuffer, &len) < 0)
		{
			hal_streamIn(&mjpAEncCtrl.vids,0,JPG_PKG_ERR,0); // sync as frame done flag
			hx330x_mjpA_EncodeEnable(0);
			return;
		}
		hal_streamIn(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen,sync_flag,mjpAEncCtrl.curLen - len);
		mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
		if(mjpAEncCtrl.curBuffer)
		{
			//deg_Printf("A");
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			//kick
			hx330x_mjpA_Flag_Clr(flag);
			
		}else
		{
			//deg_Printf("B");
			hx330x_intEnable(IRQ_JPGA,0);
			mjpAEncCtrl.mjp_flg = flag;
			mjpAEncCtrl.manual_kick_noisr = 1;
		}
	}else{
		deg_Printf("ERR:%x\n",flag);
		hx330x_mjpA_Flag_Clr(flag);
	}
#endif
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeStepSubManualResume
* Description    : mjp resume encode(step mode) for sub or ram
* Input          : None
* Output         : None
* Return         : < 0: fail , 0 : not enter step, >0: enter step 
*******************************************************************************/
int hal_mjpAEncodeStepSubManualResume(void)
{
	int res = 0;
	u8 step = hal_mjp_step_get();
	if(step != ENC_STEP_END)
	{
		res = -1;
		goto STEP_SUM_RESUME_END;
	}
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer == 0)
	{
		res = -2;
		goto STEP_SUM_RESUME_END;
	}
	if(hal_mjp_stepsub_resume(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curLen ) < 0)
	{
		res = -3;
		goto STEP_SUM_RESUME_END;		
	}
	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodeStepDoneManual);
	hx330x_mjpA_Encode_manual_on();
	hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
STEP_SUM_RESUME_END:
	if(res < 0)
	{
		deg_Printf("[MJP STEP SUB] resume fali:%d\n", res);
	}
	return res;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeStepManualResume
* Description    : mjp encode step mode resume
* Input          : None
* Output         : None
* Return         : < 0: fail , 0 : not enter step, >0: enter step 
*******************************************************************************/
int hal_mjpAEncodeStepManualResume(void)
{
	//int start = hal_mjp_manual_kick_get();
	u8  step;
	//if(start == 0)
	//{
	//	return 0;
	//}else
	{
		step = hal_mjp_step_get();
		//if(step != ENC_STEP_NONE)
		//{
		//	deg_Printf("[r:%d]", step);
		//}
		if(step == ENC_STEP_FAIL)
		{
			return -1;
		}else if(step == ENC_STEP_NONE)
		{
			return 0;
		}
		else if(step == ENC_STEP_0)
		{
			deg_Printf("---------------step0\n");
			if(hal_mjpA_step_buf_MenInit() == false)
			{
				return -2;
			}	
			hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);
			mjpAEncCtrl.curLen = hal_step_is_elne_mode() ? mjpAEncCtrl.mjpsize : (mjpAEncCtrl.mjpsize/2);
			//mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize);
			mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
			if(hal_mjp_step_resume(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen ) < 0)
			{
				return -3;
			}
			//hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodeStepDoneManual);
			hx330x_mjpA_Encode_manual_on();
			hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
			return 0;
		}else if(step == ENC_STEP_1)
		{
			mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
			if(mjpAEncCtrl.curBuffer)
			{
				deg_Printf("--------------step1\n");
				if(hal_mjp_step_resume(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen ) < 0)
				{
					return -4;
				}
				hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodeStepDoneManual);
				hx330x_mjpA_Encode_manual_on();
				hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
			}
			return 1;

		}else if(step == ENC_STEP_2 || step == ENC_STEP_END)
		{
			//deg_Printf("step2\n");
			hal_mjpAEncodeManualResume();
			return 1;
		}	
	}
	return 0;
	
}




/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Video_Start
* Description    : hal layer .mjpeg initial
* Input          : u8 type MJPEG_TYPE_VIDEO/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Video_Start(u8 type, u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	if(type != MJPEG_TYPE_VIDEO && type != MJPEG_TYPE_UVC)
	{
		return false;
	}
	//hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_SensorRatioResolutionGet(&mjpAEncCtrl.csi_ratio_width,&mjpAEncCtrl.csi_ratio_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);	

	hal_lcdGetCsiCrop(&mjpAEncCtrl.crop_sx, &mjpAEncCtrl.crop_sy, &mjpAEncCtrl.crop_ex, &mjpAEncCtrl.crop_ey);
	mjpAEncCtrl.crop_width  = mjpAEncCtrl.crop_ex - mjpAEncCtrl.crop_sx;
	mjpAEncCtrl.crop_height = mjpAEncCtrl.crop_ey - mjpAEncCtrl.crop_sy;

	mjpAEncCtrl.type 			= type;
	mjpAEncCtrl.timestamp 		= timestamp;
	mjpAEncCtrl.encode_lcd_mode = 0;
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
	mjpAEncCtrl.mjpeg_width 	= target_w;
	mjpAEncCtrl.mjpeg_height 	= target_h;
	mjpAEncCtrl.manual_kick_noisr = 0;
	mjpAEncCtrl.manual_pkg_start = 0;
	if(target_w < mjpAEncCtrl.crop_width || target_h < mjpAEncCtrl.crop_height)
	{
		deg_Printf("mjpA Encode : crop mode not support scaler down\n");
		return false;	
	}
	if(false == hx330x_csiMJPEGCrop(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,
									mjpAEncCtrl.crop_sx,mjpAEncCtrl.crop_sy,
									mjpAEncCtrl.crop_ex,mjpAEncCtrl.crop_ey))
	{
		deg_Printf("mjpA Encode : I2J CROP fail\n");
		return false;
	}
	//mjpAEncCtrl.curBuffer 		= 0;
	//if(hx330x_csiMJPEGScaler(target_w,target_h,0,0,mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height) == false)
	//{
	//	deg_Printf("mjpA Encode : I2J Scaler down fail\n");
	//	return false;
	//}
	if(hal_mjpA_LineBuf_MenInit(type, target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit(type) == false) // mjpeg memory malloc
	{
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		hal_mjpA_MemUninit();
		return false;
	}	
	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);
	u32 line_size = (u32)mjpAEncCtrl.line_height * (u32)mjpAEncCtrl.line_width;
	u32 line_width = (mjpAEncCtrl.crop_width + 0x1f)&~0x1f;
	u32 line_height = line_size/line_width;
	hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,line_height,line_width);
	//hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,mjpAEncCtrl.line_height,mjpAEncCtrl.line_width);

	mjpAEncCtrl.curLen 		= mjpAEncCtrl.mjpsize/2;
	mjpAEncCtrl.curBuffer 	= hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	mjpAEncCtrl.qulity 		= hal_mjpA_QualityCheck(quality);
	hx330x_mjpA_EncodeInit(0, (HAL_CFG_MJPEG_HIGH_QT?2:1));
	//hx330x_mjpA_EncodeInit(0,1); //auto, video q
	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.crop_width,mjpAEncCtrl.crop_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
	hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
	hx330x_mjpA_EncodeInfoSet((type == MJPEG_TYPE_VIDEO)?1:0);
	hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curBuffer + mjpAEncCtrl.curLen);
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_mjpA_frameDoneCheck);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	hal_mjpA_Start);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	hal_mjpA_Restart);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,hal_mjpA_Restart);	
	hx330x_mjpA_EncodeISRRegister(hal_mjpA_AutoEncodeCallback);
	hx330x_csiEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode, only one frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodeKickManual(void)
{
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		hx330x_csiMJPEGDmaEnable(0);
		if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_LLPKG)
		{
			if(mjpAEncCtrl.encode_crop_scaler)
			{
				hx330x_mjpA_EncodeQuilitySet(JPEG_Q_95);
				hal_jpg_watermarkStart(mjpAEncCtrl.csi_ratio_width,mjpAEncCtrl.csi_ratio_height,0);
				hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.crop_width,mjpAEncCtrl.crop_height, mjpAEncCtrl.csi_ratio_width,mjpAEncCtrl.csi_ratio_height);
				hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
				hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
				hx330x_mjpA_Encode_manual_on();
				if(hx330x_mjpA_Encode_check() == false)
				{
					deg_Printf("ENC crop scaler STEP1 fail\n");
					hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
					return;
				}
				hal_mjpHeaderParse(mjpAEncCtrl.curBuffer);
				if(hal_mjpegDecodePicture_noisr(mjpAEncCtrl.curBuffer,mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,mjpAEncCtrl.csi_ratio_width,mjpAEncCtrl.csi_ratio_height) == false)
				{
					deg_Printf("ENC crop scaler STEP2 fail\n");
					hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
					return;
				}
				if(hx330x_mjpB_Decode_check() == false)
				{
					deg_Printf("ENC crop scaler STEP3 fail\n");
					hal_streamIn(&mjpAEncCtrl.vids, 0, 0, 0);
					return;
				}
				hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			}

			hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
		}else{
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			hx330x_mjpA_Encode_manual_on();
		}

		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
	//return true;
}
/*******************************************************************************
* Function Name  : hal_mjpA_Enc_Video_Start
* Description    : hal layer .mjpeg initial
* Input          : u8 type MJPEG_TYPE_VIDEO/MJPEG_TYPE_UVC
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_Enc_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	//hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));

	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_SensorRatioResolutionGet(&mjpAEncCtrl.csi_ratio_width,&mjpAEncCtrl.csi_ratio_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	hal_lcdGetCsiCrop(&mjpAEncCtrl.crop_sx, &mjpAEncCtrl.crop_sy, &mjpAEncCtrl.crop_ex, &mjpAEncCtrl.crop_ey);
	mjpAEncCtrl.crop_width  = mjpAEncCtrl.crop_ex - mjpAEncCtrl.crop_sx;
	mjpAEncCtrl.crop_height = mjpAEncCtrl.crop_ey - mjpAEncCtrl.crop_sy;
	
	if(target_w < mjpAEncCtrl.crop_width || target_h < mjpAEncCtrl.crop_height)
	{
		deg_Printf("mjpA Encode : crop mode not support scaler down\n");
		return false;	
	}
	if(mjpAEncCtrl.crop_width < mjpAEncCtrl.csi_ratio_width || mjpAEncCtrl.crop_height < mjpAEncCtrl.csi_ratio_height)
	{
		mjpAEncCtrl.encode_crop_scaler = 1;
	}else{
		mjpAEncCtrl.encode_crop_scaler = 0;
	}


	mjpAEncCtrl.type 			= MJPEG_TYPE_PHOTO;
	mjpAEncCtrl.timestamp 		= timestamp;
	mjpAEncCtrl.encode_lcd_mode = 0;
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
	mjpAEncCtrl.mjpeg_width 	= target_w;
	mjpAEncCtrl.mjpeg_height 	= target_h;
	mjpAEncCtrl.manual_kick_noisr = 0;
	mjpAEncCtrl.manual_pkg_start = 0;
	if(false == hx330x_csiMJPEGCrop(mjpAEncCtrl.csi_width,mjpAEncCtrl.csi_height,
									mjpAEncCtrl.crop_sx,mjpAEncCtrl.crop_sy,
									mjpAEncCtrl.crop_ex,mjpAEncCtrl.crop_ey))
	{
		deg_Printf("mjpA Encode : I2J CROP fail\n");
		return false;
	}

	if((SDRAM_SIZE == SDRAM_SIZE_8M) ||(mjpAEncCtrl.csi_width <= 640 && mjpAEncCtrl.csi_height <= 480)) //VGA摄像头
	{
		int enle_check = hal_mjp_enle_check(mjpAEncCtrl.csi_ratio_width, mjpAEncCtrl.csi_ratio_height, target_w, target_h, quality,timestamp,MJPEG_LLPKG_DOUBLEBUF);
		if(enle_check < 0)
		{
			deg_Printf("hal_mjp_enle_check fail:%d\n",enle_check);
			return false;
		}else if(enle_check > 0)
		{
			mjpAEncCtrl.jpg_encode_type = ENC_MODE_LLPKG;
		}
		else {
			mjpAEncCtrl.jpg_encode_type = ENC_MODE_PKG; //手动模式
		}
	}else //720P摄像头
	{
		if(hardware_setup.usb_host_en == 0 && target_w > 1920)
		{
			mjpAEncCtrl.jpg_encode_type = ENC_MODE_STEP;
		}
		//else{
		//	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
		//}
	}

	mjpAEncCtrl.qulity 			= hal_mjpA_QualityCheck(quality);
	if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_STEP)
	{

		if(hal_mjp_step_start(mjpAEncCtrl.crop_width, mjpAEncCtrl.crop_height,
							  mjpAEncCtrl.csi_ratio_width, mjpAEncCtrl.csi_ratio_height,
							  target_w, target_h, timestamp,  mjpAEncCtrl.qulity,MJPEG_STEP_SRC_USELINE, MJPEG_STEP_SRC_Q) < 0)
		{
			deg_Printf("hal_mjp_step_start fail\n");
			return false;
		}
		mjpAEncCtrl.manual_pkg_start = 1;
		hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
		hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	hal_mjpA_Start);
		hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	hal_mjpA_Restart);
		hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
		hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
		hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,hal_mjpA_Restart);	
		hx330x_mjpA_EncodeISRRegister(hal_mjp_Step0_EncodeCallback);
	}else{
		//hal_mjpA_EncodeInit();
		if(hal_mjpA_LineBuf_MenInit(MJPEG_TYPE_PHOTO, target_w,target_h) == false)
		{
			deg_Printf("mjpA encode : linebuf malloc fail\n");
			return false;
		}
		if(hal_mjpA_buf_MenInit(MJPEG_TYPE_PHOTO) == false) // mjpeg memory malloc
		{
			deg_Printf("mjpA encode : mjpbuf malloc fail\n");
			return false;
		}

		hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);
		u32 line_size = (u32)mjpAEncCtrl.line_height * (u32)mjpAEncCtrl.line_width;
		u32 line_width = (mjpAEncCtrl.crop_width + 0x1f)&~0x1f;
		u32 line_height = line_size/line_width;
		hx330x_csiMJPEGFrameSet(mjpAEncCtrl.ybuffer,mjpAEncCtrl.uvbuffer,line_height,line_width);
		if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_NORMAL)
		{
			mjpAEncCtrl.curLen =  mjpAEncCtrl.mjpsize;
			mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
			hx330x_mjpA_EncodeInit(0,0);
			//hx330x_mjpA_EncodeInit(0,1); //auto, video q
			hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.crop_width,mjpAEncCtrl.crop_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
			hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
			hx330x_mjpA_EncodeInfoSet(0);
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curBuffer + mjpAEncCtrl.curLen);
			hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
			hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	hal_mjpA_Start);
			hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	hal_mjpA_Restart);
			hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
			hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
			hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,hal_mjpA_Restart);	
			hx330x_mjpA_EncodeISRRegister(hal_mjpA_AutoEncodeCallback);	
		}else
		{
			mjpAEncCtrl.manual_pkg_start = 1;
			hx330x_mjpA_EncodeInit(1,0); //manual, photo q
			hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
			hx330x_mjpA_EncodeInfoSet(0);
			if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_PKG)
			{
				mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize/2);
				hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.crop_width,mjpAEncCtrl.crop_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
				hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
				hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
				
			}else{
				mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize);
			}
			//hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer, mjpAEncCtrl.curBuffer + mjpAEncCtrl.curLen);
			hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_mjpAEncodeKickManual);
			hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
			hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
			hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
			hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
			hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
			hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodePhotoDoneManual);
			hx330x_csiMJPEGDmaEnable(1);
		}	
	}
	hx330x_csiEnable(1);
	return true;
}
#if 1//MJP_ENC_USE_LCD
/*******************************************************************************
* Function Name  : hal_mjpA_MemInit
* Description    : hal layer .mjpeg memory initial
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_LineBuf_lcd_MenInit(u8 type,u16 width,u16 height)
{
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	mjpAEncCtrl.line_width = (mjpAEncCtrl.lcd_width > width)? width : mjpAEncCtrl.lcd_width;
	mjpAEncCtrl.line_height = mjpAEncCtrl.lcd_height;
	mjpAEncCtrl.line_width = (mjpAEncCtrl.line_width + 0x1f)&~0x1f;
	deg_Printf("MJPA LCD LINE W:%d,H:%d\n",mjpAEncCtrl.line_width,mjpAEncCtrl.line_height);
	if(mjpAEncCtrl.ybuffer == 0)
	{
		mjpAEncCtrl.ybuffer = (u32)hal_sysMemMalloc(mjpAEncCtrl.line_width*mjpAEncCtrl.line_height*3/2);
		if(mjpAEncCtrl.ybuffer == 0)
		{
			return false;
		}
		mjpAEncCtrl.uvbuffer = mjpAEncCtrl.ybuffer + mjpAEncCtrl.line_width*mjpAEncCtrl.line_height;
		deg_Printf("mjpAEncCtrl.ybuffer:%x\n",mjpAEncCtrl.ybuffer);
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodeVideoKickLcd(u32 y_addr, u32 uv_addr)
{
	if(mjpAEncCtrl.curBuffer)
		return;
	//deg_Printf("[%d,%d]\n",mjpAEncCtrl.linebuf_dma_ch,mjpAEncCtrl.linebuf_encode_ch );
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.ybuffer, (void*)y_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height);
		hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.uvbuffer, (void*)uv_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height/2);
		//hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);	
		hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
		hx330x_mjpA_Encode_manual_on();
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
}


/*******************************************************************************
* Function Name  : hal_mjpA_EncLcd_Video_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_EncLcd_Video_Start(u8 type,u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	if(type != MJPEG_TYPE_VIDEO)
	{
		return false;
	}
	//hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_SensorRatioResolutionGet(&mjpAEncCtrl.csi_ratio_width,&mjpAEncCtrl.csi_ratio_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	mjpAEncCtrl.type 			= type;
	mjpAEncCtrl.timestamp 		= timestamp;
	mjpAEncCtrl.encode_lcd_mode = 1;
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
	mjpAEncCtrl.mjpeg_width 	= target_w;
	mjpAEncCtrl.mjpeg_height 	= target_h;
	mjpAEncCtrl.manual_kick_noisr = 0;
	mjpAEncCtrl.manual_pkg_start = 0;
	if((mjpAEncCtrl.lcd_width > target_w) && (mjpAEncCtrl.lcd_height > target_h)) //scaler down
	{
		deg_Printf("mjpA Encode : LCD Scaler down fail\n");
		return false;
	}	
	if(hal_mjpA_LineBuf_lcd_MenInit(type, target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit(type) == false) // mjpeg memory malloc
	{
		hal_mjpA_MemUninit();
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		return false;
	}
	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);
	mjpAEncCtrl.qulity = hal_mjpA_QualityCheck(quality);
	mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize/2);
	deg_Printf("mjpsize:%x,curLen:%x\n",mjpAEncCtrl.mjpsize, mjpAEncCtrl.curLen );

	hx330x_mjpA_EncodeInit(1,(HAL_CFG_MJPEG_HIGH_QT?2:1)); 	
	hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
	hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	hx330x_mjpA_EncodeInfoSet(1);
	hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);	
	hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
	

	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_MJPA_EncodeLcdPreRegister(NULL);
	hx330x_MJPA_EncodeLcdBackRegister(NULL);
	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodeVideoDoneManual);
	hx330x_MJPA_EncodeLcdPreRegister(hal_mjpAEncodeVideoKickLcd);

	
	hx330x_csiEnable(1);
	return true;
}
/*******************************************************************************
* Function Name  : hal_mjpAEncodeKickManual
* Description    : hal layer .mjpeg callback function for mjpeg irq encode
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_mjpAEncodePhotoKickLcd(u32 y_addr, u32 uv_addr)
{
	if(mjpAEncCtrl.curBuffer)
		return;
	//deg_Printf("[%d,%d]\n",mjpAEncCtrl.linebuf_dma_ch,mjpAEncCtrl.linebuf_encode_ch );
	mjpAEncCtrl.curBuffer = hal_streamMalloc(&mjpAEncCtrl.vids,mjpAEncCtrl.curLen);
	if(mjpAEncCtrl.curBuffer)
	{
		hx330x_MJPA_EncodeLcdPreRegister(NULL);
		hx330x_MJPA_EncodeLcdBackRegister(NULL);
		hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.ybuffer, (void*)y_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height);
		hx330x_mcpy0_sdram2gram_nocache((void*)mjpAEncCtrl.uvbuffer, (void*)uv_addr, mjpAEncCtrl.line_width * mjpAEncCtrl.line_height/2);
		//hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);	
		if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_LLPKG)
		{
			hal_mjp_enle_manual_kickstart(mjpAEncCtrl.ybuffer, mjpAEncCtrl.uvbuffer, mjpAEncCtrl.curBuffer, mjpAEncCtrl.curLen);
		}else
		{
			hx330x_mjpA_EncodeBufferSet(mjpAEncCtrl.curBuffer,mjpAEncCtrl.curBuffer+mjpAEncCtrl.curLen);
			hx330x_mjpA_Encode_manual_on();
		}
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
		//debg("M");
	}
}
/*******************************************************************************
* Function Name  : hal_mjpA_EncLcd_Photo_Start
* Description    : hal layer .mjpeg initial
* Input          : 
				   u16 win_w : output width
				   u16 win_h : output height
				   u8  quality: encode quality
				   u8  timestamp
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_mjpA_EncLcd_Photo_Start(u16 target_w, u16 target_h, u8 quality, u8 timestamp)
{
	//hx330x_bytes_memset((u8*)&mjpAEncCtrl, 0, sizeof(mjpAEncCtrl));
	hal_SensorResolutionGet(&mjpAEncCtrl.csi_width,&mjpAEncCtrl.csi_height);
	hal_SensorRatioResolutionGet(&mjpAEncCtrl.csi_ratio_width,&mjpAEncCtrl.csi_ratio_height);
	hal_lcdGetVideoRatioResolution(&mjpAEncCtrl.lcd_width,&mjpAEncCtrl.lcd_height);
	mjpAEncCtrl.type 			= MJPEG_TYPE_PHOTO;
	mjpAEncCtrl.timestamp 		= timestamp;
	mjpAEncCtrl.encode_lcd_mode = 1;
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
	mjpAEncCtrl.mjpeg_width 	= target_w;
	mjpAEncCtrl.mjpeg_height 	= target_h;
	mjpAEncCtrl.manual_pkg_start= 1;
	mjpAEncCtrl.manual_kick_noisr = 0;
	if((mjpAEncCtrl.lcd_width > target_w) && (mjpAEncCtrl.lcd_height > target_h)) //scaler down
	{
		deg_Printf("mjpA Encode : LCD Scaler down fail\n");
		return false;
	}	
	int enle_check = hal_mjp_enle_check(mjpAEncCtrl.lcd_width, mjpAEncCtrl.lcd_height, target_w, target_h, quality,timestamp,MJPEG_LLPKG_DOUBLEBUF);

	if(enle_check < 0)
	{
		deg_Printf("hal_mjp_enle_check fail:%d\n",enle_check);
		return false;
	}else if(enle_check > 0)
	{
		mjpAEncCtrl.jpg_encode_type = ENC_MODE_LLPKG;
	}else
	{
		mjpAEncCtrl.jpg_encode_type = ENC_MODE_PKG;
	}
	mjpAEncCtrl.jpg_encode_type = ENC_MODE_NORMAL;
	if(hal_mjpA_LineBuf_lcd_MenInit(MJPEG_TYPE_PHOTO, target_w,target_h) == false)
	{
		deg_Printf("mjpA encode : linebuf malloc fail\n");
		return false;
	}
	if(hal_mjpA_buf_MenInit(MJPEG_TYPE_PHOTO) == false) // mjpeg memory malloc
	{
		hal_mjpA_MemUninit();
		deg_Printf("mjpA encode : mjpbuf malloc fail\n");
		return false;
	}
	hal_streamInit(&mjpAEncCtrl.vids,mjpAEncCtrl.mjpegNode,MJPEG_ITEM_NUM,(u32)mjpAEncCtrl.mjpbuf,mjpAEncCtrl.mjpsize);
	mjpAEncCtrl.qulity = hal_mjpA_QualityCheck(quality);
	if(mjpAEncCtrl.jpg_encode_type == ENC_MODE_PKG)
	{
		mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize/2);
	}else
	{
		mjpAEncCtrl.curLen = (mjpAEncCtrl.mjpsize);
	}
	deg_Printf("mjpsize:%x,curLen:%x\n",mjpAEncCtrl.mjpsize, mjpAEncCtrl.curLen );

	hx330x_mjpA_EncodeInit(1,0); //manual, photo q 	
	hx330x_mjpA_EncodeQuilitySet(mjpAEncCtrl.qulity);
	hx330x_mjpA_EncodeInfoSet(0);
	if(mjpAEncCtrl.jpg_encode_type != ENC_MODE_LLPKG)
	{
		hx330x_mjpA_EncodeSizeSet(mjpAEncCtrl.lcd_width,mjpAEncCtrl.lcd_height, mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height);
		hal_jpg_watermarkStart(mjpAEncCtrl.mjpeg_width, mjpAEncCtrl.mjpeg_height,timestamp);
		hx330x_mjpA_Encode_inlinebuf_init((u32)mjpAEncCtrl.ybuffer,(u32)mjpAEncCtrl.uvbuffer);
	}	
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_SEN_STATE_INT,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_JBF_DMAWR_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFIBF_ERR,	NULL);
	hx330x_csiISRRegiser(CSI_IRQ_WIFI_FRAME_END,NULL);
	hx330x_csiISRRegiser(CSI_JE_LB_OVERFLOW_ERR,NULL);
	hx330x_MJPA_EncodeLcdPreRegister(NULL);
	hx330x_MJPA_EncodeLcdBackRegister(NULL);
	hx330x_mjpA_EncodeISRRegister(hal_mjpAEncodePhotoDoneManual);
	hx330x_MJPA_EncodeLcdPreRegister(hal_mjpAEncodePhotoKickLcd);

	hx330x_csiEnable(1);
	return true;
}
#endif

/*******************************************************************************
* Function Name  : hal_mjpA_photo_encode_type
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
u32 hal_mjpA_photo_encode_type(void)
{
	return mjpAEncCtrl.jpg_encode_type;
}
/*******************************************************************************
* Function Name  : hal_mjpegRawBufferfree
* Description    : hal layer.set mjpeg fram raw data
* Input          : void *buffer : frame buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpA_RawBufferfree(void)
{
	hal_streamfree(&mjpAEncCtrl.vids);
}
/*******************************************************************************
* Function Name  : hal_mjpA_RawBufferGet
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RawBufferGet(u32 *len,u32 *sync, u32 *sync_next)
{
	u8* buff;
	u32 size = 0;

	if(hal_streamOut(&mjpAEncCtrl.vids,(u32 *)&buff,(u32 *)&size, sync, sync_next) == false)
		return NULL;
	if(size == 0){
		hal_mjpA_RawBufferfree();
		deg_Printf("jlen=0\n");
		return NULL;
	}
	hx330x_sysDcacheFlush((u32)buff,16);
	
	if((buff[0] != 0xff) || (buff[1] != 0xd8)){
		if((buff[8] != 0xff) || (buff[9] != 0xd8)){
			hal_mjpA_RawBufferfree();
			deg_Printf("jhead err:");
			deg_PrintfBuf(buff,16);
			return NULL;	
		}	
	}
	if(len)
		*len = size;

	return ((void *)buff);
}
/*******************************************************************************
* Function Name  : sync_flag
* Description    : hal layer.check raw buffer addr &length
* Input          : u32 *len : buff len
 				   u32 *sync: current buf frame stamp
				   u32 *sync_next: current buf next frame stamp
* Output         : None
* Return         : void * : buffer addr
*******************************************************************************/
void *hal_mjpA_RkgBufferGet(u32 *len,u32 *sync, u32 *sync_next)
{
	u8* buff;
	u32 size = 0;

	if(hal_streamOut(&mjpAEncCtrl.vids,(u32 *)&buff,(u32 *)&size, sync, sync_next) == false)
		return NULL;
	if(size == 0){
		hal_mjpA_RawBufferfree();
		deg_Printf("jlen=0\n");
		return NULL;
	}
	hx330x_sysDcacheFlush((u32)buff,16);
	if(len)
		*len = size;

	return ((void *)buff);
}
/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_prefull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is pre_full
*******************************************************************************/
bool hal_mjpA_Buffer_prefull(void)
{
	//if(mjpAEncCtrl.vids.head_size > (mjpAEncCtrl.mjpsize/5*2))
	//	return true;
	u32 temp = mjpAEncCtrl.vids.head_size;
	
	//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
	if(temp > ((mjpAEncCtrl.mjpsize)/2))
	{
		//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
		return true;
	}
	return false;
}
/*******************************************************************************
* Function Name  : hal_mjpA_Buffer_halffull
* Description    : hal layer.mjpeg buffer pre full(2/3) just
* Input          : None
* Output         : None
* Return         : true:mjpeg buffer is half_full
*******************************************************************************/
bool hal_mjpA_Buffer_halffull(void)
{
	//if(mjpAEncCtrl.vids.head_size > (mjpAEncCtrl.mjpsize/5))
	//	return true;
	u32 temp = mjpAEncCtrl.vids.head_size;
	if(temp > ((mjpAEncCtrl.mjpsize)/4))
		return true;
	//deg_Printf("[A:%d,%d]\n",temp,mjpAEncCtrl.mjpsize);
	return false;
}
