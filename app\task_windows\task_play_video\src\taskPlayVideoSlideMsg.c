/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayVideoSlideWin.c"

/*******************************************************************************
* Function Name  : playVideoSlideKeyMsgOk
* Description    : playVideoSlideKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskPlayVideoSlidePause();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideKeyMsgUp
* Description    : playVideoSlideKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideKeyMsgDown
* Description    : playVideoSlideKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideKeyMsgMenu
* Description    : playVideoSlideKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskPlayVideoSlideClose();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideKeyMsgMode
* Description    : playVideoSlideKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		taskPlayVideoSlideClose();
		app_taskChange();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideSysMsgSD
* Description    : playVideoSlideSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) // sdc out when recording
	{
		//taskPlayVideoSlideClose();
		
		uiOpenWindow(&noFileWindow, 0, 0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideSysMsgUSB
* Description    : playVideoSlideSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideSysMsgBattery
* Description    : playVideoSlideSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideSysMsg1S
* Description    : playVideoSlideSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideSysMsgPlay
* Description    : playVideoSlideSysMsgPlay
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideSysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	u32 playState = MSG_PLAY_MAX;
	if(parameNum == 1)
		playState = parame[0];
	if(playState == MSG_PLAY_START)	
	{
		playVideoSlideFileNameShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideOpenWin
* Description    : playVideoSlideOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(taskPlayVideoSlideOpen() < 0)
		uiWinDestroy(&handle);
	else
		taskPlayVideoSlideStart();
	deg_Printf("[WIN]playVideoSlideOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideCloseWin
* Description    : playVideoSlideCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	taskPlayVideoSlideClose();
	deg_Printf("[WIN]playVideoSlideCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideWinChildClose
* Description    : playVideoSlideWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	if(taskPlayVideoSlideOpen() < 0)
		uiWinDestroy(&handle);
	else
		taskPlayVideoSlideStart();
	deg_Printf("[WIN]playVideoSlideWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideTouchWin
* Description    : playVideoSlideTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		//deg_Printf("playVideoSlideTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0]== PLAYVIDEOSLIDE_TOUCH_ID)
		{
			taskPlayVideoSlidePause();
		}		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoSlideTouchSlideOff
* Description    : playVideoSlideTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoSlideTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
	{
		//deg_Printf("playVideoSlideTouchSlideOff, parame num error %d\n",parameNum);
		return 0;
	}
	if(parame[0] == TP_DIR_LEFT)
	{
		taskPlayVideoSlideClose();
		uiWinDestroy(&handle);
	}

	return 0;
}

ALIGNED(4) msgDealInfor playVideoSlideMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	playVideoSlideOpenWin},
	{SYS_CLOSE_WINDOW,	playVideoSlideCloseWin},
	{SYS_CHILE_COLSE,	playVideoSlideWinChildClose},
	{SYS_TOUCH_WINDOW,  playVideoSlideTouchWin},
	{SYS_TOUCH_SLIDE_OFF,playVideoSlideTouchSlideOff},
	{KEY_EVENT_OK,		playVideoSlideKeyMsgOk},
	{KEY_EVENT_UP,		playVideoSlideKeyMsgUp},
	{KEY_EVENT_DOWN,	playVideoSlideKeyMsgDown},
	{KEY_EVENT_MENU,	playVideoSlideKeyMsgMenu},
	{KEY_EVENT_MODE,	playVideoSlideKeyMsgMode},
	{SYS_EVENT_SDC,		playVideoSlideSysMsgSD},
	{SYS_EVENT_USBDEV,	playVideoSlideSysMsgUSB},
	{SYS_EVENT_BAT,		playVideoSlideSysMsgBattery},
	{SYS_EVENT_1S,		playVideoSlideSysMsg1S},
	{SYS_EVENT_PLAY,    playVideoSlideSysMsgPlay},
	{EVENT_MAX,NULL},
};

WINDOW(playVideoSlideWindow, playVideoSlideMsgDeal, playVideoSlideWin)



