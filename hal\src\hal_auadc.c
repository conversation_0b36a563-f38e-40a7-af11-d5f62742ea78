/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"


#define PCM_FRAME_DEBG				1


typedef struct AUADC_CTRL_S
{
	u8  ch_en;
	u8 	halPcmType;
	u8 	dropFlag;
    u8 	volume;
	
	u32 pcmMuteBuf;
    u32 pcmbuf;
	u32 pcmsize;
	u32 curBuffer;
	u32 nextBuffer;
	u32 curLen;
	Stream_Head_T auds;
	Stream_Node_T auadcNode[HAL_CFG_PCM_BUFFER_NUM];

}AUADC_CTRL_T;

ALIGNED(4) static AUADC_CTRL_T auadcCtrl,auadcCtrlB;




#if PCM_FRAME_DEBG
ALIGNED(4) volatile u32 auadccnt = 0;  //监控用
#endif



/*******************************************************************************
* Function Name  : hal_auadc_syncnt_clr
* Description    : hal_auadc_syncnt_clr
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
ALIGNED(4) static u32 pcmsizepersec;
ALIGNED(4) static volatile u32 tolpcmsize;
ALIGNED(4) static volatile u32 pcmseccnt;
ALIGNED(4) static volatile u32 tpcmsync;
ALIGNED(4) static volatile u32 tpcmsync_next;



static void hal_auadc_syncnt_clr(u32 frq)
{
	pcmsizepersec   = frq*2;
	tolpcmsize 		= 0;
	pcmseccnt		= 0;
	tpcmsync 		= 0;
	tpcmsync_next   = (auadcCtrl.curLen-8)*HAL_AVI_FRAMRATE/pcmsizepersec;
}
/*******************************************************************************
* Function Name  : hal_auadc_stamp_cal
* Description    : hal_auadc_stamp_cal
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_auadc_stamp_cal(u32 framesize)
{
	tolpcmsize += framesize;
	while(tolpcmsize >= pcmsizepersec)
	{
		pcmseccnt++;
		tolpcmsize -= pcmsizepersec;
	}
	tpcmsync 		=  pcmseccnt*HAL_AVI_FRAMRATE + tolpcmsize*HAL_AVI_FRAMRATE/pcmsizepersec;
	tpcmsync_next   =  pcmseccnt*HAL_AVI_FRAMRATE + (tolpcmsize+framesize)*HAL_AVI_FRAMRATE/pcmsizepersec;
}
/*******************************************************************************
* Function Name  : hal_auadc_stamp_out
* Description    : cal frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: current frame time stamp
*******************************************************************************/
u32 hal_auadc_stamp_out(void)
{
	return tpcmsync;
}
/*******************************************************************************
* Function Name  : hal_auadc_stamp_next
* Description    : cal next frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: next frame time stamp
*******************************************************************************/
u32 hal_auadc_stamp_next(void)
{
	return tpcmsync_next;
}
/*******************************************************************************
* Function Name  : hal_auadc_stamp_next
* Description    : cal next frame cnt stamp
* Input          : 
* Output         : None
* Return         : u32: next frame time stamp
*******************************************************************************/
void hal_auadcInit(void)
{	
#if CURRENT_CHIP == FPGA
	hx330x_auadcInit(0);
#else
	hx330x_auadcInit(1);
#endif
	hal_auadc_syncnt_clr(0);
	memset((u8*)&auadcCtrl,0,sizeof(AUADC_CTRL_T)); 
	memset((u8*)&auadcCtrlB,0,sizeof(AUADC_CTRL_T));
}
/*******************************************************************************
* Function Name  : hal_auadcMemInit
* Description    : hal layer.adadc memory initial
* Input          : 
* Output         : None
* Return         : int
*******************************************************************************/
bool hal_auadcMemInit(void)
{
//	hal_auadc_syncnt_clr();
	if(auadcCtrl.pcmbuf == 0)
	{
		auadcCtrl.pcmbuf = (u32)hal_sysMemMalloc(auadcCtrl.curLen*HAL_CFG_PCM_BUFFER_NUM);	
		if(auadcCtrl.pcmbuf == 0)
			return false;
		auadcCtrl.pcmsize = auadcCtrl.curLen*HAL_CFG_PCM_BUFFER_NUM;
		deg_Printf("HAL :<INFO> AUADC addr = 0x%x,size = %d\n",auadcCtrl.pcmbuf,auadcCtrl.pcmsize);
	}
	if(auadcCtrl.pcmMuteBuf == 0)
	{
		auadcCtrl.pcmMuteBuf = (u32)hal_sysMemMalloc(auadcCtrl.curLen);	
		if(auadcCtrl.pcmMuteBuf == 0)
			return false;		
	}
	auadcCtrlB.ch_en = 0;
	return true;
}
bool hal_auadcMemInitB(void)
{
//	hal_auadc_syncnt_clr();
	if(auadcCtrlB.pcmbuf==0)
	{
		auadcCtrlB.pcmbuf = (u32)hal_sysMemMalloc(auadcCtrlB.curLen*HAL_CFG_PCM_BUFFER_NUM);	
		if(auadcCtrlB.pcmbuf == 0)
			return false;
		auadcCtrlB.pcmsize = auadcCtrlB.curLen*HAL_CFG_PCM_BUFFER_NUM;
		deg_Printf("HAL :<INFO> AUADC B addr = 0x%x,size = %d\n",auadcCtrlB.pcmbuf,auadcCtrlB.pcmsize);
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_auadc_pcmsize_get
* Description    : hal layer.adadc memory buf size get
* Input          : 
* Output         : None
* Return         : int
*******************************************************************************/
u32 hal_auadc_pcmsize_get(void)
{
	return auadcCtrl.pcmsize;
}
/*******************************************************************************
* Function Name  : hal_auadcMemUninit
* Description    : hal layer.adadc memory uninitial
* Input          : 
* Output         : None
* Return         : int
*******************************************************************************/
void hal_auadcMemUninit(void)
{
	if(auadcCtrl.pcmbuf)
		hal_sysMemFree((void *)auadcCtrl.pcmbuf);
	if(auadcCtrl.pcmMuteBuf)
		hal_sysMemFree((void *)auadcCtrl.pcmMuteBuf);
	auadcCtrl.pcmbuf = 0;
	auadcCtrl.pcmsize = 0;
	auadcCtrl.pcmMuteBuf = 0;
}
void hal_auadcMemUninitB(void)
{
	auadcCtrlB.ch_en = 0;
	if(auadcCtrlB.pcmbuf)
		hal_sysMemFree((void *)auadcCtrlB.pcmbuf);
	auadcCtrlB.pcmbuf = 0;
	auadcCtrlB.pcmsize = 0;
}
/*******************************************************************************
* Function Name  : hal_auadcBufferSetAVI
* Description    : hal layer.adadc bufer change for avi
* Input          : QDT_PCM  *p : queue 
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_auadcBufferSet(u8 type,u32 buffer,u32 len)
{
	u32 addr,size;

	if((!buffer)||(!len))
		return ;
	//deg_Printf("auadc set:%x,%x\n",buffer,len);
    if(type == PCM_REC_TYPE_AVI)
    {
		addr = buffer + 8;
		size = len - 8;
    }
	else
	{
		addr = buffer;
		size = len;
	}

	hx330x_auadcBufferSet(addr,size);
			
}
/*******************************************************************************
* Function Name  : hal_auadcCallBackHalf
* Description    : hal layer.adadc half flag callback
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
static void hal_auadcCallBackHalf(void)
{
	//auadcCtrl.curBuffer = auadcCtrl.curBuffer;
	u32 buffer 		= hal_streamMalloc(&auadcCtrl.auds,auadcCtrl.curLen);  //申请下一帧buf
	//deg_Printf("S");
	if(buffer)
	{
		auadcCtrl.dropFlag = 0;
		auadcCtrl.nextBuffer = buffer;
	}
	else
	{
		auadcCtrl.dropFlag = 1;
		auadcCtrl.nextBuffer = auadcCtrl.curBuffer;
		//hal_streamMalloc(&auadcCtrl.auds,auadcCtrl.curLen);
		//buffer = auadcCtrl.curBuffer;
		deg_Printf("HAL : [AUADC]<WARNING> drop\n");
	}	
    hal_auadcBufferSet(auadcCtrl.halPcmType,auadcCtrl.nextBuffer,auadcCtrl.curLen);
}
/*******************************************************************************
* Function Name  : hal_auadcCallBackEnd
* Description    : hal layer.adadc end flag callback
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
static void hal_auadcCallBackEnd(void)
{	
	if(auadcCtrl.halPcmType == PCM_REC_TYPE_AVI)
	{
		hal_auadc_stamp_cal(auadcCtrl.curLen-8);
	}
	u32 tadcsync 		= hal_auadc_stamp_out();
	u32 tadcsync_next 	= hal_auadc_stamp_next();
#if PCM_FRAME_DEBG
	auadccnt++;
#endif
	//deg_Printf("E");
    if(auadcCtrl.dropFlag == 0) //dropFlag == 0时，curBuffer不为0
	{
		//deg_Printf("+");
		hal_streamIn(&auadcCtrl.auds,auadcCtrl.curLen,tadcsync,tadcsync_next);

		if(auadcCtrlB.ch_en){
			auadcCtrlB.curBuffer = hal_streamMalloc(&auadcCtrlB.auds,auadcCtrl.curLen);
			if(auadcCtrlB.curBuffer)
			{
				//deg_Printf("b:%x\n",curBuffer_temp);
				hx330x_mcpy0_sdram2gram_nocache((void *)auadcCtrlB.curBuffer,(void *)auadcCtrl.curBuffer,auadcCtrl.curLen);
				hal_streamIn(&auadcCtrlB.auds,auadcCtrlB.curLen,tadcsync,tadcsync_next);
			}
		}
	}
	auadcCtrl.curBuffer  = auadcCtrl.nextBuffer;
	auadcCtrl.nextBuffer = 0;
}
/*******************************************************************************
* Function Name  : hal_auadc_cnt
* Description    : hal_auadc_cnt
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadc_cnt(void)
{
#if PCM_FRAME_DEBG	
	debg("auadc cnt/sec:%d\n",auadccnt);
	auadccnt = 0;
#endif
}
/*******************************************************************************
* Function Name  : hal_auadcmutebuf_get
* Description    : hal_auadcmutebuf_get
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
u32 hal_auadcmutebuf_get(void)
{
	return auadcCtrl.pcmMuteBuf;
}
/*******************************************************************************
* Function Name  : hal_auadcStart
* Description    : hal layer.auadc start 
* Input          : u32 type: PCM_REC_TYPE_AVI, PCM_REC_TYPE_WAV, PCM_REC_TYPE_UAC
* 				   u32 frq: 8000, 16000,24000,32000
* 				   u32 volume: 0~MIC_VOLUME_MAX
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_auadcStart(u32 type, u32 frq,u32 volume)
{
	if(type == PCM_REC_TYPE_UAC)
	{
		auadcCtrl.curLen = (frq*2)/1000;
	}
	else
	{
		auadcCtrl.curLen = (((frq*2)/PCM_REC_SYNC) + 0x1ff)&~0x1ff;
	}
	//deg_Printf("auadcCtrl.curLen:%d\n",auadcCtrl.curLen);
	if(hal_auadcMemInit() == false)
	{
		deg_Printf("HAL : [AUADC]<ERROR> memory malloc fail.\n");
		return false;
	}		
	auadcCtrl.halPcmType = type;
    hal_streamInit(&auadcCtrl.auds,auadcCtrl.auadcNode,HAL_CFG_PCM_BUFFER_NUM,(u32)auadcCtrl.pcmbuf,auadcCtrl.pcmsize);
	auadcCtrl.curBuffer = hal_streamMalloc(&auadcCtrl.auds,auadcCtrl.curLen);
	if(auadcCtrl.curBuffer == 0)
	{
		deg_Printf("HAL : [AUADC]<ERROR> stream malloc fail.\n");
		hal_auadcMemUninit();
		return false;
	}
	auadcCtrl.nextBuffer = 0;
	auadcCtrl.dropFlag = 0;	
	auadcCtrl.volume = volume;
	memset((u8 *)auadcCtrl.pcmMuteBuf,0,auadcCtrl.curLen );	
	hal_auadc_syncnt_clr(frq);
    	
	if(hx330x_auadcSetSampleSet(frq)==false)
	{
		deg_Printf("HAL : [AUADC]<ERROR> set frq %d fail.\n",frq);
		hal_auadcMemUninit();
		return false;
	}
	hx330x_auadcGainSet(auadcCtrl.volume);//u8 again
	if(type == PCM_REC_TYPE_UAC)
	{
		hx330x_auadcAGCEnable(0);	
	}else{
		hx330x_auadcAGCEnable(1);	
	}
	hal_auadcBufferSet(auadcCtrl.halPcmType,auadcCtrl.curBuffer,auadcCtrl.curLen);
	hx330x_auadcHalfIRQRegister(hal_auadcCallBackHalf);
    hx330x_auadcEndIRQRegister(hal_auadcCallBackEnd);	
	
	hx330x_auadcEnable(1);
	return true;
}
bool hal_auadcStartB(u32 type, u32 frq,u32 volume)
{
	if(type == PCM_REC_TYPE_UAC)
	{
		auadcCtrlB.curLen = (frq*2)/1000;
	}
	else
	{
		auadcCtrlB.curLen = (((frq*2)/PCM_REC_SYNC) + 0x1ff)&~0x1ff;
	}	
	if(hal_auadcMemInitB() == false)
	{
		debg("HAL : [AUADC B]<ERROR> memory malloc fial.\n");
		return false;
	}

  
	auadcCtrlB.volume = volume;

    hal_streamInit(&auadcCtrlB.auds,auadcCtrlB.auadcNode,HAL_CFG_PCM_BUFFER_NUM,(u32)auadcCtrlB.pcmbuf,auadcCtrlB.pcmsize);
	
	auadcCtrlB.ch_en = 1;
    return true;
}
/*******************************************************************************
* Function Name  : hal_auadcBufferGet
* Description    : hal layer.get pcm raw data addr & length
* Input          : u32 *len : buffer length
*                  u32 *syncnt: current frame sync stamp
				   u32 *syncnt_next: next frame sync stamp
* Output         : None
* Return         : void* : return buffer addr
*******************************************************************************/
void *hal_auadcBufferGet(u32 *len,u32 *syncnt, u32 *syncnt_next)
{
	u32 buffer;
	//*len         = 0;
	//*syncnt      = 0;
	//*syncnt_next = 0;

	if(hal_streamOut(&auadcCtrl.auds,&buffer,len,syncnt,syncnt_next) == false)
		return NULL;
	if(auadcCtrl.volume == 0)
	{
		//buffer = auadcCtrl.pcmbuf;
		buffer =(u32)auadcCtrl.pcmMuteBuf;
		//size = PCM_SHEET_SIZE;
	}
	return ((void *)buffer);
}
/*******************************************************************************
* Function Name  : hal_auadcBufferRelease
* Description    : hal layer.set pcm raw data addr & length
* Input          : void *buffer : buffer addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadcBufferRelease(void)
{
	//if(buffer)
	{
		//debg("-");
		hal_streamfree(&auadcCtrl.auds);
	}	
}
/*******************************************************************************
* Function Name  : hal_auadcBufferGetB
* Description    : hal layer.get pcm raw data addr & length
* Input          : u32 *len : buffer length
*                  u32 *syncnt: current frame sync stamp
				   u32 *syncnt_next: next frame sync stamp
* Output         : None
* Return         : void* : return buffer addr
*******************************************************************************/
void *hal_auadcBufferGetB(u32 *len,u32 *syncnt, u32 *syncnt_next)
{
	u32 buffer;
	if(hal_streamOut(&auadcCtrlB.auds,&buffer,len,syncnt,syncnt_next) == false)
		return NULL;
	if(auadcCtrlB.volume==0)
	{
		//buffer = auadcCtrl.pcmbuf;
		buffer =(u32)auadcCtrl.pcmMuteBuf;
		//size = PCM_SHEET_SIZE;
	}
	return ((void *)buffer);
}
/*******************************************************************************
* Function Name  : hal_auadcBufferReleaseB
* Description    : hal layer.set pcm raw data addr & length
* Input          : void *buffer : buffer addr
* Output         : None
* Return         : none
*******************************************************************************/
void hal_auadcBufferReleaseB(void)
{
	//if(buffer)
	{
		hal_streamfree(&auadcCtrlB.auds);	
	}	
}

/*******************************************************************************
* Function Name  : hal_auadcStop
* Description    : hal layer.auadc stop
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_auadcStop(void)
{
	hal_auadc_syncnt_clr(0);
	hx330x_auadcEnable(0);
	hx330x_auadcHalfIRQRegister(NULL);
    hx330x_auadcEndIRQRegister(NULL);
	hal_auadcMemUninit();
	
	hal_auadcMemUninitB();
}
/*******************************************************************************
* Function Name  : hal_adcBuffer_prefull
* Description    : hal layer.auadc stop
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_adcBuffer_prefull(void)
{
	u32 size  = auadcCtrl.auds.head_size;
	//deg_Printf("[PA:%d,%d]\n",size,auadcCtrl.pcmsize);
	if(size > (auadcCtrl.pcmsize/3))
	{
		//deg_Printf("[PA:%d,%d]\n",size,auadcCtrl.pcmsize);
		return true;
	}
	return false;
}
bool hal_adcBufferB_prefull(void)
{
	u32 size  = auadcCtrlB.auds.head_size;
	//deg_Printf("[PB:%d,%d]\n",size,auadcCtrlB.pcmsize);
	if(size > (auadcCtrlB.pcmsize/3))
	{
		//deg_Printf("[PB:%d,%d]\n",size,auadcCtrlB.pcmsize);
		return true;
	}
	return false;
}
void hal_adc_volume_set(int volume)
{
	auadcCtrl.volume = volume;
}
void hal_adc_volume_setB(int volume)
{
	auadcCtrlB.volume = volume;
}