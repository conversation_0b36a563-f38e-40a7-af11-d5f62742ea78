/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  MP3_LAYER3_H
       #define  MP3_LAYER3_H

typedef enum {
	CH_FLAG_COUNT1TABLE_SELECT	= 0x01,
	CH_FLAG_SCALEFAC_SCALE		= 0x02,
	CH_FLAG_PREFLAG				= 0x04,
	CH_FLAG_MIXED_BLOCK			= 0x08,
}SIDEINFO_CH_FLAG;


/*******************************************************************************
* Function Name  : mp3_layer3_get_sideinfo
* Description    : mp3_layer3_get_sideinfo: main_data_begin, side_len
* Input          : u8 *buf, u32 size
* Output         : none
* Return         : none
*******************************************************************************/	 
int mp3_layer3_get_sideinfo(void);
/*******************************************************************************
* Function Name  : layer->III()
* Description    : decode a single Layer III frame
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int mp3_layer3_dec(void);
#endif
