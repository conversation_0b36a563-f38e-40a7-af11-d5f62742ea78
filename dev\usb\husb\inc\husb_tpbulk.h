/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_TPBULK_H_
#define USB_HOST_TPBULK_H_


#define CBW_SIGNATURE   		0x43425355
#define CSW_SIGNATURE  			0x43425355
#define CBW_TAG         		0xde24a660


#define	INQUIRY					0x12
#define READ_CAPACITY			0x25
#define READ_FORMAT_CAPACITIES	0x23
#define READ_DATA				0x28
#define WRITE_DATA				0x2A
#define MODE_SENSE				0x5A
#define REQUEST_SENSE			0x03
#define STOP_START_UNIT         0x1B


//struct for CBW
typedef struct _COMMAND_BLOCK_WRAPPER
{
    u32   cbw_signature;
    u32   cbw_tag;
    u32   cbw_data_xferlen;
    u8    cbw_flag;
    u8    cbw_lun;
    u8    cbw_cbdlen;
    u8    operation_code;
    u8    reserve;
    u8    cbw_cblba[4];
    u8    reserve1;
    u16   cbw_cblength;
    u8    control;
    u8    reserved[6];
} CBW, *PCBW;


//struct for  CSW
typedef struct _COMMAND_STATUS_WRAPPER
{
    u32   csw_signature;
    u32   csw_tag;
    u32   csw_data_residue;
    u8    csw_status;
} CSW, *PCSW;
/*******************************************************************************
* Function Name  : msc_init
* Description    : msc_init
* Input          : None
* Output         : None
* Return         : pHusb_handle->usbsta.device_sta |= USB_MSC_ATECH;
*******************************************************************************/
bool husb_msc_init(void *handle);

/*******************************************************************************
* Function Name  : enum_mass_dev
* Description    : enum_mass_dev
* Input          : None
* Output         : None
* Return         : pHusb_handle->usbsta.device_sta |= USB_MSC_TRAN;
*******************************************************************************/
bool enum_mass_dev(void *handle);

#endif /* USB_HOST_TPBULK_H_ */
