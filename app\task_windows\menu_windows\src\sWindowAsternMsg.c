/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowAsternWin.c"
/*******************************************************************************
* Function Name  : asternSysMsgUSBHost
* Description    : asternSysMsgUSBHost
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int asternSysMsgUSBHost(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 usbhost_state = USBHOST_STAT_MAX;
	if(parameNum == 1)
		usbhost_state = parame[0];
	if(usbhost_state >= USBHOST_STAT_MAX)
		return 0;
	switch(usbhost_state)
	{
		case USBHOST_STAT_NULL:
			SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
			uiWinDestroy(&handle);
			break;
		case USBHOST_STAT_SHOW:
			uiWinDestroy(&handle);
			break;
		default: break;
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgDown
* Description    : videoKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
#if (CURRENT_CHIP == FPGA)
static int asternKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		husb_api_usensor_asternset(0);
	}
	return 0;
}
#endif
/*******************************************************************************
* Function Name  : asternOpenWin
* Description    : asternOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int asternOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]asternOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : asternCloseWin
* Description    : asternCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int asternCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]asternCloseWin\n");
	if(app_taskCurId() == TASK_PLAY_VIDEO)
	{
		app_lcdShowWinModeCfg(LCDSHOW_WIN_DISABLE);
		app_lcdCsiVideoShowStop();
		hal_csiEnable(0);						
		deg_Printf("player : astern off\n");
	}
	else
	{
		app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : asternWinChildClose
* Description    : asternWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int asternWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]asternWinChildClose\n");
	return 0;
}



ALIGNED(4) msgDealInfor asternMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	asternOpenWin},
	{SYS_CLOSE_WINDOW,	asternCloseWin},
	{SYS_CHILE_COLSE,	asternWinChildClose},
	{SYS_EVENT_USBHOST, asternSysMsgUSBHost},
#if (CURRENT_CHIP == FPGA)
	{KEY_EVENT_DOWN,    asternKeyMsgDown},
#endif
	{EVENT_MAX,			NULL},
};

WINDOW(asternWindow,asternMsgDeal,asternWin)



