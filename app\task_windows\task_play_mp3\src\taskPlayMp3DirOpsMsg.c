/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#if FUN_MP3_PLAY_EN
#include "taskPlayMp3DirOpsWin.c"
static u32 cur_dir_item;
static char *cur_dir_name;
typedef enum{
	MP3_DIR_OPS_PLAY = 0,
	MP3_DIR_OPS_OPEN,
	MP3_DIR_OPS_PREDIR,
}MP3_DIR_OPS;
/*******************************************************************************
* Function Name  : getdelCurResInfor
* Description    : getdelCurResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getplayMp3DirOpsResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image = INVALID_RES_ID;
	if(str)
	{
		switch(item)
		{
			case MP3_DIR_OPS_PLAY:	 *str = RAM_ID_MAKE("PLAY"); break;
			case MP3_DIR_OPS_OPEN:	 *str = RAM_ID_MAKE("OPEN"); break;
			case MP3_DIR_OPS_PREDIR:  *str = RAM_ID_MAKE("PRE DIR"); break;
			default: *str = INVALID_RES_ID; break;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsOpenWin
* Description    : playMp3DirOpsOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3DirOpsOpenWin\n");
	
	cur_dir_item = parame[0];
	cur_dir_name = (char*)parame[1];
	uiItemManageSetHeightAvgGap(winItem(handle,PLAYMP3DIROPS_SELECT_ID),	Rh(40));
	uiItemManageCreateItem(		winItem(handle,PLAYMP3DIROPS_SELECT_ID),	uiItemCreateMenuOption,	getplayMp3DirOpsResInfor, 3);
	uiItemManageSetCharInfor(	winItem(handle,PLAYMP3DIROPS_SELECT_ID),	DEFAULT_FONT,	ALIGNMENT_CENTER,	R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,PLAYMP3DIROPS_SELECT_ID),	R_ID_PALETTE_White);
	uiItemManageSetUnselectColor(winItem(handle,PLAYMP3DIROPS_SELECT_ID),	R_ID_PALETTE_Gray);
	uiItemManageSetCurItem(		winItem(handle,PLAYMP3DIROPS_SELECT_ID),	0);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsCloseWin
* Description    : playMp3DirOpsCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3DirOpsCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsWinChildClose
* Description    : playMp3DirOpsWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playMp3DirOpsWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsKeyMsgOk
* Description    : playMp3DirOpsKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	int index;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;	
		//}
		item = uiItemManageGetCurrentItem(winItem(handle,PLAYMP3DIROPS_SELECT_ID));
		if(item == MP3_DIR_OPS_PREDIR)
		{
			if(taskPlayMp3ParentDirOpen() < 0)
			{
				uiOpenWindow(&tipsWindow,0,2,"[FAIL] ROOT DIR",2);
				return 0;
			}else
			{
				if(mp3_dec_sta() >= MP3_DEC_START)
					task_playMp3_op.playfirstIndex = task_playMp3_op.openListTotal;	//当前音乐播放完继续播放打开的列表
				else
					task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
			}
		}else
		{
			if(taskPlayMp3ChildDirOpen(cur_dir_item, cur_dir_name) >= 0)
			{
				//task_playMp3_op.playfirstIndex = INVALID_PLAY_INDEX;	//当前音乐播放完后停止
				task_playMp3_op.playfirstIndex = task_playMp3_op.openListTotal;	//当前音乐播放完继续播放打开的列表
				if(item == MP3_DIR_OPS_PLAY)
				{
					index = taskPlayMp3DirFindFile(SysCtrl.file_index, -1, 1); //try to find a mp3 file
					if(index >= 0)
					{
						taskPlayMp3MainStart(index);
					}else
					{
						if(mp3_dec_sta() >= MP3_DEC_START)
						{
							mp3_api_stop();
							task_com_keysound_play();	
							while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY){XOSTimeDly(1);}
						}
						uiOpenWindow(&tipsWindow,0,2,"[FAIL] NOT MP3 FILE",2);
						return 0;
					}
				}
			}else{
				uiOpenWindow(&tipsWindow,0,2,"[FAIL] OPEN DIR",2);
				return 0;
			}
		}
		uiWinDestroy(&handle);
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsKeyMsgUp
* Description    : playMp3DirOpsKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}
		uiItemManagePreItem(winItem(handle,PLAYMP3DIROPS_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsKeyMsgDown
* Description    : playMp3DirOpsKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		//if(mp3_dec_sta() < MP3_DEC_START)
		//{
		//	task_com_keysound_play();	
		//	PLAYMP3MAIN_WAIT_KEYSOUND_END;		
		//}		
		uiItemManageNextItem(winItem(handle,PLAYMP3DIROPS_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsKeyMsgMode
* Description    : playMp3DirOpsKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(mp3_dec_sta() < MP3_DEC_START)
		{
			//task_com_keysound_play();	
			//PLAYMP3MAIN_WAIT_KEYSOUND_END;				
			app_taskChange();
		}else
		{
			uiOpenWindow(&playMp3SubWindow, 0, 0);
		}		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playMp3DirOpsKeyMsgMenu
* Description    : playMp3DirOpsKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);	
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playAudioSysMsgSD
* Description    : playAudioSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playMp3DirOpsSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if((SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)) 
	{
		if(mp3_dec_sta() >= MP3_DEC_START)
		{
			mp3_api_stop();
		}
		uiOpenWindow(&noFileWindow, 0, 1, "no mp3 file");
	}
	return 0;
}



ALIGNED(4) msgDealInfor playMp3DirOpsMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playMp3DirOpsOpenWin},
	{SYS_CLOSE_WINDOW,		playMp3DirOpsCloseWin},
	{SYS_CHILE_COLSE,		playMp3DirOpsWinChildClose},
	{KEY_EVENT_OK,			playMp3DirOpsKeyMsgOk},
	{KEY_EVENT_UP,			playMp3DirOpsKeyMsgUp},
	{KEY_EVENT_DOWN,		playMp3DirOpsKeyMsgDown},
	{KEY_EVENT_MODE,		playMp3DirOpsKeyMsgMode},
	{KEY_EVENT_MENU,		playMp3DirOpsKeyMsgMenu},
	{SYS_EVENT_SDC,			playMp3DirOpsSysMsgSD},

	{EVENT_MAX,NULL},
};

WINDOW(playMp3DirOpsWindow,playMp3DirOpsMsgDeal,playMp3DirOpsWin)


#endif
