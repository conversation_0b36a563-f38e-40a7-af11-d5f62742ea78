/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : dev_led_pwm_init
* Description    : dev_led_pwm_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_init(void)
{
	if(hardware_setup.led_pwm_en)
	{
		hal_gpioInit(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin, GPIO_OUTPUT,GPIO_PULL_FLOATING);
		hal_gpioInit(hardware_setup.led_on_off2_ch, hardware_setup.led_on_off2_pin, GPIO_OUTPUT,GPIO_PULL_FLOATING);

		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, GPIO_OUTPUT,GPIO_PULL_FLOATING);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin,GPIO_LOW);
	}
	return 0;
}

/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_ioctrl(u32 op, u32 para)
{

	if(hardware_setup.led_pwm_en)
	{
		static u32 dev_led_pwm_state = 0;
		switch(op)
		{
			case DEV_LED_ON_OFF_READ:
				if(para)
					*((u32*)para) = dev_led_pwm_state;
				deg_Printf("[dev_led_pwm_ioctrl-DEV_LED_ON_OFF_READ]dev_led_pwm_state=%d\n",dev_led_pwm_state);
				break;
			case DEV_LED_ON_OFF_WRITE:
				dev_led_pwm_state = para;
				deg_Printf("[dev_led_pwm_ioctrl-DEV_LED_ON_OFF_WRITE]para=%d\n",para);
				if(para == 1)
				{
					hal_gpioWrite(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin,GPIO_HIGH);
					hal_gpioWrite(hardware_setup.led_on_off2_ch, hardware_setup.led_on_off2_pin,GPIO_LOW);
				}else if(para == 2)
				{
					hal_gpioWrite(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin,GPIO_LOW);
					hal_gpioWrite(hardware_setup.led_on_off2_ch, hardware_setup.led_on_off2_pin,GPIO_HIGH);
				}
				else
				{	hal_timerPWMStop(hardware_setup.led_pwn_timer);
					hal_gpioWrite(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin,GPIO_LOW);	
					hal_gpioWrite(hardware_setup.led_on_off2_ch, hardware_setup.led_on_off2_pin,GPIO_LOW);	

					hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, GPIO_OUTPUT,GPIO_PULL_FLOATING);
					hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin,GPIO_LOW);	
				}				
				break;
			case DEV_LED_PWM_ADJUST:
				hal_timerPWMStart(hardware_setup.led_pwn_timer,hardware_setup.led_pwm_timer_ch,40000, para);
			break;
		}
	}
	return 0;
}

