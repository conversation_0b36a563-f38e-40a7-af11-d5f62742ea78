/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef __MENU_API_H
#define __MENU_API_H
#include "menu_typedef.h"


EXTERN_MENU(playBack);
EXTERN_MENU(record);

EXTERN_WINDOW(menuItemWindow);
EXTERN_WINDOW(menuOptionWindow);
EXTERN_WINDOW(dateTimeWindow);
EXTERN_WINDOW(defaultWindow);
EXTERN_WINDOW(delAllWindow);
EXTERN_WINDOW(delCurWindow);
EXTERN_WINDOW(formatWindow);
EXTERN_WINDOW(lockCurWindow);
EXTERN_WINDOW(unlockAllWindow);
EXTERN_WINDOW(unlockCurWindow);
EXTERN_WINDOW(versionWindow);
EXTERN_WINDOW(asternWindow);
EXTERN_WINDOW(noFileWindow);
EXTERN_WINDOW(selfTestWindow);
EXTERN_WINDOW(tips1Window);
EXTERN_WINDOW(tipsWindow);
EXTERN_WINDOW(videoResolutionWindow);
EXTERN_WINDOW(autoPowerOffWindow);



/*******************************************************************************
* Function Name  : menuWinIsOpen
* Description    : menuWinIsOpen
* Input          : none
* Output         : none
* Return         : u32: 0: close, 1: open
*******************************************************************************/
u32 menuWinIsOpen(void);

#endif
