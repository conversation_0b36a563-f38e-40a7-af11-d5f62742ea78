/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_LGDP4532

#define CMD(x)	  LCD_CMD_MCU_CMD16(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()

    CMD(0x00),DAT(0x00),DAT(0x01),
    CMD(0x15),DAT(0x00),DAT(0x30),
    CMD(0x11),DAT(0x00),DAT(0x40),
    CMD(0x10),DAT(0x16),DAT(0x28),
    CMD(0x12),DAT(0x00),DAT(0x00),
    CMD(0x13),DAT(0x10),DAT(0x47),	//1047 104d
    CMD(0x12),DAT(0x00),DAT(0x10),
    CMD(0x10),DAT(0x26),DAT(0x20),
    CMD(0x13),DAT(0x30),DAT(0x44),
//-----Gamma control-----------------------
    CMD(0x30),DAT(0x00),DAT(0x00),
    CMD(0x31),DAT(0x04),DAT(0x02),
    CMD(0x32),DAT(0x01),DAT(0x06),
    CMD(0x33),DAT(0x05),DAT(0x03),
    CMD(0x34),DAT(0x01),DAT(0x04),
    CMD(0x35),DAT(0x03),DAT(0x01),
    CMD(0x36),DAT(0x07),DAT(0x07),
    CMD(0x37),DAT(0x03),DAT(0x05),
    CMD(0x38),DAT(0x02),DAT(0x08),
    CMD(0x39),DAT(0x0f),DAT(0x0b),
//-----Normal Setting-----------------------
    CMD(0x01),DAT(0x01),DAT(0x00),
    CMD(0x02),DAT(0x03),DAT(0x00),
    CMD(0x03),DAT(0x10),DAT(0x80),	//1080
    CMD(0x08),DAT(0x06),DAT(0x04),
    CMD(0x09),DAT(0x00),DAT(0x00),
    CMD(0x0A),DAT(0x00),DAT(0x00),//0000 08
    CMD(0x17),DAT(0x07),DAT(0x71),
    CMD(0x41),DAT(0x00),DAT(0x02),
    CMD(0x60),DAT(0x27),DAT(0x00),
    CMD(0x61),DAT(0x00),DAT(0x01),
    CMD(0x90),DAT(0x01),DAT(0x99), //199 182
    CMD(0x93),DAT(0x00),DAT(0x01),
    CMD(0xa3),DAT(0x00),DAT(0x10),

    CMD(0x50),DAT(0x00),DAT(0x00),		//设置水平开始地址
    CMD(0x51),DAT(0x00),DAT(0xef),		//设置水平结束地址
    CMD(0x52),DAT(0x00),DAT(0x00),		//设置水平开始地址
    CMD(0x53),DAT(0x01),DAT(0x3f),
    CMD(0x21),DAT(0x01),DAT(0x3f),
    CMD(0x20),DAT(0x00),DAT(0xef),
//-----Display on-----------------------
    CMD(0x07),DAT(0x00),DAT(0x01),
    CMD(0x07),DAT(0x00),DAT(0x21),
    CMD(0x07),DAT(0x00),DAT(0x23),
    CMD(0x07),DAT(0x00),DAT(0x33),
    CMD(0x07),DAT(0x01),DAT(0x33),
    CMD(0x22),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_lgdp4532",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,
    .te_mode 		= LCD_MCU_TE_DISABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()



#endif







