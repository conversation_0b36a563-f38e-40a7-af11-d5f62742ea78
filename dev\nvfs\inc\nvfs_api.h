/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  NVFS_API_H
    #define  NVFS_API_H


#define  NV_STROAGE_SPI         	0
#define  NV_STROAGE_SDC         	1
#define  NV_STROAGE_SDRAM         	2


#define  NVFS_STROAGE      			NV_STROAGE_SPI        // -----user resource configure


/*******************************************************************************
* Function Name  : nv_port_read
* Description    : nv fs read
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
void nv_port_read(INT32U addr,INT32 buffer,INT32U len);
/*******************************************************************************
* Function Name  : nv_Init
* Description	 :	nv fs initial
* Input 		 : none
* Output		 : none
* Return		 : int 
					  
*******************************************************************************/
int nv_init(void);
/*******************************************************************************
* Function Name  : nv_uninit
* Description    :  nv fs uninitial
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int nv_uninit(void);
/*******************************************************************************
* Function Name  : nv_configAddr
* Description	 :	get nv fs configure address
* Input 		 : none
* Output		 : int res_num
* Return		 : int 
					  
*******************************************************************************/
int nv_configAddr(void);
/*******************************************************************************
* Function Name  : nv_open
* Description	 :	nv fs open
* Input 		 : none
* Output		 : int res_num
* Return		 : int 
					  
*******************************************************************************/
int nv_open(int res_num);
/*******************************************************************************
* Function Name  : nv_size
* Description    :  nv fs get file size
* Input          : none
* Output         : int res_num
* Return         : int 
                      
*******************************************************************************/
int nv_size(int res_num);
/*******************************************************************************
* Function Name  : nv_read
* Description    :  nv fs read data
* Input          : none
* Output         : int res_num
* Return         : int 
                      
*******************************************************************************/
int nv_read(int addr,void *buffer,int size);























#endif







