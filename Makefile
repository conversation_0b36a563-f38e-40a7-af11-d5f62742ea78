# HX330x SDK Makefile for VSCode
# Wrapper around build.bat script

.PHONY: all build clean rebuild help

# Default target
all: build

# Build using build.bat
build:
	@build.bat

# Clean using build.bat
clean:
	@build.bat clean

# Rebuild using build.bat
rebuild:
	@build.bat rebuild

# Show help
help:
	@echo HX330x SDK Build System
	@echo =======================
	@echo Available targets:
	@echo   all      - Build the project (default)
	@echo   build    - Build the project
	@echo   clean    - Clean build files
	@echo   rebuild  - Clean and build
	@echo   help     - Show this help
	@echo.
	@echo Output files:
	@echo   Binary:  app/bin/Debug/hx330x_sdk.elf
	@echo   Objects: app/obj/Debug/*.o
