{"version": "2.0.0", "tasks": [{"label": "Build HX330x SDK", "type": "shell", "command": "${workspaceFolder}/build.bat", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Clean HX330x SDK", "type": "shell", "command": "${workspaceFolder}/build.bat", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "clear": true}}, {"label": "Rebuild HX330x SDK", "type": "shell", "command": "${workspaceFolder}/build.bat", "args": ["rebuild"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "clear": true}}, {"label": "Clean and Build", "dependsOrder": "sequence", "dependsOn": ["Clean HX330x SDK", "Build HX330x SDK"], "group": "build"}, {"label": "Build with Git Make", "type": "shell", "command": "\"C:/Program Files (x86)/GnuWin32/bin/make.exe\"", "options": {"env": {"PATH": "E:/develop/day1/hx330x-gcc-elf-newlib-mingw-V4.9.1/bin;${env:PATH}"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Build with Code::Blocks", "type": "shell", "command": "${workspaceFolder}/build_codeblocks.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}]}