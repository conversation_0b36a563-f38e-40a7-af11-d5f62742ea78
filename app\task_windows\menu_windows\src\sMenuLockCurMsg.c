/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuLockCurWin.c"

/*******************************************************************************
* Function Name  : getlockCurResInfor
* Description    : getlockCurResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getlockCurResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurKeyMsgOk
* Description    : lockCurKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	char *name;
	INT32S list;
	int res;
	//char *srcName;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,LOCKCUR_SELECT_ID));
		if(item == 0)
		{
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					task_com_tips_show(TIPS_COM_FAIL);
					return 0;
				}
				list = SysCtrl.avi_list;
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else if(app_taskCurId()  == TASK_NES_GAME)
			{
				list = SysCtrl.nes_list;
			}else
			{
				return 0;
			}
			if(filelist_api_CountGet(list) <= 0)
			{
				task_com_tips_show(TIPS_NO_FILE);
				return 0;
			}
			res = filelist_fnameChecklockByIndex(list,SysCtrl.file_index); // > 0: lock, 0: AVI and unlock, <0: lock invalid
			if(res == 0)
			{
				name = filelist_GetFileFullNameByIndex(list, SysCtrl.file_index, NULL);
				hx330x_str_cpy(SysCtrl.file_fullname,name);
				filenode_filefullnameLock(name);
				deg_Printf("lock : %s -> %s.",SysCtrl.file_fullname,name);
				if(f_rename(SysCtrl.file_fullname,name)==FR_OK)  // rename in file system
				{
					deg_Printf("->ok\n");
					task_com_tips_show(TIPS_COM_SUCCESS);
					filenode_fnameLockByIndex(list,SysCtrl.file_index);
				}
				else
				{
					task_com_tips_show(TIPS_COM_FAIL);
					deg_Printf("->fail\n");
				}
			}else if(res < 0)
			{
				task_com_tips_show(TIPS_COM_FAIL);
			}		
			else
				task_com_tips_show(TIPS_SET_LOCKED);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurKeyMsgUp
* Description    : lockCurKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,LOCKCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurKeyMsgDown
* Description    : lockCurKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,LOCKCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurKeyMsgMenu
* Description    : lockCurKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurKeyMsgMode
* Description    : lockCurKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurOpenWin
* Description    : lockCurOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]lockCurOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,LOCKCUR_SELECT_ID),1,Rh(32));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,LOCKCUR_SELECT_ID),0,2,Rw(50), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,LOCKCUR_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,LOCKCUR_SELECT_ID),uiItemCreateMenuOption,getlockCurResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,LOCKCUR_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,LOCKCUR_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,LOCKCUR_SELECT_ID),R_ID_PALETTE_Gray);

	uiItemManageSetCurItem(		winItem(handle,LOCKCUR_SELECT_ID),1);

	return 0;
}
/*******************************************************************************
* Function Name  : lockCurCloseWin
* Description    : lockCurCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]lockCurCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurWinChildClose
* Description    : lockCurWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int lockCurWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]lockCurWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurTouchWin
* Description    : lockCurTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int lockCurTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("lockCurTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == LOCKCUR_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : lockCurTouchSlideOff
* Description    : lockCurTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int lockCurTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor lockCurMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	lockCurOpenWin},
	{SYS_CLOSE_WINDOW,	lockCurCloseWin},
	{SYS_CHILE_COLSE,	lockCurWinChildClose},
	{SYS_TOUCH_WINDOW,  lockCurTouchWin},
	{SYS_TOUCH_SLIDE_OFF,lockCurTouchSlideOff},
	{KEY_EVENT_OK,		lockCurKeyMsgOk},
	{KEY_EVENT_UP,		lockCurKeyMsgUp},
	{KEY_EVENT_DOWN,	lockCurKeyMsgDown},
	{KEY_EVENT_MENU,	lockCurKeyMsgMenu},
	{KEY_EVENT_MODE,	lockCurKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(lockCurWindow,lockCurMsgDeal,lockCurWin)


